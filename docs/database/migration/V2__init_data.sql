-- V2__init_data.sql
-- 初始化基础数据

USE `user_db`;

-- 插入角色数据
INSERT INTO `role` (`name`, `code`, `description`) VALUES
('系统管理员', 'ADMIN', '系统管理员角色'),
('拍卖师', 'AUCTIONEER', '拍卖师角色'),
('买家', 'BUYER', '买家角色'),
('质检员', 'QUALITY_INSPECTOR', '质检员角色');

-- 插入管理员用户
INSERT INTO `user` (`username`, `password`, `real_name`, `phone`, `email`, `user_type`, `status`) VALUES
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUneSZedOgClFisA8XkXy8e.PYwB8kwXBLG', '系统管理员', '13800000000', '<EMAIL>', 3, 1);

-- 插入管理员角色关联
INSERT INTO `user_role` (`user_id`, `role_id`)
SELECT u.id, r.id FROM `user` u, `role` r WHERE u.username = 'admin' AND r.code = 'ADMIN';

USE `product_db`;

-- 插入商品类别数据
INSERT INTO `category` (`name`, `parent_id`, `level`, `sort_order`) VALUES
('鲜花', NULL, 1, 1),
('绿植', NULL, 1, 2),
('玫瑰', 1, 2, 1),
('百合', 1, 2, 2),
('康乃馨', 1, 2, 3),
('盆栽', 2, 2, 1),
('观叶植物', 2, 2, 2); 