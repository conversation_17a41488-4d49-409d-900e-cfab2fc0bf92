 # 昆明花卉拍卖系统数据库设计

## 1. 业务实体分析

### 1.1 用户相关实体
- 用户（User）
- 角色（Role）
- 权限（Permission）
- 用户认证信息（UserAuth）

### 1.2 商品相关实体
- 商品（Product）
- 商品类别（Category）
- 商品图片（ProductImage）
- 商品质检记录（QualityCheck）

### 1.3 拍卖相关实体
- 拍卖会（Auction）
- 拍卖场次（AuctionSession）
- 拍卖商品（AuctionItem）
- 竞价记录（Bid）

### 1.4 订单相关实体
- 订单（Order）
- 订单明细（OrderDetail）
- 支付记录（Payment）
- 物流信息（Logistics）

### 1.5 系统相关实体
- 系统配置（SystemConfig）
- 操作日志（OperationLog）
- 系统消息（SystemMessage）
- 统计数据（Statistics）

## 2. 数据表设计

### 2.1 用户相关表

#### 2.1.1 用户表（user）
```sql
CREATE TABLE `user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '用户ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
    `phone` varchar(20) NOT NULL COMMENT '手机号',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `user_type` tinyint(4) NOT NULL COMMENT '用户类型：1-拍卖师 2-买家 3-管理员 4-质检员',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_phone` (`phone`),
    KEY `idx_user_type` (`user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';
```

#### 2.1.2 角色表（role）
```sql
CREATE TABLE `role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '角色ID',
    `name` varchar(50) NOT NULL COMMENT '角色名称',
    `code` varchar(50) NOT NULL COMMENT '角色编码',
    `description` varchar(200) DEFAULT NULL COMMENT '角色描述',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';
```

#### 2.1.3 用户角色关联表（user_role）
```sql
CREATE TABLE `user_role` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `role_id` bigint(20) NOT NULL COMMENT '角色ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_user_role` (`user_id`,`role_id`),
    KEY `idx_role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';
```

### 2.2 商品相关表

#### 2.2.1 商品表（product）
```sql
CREATE TABLE `product` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '商品ID',
    `name` varchar(100) NOT NULL COMMENT '商品名称',
    `category_id` bigint(20) NOT NULL COMMENT '类别ID',
    `description` text COMMENT '商品描述',
    `quality_level` tinyint(4) NOT NULL COMMENT '品质等级：1-优 2-良 3-中',
    `origin` varchar(100) NOT NULL COMMENT '产地',
    `supplier_id` bigint(20) NOT NULL COMMENT '供应商ID',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-下架 1-上架',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_category` (`category_id`),
    KEY `idx_supplier` (`supplier_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';
```

#### 2.2.2 商品类别表（category）
```sql
CREATE TABLE `category` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '类别ID',
    `name` varchar(50) NOT NULL COMMENT '类别名称',
    `parent_id` bigint(20) DEFAULT NULL COMMENT '父类别ID',
    `level` tinyint(4) NOT NULL COMMENT '层级',
    `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用 1-启用',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_parent` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品类别表';
```

### 2.3 拍卖相关表

#### 2.3.1 拍卖会表（auction）
```sql
CREATE TABLE `auction` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '拍卖会ID',
    `name` varchar(100) NOT NULL COMMENT '拍卖会名称',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime NOT NULL COMMENT '结束时间',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始 1-进行中 2-已结束',
    `auctioneer_id` bigint(20) NOT NULL COMMENT '拍卖师ID',
    `description` text COMMENT '拍卖会描述',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_auctioneer` (`auctioneer_id`),
    KEY `idx_time` (`start_time`, `end_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拍卖会表';
```

#### 2.3.2 拍卖商品表（auction_item）
```sql
CREATE TABLE `auction_item` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '拍卖商品ID',
    `auction_id` bigint(20) NOT NULL COMMENT '拍卖会ID',
    `product_id` bigint(20) NOT NULL COMMENT '商品ID',
    `start_price` decimal(10,2) NOT NULL COMMENT '起拍价',
    `current_price` decimal(10,2) NOT NULL COMMENT '当前价格',
    `step_price` decimal(10,2) NOT NULL COMMENT '加价幅度',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-未开始 1-进行中 2-已成交 3-流拍',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime DEFAULT NULL COMMENT '结束时间',
    `winner_id` bigint(20) DEFAULT NULL COMMENT '中标用户ID',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    KEY `idx_auction` (`auction_id`),
    KEY `idx_product` (`product_id`),
    KEY `idx_winner` (`winner_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='拍卖商品表';
```

#### 2.3.3 竞价记录表（bid）
```sql
CREATE TABLE `bid` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '竞价ID',
    `auction_item_id` bigint(20) NOT NULL COMMENT '拍卖商品ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `price` decimal(10,2) NOT NULL COMMENT '出价',
    `status` tinyint(4) NOT NULL DEFAULT '1' COMMENT '状态：1-有效 0-无效',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `idx_auction_item` (`auction_item_id`),
    KEY `idx_user` (`user_id`),
    KEY `idx_create_time` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='竞价记录表';
```

### 2.4 订单相关表

#### 2.4.1 订单表（order）
```sql
CREATE TABLE `order` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
    `order_no` varchar(32) NOT NULL COMMENT '订单编号',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `auction_item_id` bigint(20) NOT NULL COMMENT '拍卖商品ID',
    `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待支付 1-已支付 2-已发货 3-已完成 4-已取消',
    `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
    `ship_time` datetime DEFAULT NULL COMMENT '发货时间',
    `complete_time` datetime DEFAULT NULL COMMENT '完成时间',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_order_no` (`order_no`),
    KEY `idx_user` (`user_id`),
    KEY `idx_auction_item` (`auction_item_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';
```

#### 2.4.2 支付记录表（payment）
```sql
CREATE TABLE `payment` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '支付ID',
    `order_id` bigint(20) NOT NULL COMMENT '订单ID',
    `payment_no` varchar(64) NOT NULL COMMENT '支付流水号',
    `amount` decimal(10,2) NOT NULL COMMENT '支付金额',
    `payment_method` tinyint(4) NOT NULL COMMENT '支付方式：1-支付宝 2-微信 3-银行卡',
    `status` tinyint(4) NOT NULL DEFAULT '0' COMMENT '状态：0-待支付 1-支付成功 2-支付失败',
    `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_payment_no` (`payment_no`),
    KEY `idx_order` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';
```

## 3. 分库分表策略

### 3.1 分库策略
- 按业务垂直分库
  * user_db：用户相关数据
  * product_db：商品相关数据
  * auction_db：拍卖相关数据
  * order_db：订单相关数据

### 3.2 分表策略
- 竞价记录表（bid）：按月分表
- 订单表（order）：按用户ID范围分表
- 支付记录表（payment）：按月分表
- 操作日志表（operation_log）：按月分表

## 4. 索引设计原则

### 4.1 通用原则
- 主键使用自增ID
- 常用查询字段建立索引
- 避免冗余索引
- 控制索引数量

### 4.2 重要索引
- 用户表：用户名、手机号唯一索引
- 订单表：订单编号唯一索引
- 支付表：支付流水号唯一索引
- 商品表：类别ID、供应商ID索引
- 拍卖商品表：拍卖会ID、商品ID索引
- 竞价记录表：拍卖商品ID、用户ID复合索引

## 5. 数据库优化策略

### 5.1 读写分离
- 主库：处理写操作
- 从库：处理读操作
- 从库数量：4个

### 5.2 缓存策略
- 热点商品信息缓存
- 用户信息缓存
- 竞价数据缓存
- 订单状态缓存

### 5.3 性能优化
- 合理使用索引
- SQL语句优化
- 表结构优化
- 定期维护