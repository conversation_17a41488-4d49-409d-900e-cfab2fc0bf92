<!DOCTYPE html>
<html>
<head>
    <title>登录测试</title>
</head>
<body>
    <h1>登录API测试</h1>
    <button onclick="testLogin()">测试登录</button>
    <div id="result"></div>

    <script>
        async function testLogin() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在测试登录...';
            
            try {
                const response = await fetch('http://localhost:8081/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });
                
                const data = await response.json();
                resultDiv.innerHTML = `
                    <h3>响应状态: ${response.status}</h3>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<h3>错误: ${error.message}</h3>`;
            }
        }
    </script>
</body>
</html>
