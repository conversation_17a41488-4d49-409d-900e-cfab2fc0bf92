const express = require('express');
const cors = require('cors');
const app = express();
const port = 8082;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟数据
const users = [
  { id: 1, username: 'admin', realName: '管理员', userType: 1, status: 1, email: '<EMAIL>', phone: '13800138000' },
  { id: 2, username: 'user1', realName: '用户1', userType: 3, status: 1, email: '<EMAIL>', phone: '13800138001' },
];

const products = [
  { id: 1, name: '玫瑰花', description: '红色玫瑰花', qualityLevel: 3, origin: '昆明', status: 1 },
  { id: 2, name: '康乃馨', description: '粉色康乃馨', qualityLevel: 2, origin: '昆明', status: 1 },
];

const auctions = [
  { id: 1, name: '春季花卉拍卖会', description: '2024年春季花卉拍卖会', status: 1, startTime: '2024-06-01 09:00:00', endTime: '2024-06-01 17:00:00' },
];

const orders = [
  { id: 1, orderNo: 'ORD20240529001', status: 1, amount: 100.00, userId: 2, productName: '玫瑰花' },
];

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'ok', message: '模拟后端服务器运行正常' });
});

// 认证接口
app.post('/api/v1/auth/login', (req, res) => {
  const { username, password } = req.body;
  if (username === 'admin' && password === 'admin123') {
    res.json({
      success: true,
      data: {
        token: 'mock-token-123',
        refreshToken: 'mock-refresh-token-123',
        user: users[0]
      }
    });
  } else {
    res.status(401).json({
      success: false,
      message: '用户名或密码错误'
    });
  }
});

app.post('/api/v1/auth/logout', (req, res) => {
  res.json({ success: true, message: '登出成功' });
});

app.post('/api/v1/auth/refresh', (req, res) => {
  res.json({
    success: true,
    data: {
      token: 'new-mock-token-123',
      refreshToken: 'new-mock-refresh-token-123'
    }
  });
});

app.get('/api/v1/auth/me', (req, res) => {
  res.json({
    success: true,
    data: users[0]
  });
});

// 用户管理接口
app.get('/api/v1/users', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const size = parseInt(req.query.size) || 10;

  res.json({
    success: true,
    data: {
      list: users,
      total: users.length,
      page,
      size
    }
  });
});

app.get('/api/v1/users/info/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const user = users.find(u => u.id === id);

  if (user) {
    res.json({ success: true, data: user });
  } else {
    res.status(404).json({ success: false, message: '用户不存在' });
  }
});

app.put('/api/v1/users/status/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const user = users.find(u => u.id === id);

  if (user) {
    user.status = req.body.status;
    res.json({ success: true, message: '用户状态更新成功' });
  } else {
    res.status(404).json({ success: false, message: '用户不存在' });
  }
});

app.delete('/api/v1/users/batch', (req, res) => {
  res.json({ success: true, message: '批量删除成功' });
});

app.get('/api/v1/users/export', (req, res) => {
  res.setHeader('Content-Type', 'text/csv');
  res.setHeader('Content-Disposition', 'attachment; filename=users.csv');
  res.send('ID,用户名,真实姓名,用户类型,状态\n1,admin,管理员,管理员,启用\n2,user1,用户1,普通用户,启用');
});

app.get('/api/v1/users/statistics', (req, res) => {
  res.json({
    success: true,
    data: {
      total: users.length,
      activeUsers: users.filter(u => u.status === 1).length,
      newUsersToday: 0,
      userTypeDistribution: { 1: 1, 2: 0, 3: 1 }
    }
  });
});

// 商品管理接口
app.get('/api/v1/products', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const size = parseInt(req.query.size) || 10;

  res.json({
    success: true,
    data: {
      list: products,
      total: products.length,
      page,
      size
    }
  });
});

app.put('/api/v1/products/status/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const product = products.find(p => p.id === id);

  if (product) {
    product.status = req.body.status;
    res.json({ success: true, message: '商品状态更新成功' });
  } else {
    res.status(404).json({ success: false, message: '商品不存在' });
  }
});

app.get('/api/v1/categories/tree', (req, res) => {
  res.json({
    success: true,
    data: [
      { id: 1, name: '鲜花', children: [
        { id: 11, name: '玫瑰' },
        { id: 12, name: '康乃馨' }
      ]},
      { id: 2, name: '绿植', children: [
        { id: 21, name: '多肉植物' }
      ]}
    ]
  });
});

// 拍卖管理接口
app.get('/api/v1/auctions', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const size = parseInt(req.query.size) || 10;

  res.json({
    success: true,
    data: {
      list: auctions,
      total: auctions.length,
      page,
      size
    }
  });
});

app.get('/api/v1/auction-items/auction/:auctionId', (req, res) => {
  res.json({
    success: true,
    data: {
      list: [
        {
          id: 1,
          productId: 1,
          productName: '玫瑰花',
          startingPrice: 50.00,
          currentPrice: 80.00,
          bidCount: 5,
          status: '竞拍中'
        }
      ],
      total: 1
    }
  });
});

// 订单管理接口
app.get('/api/v1/orders', (req, res) => {
  const page = parseInt(req.query.page) || 1;
  const size = parseInt(req.query.size) || 10;

  res.json({
    success: true,
    data: {
      list: orders,
      total: orders.length,
      page,
      size
    }
  });
});

// 启动服务器
app.listen(port, () => {
  console.log(`模拟后端服务器运行在 http://localhost:${port}`);
  console.log(`健康检查: http://localhost:${port}/health`);
  console.log(`API基础路径: http://localhost:${port}/api/v1`);
});
