package main

import (
	"fmt"
	"golang.org/x/crypto/bcrypt"
)

func main() {
	// 数据库中的哈希值
	dbHash := "$2a$10$N.zmdr9k7uOCQb376NoUneSZedOgClFisA8XkXy8e.PYwB8kwXBLG"
	
	// 常见密码列表
	passwords := []string{
		"admin",
		"admin123",
		"123456",
		"password",
		"admin@123",
		"flower123",
		"auction123",
		"flower_auction",
		"123456789",
		"qwerty",
		"password123",
	}
	
	fmt.Println("测试密码...")
	for _, password := range passwords {
		err := bcrypt.CompareHashAndPassword([]byte(dbHash), []byte(password))
		if err == nil {
			fmt.Printf("✅ 找到匹配密码: %s\n", password)
			return
		} else {
			fmt.Printf("❌ %s - 不匹配\n", password)
		}
	}
	
	fmt.Println("没有找到匹配的密码")
}
