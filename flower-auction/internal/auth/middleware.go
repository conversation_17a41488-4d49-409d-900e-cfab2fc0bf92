package auth

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
)

const (
	AuthorizationHeader = "Authorization"
	BearerPrefix        = "Bearer "
	UserIDKey           = "user_id"
	UsernameKey         = "username"
	UserTypeKey         = "user_type"
	RolesKey            = "roles"
)

// Middleware 认证中间件
type Middleware struct {
	authService Service
}

// NewMiddleware 创建认证中间件
func NewMiddleware(authService Service) *Middleware {
	return &Middleware{
		authService: authService,
	}
}

// JWTAuth JWT认证中间件
func (m *Middleware) JWTAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>eader(AuthorizationHeader)
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证头"})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, BearerPrefix) {
			c.<PERSON><PERSON>(http.StatusUnauthorized, gin.H{"error": "无效的认证头格式"})
			c.Abort()
			return
		}

		// 提取token
		token := strings.TrimPrefix(authHeader, BearerPrefix)
		if token == "" {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "缺少认证令牌"})
			c.Abort()
			return
		}

		// 验证token
		tokenInfo, err := m.authService.ValidateToken(token)
		if err != nil {
			if err == ErrTokenExpired {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "令牌已过期"})
			} else {
				c.JSON(http.StatusUnauthorized, gin.H{"error": "无效的令牌"})
			}
			c.Abort()
			return
		}

		// 将用户信息存储到上下文
		c.Set(UserIDKey, tokenInfo.UserID)
		c.Set(UsernameKey, tokenInfo.Username)
		c.Set(UserTypeKey, tokenInfo.UserType)
		c.Set(RolesKey, tokenInfo.Roles)

		c.Next()
	}
}

// RequireRole 要求特定角色的中间件
func (m *Middleware) RequireRole(requiredRoles ...int64) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户角色
		rolesInterface, exists := c.Get(RolesKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "未找到用户角色信息"})
			c.Abort()
			return
		}

		userRoles, ok := rolesInterface.([]int64)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "角色信息格式错误"})
			c.Abort()
			return
		}

		// 检查是否有所需角色
		hasRole := false
		for _, userRole := range userRoles {
			for _, requiredRole := range requiredRoles {
				if userRole == requiredRole {
					hasRole = true
					break
				}
			}
			if hasRole {
				break
			}
		}

		if !hasRole {
			c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequirePermission 要求特定权限的中间件
func (m *Middleware) RequirePermission(permission string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取用户ID
		userIDInterface, exists := c.Get(UserIDKey)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{"error": "未找到用户信息"})
			c.Abort()
			return
		}

		_, ok := userIDInterface.(int64)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "用户ID格式错误"})
			c.Abort()
			return
		}

		// 这里需要调用权限服务检查用户是否有指定权限
		// 由于权限服务在另一个包中，这里先简化处理
		// 实际实现时需要注入权限服务

		// TODO: 实现权限检查逻辑
		// hasPermission := m.permissionService.CheckUserPermission(userID, permission)
		// if !hasPermission {
		//     c.JSON(http.StatusForbidden, gin.H{"error": "权限不足"})
		//     c.Abort()
		//     return
		// }

		c.Next()
	}
}

// GetCurrentUser 获取当前用户信息
func GetCurrentUser(c *gin.Context) (userID int64, username string, userType int8, roles []int64, ok bool) {
	userIDInterface, exists := c.Get(UserIDKey)
	if !exists {
		return 0, "", 0, nil, false
	}

	usernameInterface, exists := c.Get(UsernameKey)
	if !exists {
		return 0, "", 0, nil, false
	}

	userTypeInterface, exists := c.Get(UserTypeKey)
	if !exists {
		return 0, "", 0, nil, false
	}

	rolesInterface, exists := c.Get(RolesKey)
	if !exists {
		return 0, "", 0, nil, false
	}

	userID, ok1 := userIDInterface.(int64)
	username, ok2 := usernameInterface.(string)
	userType, ok3 := userTypeInterface.(int8)
	roles, ok4 := rolesInterface.([]int64)

	if !ok1 || !ok2 || !ok3 || !ok4 {
		return 0, "", 0, nil, false
	}

	return userID, username, userType, roles, true
}

// GetCurrentUserID 获取当前用户ID
func GetCurrentUserID(c *gin.Context) (int64, bool) {
	userID, _, _, _, ok := GetCurrentUser(c)
	return userID, ok
}
