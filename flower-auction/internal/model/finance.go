package model

import "time"

// Account 资金账户
type Account struct {
	ID           int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID       int64     `json:"userId" gorm:"not null;index"`
	AccountType  int8      `json:"accountType" gorm:"not null"` // 1:主账户 2:保证金账户 3:佣金账户
	Balance      float64   `json:"balance" gorm:"type:decimal(15,2);default:0"`
	FrozenAmount float64   `json:"frozenAmount" gorm:"type:decimal(15,2);default:0"`
	Status       int8      `json:"status" gorm:"default:1"` // 1:正常 2:冻结 3:注销
	CreatedAt    time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (Account) TableName() string {
	return "account"
}

// Transaction 交易流水
type Transaction struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID        int64     `json:"userId" gorm:"not null;index"`
	TransactionNo string    `json:"transactionNo" gorm:"uniqueIndex;size:64;not null"`
	Type          int8      `json:"type" gorm:"not null"` // 1:充值 2:提现 3:消费 4:冻结 5:解冻 6:佣金收入
	Amount        float64   `json:"amount" gorm:"type:decimal(15,2);not null"`
	Balance       float64   `json:"balance" gorm:"type:decimal(15,2);not null"`
	Description   string    `json:"description" gorm:"size:500"`
	Status        int8      `json:"status" gorm:"default:1"` // 1:成功 2:失败 3:处理中
	CreatedAt     time.Time `json:"createdAt" gorm:"autoCreateTime"`
}

// TableName 指定表名
func (Transaction) TableName() string {
	return "transaction"
}

// Commission 佣金记录
type Commission struct {
	ID               int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	OrderID          int64     `json:"orderId" gorm:"not null;index"`
	UserID           int64     `json:"userId" gorm:"not null;index"`
	Amount           float64   `json:"amount" gorm:"type:decimal(15,2);not null"`
	CommissionRate   float64   `json:"commissionRate" gorm:"type:decimal(5,4);not null"`
	CommissionAmount float64   `json:"commissionAmount" gorm:"type:decimal(15,2);not null"`
	Status           int8      `json:"status" gorm:"default:0"` // 0:待结算 1:已结算 2:已取消
	SettledAt        time.Time `json:"settledAt"`
	CreatedAt        time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt        time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (Commission) TableName() string {
	return "commission"
}

// IncomeStatistics 收入统计
type IncomeStatistics struct {
	StartDate        time.Time `json:"startDate"`
	EndDate          time.Time `json:"endDate"`
	TotalIncome      float64   `json:"totalIncome"`
	TotalExpense     float64   `json:"totalExpense"`
	NetIncome        float64   `json:"netIncome"`
	TransactionCount int64     `json:"transactionCount"`
}

// UserIncomeStatistics 用户收入统计
type UserIncomeStatistics struct {
	UserID           int64     `json:"userId"`
	StartDate        time.Time `json:"startDate"`
	EndDate          time.Time `json:"endDate"`
	TotalIncome      float64   `json:"totalIncome"`
	TotalExpense     float64   `json:"totalExpense"`
	NetIncome        float64   `json:"netIncome"`
	TransactionCount int64     `json:"transactionCount"`
}

// DailyFinanceReport 日财务报表
type DailyFinanceReport struct {
	Date             time.Time `json:"date"`
	TotalIncome      float64   `json:"totalIncome"`
	TotalExpense     float64   `json:"totalExpense"`
	NetIncome        float64   `json:"netIncome"`
	TransactionCount int64     `json:"transactionCount"`
}

// MonthlyFinanceReport 月财务报表
type MonthlyFinanceReport struct {
	Year             int     `json:"year"`
	Month            int     `json:"month"`
	TotalIncome      float64 `json:"totalIncome"`
	TotalExpense     float64 `json:"totalExpense"`
	NetIncome        float64 `json:"netIncome"`
	TransactionCount int64   `json:"transactionCount"`
}

// AccountDetail 账户详情（包含用户信息）
type AccountDetail struct {
	Account
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// TransactionDetail 交易详情（包含用户信息）
type TransactionDetail struct {
	Transaction
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// CommissionDetail 佣金详情（包含订单和用户信息）
type CommissionDetail struct {
	Commission
	Order Order `json:"order" gorm:"foreignKey:OrderID"`
	User  User  `json:"user" gorm:"foreignKey:UserID"`
}

// AccountSummary 账户汇总
type AccountSummary struct {
	UserID             int64   `json:"userId"`
	TotalBalance       float64 `json:"totalBalance"`
	TotalFrozenAmount  float64 `json:"totalFrozenAmount"`
	AvailableBalance   float64 `json:"availableBalance"`
	MainAccountBalance float64 `json:"mainAccountBalance"`
	DepositBalance     float64 `json:"depositBalance"`
	CommissionBalance  float64 `json:"commissionBalance"`
}

// TransactionSummary 交易汇总
type TransactionSummary struct {
	UserID           int64        `json:"userId"`
	TotalIncome      float64      `json:"totalIncome"`
	TotalExpense     float64      `json:"totalExpense"`
	NetIncome        float64      `json:"netIncome"`
	TransactionCount int64        `json:"transactionCount"`
	LastTransaction  *Transaction `json:"lastTransaction"`
}

// CommissionSummary 佣金汇总
type CommissionSummary struct {
	UserID            int64   `json:"userId"`
	TotalCommission   float64 `json:"totalCommission"`
	SettledCommission float64 `json:"settledCommission"`
	PendingCommission float64 `json:"pendingCommission"`
	CommissionCount   int64   `json:"commissionCount"`
	SettledCount      int64   `json:"settledCount"`
	PendingCount      int64   `json:"pendingCount"`
}

// FinanceDashboard 财务仪表板
type FinanceDashboard struct {
	TotalUsers         int64                `json:"totalUsers"`
	TotalAccounts      int64                `json:"totalAccounts"`
	TotalBalance       float64              `json:"totalBalance"`
	TotalFrozenAmount  float64              `json:"totalFrozenAmount"`
	TodayIncome        float64              `json:"todayIncome"`
	TodayExpense       float64              `json:"todayExpense"`
	TodayTransactions  int64                `json:"todayTransactions"`
	MonthIncome        float64              `json:"monthIncome"`
	MonthExpense       float64              `json:"monthExpense"`
	MonthTransactions  int64                `json:"monthTransactions"`
	PendingCommissions float64              `json:"pendingCommissions"`
	SettledCommissions float64              `json:"settledCommissions"`
	RecentTransactions []*TransactionDetail `json:"recentTransactions"`
	TopUsers           []*AccountSummary    `json:"topUsers"`
}

// WithdrawRequest 提现申请
type WithdrawRequest struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID      int64     `json:"userId" gorm:"not null;index"`
	Amount      float64   `json:"amount" gorm:"type:decimal(15,2);not null"`
	BankCard    string    `json:"bankCard" gorm:"size:32;not null"`
	BankName    string    `json:"bankName" gorm:"size:100"`
	AccountName string    `json:"accountName" gorm:"size:100"`
	Status      int8      `json:"status" gorm:"default:0"` // 0:待审核 1:已通过 2:已拒绝 3:已完成
	Reason      string    `json:"reason" gorm:"size:500"`
	ProcessedBy int64     `json:"processedBy"`
	ProcessedAt time.Time `json:"processedAt"`
	CreatedAt   time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt   time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (WithdrawRequest) TableName() string {
	return "withdraw_request"
}

// DepositRecord 充值记录
type DepositRecord struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID        int64     `json:"userId" gorm:"not null;index"`
	Amount        float64   `json:"amount" gorm:"type:decimal(15,2);not null"`
	PaymentMethod int8      `json:"paymentMethod" gorm:"not null"` // 1:支付宝 2:微信 3:银行卡
	PaymentNo     string    `json:"paymentNo" gorm:"size:64"`
	Status        int8      `json:"status" gorm:"default:0"` // 0:待支付 1:已支付 2:支付失败
	CreatedAt     time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt     time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// TableName 指定表名
func (DepositRecord) TableName() string {
	return "deposit_record"
}

// Reconciliation 对账记录
type Reconciliation struct {
	ID           int64      `json:"id" gorm:"primaryKey;autoIncrement"`
	BatchNo      string     `json:"batchNo" gorm:"uniqueIndex;size:64;not null"`
	Type         int8       `json:"type" gorm:"not null"` // 1:支付对账 2:提现对账 3:佣金对账
	StartDate    time.Time  `json:"startDate" gorm:"not null"`
	EndDate      time.Time  `json:"endDate" gorm:"not null"`
	Status       int8       `json:"status" gorm:"default:0"` // 0:待处理 1:处理中 2:已完成 3:异常
	TotalCount   int64      `json:"totalCount" gorm:"default:0"`
	SuccessCount int64      `json:"successCount" gorm:"default:0"`
	FailCount    int64      `json:"failCount" gorm:"default:0"`
	TotalAmount  float64    `json:"totalAmount" gorm:"type:decimal(15,2);default:0"`
	DiffAmount   float64    `json:"diffAmount" gorm:"type:decimal(15,2);default:0"`
	Remark       string     `json:"remark" gorm:"size:500"`
	CreatedBy    int64      `json:"createdBy" gorm:"not null"`
	CreatedAt    time.Time  `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt    time.Time  `json:"updatedAt" gorm:"autoUpdateTime"`
	ProcessedAt  *time.Time `json:"processedAt"`
}

// ReconciliationResult 对账结果
type ReconciliationResult struct {
	BatchNo      string                    `json:"batchNo"`
	Type         int8                      `json:"type"`
	Status       int8                      `json:"status"`
	TotalCount   int64                     `json:"totalCount"`
	SuccessCount int64                     `json:"successCount"`
	FailCount    int64                     `json:"failCount"`
	TotalAmount  float64                   `json:"totalAmount"`
	DiffAmount   float64                   `json:"diffAmount"`
	DiffItems    []*ReconciliationDiffItem `json:"diffItems"`
	Summary      *ReconciliationSummary    `json:"summary"`
	ProcessedAt  time.Time                 `json:"processedAt"`
}

// ReconciliationDiffItem 对账差异项
type ReconciliationDiffItem struct {
	TransactionNo string  `json:"transactionNo"`
	OrderNo       string  `json:"orderNo"`
	Amount        float64 `json:"amount"`
	SystemAmount  float64 `json:"systemAmount"`
	ThirdAmount   float64 `json:"thirdAmount"`
	DiffAmount    float64 `json:"diffAmount"`
	DiffType      string  `json:"diffType"` // amount_diff, missing_system, missing_third
	Reason        string  `json:"reason"`
}

// ReconciliationSummary 对账汇总
type ReconciliationSummary struct {
	AmountDiffCount    int64   `json:"amountDiffCount"`
	MissingSystemCount int64   `json:"missingSystemCount"`
	MissingThirdCount  int64   `json:"missingThirdCount"`
	TotalDiffAmount    float64 `json:"totalDiffAmount"`
}
