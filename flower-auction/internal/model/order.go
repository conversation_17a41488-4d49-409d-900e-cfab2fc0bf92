package model

import (
	"time"
)

// Order 订单模型
type Order struct {
	ID            int64      `gorm:"primaryKey;column:id" json:"id"`
	OrderNo       string     `gorm:"column:order_no;uniqueIndex" json:"orderNo"`
	UserID        int64      `gorm:"column:user_id" json:"userId"`
	AuctionItemID int64      `gorm:"column:auction_item_id" json:"auctionItemId"`
	Amount        float64    `gorm:"column:amount" json:"amount"`
	Status        int8       `gorm:"column:status" json:"status"` // 0-待支付 1-已支付 2-已发货 3-已完成 4-已取消
	PayTime       *time.Time `gorm:"column:pay_time" json:"payTime"`
	ShipTime      *time.Time `gorm:"column:ship_time" json:"shipTime"`
	CompleteTime  *time.Time `gorm:"column:complete_time" json:"completeTime"`
	CreatedAt     time.Time  `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt     time.Time  `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Order) TableName() string {
	return "order"
}

// Payment 支付记录模型
type Payment struct {
	ID            int64     `gorm:"primaryKey;column:id" json:"id"`
	OrderID       int64     `gorm:"column:order_id" json:"orderId"`
	PaymentNo     string    `gorm:"column:payment_no;uniqueIndex" json:"paymentNo"`
	Amount        float64   `gorm:"column:amount" json:"amount"`
	PaymentMethod int8      `gorm:"column:payment_method" json:"paymentMethod"` // 1-支付宝 2-微信 3-银行卡
	Status        int8      `gorm:"column:status" json:"status"`                // 0-待支付 1-支付成功 2-支付失败
	CreatedAt     time.Time `gorm:"column:created_at" json:"createdAt"`
	UpdatedAt     time.Time `gorm:"column:updated_at" json:"updatedAt"`
}

// TableName 指定表名
func (Payment) TableName() string {
	return "payment"
}

// OrderDetail 订单详情（包含商品信息和支付信息）
type OrderDetail struct {
	Order
	AuctionItem AuctionItem `json:"auctionItem"`
	Product     Product     `json:"product"`
	User        User        `json:"user"`
	Payment     *Payment    `json:"payment"`
}

// PaymentOrder 支付订单
type PaymentOrder struct {
	PaymentNo     string    `json:"paymentNo"`
	OrderID       int64     `json:"orderId"`
	Amount        float64   `json:"amount"`
	PaymentMethod int8      `json:"paymentMethod"` // 1:支付宝 2:微信 3:银行卡
	PaymentURL    string    `json:"paymentUrl,omitempty"`
	QRCode        string    `json:"qrCode,omitempty"`
	BankCard      string    `json:"bankCard,omitempty"`
	ExpireTime    time.Time `json:"expireTime"`
}

// PaymentStatus 支付状态
type PaymentStatus struct {
	PaymentNo     string    `json:"paymentNo"`
	OrderID       int64     `json:"orderId"`
	Amount        float64   `json:"amount"`
	PaymentMethod int8      `json:"paymentMethod"`
	Status        int8      `json:"status"` // 0:待支付 1:已支付 2:支付失败
	CreatedAt     time.Time `json:"createdAt"`
	UpdatedAt     time.Time `json:"updatedAt"`
}

// RefundOrder 退款订单
type RefundOrder struct {
	ID        int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	RefundNo  string    `json:"refundNo" gorm:"uniqueIndex;size:64;not null"`
	PaymentNo string    `json:"paymentNo" gorm:"size:64;not null"`
	OrderID   int64     `json:"orderId" gorm:"not null"`
	Amount    float64   `json:"amount" gorm:"type:decimal(10,2);not null"`
	Reason    string    `json:"reason" gorm:"size:500"`
	Status    int8      `json:"status" gorm:"default:0"` // 0:退款中 1:退款成功 2:退款失败
	CreatedAt time.Time `json:"createdAt" gorm:"autoCreateTime"`
	UpdatedAt time.Time `json:"updatedAt" gorm:"autoUpdateTime"`
}

// OrderExportQuery 订单导出查询条件
type OrderExportQuery struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
	Status    *int8     `json:"status"`
	UserID    *int64    `json:"userId"`
	MinAmount *float64  `json:"minAmount"`
	MaxAmount *float64  `json:"maxAmount"`
	Format    string    `json:"format"` // csv, excel
}

// OrderStatisticsQuery 订单统计查询条件
type OrderStatisticsQuery struct {
	StartDate time.Time `json:"startDate"`
	EndDate   time.Time `json:"endDate"`
	GroupBy   string    `json:"groupBy"` // day, week, month
	Status    *int8     `json:"status"`
	UserID    *int64    `json:"userId"`
}

// OrderStatistics 订单统计结果
type OrderStatistics struct {
	TotalOrders    int64                  `json:"totalOrders"`
	TotalAmount    float64                `json:"totalAmount"`
	AvgAmount      float64                `json:"avgAmount"`
	StatusStats    map[string]int64       `json:"statusStats"`
	TimeSeriesData []*OrderTimeSeriesData `json:"timeSeriesData"`
	TopUsers       []*OrderUserStats      `json:"topUsers"`
}

// OrderTimeSeriesData 订单时间序列数据
type OrderTimeSeriesData struct {
	Date        string  `json:"date"`
	OrderCount  int64   `json:"orderCount"`
	TotalAmount float64 `json:"totalAmount"`
}

// OrderUserStats 用户订单统计
type OrderUserStats struct {
	UserID      int64   `json:"userId"`
	Username    string  `json:"username"`
	OrderCount  int64   `json:"orderCount"`
	TotalAmount float64 `json:"totalAmount"`
}

// PaymentRecord 支付记录
type PaymentRecord struct {
	Payment
	OrderNo     string `json:"orderNo"`
	UserName    string `json:"userName"`
	ProductName string `json:"productName"`
}

// PaymentRecordQuery 支付记录查询条件
type PaymentRecordQuery struct {
	Page          int       `json:"page"`
	Size          int       `json:"size"`
	PaymentNo     string    `json:"paymentNo"`
	OrderNo       string    `json:"orderNo"`
	UserID        *int64    `json:"userId"`
	PaymentMethod *int8     `json:"paymentMethod"`
	Status        *int8     `json:"status"`
	StartDate     time.Time `json:"startDate"`
	EndDate       time.Time `json:"endDate"`
}

// PaymentRecordUpdate 支付记录更新
type PaymentRecordUpdate struct {
	Status *int8    `json:"status"`
	Amount *float64 `json:"amount"`
}
