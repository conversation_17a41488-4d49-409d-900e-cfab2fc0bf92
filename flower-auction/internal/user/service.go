package user

import (
	"context"
	"errors"
	"time"

	"golang.org/x/crypto/bcrypt"
)

var (
	ErrUserNotFound    = errors.New("用户不存在")
	ErrUserExists      = errors.New("用户已存在")
	ErrInvalidPassword = errors.New("密码错误")
	ErrUserDisabled    = errors.New("用户已被禁用")
	ErrRoleNotFound    = errors.New("角色不存在")
	ErrRoleExists      = errors.New("角色已存在")
)

// Service 用户服务接口
type Service interface {
	// 用户管理
	CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error)
	UpdateUser(ctx context.Context, id int64, req *UpdateUserRequest) error
	DeleteUser(ctx context.Context, id int64) error
	GetUser(ctx context.Context, id int64) (*User, error)
	GetUserByUsername(ctx context.Context, username string) (*User, error)
	ListUsers(ctx context.Context, query *UserQuery) ([]*User, int64, error)
	UpdateUserStatus(ctx context.Context, id int64, status int8) error

	// 密码管理
	ChangePassword(ctx context.Context, userID int64, oldPassword, newPassword string) error
	ResetPassword(ctx context.Context, username string) (string, error)

	// 认证相关（实现auth.UserService接口）
	ValidateCredentials(ctx context.Context, username, password string) (User, error)
	IsUserActive(user User) bool
	GetUserRoles(ctx context.Context, userID int64) ([]int64, error)

	// 角色管理
	CreateRole(ctx context.Context, name, code, description string) (*Role, error)
	UpdateRole(ctx context.Context, id int64, name, code, description string) error
	DeleteRole(ctx context.Context, id int64) error
	GetRole(ctx context.Context, id int64) (*Role, error)
	ListRoles(ctx context.Context) ([]*Role, error)
	ListRolesWithParams(ctx context.Context, name, code string, status *int8, page, size int) ([]*Role, int64, error)

	// 用户角色关联
	AssignRole(ctx context.Context, userID, roleID int64) error
	RemoveRole(ctx context.Context, userID, roleID int64) error
	GetUserWithRoles(ctx context.Context, userID int64) (*UserWithRoles, error)
}

// service 用户服务实现
type service struct {
	repo Repository
}

// NewService 创建用户服务实例
func NewService(repo Repository) Service {
	return &service{
		repo: repo,
	}
}

// CreateUser 创建用户
func (s *service) CreateUser(ctx context.Context, req *CreateUserRequest) (*User, error) {
	// 检查用户名是否已存在
	existUser, err := s.repo.FindByUsername(ctx, req.Username)
	if err != nil {
		return nil, err
	}
	if existUser != nil {
		return nil, ErrUserExists
	}

	// 检查手机号是否已存在
	existUser, err = s.repo.FindByPhone(ctx, req.Phone)
	if err != nil {
		return nil, err
	}
	if existUser != nil {
		return nil, errors.New("手机号已存在")
	}

	// 检查邮箱是否已存在
	existUser, err = s.repo.FindByEmail(ctx, req.Email)
	if err != nil {
		return nil, err
	}
	if existUser != nil {
		return nil, errors.New("邮箱已存在")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := &User{
		Username:  req.Username,
		Password:  string(hashedPassword),
		RealName:  req.RealName,
		Phone:     req.Phone,
		Email:     req.Email,
		UserType:  req.UserType,
		Status:    1,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.repo.Create(ctx, user); err != nil {
		return nil, err
	}

	return user, nil
}

// UpdateUser 更新用户
func (s *service) UpdateUser(ctx context.Context, id int64, req *UpdateUserRequest) error {
	user, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	user.RealName = req.RealName
	user.Phone = req.Phone
	user.Email = req.Email
	user.UpdatedAt = time.Now()

	return s.repo.Update(ctx, user)
}

// DeleteUser 删除用户
func (s *service) DeleteUser(ctx context.Context, id int64) error {
	user, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	return s.repo.Delete(ctx, id)
}

// GetUser 获取用户
func (s *service) GetUser(ctx context.Context, id int64) (*User, error) {
	user, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, ErrUserNotFound
	}
	return user, nil
}

// GetUserByUsername 根据用户名获取用户
func (s *service) GetUserByUsername(ctx context.Context, username string) (*User, error) {
	user, err := s.repo.FindByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, ErrUserNotFound
	}
	return user, nil
}

// ListUsers 查询用户列表
func (s *service) ListUsers(ctx context.Context, query *UserQuery) ([]*User, int64, error) {
	return s.repo.List(ctx, query)
}

// UpdateUserStatus 更新用户状态
func (s *service) UpdateUserStatus(ctx context.Context, id int64, status int8) error {
	user, err := s.repo.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	return s.repo.UpdateStatus(ctx, id, status)
}

// ChangePassword 修改密码
func (s *service) ChangePassword(ctx context.Context, userID int64, oldPassword, newPassword string) error {
	user, err := s.repo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	// 验证旧密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(oldPassword)); err != nil {
		return ErrInvalidPassword
	}

	// 加密新密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return err
	}

	return s.repo.UpdatePassword(ctx, userID, string(hashedPassword))
}

// ResetPassword 重置密码
func (s *service) ResetPassword(ctx context.Context, username string) (string, error) {
	user, err := s.repo.FindByUsername(ctx, username)
	if err != nil {
		return "", err
	}
	if user == nil {
		return "", ErrUserNotFound
	}

	// 生成临时密码
	tempPassword := generateTempPassword()

	// 加密临时密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(tempPassword), bcrypt.DefaultCost)
	if err != nil {
		return "", err
	}

	if err := s.repo.UpdatePassword(ctx, user.ID, string(hashedPassword)); err != nil {
		return "", err
	}

	return tempPassword, nil
}

// ValidateCredentials 验证用户凭据（实现auth.UserService接口）
func (s *service) ValidateCredentials(ctx context.Context, username, password string) (User, error) {
	user, err := s.repo.FindByUsername(ctx, username)
	if err != nil {
		return User{}, err
	}
	if user == nil {
		return User{}, ErrUserNotFound
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return User{}, ErrInvalidPassword
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	s.repo.Update(ctx, user)

	return *user, nil
}

// IsUserActive 检查用户是否活跃（实现auth.UserService接口）
func (s *service) IsUserActive(user User) bool {
	return user.Status == 1
}

// GetUserRoles 获取用户角色ID列表（实现auth.UserService接口）
func (s *service) GetUserRoles(ctx context.Context, userID int64) ([]int64, error) {
	return s.repo.GetUserRoleIDs(ctx, userID)
}

// CreateRole 创建角色
func (s *service) CreateRole(ctx context.Context, name, code, description string) (*Role, error) {
	// 检查角色代码是否已存在
	existRole, err := s.repo.FindRoleByCode(ctx, code)
	if err != nil {
		return nil, err
	}
	if existRole != nil {
		return nil, ErrRoleExists
	}

	role := &Role{
		Name:        name,
		Code:        code,
		Description: description,
		Status:      1,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.repo.CreateRole(ctx, role); err != nil {
		return nil, err
	}

	return role, nil
}

// UpdateRole 更新角色
func (s *service) UpdateRole(ctx context.Context, id int64, name, code, description string) error {
	role, err := s.repo.FindRoleByID(ctx, id)
	if err != nil {
		return err
	}
	if role == nil {
		return ErrRoleNotFound
	}

	role.Name = name
	role.Code = code
	role.Description = description
	role.UpdatedAt = time.Now()

	return s.repo.UpdateRole(ctx, role)
}

// DeleteRole 删除角色
func (s *service) DeleteRole(ctx context.Context, id int64) error {
	role, err := s.repo.FindRoleByID(ctx, id)
	if err != nil {
		return err
	}
	if role == nil {
		return ErrRoleNotFound
	}

	return s.repo.DeleteRole(ctx, id)
}

// GetRole 获取角色
func (s *service) GetRole(ctx context.Context, id int64) (*Role, error) {
	role, err := s.repo.FindRoleByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if role == nil {
		return nil, ErrRoleNotFound
	}
	return role, nil
}

// ListRoles 查询角色列表
func (s *service) ListRoles(ctx context.Context) ([]*Role, error) {
	return s.repo.ListRoles(ctx)
}

// ListRolesWithParams 带参数查询角色列表
func (s *service) ListRolesWithParams(ctx context.Context, name, code string, status *int8, page, size int) ([]*Role, int64, error) {
	return s.repo.ListRolesWithParams(ctx, name, code, status, page, size)
}

// AssignRole 分配角色
func (s *service) AssignRole(ctx context.Context, userID, roleID int64) error {
	// 检查用户是否存在
	user, err := s.repo.FindByID(ctx, userID)
	if err != nil {
		return err
	}
	if user == nil {
		return ErrUserNotFound
	}

	// 检查角色是否存在
	role, err := s.repo.FindRoleByID(ctx, roleID)
	if err != nil {
		return err
	}
	if role == nil {
		return ErrRoleNotFound
	}

	return s.repo.AssignRole(ctx, userID, roleID)
}

// RemoveRole 移除角色
func (s *service) RemoveRole(ctx context.Context, userID, roleID int64) error {
	return s.repo.RemoveRole(ctx, userID, roleID)
}

// GetUserWithRoles 获取用户及其角色
func (s *service) GetUserWithRoles(ctx context.Context, userID int64) (*UserWithRoles, error) {
	return s.repo.FindUserWithRoles(ctx, userID)
}

// generateTempPassword 生成临时密码
func generateTempPassword() string {
	// 简单实现，实际应该使用更安全的随机密码生成
	return "temp123456"
}
