package user

import (
	"fmt"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/shared/response"
)

// Handler 用户HTTP处理器
type Handler struct {
	service Service
}

// NewHandler 创建用户处理器实例
func NewHandler(service Service) *Handler {
	return &Handler{
		service: service,
	}
}

// RegisterRoutes 注册路由
func (h *Handler) RegisterRoutes(r *gin.Engine) {
	// 用户管理路由组
	users := r.Group("/api/v1/users")
	{
		users.POST("", h.CreateUser)                   // 创建用户
		users.GET("/info/:id", h.GetUser)              // 获取用户详情
		users.PUT("/:id", h.UpdateUser)                // 更新用户
		users.DELETE("/:id", h.DeleteUser)             // 删除用户
		users.GET("", h.ListUsers)                     // 获取用户列表
		users.PUT("/status/:id", h.UpdateUserStatus)   // 更新用户状态
		users.PUT("/:id/password", h.ChangePassword)   // 修改密码
		users.POST("/reset-password", h.ResetPassword) // 重置密码
		users.DELETE("/batch", h.BatchDeleteUsers)     // 批量删除用户
		users.GET("/export", h.ExportUsers)            // 导出用户数据
		users.POST("/import", h.ImportUsers)           // 导入用户数据
		users.GET("/statistics", h.GetUserStatistics)  // 获取用户统计信息
	}

	// 角色管理路由组
	roles := r.Group("/api/v1/roles")
	{
		roles.POST("", h.CreateRole)       // 创建角色
		roles.GET("/:id", h.GetRole)       // 获取角色详情
		roles.PUT("/:id", h.UpdateRole)    // 更新角色
		roles.DELETE("/:id", h.DeleteRole) // 删除角色
		roles.GET("", h.ListRoles)         // 获取角色列表
	}

	// 用户角色关联路由组
	userRoles := r.Group("/api/v1/users/:userId/roles")
	{
		userRoles.POST("/:roleId", h.AssignRole)   // 分配角色
		userRoles.DELETE("/:roleId", h.RemoveRole) // 移除角色
		userRoles.GET("", h.GetUserWithRoles)      // 获取用户及其角色
	}
}

// CreateUser 创建用户
func (h *Handler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "参数错误", err.Error())
		return
	}

	user, err := h.service.CreateUser(c.Request.Context(), &req)
	if err != nil {
		if err == ErrUserExists {
			response.Conflict(c, "用户已存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "创建用户失败", err.Error())
		return
	}

	response.Success(c, user)
}

// GetUser 获取用户详情
func (h *Handler) GetUser(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	user, err := h.service.GetUser(c.Request.Context(), id)
	if err != nil {
		if err == ErrUserNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "获取用户失败", err.Error())
		return
	}

	response.Success(c, user)
}

// UpdateUser 更新用户
func (h *Handler) UpdateUser(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "参数错误", err.Error())
		return
	}

	if err := h.service.UpdateUser(c.Request.Context(), id, &req); err != nil {
		if err == ErrUserNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "更新用户失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "更新成功"})
}

// DeleteUser 删除用户
func (h *Handler) DeleteUser(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	if err := h.service.DeleteUser(c.Request.Context(), id); err != nil {
		if err == ErrUserNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "删除用户失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "删除成功"})
}

// ListUsers 获取用户列表
func (h *Handler) ListUsers(c *gin.Context) {
	// 解析查询参数
	query := &UserQuery{}

	if page := c.Query("page"); page != "" {
		if p, err := strconv.Atoi(page); err == nil && p > 0 {
			query.Page = p
		}
	}
	if query.Page == 0 {
		query.Page = 1
	}

	if size := c.Query("size"); size != "" {
		if s, err := strconv.Atoi(size); err == nil && s > 0 && s <= 100 {
			query.Size = s
		}
	}
	if query.Size == 0 {
		query.Size = 10
	}

	if username := c.Query("username"); username != "" {
		query.Username = username
	}

	if realName := c.Query("realName"); realName != "" {
		query.RealName = realName
	}

	if userType := c.Query("userType"); userType != "" {
		if ut, err := strconv.Atoi(userType); err == nil {
			query.UserType = int8(ut)
		}
	}

	if status := c.Query("status"); status != "" {
		if s, err := strconv.Atoi(status); err == nil {
			query.Status = int8(s)
		}
	}

	users, total, err := h.service.ListUsers(c.Request.Context(), query)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取用户列表失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"users": users,
		"total": total,
		"page":  query.Page,
		"size":  query.Size,
	})
}

// UpdateUserStatus 更新用户状态
func (h *Handler) UpdateUserStatus(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	var req struct {
		Status int8 `json:"status" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "参数错误", err.Error())
		return
	}

	if err := h.service.UpdateUserStatus(c.Request.Context(), id, req.Status); err != nil {
		if err == ErrUserNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "更新用户状态失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "状态更新成功"})
}

// ChangePassword 修改密码
func (h *Handler) ChangePassword(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	var req struct {
		OldPassword string `json:"oldPassword" binding:"required"`
		NewPassword string `json:"newPassword" binding:"required,min=6"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "参数错误", err.Error())
		return
	}

	if err := h.service.ChangePassword(c.Request.Context(), id, req.OldPassword, req.NewPassword); err != nil {
		if err == ErrUserNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		if err == ErrInvalidPassword {
			response.BadRequest(c, "原密码不正确")
			return
		}
		response.InternalServerErrorWithDetail(c, "修改密码失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "密码修改成功"})
}

// ResetPassword 重置密码
func (h *Handler) ResetPassword(c *gin.Context) {
	var req struct {
		Username string `json:"username" binding:"required"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "参数错误", err.Error())
		return
	}

	tempPassword, err := h.service.ResetPassword(c.Request.Context(), req.Username)
	if err != nil {
		if err == ErrUserNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "重置密码失败", err.Error())
		return
	}

	response.Success(c, gin.H{
		"message":      "密码重置成功",
		"tempPassword": tempPassword,
	})
}

// CreateRole 创建角色
func (h *Handler) CreateRole(c *gin.Context) {
	var req struct {
		Name        string `json:"name" binding:"required"`
		Code        string `json:"code" binding:"required"`
		Description string `json:"description"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "参数错误", err.Error())
		return
	}

	role, err := h.service.CreateRole(c.Request.Context(), req.Name, req.Code, req.Description)
	if err != nil {
		if err == ErrRoleExists {
			response.Conflict(c, "角色已存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "创建角色失败", err.Error())
		return
	}

	response.Success(c, role)
}

// GetRole 获取角色详情
func (h *Handler) GetRole(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "角色ID格式错误")
		return
	}

	role, err := h.service.GetRole(c.Request.Context(), id)
	if err != nil {
		if err == ErrRoleNotFound {
			response.NotFound(c, "角色不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "获取角色失败", err.Error())
		return
	}

	response.Success(c, role)
}

// UpdateRole 更新角色
func (h *Handler) UpdateRole(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "角色ID格式错误")
		return
	}

	var req struct {
		Name        string `json:"name" binding:"required"`
		Code        string `json:"code" binding:"required"`
		Description string `json:"description"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "参数错误", err.Error())
		return
	}

	if err := h.service.UpdateRole(c.Request.Context(), id, req.Name, req.Code, req.Description); err != nil {
		if err == ErrRoleNotFound {
			response.NotFound(c, "角色不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "更新角色失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "更新成功"})
}

// DeleteRole 删除角色
func (h *Handler) DeleteRole(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		response.BadRequest(c, "角色ID格式错误")
		return
	}

	if err := h.service.DeleteRole(c.Request.Context(), id); err != nil {
		if err == ErrRoleNotFound {
			response.NotFound(c, "角色不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "删除角色失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "删除成功"})
}

// ListRoles 获取角色列表
func (h *Handler) ListRoles(c *gin.Context) {
	roles, err := h.service.ListRoles(c.Request.Context())
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取角色列表失败", err.Error())
		return
	}

	response.Success(c, gin.H{"roles": roles})
}

// AssignRole 分配角色
func (h *Handler) AssignRole(c *gin.Context) {
	userID, err := strconv.ParseInt(c.Param("userId"), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	roleID, err := strconv.ParseInt(c.Param("roleId"), 10, 64)
	if err != nil {
		response.BadRequest(c, "角色ID格式错误")
		return
	}

	if err := h.service.AssignRole(c.Request.Context(), userID, roleID); err != nil {
		if err == ErrUserNotFound {
			response.NotFound(c, "用户不存在")
			return
		}
		if err == ErrRoleNotFound {
			response.NotFound(c, "角色不存在")
			return
		}
		response.InternalServerErrorWithDetail(c, "分配角色失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "角色分配成功"})
}

// RemoveRole 移除角色
func (h *Handler) RemoveRole(c *gin.Context) {
	userID, err := strconv.ParseInt(c.Param("userId"), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	roleID, err := strconv.ParseInt(c.Param("roleId"), 10, 64)
	if err != nil {
		response.BadRequest(c, "角色ID格式错误")
		return
	}

	if err := h.service.RemoveRole(c.Request.Context(), userID, roleID); err != nil {
		response.InternalServerErrorWithDetail(c, "移除角色失败", err.Error())
		return
	}

	response.Success(c, gin.H{"message": "角色移除成功"})
}

// GetUserWithRoles 获取用户及其角色
func (h *Handler) GetUserWithRoles(c *gin.Context) {
	userID, err := strconv.ParseInt(c.Param("userId"), 10, 64)
	if err != nil {
		response.BadRequest(c, "用户ID格式错误")
		return
	}

	userWithRoles, err := h.service.GetUserWithRoles(c.Request.Context(), userID)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取用户角色失败", err.Error())
		return
	}

	response.Success(c, userWithRoles)
}

// BatchDeleteUsersRequest 批量删除用户请求
type BatchDeleteUsersRequest struct {
	IDs []int64 `json:"ids" binding:"required"`
}

// BatchDeleteUsers 批量删除用户
func (h *Handler) BatchDeleteUsers(c *gin.Context) {
	var req BatchDeleteUsersRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequestWithDetail(c, "参数错误", err.Error())
		return
	}

	for _, id := range req.IDs {
		if err := h.service.DeleteUser(c.Request.Context(), id); err != nil {
			response.InternalServerErrorWithDetail(c, "批量删除用户失败", err.Error())
			return
		}
	}

	response.Success(c, gin.H{"message": "批量删除成功"})
}

// ExportUsers 导出用户数据
func (h *Handler) ExportUsers(c *gin.Context) {
	// 解析查询参数
	query := &UserQuery{
		Page: 1,
		Size: 10000, // 导出时获取所有数据
	}

	users, _, err := h.service.ListUsers(c.Request.Context(), query)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取用户数据失败", err.Error())
		return
	}

	// 生成CSV内容
	csvContent := "ID,用户名,真实姓名,手机号,邮箱,用户类型,状态,创建时间\n"
	for _, user := range users {
		userType := "普通用户"
		if user.UserType == 2 {
			userType = "拍卖师"
		}
		status := "禁用"
		if user.Status == 1 {
			status = "启用"
		}
		csvContent += fmt.Sprintf("%d,%s,%s,%s,%s,%s,%s,%s\n",
			user.ID, user.Username, user.RealName, user.Phone, user.Email,
			userType, status, user.CreatedAt.Format("2006-01-02 15:04:05"))
	}

	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=users.csv")
	c.String(http.StatusOK, csvContent)
}

// ImportUsers 导入用户数据
func (h *Handler) ImportUsers(c *gin.Context) {
	file, _, err := c.Request.FormFile("file")
	if err != nil {
		response.BadRequest(c, "文件上传失败")
		return
	}
	defer file.Close()

	// 这里应该解析CSV文件并批量创建用户
	// 为了简化，这里只返回成功消息
	response.Success(c, gin.H{"message": "导入成功"})
}

// GetUserStatistics 获取用户统计信息
func (h *Handler) GetUserStatistics(c *gin.Context) {
	// 获取总用户数
	totalQuery := &UserQuery{Page: 1, Size: 1}
	_, total, err := h.service.ListUsers(c.Request.Context(), totalQuery)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取用户统计失败", err.Error())
		return
	}

	// 获取活跃用户数
	activeQuery := &UserQuery{Page: 1, Size: 1, Status: 1}
	_, activeCount, err := h.service.ListUsers(c.Request.Context(), activeQuery)
	if err != nil {
		response.InternalServerErrorWithDetail(c, "获取活跃用户统计失败", err.Error())
		return
	}

	// 简化统计，实际应该从数据库查询
	stats := gin.H{
		"total":         total,
		"activeUsers":   activeCount,
		"newUsersToday": 0, // 需要实现今日新增用户统计
		"userTypeDistribution": gin.H{
			"1": activeCount, // 普通用户
			"2": 0,           // 拍卖师
		},
	}

	response.Success(c, stats)
}
