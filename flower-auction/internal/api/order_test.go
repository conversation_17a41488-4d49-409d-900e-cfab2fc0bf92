package api

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockOrderService 模拟订单服务
type MockOrderService struct {
	mock.Mock
}

func (m *MockOrderService) CreateOrder(ctx context.Context, userID, auctionItemID int64, amount float64) (*model.Order, error) {
	args := m.Called(ctx, userID, auctionItemID, amount)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Order), args.Error(1)
}

func (m *MockOrderService) GetOrder(ctx context.Context, id int64) (*model.OrderDetail, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.OrderDetail), args.Error(1)
}

func (m *MockOrderService) GetOrderByNo(ctx context.Context, orderNo string) (*model.OrderDetail, error) {
	args := m.Called(ctx, orderNo)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.OrderDetail), args.Error(1)
}

func (m *MockOrderService) ListUserOrders(ctx context.Context, userID int64, page, size int) ([]*model.OrderDetail, int64, error) {
	args := m.Called(ctx, userID, page, size)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*model.OrderDetail), args.Get(1).(int64), args.Error(2)
}

func (m *MockOrderService) ListAllOrders(ctx context.Context, page, size int, status *int8) ([]*model.OrderDetail, int64, error) {
	args := m.Called(ctx, page, size, status)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*model.OrderDetail), args.Get(1).(int64), args.Error(2)
}

func (m *MockOrderService) UpdateOrderStatus(ctx context.Context, id int64, status int8) error {
	args := m.Called(ctx, id, status)
	return args.Error(0)
}

func (m *MockOrderService) CreatePayment(ctx context.Context, orderID int64, amount float64, paymentMethod int8) (*model.Payment, error) {
	args := m.Called(ctx, orderID, amount, paymentMethod)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Payment), args.Error(1)
}

func (m *MockOrderService) GetPayment(ctx context.Context, paymentNo string) (*model.Payment, error) {
	args := m.Called(ctx, paymentNo)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.Payment), args.Error(1)
}

func (m *MockOrderService) UpdatePaymentStatus(ctx context.Context, paymentNo string, status int8) error {
	args := m.Called(ctx, paymentNo, status)
	return args.Error(0)
}

// 新增的方法
func (m *MockOrderService) ExportOrders(ctx context.Context, query *model.OrderExportQuery) ([]byte, error) {
	args := m.Called(ctx, query)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockOrderService) GetOrderStatistics(ctx context.Context, query *model.OrderStatisticsQuery) (*model.OrderStatistics, error) {
	args := m.Called(ctx, query)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.OrderStatistics), args.Error(1)
}

func TestOrderHandler_CreateOrder(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockOrderService)
	handler := &OrderHandler{orderService: mockService}

	tests := []struct {
		name           string
		requestBody    interface{}
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "成功创建订单",
			requestBody: CreateOrderRequest{
				AuctionItemID: 1,
				Amount:        100.0,
			},
			setup: func() {
				mockService.On("CreateOrder", mock.Anything, int64(1), int64(1), 100.0).Return(&model.Order{
					ID:            1,
					OrderNo:       "20240101120000001",
					UserID:        1,
					AuctionItemID: 1,
					Amount:        100.0,
					Status:        0,
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "20240101120000001",
		},
		{
			name: "请求参数错误",
			requestBody: CreateOrderRequest{
				AuctionItemID: 0,
				Amount:        -100.0,
			},
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil

			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := gin.New()
			r.Use(func(c *gin.Context) {
				c.Set("userID", int64(1))
				c.Next()
			})
			r.POST("/orders", handler.CreateOrder)

			// 准备请求体
			body, _ := json.Marshal(tt.requestBody)

			// 创建请求
			req, _ := http.NewRequest("POST", "/orders", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

func TestOrderHandler_GetOrder(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockOrderService)
	handler := &OrderHandler{orderService: mockService}

	tests := []struct {
		name           string
		orderID        string
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name:    "成功获取订单",
			orderID: "1",
			setup: func() {
				mockService.On("GetOrder", mock.Anything, int64(1)).Return(&model.OrderDetail{
					Order: model.Order{
						ID:      1,
						OrderNo: "20240101120000001",
						UserID:  1,
						Amount:  100.0,
						Status:  0,
					},
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "20240101120000001",
		},
		{
			name:           "订单ID格式错误",
			orderID:        "invalid",
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil

			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := gin.New()
			r.GET("/orders/:id", handler.GetOrder)

			// 创建请求
			req, _ := http.NewRequest("GET", "/orders/"+tt.orderID, nil)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

func TestOrderHandler_CreatePayment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockOrderService)
	handler := &OrderHandler{orderService: mockService}

	tests := []struct {
		name           string
		requestBody    interface{}
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "成功创建支付记录",
			requestBody: CreatePaymentRequest{
				OrderID:       1,
				Amount:        100.0,
				PaymentMethod: 1,
			},
			setup: func() {
				mockService.On("CreatePayment", mock.Anything, int64(1), 100.0, int8(1)).Return(&model.Payment{
					ID:            1,
					OrderID:       1,
					PaymentNo:     "PAY20240101120000001",
					Amount:        100.0,
					PaymentMethod: 1,
					Status:        0,
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "PAY20240101120000001",
		},
		{
			name: "请求参数错误",
			requestBody: CreatePaymentRequest{
				OrderID:       0,
				Amount:        -100.0,
				PaymentMethod: 0,
			},
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil

			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := gin.New()
			r.POST("/payments", handler.CreatePayment)

			// 准备请求体
			body, _ := json.Marshal(tt.requestBody)

			// 创建请求
			req, _ := http.NewRequest("POST", "/payments", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}
