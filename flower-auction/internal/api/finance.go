package api

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/middleware"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/putonghao/flower-auction/internal/service"
)

// FinanceHandler 财务处理器
type FinanceHandler struct {
	financeService service.FinanceService
}

// NewFinanceHandler 创建财务处理器实例
func NewFinanceHandler() *FinanceHandler {
	return &FinanceHandler{
		financeService: service.NewFinanceService(),
	}
}

// RegisterRoutes 注册路由
func (h *FinanceHandler) RegisterRoutes(r *gin.Engine) {
	// 财务管理路由组
	finance := r.Group("/api/v1/finance")
	finance.Use(middleware.JWTAuth())
	{
		// 账户管理
		finance.POST("/account", h.CreateAccount)                 // 创建账户
		finance.GET("/account/:accountType", h.GetAccount)        // 获取账户信息
		finance.GET("/balance/:accountType", h.GetAccountBalance) // 获取账户余额
		finance.POST("/freeze", h.FreezeAmount)                   // 冻结金额
		finance.POST("/unfreeze", h.UnfreezeAmount)               // 解冻金额

		// 交易流水
		finance.POST("/transaction", h.CreateTransaction)                // 创建交易记录
		finance.GET("/transactions", h.GetTransactionHistory)            // 获取交易历史
		finance.GET("/transaction/:transactionNo", h.GetTransactionByNo) // 根据交易号获取交易

		// 统计报表
		finance.GET("/statistics/income", h.GetIncomeStatistics)           // 收入统计
		finance.GET("/statistics/user/:userId", h.GetUserIncomeStatistics) // 用户收入统计
		finance.GET("/report/daily", h.GetDailyReport)                     // 日报表
		finance.GET("/report/monthly", h.GetMonthlyReport)                 // 月报表
	}

	// 管理员财务功能
	admin := r.Group("/api/v1/admin/finance")
	admin.Use(middleware.JWTAuth(), middleware.RequireRole(1)) // 只有管理员可以访问
	{
		// 佣金管理
		admin.POST("/commission/:orderId", h.CalculateCommission)         // 计算佣金
		admin.PUT("/commission/:commissionId/settle", h.SettleCommission) // 结算佣金
		admin.GET("/commissions", h.GetCommissionList)                    // 获取佣金列表

		// 对账功能
		admin.POST("/reconciliation", h.CreateReconciliation)              // 创建对账任务
		admin.GET("/reconciliation/:id", h.GetReconciliation)              // 获取对账记录
		admin.GET("/reconciliations", h.ListReconciliations)               // 获取对账记录列表
		admin.POST("/reconciliation/:id/process", h.ProcessReconciliation) // 处理对账
	}
}

// CreateAccountRequest 创建账户请求
type CreateAccountRequest struct {
	AccountType int8 `json:"accountType" binding:"required,min=1,max=3"`
}

// CreateAccount 创建账户
func (h *FinanceHandler) CreateAccount(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	var req CreateAccountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	account, err := h.financeService.CreateAccount(c.Request.Context(), userID.(int64), req.AccountType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, account)
}

// GetAccount 获取账户信息
func (h *FinanceHandler) GetAccount(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	accountType, err := strconv.Atoi(c.Param("accountType"))
	if err != nil || accountType < 1 || accountType > 3 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "账户类型无效"})
		return
	}

	account, err := h.financeService.GetAccount(c.Request.Context(), userID.(int64), int8(accountType))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, account)
}

// GetAccountBalance 获取账户余额
func (h *FinanceHandler) GetAccountBalance(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	accountType, err := strconv.Atoi(c.Param("accountType"))
	if err != nil || accountType < 1 || accountType > 3 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "账户类型无效"})
		return
	}

	balance, err := h.financeService.GetAccountBalance(c.Request.Context(), userID.(int64), int8(accountType))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"balance": balance})
}

// FreezeAmountRequest 冻结金额请求
type FreezeAmountRequest struct {
	Amount float64 `json:"amount" binding:"required,gt=0"`
	Reason string  `json:"reason" binding:"required"`
}

// FreezeAmount 冻结金额
func (h *FinanceHandler) FreezeAmount(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	var req FreezeAmountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.financeService.FreezeAmount(c.Request.Context(), userID.(int64), req.Amount, req.Reason); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "金额冻结成功"})
}

// UnfreezeAmountRequest 解冻金额请求
type UnfreezeAmountRequest struct {
	Amount float64 `json:"amount" binding:"required,gt=0"`
	Reason string  `json:"reason" binding:"required"`
}

// UnfreezeAmount 解冻金额
func (h *FinanceHandler) UnfreezeAmount(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	var req UnfreezeAmountRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.financeService.UnfreezeAmount(c.Request.Context(), userID.(int64), req.Amount, req.Reason); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "金额解冻成功"})
}

// CreateTransactionRequest 创建交易记录请求
type CreateTransactionRequest struct {
	Type        int8    `json:"type" binding:"required,min=1,max=6"`
	Amount      float64 `json:"amount" binding:"required,gt=0"`
	Description string  `json:"description"`
}

// CreateTransaction 创建交易记录
func (h *FinanceHandler) CreateTransaction(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	var req CreateTransactionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	transaction := &model.Transaction{
		UserID:      userID.(int64),
		Type:        req.Type,
		Amount:      req.Amount,
		Description: req.Description,
		Status:      1, // 1: 成功
	}

	if err := h.financeService.CreateTransaction(c.Request.Context(), transaction); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, transaction)
}

// GetTransactionHistory 获取交易历史
func (h *FinanceHandler) GetTransactionHistory(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	page, size := ParsePagination(c)

	transactions, total, err := h.financeService.GetTransactionHistory(c.Request.Context(), userID.(int64), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"transactions": transactions,
		"total":        total,
		"page":         page,
		"size":         size,
	})
}

// GetTransactionByNo 根据交易号获取交易
func (h *FinanceHandler) GetTransactionByNo(c *gin.Context) {
	transactionNo := c.Param("transactionNo")
	if transactionNo == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "交易号不能为空"})
		return
	}

	transaction, err := h.financeService.GetTransactionByNo(c.Request.Context(), transactionNo)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, transaction)
}

// GetIncomeStatistics 获取收入统计
func (h *FinanceHandler) GetIncomeStatistics(c *gin.Context) {
	startDateStr := c.Query("startDate")
	endDateStr := c.Query("endDate")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "开始日期和结束日期不能为空"})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "开始日期格式错误"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "结束日期格式错误"})
		return
	}

	statistics, err := h.financeService.GetIncomeStatistics(c.Request.Context(), startDate, endDate.Add(24*time.Hour))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, statistics)
}

// GetDailyReport 获取日报表
func (h *FinanceHandler) GetDailyReport(c *gin.Context) {
	dateStr := c.Query("date")
	if dateStr == "" {
		dateStr = time.Now().Format("2006-01-02")
	}

	date, err := time.Parse("2006-01-02", dateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "日期格式错误"})
		return
	}

	report, err := h.financeService.GetDailyReport(c.Request.Context(), date)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, report)
}

// GetMonthlyReport 获取月报表
func (h *FinanceHandler) GetMonthlyReport(c *gin.Context) {
	yearStr := c.Query("year")
	monthStr := c.Query("month")

	if yearStr == "" || monthStr == "" {
		now := time.Now()
		yearStr = strconv.Itoa(now.Year())
		monthStr = strconv.Itoa(int(now.Month()))
	}

	year, err := strconv.Atoi(yearStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "年份格式错误"})
		return
	}

	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "月份格式错误"})
		return
	}

	report, err := h.financeService.GetMonthlyReport(c.Request.Context(), year, month)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, report)
}

// CalculateCommission 计算佣金
func (h *FinanceHandler) CalculateCommission(c *gin.Context) {
	orderID, err := ParseParamID(c, "orderId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	commission, err := h.financeService.CalculateCommission(c.Request.Context(), orderID)
	if err != nil {
		if err == service.ErrOrderNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "订单不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, commission)
}

// SettleCommission 结算佣金
func (h *FinanceHandler) SettleCommission(c *gin.Context) {
	commissionID, err := ParseParamID(c, "commissionId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.financeService.SettleCommission(c.Request.Context(), commissionID); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "佣金结算成功"})
}

// GetCommissionList 获取佣金列表
func (h *FinanceHandler) GetCommissionList(c *gin.Context) {
	page, size := ParsePagination(c)

	commissions, total, err := h.financeService.GetCommissionList(c.Request.Context(), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"commissions": commissions,
		"total":       total,
		"page":        page,
		"size":        size,
	})
}

// GetUserIncomeStatistics 获取用户收入统计
func (h *FinanceHandler) GetUserIncomeStatistics(c *gin.Context) {
	userID, err := ParseParamID(c, "userId")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	startDateStr := c.Query("startDate")
	endDateStr := c.Query("endDate")

	if startDateStr == "" || endDateStr == "" {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "开始日期和结束日期不能为空"})
		return
	}

	startDate, err := time.Parse("2006-01-02", startDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "开始日期格式错误"})
		return
	}

	endDate, err := time.Parse("2006-01-02", endDateStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "结束日期格式错误"})
		return
	}

	statistics, err := h.financeService.GetUserIncomeStatistics(c.Request.Context(), userID, startDate, endDate.Add(24*time.Hour))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, statistics)
}

// CreateReconciliationRequest 创建对账任务请求
type CreateReconciliationRequest struct {
	Type      int8   `json:"type" binding:"required,min=1,max=3"` // 1:支付对账 2:提现对账 3:佣金对账
	StartDate string `json:"startDate" binding:"required"`
	EndDate   string `json:"endDate" binding:"required"`
	Remark    string `json:"remark"`
}

// CreateReconciliation 创建对账任务
func (h *FinanceHandler) CreateReconciliation(c *gin.Context) {
	userID, exists := c.Get("userID")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户未登录"})
		return
	}

	var req CreateReconciliationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// 解析日期
	startDate, err := time.Parse("2006-01-02", req.StartDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "开始日期格式错误"})
		return
	}

	endDate, err := time.Parse("2006-01-02", req.EndDate)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "结束日期格式错误"})
		return
	}

	reconciliation := &model.Reconciliation{
		Type:      req.Type,
		StartDate: startDate,
		EndDate:   endDate.Add(24 * time.Hour), // 包含结束日期当天
		Remark:    req.Remark,
		CreatedBy: userID.(int64),
		Status:    0, // 待处理
	}

	if err := h.financeService.CreateReconciliation(c.Request.Context(), reconciliation); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, reconciliation)
}

// GetReconciliation 获取对账记录
func (h *FinanceHandler) GetReconciliation(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	reconciliation, err := h.financeService.GetReconciliation(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, reconciliation)
}

// ListReconciliations 获取对账记录列表
func (h *FinanceHandler) ListReconciliations(c *gin.Context) {
	page, size := ParsePagination(c)

	reconciliations, total, err := h.financeService.ListReconciliations(c.Request.Context(), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"reconciliations": reconciliations,
		"total":           total,
		"page":            page,
		"size":            size,
	})
}

// ProcessReconciliation 处理对账
func (h *FinanceHandler) ProcessReconciliation(c *gin.Context) {
	id, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	result, err := h.financeService.ProcessReconciliation(c.Request.Context(), id)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}
