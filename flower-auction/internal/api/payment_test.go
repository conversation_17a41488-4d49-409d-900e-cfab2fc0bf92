package api

import (
	"bytes"
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/model"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockPaymentService 模拟支付服务
type MockPaymentService struct {
	mock.Mock
}

func (m *MockPaymentService) CreateAlipayOrder(ctx context.Context, orderID int64, amount float64, subject string) (*model.PaymentOrder, error) {
	args := m.Called(ctx, orderID, amount, subject)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PaymentOrder), args.Error(1)
}

func (m *MockPaymentService) CreateWechatOrder(ctx context.Context, orderID int64, amount float64, subject string) (*model.PaymentOrder, error) {
	args := m.Called(ctx, orderID, amount, subject)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PaymentOrder), args.Error(1)
}

func (m *MockPaymentService) CreateBankOrder(ctx context.Context, orderID int64, amount float64, bankCard string) (*model.PaymentOrder, error) {
	args := m.Called(ctx, orderID, amount, bankCard)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PaymentOrder), args.Error(1)
}

func (m *MockPaymentService) AlipayNotify(ctx context.Context, params map[string]string) error {
	args := m.Called(ctx, params)
	return args.Error(0)
}

func (m *MockPaymentService) WechatNotify(ctx context.Context, params map[string]string) error {
	args := m.Called(ctx, params)
	return args.Error(0)
}

func (m *MockPaymentService) QueryPaymentStatus(ctx context.Context, paymentNo string) (*model.PaymentStatus, error) {
	args := m.Called(ctx, paymentNo)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PaymentStatus), args.Error(1)
}

func (m *MockPaymentService) RefundPayment(ctx context.Context, paymentNo string, refundAmount float64, reason string) (*model.RefundOrder, error) {
	args := m.Called(ctx, paymentNo, refundAmount, reason)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.RefundOrder), args.Error(1)
}

// 新增的支付记录管理方法
func (m *MockPaymentService) ListPaymentRecords(ctx context.Context, query *model.PaymentRecordQuery) ([]*model.PaymentRecord, int64, error) {
	args := m.Called(ctx, query)
	if args.Get(0) == nil {
		return nil, args.Get(1).(int64), args.Error(2)
	}
	return args.Get(0).([]*model.PaymentRecord), args.Get(1).(int64), args.Error(2)
}

func (m *MockPaymentService) GetPaymentRecord(ctx context.Context, id int64) (*model.PaymentRecord, error) {
	args := m.Called(ctx, id)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PaymentRecord), args.Error(1)
}

func (m *MockPaymentService) UpdatePaymentRecord(ctx context.Context, id int64, updates *model.PaymentRecordUpdate) error {
	args := m.Called(ctx, id, updates)
	return args.Error(0)
}

func (m *MockPaymentService) DeletePaymentRecord(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func TestPaymentHandler_CreateAlipayOrder(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockPaymentService)
	handler := &PaymentHandler{paymentService: mockService}

	tests := []struct {
		name           string
		requestBody    interface{}
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "成功创建支付宝支付订单",
			requestBody: CreateAlipayOrderRequest{
				OrderID: 1,
				Amount:  100.0,
				Subject: "测试商品",
			},
			setup: func() {
				mockService.On("CreateAlipayOrder", mock.Anything, int64(1), 100.0, "测试商品").Return(&model.PaymentOrder{
					PaymentNo:     "ALIPAY20240101120000001",
					OrderID:       1,
					Amount:        100.0,
					PaymentMethod: 1,
					PaymentURL:    "https://openapi.alipay.com/gateway.do?...",
					QRCode:        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
					ExpireTime:    time.Now().Add(30 * time.Minute),
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "ALIPAY20240101120000001",
		},
		{
			name: "请求参数错误",
			requestBody: CreateAlipayOrderRequest{
				OrderID: 0,
				Amount:  -100.0,
				Subject: "",
			},
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "error",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil

			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := gin.New()
			r.POST("/payment/alipay", handler.CreateAlipayOrder)

			// 准备请求体
			body, _ := json.Marshal(tt.requestBody)

			// 创建请求
			req, _ := http.NewRequest("POST", "/payment/alipay", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

func TestPaymentHandler_CreateWechatOrder(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockPaymentService)
	handler := &PaymentHandler{paymentService: mockService}

	tests := []struct {
		name           string
		requestBody    interface{}
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "成功创建微信支付订单",
			requestBody: CreateWechatOrderRequest{
				OrderID: 1,
				Amount:  100.0,
				Subject: "测试商品",
			},
			setup: func() {
				mockService.On("CreateWechatOrder", mock.Anything, int64(1), 100.0, "测试商品").Return(&model.PaymentOrder{
					PaymentNo:     "WECHAT20240101120000001",
					OrderID:       1,
					Amount:        100.0,
					PaymentMethod: 2,
					PaymentURL:    "weixin://wxpay/bizpayurl?pr=WECHAT20240101120000001",
					QRCode:        "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",
					ExpireTime:    time.Now().Add(30 * time.Minute),
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "WECHAT20240101120000001",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil

			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := gin.New()
			r.POST("/payment/wechat", handler.CreateWechatOrder)

			// 准备请求体
			body, _ := json.Marshal(tt.requestBody)

			// 创建请求
			req, _ := http.NewRequest("POST", "/payment/wechat", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

func TestPaymentHandler_QueryPaymentStatus(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockPaymentService)
	handler := &PaymentHandler{paymentService: mockService}

	tests := []struct {
		name           string
		paymentNo      string
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name:      "成功查询支付状态",
			paymentNo: "PAY20240101120000001",
			setup: func() {
				mockService.On("QueryPaymentStatus", mock.Anything, "PAY20240101120000001").Return(&model.PaymentStatus{
					PaymentNo:     "PAY20240101120000001",
					OrderID:       1,
					Amount:        100.0,
					PaymentMethod: 1,
					Status:        1,
					CreatedAt:     time.Now(),
					UpdatedAt:     time.Now(),
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "PAY20240101120000001",
		},
		{
			name:           "支付流水号为空",
			paymentNo:      " ",
			setup:          func() {},
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "支付流水号不能为空",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil

			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := gin.New()
			r.GET("/payment/status/:paymentNo", handler.QueryPaymentStatus)

			// 创建请求
			url := "/payment/status/" + tt.paymentNo
			req, _ := http.NewRequest("GET", url, nil)
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}

func TestPaymentHandler_RefundPayment(t *testing.T) {
	gin.SetMode(gin.TestMode)

	mockService := new(MockPaymentService)
	handler := &PaymentHandler{paymentService: mockService}

	tests := []struct {
		name           string
		requestBody    interface{}
		setup          func()
		expectedStatus int
		expectedBody   string
	}{
		{
			name: "成功申请退款",
			requestBody: RefundPaymentRequest{
				PaymentNo:    "PAY20240101120000001",
				RefundAmount: 50.0,
				Reason:       "商品质量问题",
			},
			setup: func() {
				mockService.On("RefundPayment", mock.Anything, "PAY20240101120000001", 50.0, "商品质量问题").Return(&model.RefundOrder{
					ID:        1,
					RefundNo:  "REFUND20240101120000001",
					PaymentNo: "PAY20240101120000001",
					OrderID:   1,
					Amount:    50.0,
					Reason:    "商品质量问题",
					Status:    1,
					CreatedAt: time.Now(),
					UpdatedAt: time.Now(),
				}, nil)
			},
			expectedStatus: http.StatusOK,
			expectedBody:   "REFUND20240101120000001",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 重置mock
			mockService.ExpectedCalls = nil
			mockService.Calls = nil

			if tt.setup != nil {
				tt.setup()
			}

			// 创建路由
			r := gin.New()
			r.POST("/payment/refund", handler.RefundPayment)

			// 准备请求体
			body, _ := json.Marshal(tt.requestBody)

			// 创建请求
			req, _ := http.NewRequest("POST", "/payment/refund", bytes.NewBuffer(body))
			req.Header.Set("Content-Type", "application/json")
			w := httptest.NewRecorder()

			// 执行请求
			r.ServeHTTP(w, req)

			// 验证结果
			assert.Equal(t, tt.expectedStatus, w.Code)
			assert.Contains(t, w.Body.String(), tt.expectedBody)

			mockService.AssertExpectations(t)
		})
	}
}
