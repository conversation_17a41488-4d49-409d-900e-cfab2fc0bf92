package api

import (
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/putonghao/flower-auction/internal/service"
	"github.com/xuri/excelize/v2"
)

// UserHandler 用户相关接口处理器
type UserHandler struct {
	userService service.UserService
	authService service.AuthService
}

// NewUserHandler 创建用户接口处理器
func NewUserHandler() *UserHandler {
	return &UserHandler{
		userService: service.NewUserService(),
		authService: service.NewAuthService(),
	}
}

// RegisterRoutes 注册路由
func (h *UserHandler) RegisterRoutes(r *gin.Engine) {
	user := r.Group("/api/v1/users")
	{
		user.POST("/register", h.Register)
		user.POST("", h.CreateUser) // 管理员创建用户
		user.POST("/login", h.<PERSON><PERSON>)
		user.PUT("/password", h.ChangePassword)
		user.PUT("/profile", h.UpdateProfile)
		user.PUT("/:id", h.UpdateUser)    // 更新用户信息
		user.DELETE("/:id", h.DeleteUser) // 删除用户
		user.GET("/info/:id", h.GetUserInfo)
		user.PUT("/status/:id", h.UpdateUserStatus)
		user.GET("", h.ListUsers)
		user.GET("/export", h.ExportUsers) // 导出用户
	}
}

// RegisterRequest 用户注册请求
type RegisterRequest struct {
	Username string `json:"username" binding:"required,min=3,max=32"`
	Password string `json:"password" binding:"required,min=6,max=32"`
	RealName string `json:"realName" binding:"required,min=2,max=32"`
	Phone    string `json:"phone" binding:"required,len=11"`
	Email    string `json:"email" binding:"required,email"`
	UserType int8   `json:"userType" binding:"required,oneof=1 2 3"` // 1:买家 2:卖家 3:管理员
}

// Register 用户注册
// @Summary 用户注册
// @Description 创建新用户账号
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body RegisterRequest true "注册信息"
// @Success 200 {object} model.User "用户信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/register [post]
func (h *UserHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	user, err := h.userService.Register(c.Request.Context(), req.Username, req.Password,
		req.RealName, req.Phone, req.Email, req.UserType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, user)
}

// LoginRequest 用户登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户登录并获取令牌
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录信息"
// @Success 200 {object} model.UserWithRoles "用户信息和角色"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "用户名或密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/login [post]
// Login handles user authentication requests.
// It binds the request JSON to LoginRequest, validates credentials via authService,
// and returns appropriate HTTP responses:
//   - 200 OK with login response on success
//   - 400 Bad Request for invalid JSON
//   - 401 Unauthorized for invalid credentials
//   - 500 Internal Server Error for other errors
func (h *UserHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	loginResponse, err := h.authService.Login(c.Request.Context(), req.Username, req.Password)
	if err != nil {
		if err == service.ErrInvalidCredentials {
			c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "用户名或密码错误"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, loginResponse)
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"oldPassword" binding:"required"`
	NewPassword string `json:"newPassword" binding:"required,min=6,max=32"`
}

// ChangePassword 修改密码
// @Summary 修改密码
// @Description 修改用户密码
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body ChangePasswordRequest true "密码信息"
// @Success 200 {object} SuccessResponse "修改成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "密码错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/password [put]
func (h *UserHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 从 JWT Token 中获取用户 ID
	userID := int64(1)

	if err := h.userService.ChangePassword(c.Request.Context(), userID, req.OldPassword, req.NewPassword); err != nil {
		if err == service.ErrInvalidPassword {
			c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "原密码错误"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "密码修改成功"})
}

// UpdateProfileRequest 更新用户信息请求
type UpdateProfileRequest struct {
	RealName string `json:"realName" binding:"required,min=2,max=32"`
	Phone    string `json:"phone" binding:"required,len=11"`
	Email    string `json:"email" binding:"required,email"`
}

// UpdateProfile 更新用户信息
// @Summary 更新用户信息
// @Description 更新用户基本信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body UpdateProfileRequest true "用户信息"
// @Success 200 {object} SuccessResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/profile [put]
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	var req UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 从 JWT Token 中获取用户 ID
	userID := int64(1)

	if err := h.userService.UpdateProfile(c.Request.Context(), userID, req.RealName, req.Phone, req.Email); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "用户信息更新成功"})
}

// GetUserInfo 获取用户信息
// @Summary 获取用户信息
// @Description 获取用户详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} model.UserWithRoles "用户信息和角色"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/info/{id} [get]
func (h *UserHandler) GetUserInfo(c *gin.Context) {
	userID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	user, err := h.userService.GetUserInfo(c.Request.Context(), userID)
	if err != nil {
		if err == service.ErrUserNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, user)
}

// UpdateUserStatusRequest 更新用户状态请求
type UpdateUserStatusRequest struct {
	Status int8 `json:"status" binding:"required,oneof=0 1"` // 0:禁用 1:启用
}

// UpdateUserStatus 更新用户状态
// @Summary 更新用户状态
// @Description 启用或禁用用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body UpdateUserStatusRequest true "状态信息"
// @Success 200 {object} SuccessResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/status/{id} [put]
func (h *UserHandler) UpdateUserStatus(c *gin.Context) {
	userID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.userService.UpdateUserStatus(c.Request.Context(), userID, req.Status); err != nil {
		if err == service.ErrUserNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, SuccessResponse{Message: "用户状态更新成功"})
}

// ListUsers 查询用户列表
// @Summary 查询用户列表
// @Description 分页查询用户列表
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Success 200 {object} PageResponse "用户列表"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users [get]
func (h *UserHandler) ListUsers(c *gin.Context) {
	page, size := ParsePagination(c)

	users, total, err := h.userService.ListUsers(c.Request.Context(), page, size)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, PageResponse{
		List:  users,
		Total: total,
		Page:  page,
		Size:  size,
	})
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string  `json:"username" binding:"required,min=3,max=32"`
	Password string  `json:"password" binding:"required,min=6,max=32"`
	RealName string  `json:"realName" binding:"required,min=2,max=32"`
	Phone    string  `json:"phone" binding:"required,len=11"`
	Email    string  `json:"email" binding:"required,email"`
	UserType int8    `json:"userType" binding:"required,oneof=1 2 3"` // 1:买家 2:卖家 3:管理员
	RoleIds  []int64 `json:"roleIds"`                                 // 角色ID列表
}

// CreateUser 创建用户（管理员功能）
// @Summary 创建用户
// @Description 管理员创建新用户
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param request body CreateUserRequest true "用户信息"
// @Success 200 {object} model.User "用户信息"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users [post]
func (h *UserHandler) CreateUser(c *gin.Context) {
	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	user, err := h.userService.Register(c.Request.Context(), req.Username, req.Password,
		req.RealName, req.Phone, req.Email, req.UserType)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 如果提供了角色ID，分配角色给用户
	// if len(req.RoleIds) > 0 {
	//     h.userService.AssignRoles(c.Request.Context(), user.ID, req.RoleIds)
	// }

	c.JSON(http.StatusOK, user)
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	RealName string  `json:"realName" binding:"required,min=2,max=32"`
	Phone    string  `json:"phone" binding:"required,len=11"`
	Email    string  `json:"email" binding:"required,email"`
	UserType int8    `json:"userType" binding:"required,oneof=1 2 3"`
	RoleIds  []int64 `json:"roleIds"` // 角色ID列表
}

// UpdateUser 更新用户信息
// @Summary 更新用户信息
// @Description 更新用户基本信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Param request body UpdateUserRequest true "用户信息"
// @Success 200 {object} SuccessResponse "更新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id} [put]
func (h *UserHandler) UpdateUser(c *gin.Context) {
	userID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	if err := h.userService.UpdateProfile(c.Request.Context(), userID, req.RealName, req.Phone, req.Email); err != nil {
		if err == service.ErrUserNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户不存在"})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 更新用户类型和角色
	// if req.UserType != 0 {
	//     h.userService.UpdateUserType(c.Request.Context(), userID, req.UserType)
	// }
	// if len(req.RoleIds) > 0 {
	//     h.userService.UpdateUserRoles(c.Request.Context(), userID, req.RoleIds)
	// }

	c.JSON(http.StatusOK, SuccessResponse{Message: "用户信息更新成功"})
}

// DeleteUser 删除用户
// @Summary 删除用户
// @Description 删除用户账号
// @Tags 用户管理
// @Accept json
// @Produce json
// @Param id path int true "用户ID"
// @Success 200 {object} SuccessResponse "删除成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/{id} [delete]
func (h *UserHandler) DeleteUser(c *gin.Context) {
	userID, err := ParseParamID(c, "id")
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	// TODO: 实现删除用户逻辑
	// if err := h.userService.DeleteUser(c.Request.Context(), userID); err != nil {
	//     if err == service.ErrUserNotFound {
	//         c.JSON(http.StatusNotFound, ErrorResponse{Error: "用户不存在"})
	//         return
	//     }
	//     c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
	//     return
	// }

	// 暂时返回成功，避免未使用变量警告
	_ = userID
	c.JSON(http.StatusOK, SuccessResponse{Message: "用户删除成功"})
}

// ExportUsers 导出用户列表
// @Summary 导出用户列表
// @Description 导出用户列表为Excel文件
// @Tags 用户管理
// @Accept json
// @Produce application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
// @Param page query int false "页码" default(1)
// @Param size query int false "每页数量" default(10)
// @Param username query string false "用户名"
// @Param realName query string false "真实姓名"
// @Param userType query int false "用户类型"
// @Success 200 {file} file "Excel文件"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 500 {object} ErrorResponse "服务器内部错误"
// @Router /api/v1/users/export [get]
func (h *UserHandler) ExportUsers(c *gin.Context) {
	// 获取查询参数（暂时不使用，后续可以实现筛选导出）
	_, _ = ParsePagination(c)
	_ = c.Query("username")
	_ = c.Query("realName")
	_ = c.Query("userType")

	// 获取所有用户数据（导出时不分页）
	users, _, err := h.userService.ListUsers(c.Request.Context(), 1, 1000)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	// 创建Excel文件
	f := excelize.NewFile()
	defer func() {
		if err := f.Close(); err != nil {
			fmt.Println(err)
		}
	}()

	// 设置工作表名称
	sheetName := "用户列表"
	f.SetSheetName("Sheet1", sheetName)

	// 设置表头
	headers := []string{"ID", "用户名", "真实姓名", "手机号", "邮箱", "用户类型", "状态", "创建时间"}
	for i, header := range headers {
		cell := fmt.Sprintf("%c1", 'A'+i)
		f.SetCellValue(sheetName, cell, header)
	}

	// 设置表头样式
	headerStyle, _ := f.NewStyle(&excelize.Style{
		Font: &excelize.Font{Bold: true},
		Fill: excelize.Fill{Type: "pattern", Color: []string{"E0E0E0"}, Pattern: 1},
	})
	f.SetRowStyle(sheetName, 1, 1, headerStyle)

	// 填充数据
	for i, user := range users {
		row := i + 2
		f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), user.ID)
		f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), user.Username)
		f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), user.RealName)
		f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), user.Phone)
		f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), user.Email)

		// 用户类型转换
		var userTypeStr string
		switch user.UserType {
		case 1:
			userTypeStr = "买家"
		case 2:
			userTypeStr = "卖家"
		case 3:
			userTypeStr = "管理员"
		case 4:
			userTypeStr = "质检员"
		default:
			userTypeStr = "未知"
		}
		f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), userTypeStr)

		// 状态转换
		statusStr := "禁用"
		if user.Status == 1 {
			statusStr = "启用"
		}
		f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), statusStr)

		f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), user.CreatedAt.Format("2006-01-02 15:04:05"))
	}

	// 自动调整列宽
	for i := 0; i < len(headers); i++ {
		col := fmt.Sprintf("%c", 'A'+i)
		f.SetColWidth(sheetName, col, col, 15)
	}

	// 生成文件名
	filename := fmt.Sprintf("用户列表_%s.xlsx", time.Now().Format("20060102_150405"))

	// 设置响应头
	c.Header("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet")
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filename))
	c.Header("Content-Transfer-Encoding", "binary")

	// 输出文件
	if err := f.Write(c.Writer); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "导出失败: " + err.Error()})
		return
	}
}
