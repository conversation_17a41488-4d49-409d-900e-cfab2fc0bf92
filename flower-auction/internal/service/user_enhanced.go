package service

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
	"golang.org/x/crypto/bcrypt"
)

var (
	ErrVerificationExists     = errors.New("实名认证已存在")
	ErrVerificationNotFound   = errors.New("实名认证不存在")
	ErrSubAccountExists       = errors.New("子账号已存在")
	ErrSubAccountNotFound     = errors.New("子账号不存在")
	ErrSubAccountLimitExceeded = errors.New("子账号数量超限")
	ErrProfileNotFound        = errors.New("用户档案不存在")
)

// UserEnhancedService 用户增强功能服务接口
type UserEnhancedService interface {
	// 实名认证
	SubmitVerification(ctx context.Context, userID int64, realName, idCardNumber, idCardFront, idCardBack, businessLicense string) (*model.UserVerification, error)
	ApproveVerification(ctx context.Context, verificationID int64) error
	RejectVerification(ctx context.Context, verificationID int64, reason string) error
	GetUserVerification(ctx context.Context, userID int64) (*model.UserVerification, error)
	ListVerifications(ctx context.Context, status int8, page, size int) ([]*model.UserVerification, int64, error)

	// 子账号管理
	CreateSubAccount(ctx context.Context, parentID int64, username, password, realName, phone, email string, permissions []string) (*model.SubAccount, error)
	UpdateSubAccount(ctx context.Context, id int64, realName, phone, email string, permissions []string) error
	DeleteSubAccount(ctx context.Context, id int64) error
	GetSubAccount(ctx context.Context, id int64) (*model.SubAccount, error)
	ListSubAccounts(ctx context.Context, parentID int64) ([]*model.SubAccount, error)
	UpdateSubAccountStatus(ctx context.Context, id int64, status int8) error
	SubAccountLogin(ctx context.Context, username, password string) (*model.SubAccount, error)

	// 用户档案
	CreateOrUpdateProfile(ctx context.Context, userID int64, avatar string, gender int8, birthday *time.Time, address, company, position, website, bio, preferences string) (*model.UserProfile, error)
	GetUserProfile(ctx context.Context, userID int64) (*model.UserProfile, error)

	// 用户完整信息
	GetUserWithProfile(ctx context.Context, userID int64) (*model.UserWithProfile, error)

	// 用户行为分析
	GetUserStats(ctx context.Context, userID int64) (map[string]interface{}, error)
	BatchImportUsers(ctx context.Context, users []map[string]interface{}) ([]int64, []error)
}

// userEnhancedService 用户增强功能服务实现
type userEnhancedService struct {
	userEnhancedDAO dao.UserEnhancedDAO
	userDAO         dao.UserDAO
}

// NewUserEnhancedService 创建用户增强功能服务实例
func NewUserEnhancedService() UserEnhancedService {
	return &userEnhancedService{
		userEnhancedDAO: dao.NewUserEnhancedDAO(),
		userDAO:         dao.NewUserDAO(),
	}
}

// SubmitVerification 提交实名认证
func (s *userEnhancedService) SubmitVerification(ctx context.Context, userID int64, realName, idCardNumber, idCardFront, idCardBack, businessLicense string) (*model.UserVerification, error) {
	// 检查用户是否存在
	user, err := s.userDAO.FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	// 检查是否已有认证记录
	existVerification, err := s.userEnhancedDAO.FindVerificationByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if existVerification != nil {
		return nil, ErrVerificationExists
	}

	verification := &model.UserVerification{
		UserID:          userID,
		RealName:        realName,
		IDCardNumber:    idCardNumber,
		IDCardFront:     idCardFront,
		IDCardBack:      idCardBack,
		BusinessLicense: businessLicense,
		Status:          0, // 待审核
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.userEnhancedDAO.CreateVerification(ctx, verification); err != nil {
		return nil, err
	}

	return verification, nil
}

// ApproveVerification 通过实名认证
func (s *userEnhancedService) ApproveVerification(ctx context.Context, verificationID int64) error {
	verification, err := s.userEnhancedDAO.FindVerificationByUserID(ctx, verificationID)
	if err != nil {
		return err
	}
	if verification == nil {
		return ErrVerificationNotFound
	}

	now := time.Now()
	verification.Status = 1 // 已通过
	verification.VerifiedAt = &now
	verification.UpdatedAt = now

	return s.userEnhancedDAO.UpdateVerification(ctx, verification)
}

// RejectVerification 拒绝实名认证
func (s *userEnhancedService) RejectVerification(ctx context.Context, verificationID int64, reason string) error {
	verification, err := s.userEnhancedDAO.FindVerificationByUserID(ctx, verificationID)
	if err != nil {
		return err
	}
	if verification == nil {
		return ErrVerificationNotFound
	}

	verification.Status = 2 // 已拒绝
	verification.RejectReason = reason
	verification.UpdatedAt = time.Now()

	return s.userEnhancedDAO.UpdateVerification(ctx, verification)
}

// GetUserVerification 获取用户实名认证信息
func (s *userEnhancedService) GetUserVerification(ctx context.Context, userID int64) (*model.UserVerification, error) {
	return s.userEnhancedDAO.FindVerificationByUserID(ctx, userID)
}

// ListVerifications 查询实名认证列表
func (s *userEnhancedService) ListVerifications(ctx context.Context, status int8, page, size int) ([]*model.UserVerification, int64, error) {
	offset := (page - 1) * size
	verifications, err := s.userEnhancedDAO.ListVerifications(ctx, status, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.userEnhancedDAO.CountVerifications(ctx, status)
	if err != nil {
		return nil, 0, err
	}

	return verifications, total, nil
}

// CreateSubAccount 创建子账号
func (s *userEnhancedService) CreateSubAccount(ctx context.Context, parentID int64, username, password, realName, phone, email string, permissions []string) (*model.SubAccount, error) {
	// 检查主账号是否存在
	parent, err := s.userDAO.FindByID(ctx, parentID)
	if err != nil {
		return nil, err
	}
	if parent == nil {
		return nil, ErrUserNotFound
	}

	// 检查子账号数量限制
	count, err := s.userEnhancedDAO.CountSubAccountsByParent(ctx, parentID)
	if err != nil {
		return nil, err
	}
	if count >= 10 { // 最多10个子账号
		return nil, ErrSubAccountLimitExceeded
	}

	// 检查用户名是否已存在
	existSubAccount, err := s.userEnhancedDAO.FindSubAccountByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if existSubAccount != nil {
		return nil, ErrSubAccountExists
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	subAccount := &model.SubAccount{
		ParentID:    parentID,
		Username:    username,
		Password:    string(hashedPassword),
		RealName:    realName,
		Phone:       phone,
		Email:       email,
		Permissions: permissions,
		Status:      1, // 默认启用
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	if err := s.userEnhancedDAO.CreateSubAccount(ctx, subAccount); err != nil {
		return nil, err
	}

	return subAccount, nil
}

// UpdateSubAccount 更新子账号
func (s *userEnhancedService) UpdateSubAccount(ctx context.Context, id int64, realName, phone, email string, permissions []string) error {
	subAccount, err := s.userEnhancedDAO.FindSubAccountByID(ctx, id)
	if err != nil {
		return err
	}
	if subAccount == nil {
		return ErrSubAccountNotFound
	}

	subAccount.RealName = realName
	subAccount.Phone = phone
	subAccount.Email = email
	subAccount.Permissions = permissions
	subAccount.UpdatedAt = time.Now()

	return s.userEnhancedDAO.UpdateSubAccount(ctx, subAccount)
}

// DeleteSubAccount 删除子账号
func (s *userEnhancedService) DeleteSubAccount(ctx context.Context, id int64) error {
	subAccount, err := s.userEnhancedDAO.FindSubAccountByID(ctx, id)
	if err != nil {
		return err
	}
	if subAccount == nil {
		return ErrSubAccountNotFound
	}

	return s.userEnhancedDAO.DeleteSubAccount(ctx, id)
}

// GetSubAccount 获取子账号信息
func (s *userEnhancedService) GetSubAccount(ctx context.Context, id int64) (*model.SubAccount, error) {
	subAccount, err := s.userEnhancedDAO.FindSubAccountByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if subAccount == nil {
		return nil, ErrSubAccountNotFound
	}
	return subAccount, nil
}

// ListSubAccounts 查询子账号列表
func (s *userEnhancedService) ListSubAccounts(ctx context.Context, parentID int64) ([]*model.SubAccount, error) {
	return s.userEnhancedDAO.ListSubAccountsByParent(ctx, parentID)
}

// UpdateSubAccountStatus 更新子账号状态
func (s *userEnhancedService) UpdateSubAccountStatus(ctx context.Context, id int64, status int8) error {
	subAccount, err := s.userEnhancedDAO.FindSubAccountByID(ctx, id)
	if err != nil {
		return err
	}
	if subAccount == nil {
		return ErrSubAccountNotFound
	}

	subAccount.Status = status
	subAccount.UpdatedAt = time.Now()

	return s.userEnhancedDAO.UpdateSubAccount(ctx, subAccount)
}

// SubAccountLogin 子账号登录
func (s *userEnhancedService) SubAccountLogin(ctx context.Context, username, password string) (*model.SubAccount, error) {
	subAccount, err := s.userEnhancedDAO.FindSubAccountByUsername(ctx, username)
	if err != nil {
		return nil, err
	}
	if subAccount == nil {
		return nil, ErrUserNotFound
	}

	// 检查状态
	if subAccount.Status != 1 {
		return nil, errors.New("子账号已被禁用")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(subAccount.Password), []byte(password)); err != nil {
		return nil, ErrInvalidPassword
	}

	// 更新最后登录时间
	now := time.Now()
	subAccount.LastLoginAt = &now
	subAccount.UpdatedAt = now
	s.userEnhancedDAO.UpdateSubAccount(ctx, subAccount)

	return subAccount, nil
}

// CreateOrUpdateProfile 创建或更新用户档案
func (s *userEnhancedService) CreateOrUpdateProfile(ctx context.Context, userID int64, avatar string, gender int8, birthday *time.Time, address, company, position, website, bio, preferences string) (*model.UserProfile, error) {
	// 检查用户是否存在
	user, err := s.userDAO.FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, ErrUserNotFound
	}

	// 查找现有档案
	existProfile, err := s.userEnhancedDAO.FindProfileByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}

	if existProfile != nil {
		// 更新现有档案
		existProfile.Avatar = avatar
		existProfile.Gender = gender
		existProfile.Birthday = birthday
		existProfile.Address = address
		existProfile.Company = company
		existProfile.Position = position
		existProfile.Website = website
		existProfile.Bio = bio
		existProfile.Preferences = preferences
		existProfile.UpdatedAt = time.Now()

		if err := s.userEnhancedDAO.UpdateProfile(ctx, existProfile); err != nil {
			return nil, err
		}
		return existProfile, nil
	} else {
		// 创建新档案
		profile := &model.UserProfile{
			UserID:      userID,
			Avatar:      avatar,
			Gender:      gender,
			Birthday:    birthday,
			Address:     address,
			Company:     company,
			Position:    position,
			Website:     website,
			Bio:         bio,
			Preferences: preferences,
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}

		if err := s.userEnhancedDAO.CreateProfile(ctx, profile); err != nil {
			return nil, err
		}
		return profile, nil
	}
}

// GetUserProfile 获取用户档案
func (s *userEnhancedService) GetUserProfile(ctx context.Context, userID int64) (*model.UserProfile, error) {
	profile, err := s.userEnhancedDAO.FindProfileByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if profile == nil {
		return nil, ErrProfileNotFound
	}
	return profile, nil
}

// GetUserWithProfile 获取用户完整信息
func (s *userEnhancedService) GetUserWithProfile(ctx context.Context, userID int64) (*model.UserWithProfile, error) {
	return s.userEnhancedDAO.FindUserWithProfile(ctx, userID)
}

// GetUserStats 获取用户统计信息
func (s *userEnhancedService) GetUserStats(ctx context.Context, userID int64) (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 子账号数量
	subAccountCount, err := s.userEnhancedDAO.CountSubAccountsByParent(ctx, userID)
	if err != nil {
		return nil, err
	}
	stats["subAccountCount"] = subAccountCount

	// 实名认证状态
	verification, err := s.userEnhancedDAO.FindVerificationByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if verification != nil {
		stats["verificationStatus"] = verification.Status
	} else {
		stats["verificationStatus"] = -1 // 未提交
	}

	// 这里可以添加更多统计信息，如订单数量、拍卖参与次数等

	return stats, nil
}

// BatchImportUsers 批量导入用户
func (s *userEnhancedService) BatchImportUsers(ctx context.Context, users []map[string]interface{}) ([]int64, []error) {
	var userIDs []int64
	var errors []error

	for _, userData := range users {
		// 提取用户数据
		username, _ := userData["username"].(string)
		password, _ := userData["password"].(string)
		realName, _ := userData["realName"].(string)
		phone, _ := userData["phone"].(string)
		email, _ := userData["email"].(string)
		userType, _ := userData["userType"].(int8)

		// 创建用户
		userService := NewUserService()
		user, err := userService.Register(ctx, username, password, realName, phone, email, userType)
		if err != nil {
			errors = append(errors, err)
			userIDs = append(userIDs, 0)
			continue
		}

		userIDs = append(userIDs, user.ID)
		errors = append(errors, nil)
	}

	return userIDs, errors
}
