package service

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrProductNotFound  = errors.New("商品不存在")
	ErrCategoryNotFound = errors.New("分类不存在")
	ErrInvalidOperation = errors.New("无效的操作")
)

// ProductService 商品服务接口
type ProductService interface {
	// 商品管理
	CreateProduct(ctx context.Context, name string, categoryID int64, description string, qualityLevel int8, origin string, supplierID int64) (*model.Product, error)
	UpdateProduct(ctx context.Context, id int64, name string, categoryID int64, description string, qualityLevel int8, origin string) error
	GetProduct(ctx context.Context, id int64) (*model.ProductWithCategory, error)
	ListProducts(ctx context.Context, categoryID int64, page, size int) ([]*model.ProductWithCategory, int64, error)
	UpdateProductStatus(ctx context.Context, id int64, status int8) error

	// 分类管理
	CreateCategory(ctx context.Context, name string, parentID *int64, level int8, sortOrder int) (*model.Category, error)
	UpdateCategory(ctx context.Context, id int64, name string, sortOrder int) error
	DeleteCategory(ctx context.Context, id int64) error
	GetCategoryTree(ctx context.Context) ([]model.CategoryTree, error)
}

// productService 商品服务实现
type productService struct {
	productDAO dao.ProductDAO
}

// NewProductService 创建商品服务实例
func NewProductService() ProductService {
	return &productService{
		productDAO: dao.NewProductDAO(),
	}
}

// CreateProduct 创建商品
func (s *productService) CreateProduct(ctx context.Context, name string, categoryID int64, description string, qualityLevel int8, origin string, supplierID int64) (*model.Product, error) {
	// 检查分类是否存在
	category, err := s.productDAO.FindCategoryByID(ctx, categoryID)
	if err != nil {
		return nil, err
	}
	if category == nil {
		return nil, ErrCategoryNotFound
	}

	product := &model.Product{
		Name:         name,
		CategoryID:   categoryID,
		Description:  description,
		QualityLevel: qualityLevel,
		Origin:       origin,
		SupplierID:   supplierID,
		Status:       0, // 默认下架
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.productDAO.Create(ctx, product); err != nil {
		return nil, err
	}

	return product, nil
}

// UpdateProduct 更新商品
func (s *productService) UpdateProduct(ctx context.Context, id int64, name string, categoryID int64, description string, qualityLevel int8, origin string) error {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if product == nil {
		return ErrProductNotFound
	}

	// 检查分类是否存在
	category, err := s.productDAO.FindCategoryByID(ctx, categoryID)
	if err != nil {
		return err
	}
	if category == nil {
		return ErrCategoryNotFound
	}

	// 更新商品信息
	product.Name = name
	product.CategoryID = categoryID
	product.Description = description
	product.QualityLevel = qualityLevel
	product.Origin = origin
	product.UpdatedAt = time.Now()

	return s.productDAO.Update(ctx, product)
}

// GetProduct 获取商品信息
func (s *productService) GetProduct(ctx context.Context, id int64) (*model.ProductWithCategory, error) {
	product, err := s.productDAO.FindWithCategory(ctx, id)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, ErrProductNotFound
	}
	return product, nil
}

// ListProducts 分页查询商品列表
func (s *productService) ListProducts(ctx context.Context, categoryID int64, page, size int) ([]*model.ProductWithCategory, int64, error) {
	offset := (page - 1) * size

	var products []*model.ProductWithCategory
	var total int64
	var err error

	if categoryID > 0 {
		products, err = s.productDAO.ListByCategoryID(ctx, categoryID, offset, size)
		if err != nil {
			return nil, 0, err
		}
		total, err = s.productDAO.CountByCategory(ctx, categoryID)
	} else {
		// 获取所有商品，需要转换为ProductWithCategory
		allProducts, err := s.productDAO.List(ctx, offset, size)
		if err != nil {
			return nil, 0, err
		}

		// 转换为ProductWithCategory
		products = make([]*model.ProductWithCategory, len(allProducts))
		for i, p := range allProducts {
			// 获取分类信息
			var category model.Category
			if p.CategoryID > 0 {
				if cat, err := s.productDAO.FindCategoryByID(ctx, p.CategoryID); err == nil && cat != nil {
					category = *cat
				}
			}

			products[i] = &model.ProductWithCategory{
				Product:  *p,
				Category: category,
			}
		}

		total, err = s.productDAO.Count(ctx)
	}

	if err != nil {
		return nil, 0, err
	}

	return products, total, nil
}

// UpdateProductStatus 更新商品状态
func (s *productService) UpdateProductStatus(ctx context.Context, id int64, status int8) error {
	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, id)
	if err != nil {
		return err
	}
	if product == nil {
		return ErrProductNotFound
	}

	// 更新状态
	product.Status = status
	product.UpdatedAt = time.Now()
	return s.productDAO.Update(ctx, product)
}

// CreateCategory 创建分类
func (s *productService) CreateCategory(ctx context.Context, name string, parentID *int64, level int8, sortOrder int) (*model.Category, error) {
	// 如果有父分类，检查父分类是否存在
	if parentID != nil {
		parent, err := s.productDAO.FindCategoryByID(ctx, *parentID)
		if err != nil {
			return nil, err
		}
		if parent == nil {
			return nil, ErrCategoryNotFound
		}
	}

	category := &model.Category{
		Name:      name,
		ParentID:  parentID,
		Level:     level,
		SortOrder: sortOrder,
		Status:    1, // 默认启用
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := s.productDAO.CreateCategory(ctx, category); err != nil {
		return nil, err
	}

	return category, nil
}

// UpdateCategory 更新分类
func (s *productService) UpdateCategory(ctx context.Context, id int64, name string, sortOrder int) error {
	// 检查分类是否存在
	category, err := s.productDAO.FindCategoryByID(ctx, id)
	if err != nil {
		return err
	}
	if category == nil {
		return ErrCategoryNotFound
	}

	// 更新分类信息
	category.Name = name
	category.SortOrder = sortOrder
	category.UpdatedAt = time.Now()

	return s.productDAO.UpdateCategory(ctx, category)
}

// DeleteCategory 删除分类
func (s *productService) DeleteCategory(ctx context.Context, id int64) error {
	// 检查分类是否存在
	category, err := s.productDAO.FindCategoryByID(ctx, id)
	if err != nil {
		return err
	}
	if category == nil {
		return ErrCategoryNotFound
	}

	// 检查是否有子分类
	children, err := s.productDAO.FindCategoriesByParentID(ctx, id)
	if err != nil {
		return err
	}
	if len(children) > 0 {
		return ErrInvalidOperation
	}

	// 检查是否有关联的商品
	count, err := s.productDAO.CountByCategory(ctx, id)
	if err != nil {
		return err
	}
	if count > 0 {
		return ErrInvalidOperation
	}

	return s.productDAO.DeleteCategory(ctx, id)
}

// GetCategoryTree 获取分类树
func (s *productService) GetCategoryTree(ctx context.Context) ([]model.CategoryTree, error) {
	categories, err := s.productDAO.ListAllCategories(ctx)
	if err != nil {
		return nil, err
	}

	// 构建分类树
	categoryMap := make(map[int64]*model.CategoryTree)
	var roots []model.CategoryTree

	// 第一遍遍历，创建所有节点
	for _, category := range categories {
		tree := model.CategoryTree{
			Category: *category,
			Children: make([]model.CategoryTree, 0),
		}
		categoryMap[category.ID] = &tree

		// 如果是根节点，加入roots
		if category.ParentID == nil {
			roots = append(roots, tree)
		}
	}

	// 第二遍遍历，建立父子关系
	for _, category := range categories {
		if category.ParentID != nil {
			if parent, ok := categoryMap[*category.ParentID]; ok {
				parent.Children = append(parent.Children, *categoryMap[category.ID])
			}
		}
	}

	return roots, nil
}
