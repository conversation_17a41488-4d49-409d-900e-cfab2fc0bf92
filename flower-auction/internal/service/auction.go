package service

import (
	"context"
	"errors"
	"time"

	"github.com/putonghao/flower-auction/internal/dao"
	"github.com/putonghao/flower-auction/internal/model"
)

var (
	ErrAuctionNotFound     = errors.New("拍卖会不存在")
	ErrAuctionItemNotFound = errors.New("拍卖商品不存在")
	ErrAuctionEnded        = errors.New("拍卖已结束")
	ErrAuctionNotStarted   = errors.New("拍卖未开始")
	ErrInvalidBidPrice     = errors.New("出价无效")
)

// AuctionService 拍卖服务接口
type AuctionService interface {
	// 拍卖会管理
	CreateAuction(ctx context.Context, name string, startTime, endTime time.Time, auctioneerID int64, description string) (*model.Auction, error)
	UpdateAuction(ctx context.Context, id int64, name string, startTime, endTime time.Time, description string) error
	GetAuction(ctx context.Context, id int64) (*model.AuctionWithItems, error)
	ListAuctions(ctx context.Context, page, size int) ([]*model.Auction, int64, error)
	UpdateAuctionStatus(ctx context.Context, id int64, status int8) error

	// 拍卖商品管理
	AddAuctionItem(ctx context.Context, auctionID, productID int64, startPrice, stepPrice float64, startTime time.Time) (*model.AuctionItem, error)
	UpdateAuctionItem(ctx context.Context, id int64, startPrice, stepPrice float64, startTime time.Time) error
	GetAuctionItem(ctx context.Context, id int64) (*model.AuctionItemDetail, error)
	ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItemDetail, error)
	UpdateAuctionItemStatus(ctx context.Context, id int64, status int8) error

	// 竞价管理
	PlaceBid(ctx context.Context, itemID, userID int64, price float64) error
	ListBids(ctx context.Context, itemID int64) ([]*model.Bid, error)
	GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error)
}

// auctionService 拍卖服务实现
type auctionService struct {
	auctionDAO dao.AuctionDAO
	productDAO dao.ProductDAO
}

// NewAuctionService 创建拍卖服务实例
func NewAuctionService() AuctionService {
	return &auctionService{
		auctionDAO: dao.NewAuctionDAO(),
		productDAO: dao.NewProductDAO(),
	}
}

// CreateAuction 创建拍卖会
func (s *auctionService) CreateAuction(ctx context.Context, name string, startTime, endTime time.Time, auctioneerID int64, description string) (*model.Auction, error) {
	auction := &model.Auction{
		Name:         name,
		StartTime:    startTime,
		EndTime:      endTime,
		Status:       0, // 未开始
		AuctioneerID: auctioneerID,
		Description:  description,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.auctionDAO.CreateAuction(ctx, auction); err != nil {
		return nil, err
	}

	return auction, nil
}

// UpdateAuction 更新拍卖会
func (s *auctionService) UpdateAuction(ctx context.Context, id int64, name string, startTime, endTime time.Time, description string) error {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	// 只有未开始的拍卖会才能修改
	if auction.Status != 0 {
		return ErrInvalidOperation
	}

	auction.Name = name
	auction.StartTime = startTime
	auction.EndTime = endTime
	auction.Description = description
	auction.UpdatedAt = time.Now()

	return s.auctionDAO.UpdateAuction(ctx, auction)
}

// GetAuction 获取拍卖会信息
func (s *auctionService) GetAuction(ctx context.Context, id int64) (*model.AuctionWithItems, error) {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if auction == nil {
		return nil, ErrAuctionNotFound
	}

	items, err := s.auctionDAO.ListAuctionItems(ctx, id)
	if err != nil {
		return nil, err
	}

	// 获取每个商品的详细信息
	itemDetails := make([]model.AuctionItemDetail, 0, len(items))
	for _, item := range items {
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil {
			return nil, err
		}

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		totalBids, err := s.auctionDAO.CountBidsByItem(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		itemDetails = append(itemDetails, model.AuctionItemDetail{
			AuctionItem: *item,
			Product:     *product,
			HighestBid:  highestBid,
			TotalBids:   totalBids,
		})
	}

	return &model.AuctionWithItems{
		Auction: *auction,
		Items:   itemDetails,
	}, nil
}

// ListAuctions 分页查询拍卖会列表
func (s *auctionService) ListAuctions(ctx context.Context, page, size int) ([]*model.Auction, int64, error) {
	offset := (page - 1) * size
	auctions, err := s.auctionDAO.ListAuctions(ctx, offset, size)
	if err != nil {
		return nil, 0, err
	}

	total, err := s.auctionDAO.CountAuctions(ctx)
	if err != nil {
		return nil, 0, err
	}

	return auctions, total, nil
}

// UpdateAuctionStatus 更新拍卖会状态
func (s *auctionService) UpdateAuctionStatus(ctx context.Context, id int64, status int8) error {
	auction, err := s.auctionDAO.FindAuctionByID(ctx, id)
	if err != nil {
		return err
	}
	if auction == nil {
		return ErrAuctionNotFound
	}

	auction.Status = status
	auction.UpdatedAt = time.Now()
	return s.auctionDAO.UpdateAuction(ctx, auction)
}

// AddAuctionItem 添加拍卖商品
func (s *auctionService) AddAuctionItem(ctx context.Context, auctionID, productID int64, startPrice, stepPrice float64, startTime time.Time) (*model.AuctionItem, error) {
	// 检查拍卖会是否存在
	auction, err := s.auctionDAO.FindAuctionByID(ctx, auctionID)
	if err != nil {
		return nil, err
	}
	if auction == nil {
		return nil, ErrAuctionNotFound
	}

	// 检查商品是否存在
	product, err := s.productDAO.FindByID(ctx, productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, ErrProductNotFound
	}

	item := &model.AuctionItem{
		AuctionID:    auctionID,
		ProductID:    productID,
		StartPrice:   startPrice,
		CurrentPrice: startPrice,
		StepPrice:    stepPrice,
		Status:       0, // 未开始
		StartTime:    startTime,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := s.auctionDAO.CreateAuctionItem(ctx, item); err != nil {
		return nil, err
	}

	return item, nil
}

// UpdateAuctionItem 更新拍卖商品
func (s *auctionService) UpdateAuctionItem(ctx context.Context, id int64, startPrice, stepPrice float64, startTime time.Time) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	// 只有未开始的商品才能修改
	if item.Status != 0 {
		return ErrInvalidOperation
	}

	item.StartPrice = startPrice
	item.CurrentPrice = startPrice
	item.StepPrice = stepPrice
	item.StartTime = startTime
	item.UpdatedAt = time.Now()

	return s.auctionDAO.UpdateAuctionItem(ctx, item)
}

// GetAuctionItem 获取拍卖商品信息
func (s *auctionService) GetAuctionItem(ctx context.Context, id int64) (*model.AuctionItemDetail, error) {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return nil, err
	}
	if item == nil {
		return nil, ErrAuctionItemNotFound
	}

	product, err := s.productDAO.FindByID(ctx, item.ProductID)
	if err != nil {
		return nil, err
	}

	highestBid, err := s.auctionDAO.GetHighestBid(ctx, id)
	if err != nil {
		return nil, err
	}

	totalBids, err := s.auctionDAO.CountBidsByItem(ctx, id)
	if err != nil {
		return nil, err
	}

	return &model.AuctionItemDetail{
		AuctionItem: *item,
		Product:     *product,
		HighestBid:  highestBid,
		TotalBids:   totalBids,
	}, nil
}

// ListAuctionItems 查询拍卖商品列表
func (s *auctionService) ListAuctionItems(ctx context.Context, auctionID int64) ([]*model.AuctionItemDetail, error) {
	items, err := s.auctionDAO.ListAuctionItems(ctx, auctionID)
	if err != nil {
		return nil, err
	}

	itemDetails := make([]*model.AuctionItemDetail, 0, len(items))
	for _, item := range items {
		product, err := s.productDAO.FindByID(ctx, item.ProductID)
		if err != nil {
			return nil, err
		}

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		totalBids, err := s.auctionDAO.CountBidsByItem(ctx, item.ID)
		if err != nil {
			return nil, err
		}

		itemDetails = append(itemDetails, &model.AuctionItemDetail{
			AuctionItem: *item,
			Product:     *product,
			HighestBid:  highestBid,
			TotalBids:   totalBids,
		})
	}

	return itemDetails, nil
}

// UpdateAuctionItemStatus 更新拍卖商品状态
func (s *auctionService) UpdateAuctionItemStatus(ctx context.Context, id int64, status int8) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, id)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	item.Status = status
	item.UpdatedAt = time.Now()

	// 如果是成交状态，设置成交时间和中标用户
	if status == 2 {
		now := time.Now()
		item.EndTime = &now

		highestBid, err := s.auctionDAO.GetHighestBid(ctx, id)
		if err != nil {
			return err
		}
		if highestBid != nil {
			item.WinnerID = &highestBid.UserID
		}
	}

	return s.auctionDAO.UpdateAuctionItem(ctx, item)
}

// PlaceBid 出价
func (s *auctionService) PlaceBid(ctx context.Context, itemID, userID int64, price float64) error {
	item, err := s.auctionDAO.FindAuctionItemByID(ctx, itemID)
	if err != nil {
		return err
	}
	if item == nil {
		return ErrAuctionItemNotFound
	}

	// 检查拍卖状态
	if item.Status != 1 {
		if item.Status == 0 {
			return ErrAuctionNotStarted
		}
		return ErrAuctionEnded
	}

	// 检查出价是否有效
	if price < item.CurrentPrice+item.StepPrice {
		return ErrInvalidBidPrice
	}

	// 创建竞价记录
	bid := &model.Bid{
		AuctionItemID: itemID,
		UserID:        userID,
		Price:         price,
		Status:        1, // 有效
		CreatedAt:     time.Now(),
	}

	if err := s.auctionDAO.CreateBid(ctx, bid); err != nil {
		return err
	}

	// 更新当前价格
	item.CurrentPrice = price
	item.UpdatedAt = time.Now()
	return s.auctionDAO.UpdateAuctionItem(ctx, item)
}

// ListBids 查询竞价记录
func (s *auctionService) ListBids(ctx context.Context, itemID int64) ([]*model.Bid, error) {
	return s.auctionDAO.ListBidsByItem(ctx, itemID)
}

// GetHighestBid 获取最高出价
func (s *auctionService) GetHighestBid(ctx context.Context, itemID int64) (*model.Bid, error) {
	return s.auctionDAO.GetHighestBid(ctx, itemID)
}
