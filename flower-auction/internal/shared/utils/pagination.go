package utils

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// PaginationParams 分页参数
type PaginationParams struct {
	Page int `json:"page"`
	Size int `json:"size"`
}

// ParsePagination 解析分页参数
func ParsePagination(c *gin.Context) (page, size int) {
	page, _ = strconv.Atoi(c<PERSON>("page", "1"))
	size, _ = strconv.Atoi(c<PERSON>("size", "10"))

	if page <= 0 {
		page = 1
	}
	if size <= 0 || size > 100 {
		size = 10
	}

	return page, size
}

// ParseParamID 解析路径参数中的ID
func ParseParamID(c *gin.Context, param string) (int64, error) {
	idStr := c.Param(param)
	return strconv.ParseInt(idStr, 10, 64)
}

// GetOffset 计算偏移量
func GetOffset(page, size int) int {
	return (page - 1) * size
}
