package config

import (
	"log"

	"github.com/spf13/viper"
)

// Config 应用配置
type Config struct {
	Server   ServerConfig   `mapstructure:"server"`
	Database DatabaseConfig `mapstructure:"database"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	OSS      OSSConfig      `mapstructure:"oss"`
	Upload   UploadConfig   `mapstructure:"upload"`
	Redis    RedisConfig    `mapstructure:"redis"`
	Log      LogConfig      `mapstructure:"log"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         int    `mapstructure:"port"`
	Mode         string `mapstructure:"mode"`
	ReadTimeout  int    `mapstructure:"read_timeout"`
	WriteTimeout int    `mapstructure:"write_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	User    DBConfig `mapstructure:"user"`
	Product DBConfig `mapstructure:"product"`
	Log     DBConfig `mapstructure:"log"`
}

// DBConfig 单个数据库配置
type DBConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
	Charset  string `mapstructure:"charset"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret      string `mapstructure:"secret"`
	ExpireHours int    `mapstructure:"expire_hours"`
}

// OSSConfig OSS配置
type OSSConfig struct {
	Endpoint  string `mapstructure:"endpoint"`
	Bucket    string `mapstructure:"bucket"`
	AccessKey string `mapstructure:"access_key"`
	SecretKey string `mapstructure:"secret_key"`
	BaseURL   string `mapstructure:"base_url"`
}

// UploadConfig 上传配置
type UploadConfig struct {
	MaxImageSize  int64    `mapstructure:"max_image_size"`
	MaxDocSize    int64    `mapstructure:"max_doc_size"`
	AllowedImages []string `mapstructure:"allowed_images"`
	AllowedDocs   []string `mapstructure:"allowed_docs"`
	LocalDir      string   `mapstructure:"local_dir"`
	BaseURL       string   `mapstructure:"base_url"`
	UseOSS        bool     `mapstructure:"use_oss"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level      string `mapstructure:"level"`
	Filename   string `mapstructure:"filename"`
	MaxSize    int    `mapstructure:"max_size"`
	MaxAge     int    `mapstructure:"max_age"`
	MaxBackups int    `mapstructure:"max_backups"`
	Compress   bool   `mapstructure:"compress"`
}

var globalConfig *Config

// Init 初始化配置
func Init(configPath string) error {
	viper.SetConfigFile(configPath)
	viper.SetConfigType("yaml")

	// 设置默认值
	setDefaults()

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		log.Printf("Warning: Failed to read config file: %v", err)
	}

	// 绑定环境变量
	viper.AutomaticEnv()

	// 解析配置
	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return err
	}

	globalConfig = &config
	return nil
}

// setDefaults 设置默认配置
func setDefaults() {
	// 服务器默认配置
	viper.SetDefault("server.port", 8081)
	viper.SetDefault("server.mode", "debug")
	viper.SetDefault("server.read_timeout", 60)
	viper.SetDefault("server.write_timeout", 60)

	// 数据库默认配置
	viper.SetDefault("database.user.host", "127.0.0.1")
	viper.SetDefault("database.user.port", 3306)
	viper.SetDefault("database.user.charset", "utf8mb4")
	viper.SetDefault("database.product.host", "127.0.0.1")
	viper.SetDefault("database.product.port", 3306)
	viper.SetDefault("database.product.charset", "utf8mb4")
	viper.SetDefault("database.log.host", "127.0.0.1")
	viper.SetDefault("database.log.port", 3306)
	viper.SetDefault("database.log.charset", "utf8mb4")

	// JWT默认配置
	viper.SetDefault("jwt.secret", "flower_auction_secret_key")
	viper.SetDefault("jwt.expire_hours", 24)

	// 上传默认配置
	viper.SetDefault("upload.max_image_size", 5242880) // 5MB
	viper.SetDefault("upload.max_doc_size", 10485760)  // 10MB
	viper.SetDefault("upload.allowed_images", []string{".jpg", ".jpeg", ".png", ".gif", ".webp"})
	viper.SetDefault("upload.allowed_docs", []string{".pdf", ".doc", ".docx", ".xls", ".xlsx", ".txt"})
	viper.SetDefault("upload.local_dir", "./uploads")
	viper.SetDefault("upload.base_url", "http://127.0.0.1:8080/uploads")
	viper.SetDefault("upload.use_oss", false)

	// Redis默认配置
	viper.SetDefault("redis.host", "127.0.0.1")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.db", 0)

	// 日志默认配置
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.filename", "./logs/app.log")
	viper.SetDefault("log.max_size", 100)
	viper.SetDefault("log.max_age", 30)
	viper.SetDefault("log.max_backups", 10)
	viper.SetDefault("log.compress", true)
}

// Get 获取全局配置
func Get() *Config {
	if globalConfig == nil {
		log.Fatal("Config not initialized")
	}
	return globalConfig
}

// GetServer 获取服务器配置
func GetServer() ServerConfig {
	return Get().Server
}

// GetDatabase 获取数据库配置
func GetDatabase() DatabaseConfig {
	return Get().Database
}

// GetJWT 获取JWT配置
func GetJWT() JWTConfig {
	return Get().JWT
}

// GetOSS 获取OSS配置
func GetOSS() OSSConfig {
	return Get().OSS
}

// GetUpload 获取上传配置
func GetUpload() UploadConfig {
	return Get().Upload
}

// GetRedis 获取Redis配置
func GetRedis() RedisConfig {
	return Get().Redis
}

// GetLog 获取日志配置
func GetLog() LogConfig {
	return Get().Log
}
