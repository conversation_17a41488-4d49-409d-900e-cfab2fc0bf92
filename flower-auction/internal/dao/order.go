package dao

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// OrderDAO 订单数据访问接口
type OrderDAO interface {
	CreateOrder(ctx context.Context, order *model.Order) error
	UpdateOrder(ctx context.Context, order *model.Order) error
	FindOrderByID(ctx context.Context, id int64) (*model.Order, error)
	FindOrderByNo(ctx context.Context, orderNo string) (*model.Order, error)
	ListOrdersByUser(ctx context.Context, userID int64, offset, limit int) ([]*model.Order, error)
	CountOrdersByUser(ctx context.Context, userID int64) (int64, error)
	ListAllOrders(ctx context.Context, offset, limit int, status *int8) ([]*model.Order, error)
	CountAllOrders(ctx context.Context, status *int8) (int64, error)

	CreatePayment(ctx context.Context, payment *model.Payment) error
	UpdatePayment(ctx context.Context, payment *model.Payment) error
	FindPaymentByNo(ctx context.Context, paymentNo string) (*model.Payment, error)
	FindPaymentByOrderID(ctx context.Context, orderID int64) (*model.Payment, error)
}

// orderDAO 订单数据访问实现
type orderDAO struct {
	db *gorm.DB
}

// NewOrderDAO 创建订单数据访问实例
func NewOrderDAO() OrderDAO {
	return &orderDAO{
		db: GetOrderDB(),
	}
}

// getOrderTableName 获取订单表名
func getOrderTableName(userID int64) string {
	return fmt.Sprintf("order_%d", userID%10)
}

// getPaymentTableName 获取支付表名
func getPaymentTableName() string {
	return fmt.Sprintf("payment_%s", time.Now().Format("200601"))
}

// CreateOrder 创建订单
func (d *orderDAO) CreateOrder(ctx context.Context, order *model.Order) error {
	tableName := getOrderTableName(order.UserID)
	return d.db.WithContext(ctx).Table(tableName).Create(order).Error
}

// UpdateOrder 更新订单
func (d *orderDAO) UpdateOrder(ctx context.Context, order *model.Order) error {
	tableName := getOrderTableName(order.UserID)
	return d.db.WithContext(ctx).Table(tableName).Save(order).Error
}

// FindOrderByID 根据ID查找订单
func (d *orderDAO) FindOrderByID(ctx context.Context, id int64) (*model.Order, error) {
	// 由于不知道用户ID，需要遍历所有分表
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("order_%d", i)
		var order model.Order
		if err := d.db.WithContext(ctx).Table(tableName).First(&order, id).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			return nil, err
		}
		return &order, nil
	}
	return nil, nil
}

// FindOrderByNo 根据订单号查找订单
func (d *orderDAO) FindOrderByNo(ctx context.Context, orderNo string) (*model.Order, error) {
	// 由于不知道用户ID，需要遍历所有分表
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("order_%d", i)
		var order model.Order
		if err := d.db.WithContext(ctx).Table(tableName).Where("order_no = ?", orderNo).First(&order).Error; err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				continue
			}
			return nil, err
		}
		return &order, nil
	}
	return nil, nil
}

// ListOrdersByUser 查询用户的订单列表
func (d *orderDAO) ListOrdersByUser(ctx context.Context, userID int64, offset, limit int) ([]*model.Order, error) {
	tableName := getOrderTableName(userID)
	var orders []*model.Order
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("user_id = ?", userID).
		Offset(offset).
		Limit(limit).
		Find(&orders).Error; err != nil {
		return nil, err
	}
	return orders, nil
}

// CountOrdersByUser 统计用户的订单总数
func (d *orderDAO) CountOrdersByUser(ctx context.Context, userID int64) (int64, error) {
	tableName := getOrderTableName(userID)
	var count int64
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("user_id = ?", userID).
		Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// CreatePayment 创建支付记录
func (d *orderDAO) CreatePayment(ctx context.Context, payment *model.Payment) error {
	tableName := getPaymentTableName()
	return d.db.WithContext(ctx).Table(tableName).Create(payment).Error
}

// UpdatePayment 更新支付记录
func (d *orderDAO) UpdatePayment(ctx context.Context, payment *model.Payment) error {
	tableName := getPaymentTableName()
	return d.db.WithContext(ctx).Table(tableName).Save(payment).Error
}

// FindPaymentByNo 根据支付流水号查找支付记录
func (d *orderDAO) FindPaymentByNo(ctx context.Context, paymentNo string) (*model.Payment, error) {
	tableName := getPaymentTableName()
	var payment model.Payment
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("payment_no = ?", paymentNo).
		First(&payment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &payment, nil
}

// FindPaymentByOrderID 根据订单ID查找支付记录
func (d *orderDAO) FindPaymentByOrderID(ctx context.Context, orderID int64) (*model.Payment, error) {
	tableName := getPaymentTableName()
	var payment model.Payment
	if err := d.db.WithContext(ctx).
		Table(tableName).
		Where("order_id = ?", orderID).
		First(&payment).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &payment, nil
}

// ListAllOrders 查询所有订单列表（管理员功能）
func (d *orderDAO) ListAllOrders(ctx context.Context, offset, limit int, status *int8) ([]*model.Order, error) {
	var allOrders []*model.Order

	// 遍历所有分表
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("order_%d", i)
		var orders []*model.Order

		query := d.db.WithContext(ctx).Table(tableName)
		if status != nil {
			query = query.Where("status = ?", *status)
		}

		if err := query.Find(&orders).Error; err != nil {
			return nil, err
		}

		allOrders = append(allOrders, orders...)
	}

	// 手动分页（由于跨表查询，需要在内存中处理）
	total := len(allOrders)
	start := offset
	end := offset + limit

	if start >= total {
		return []*model.Order{}, nil
	}
	if end > total {
		end = total
	}

	return allOrders[start:end], nil
}

// CountAllOrders 统计所有订单总数（管理员功能）
func (d *orderDAO) CountAllOrders(ctx context.Context, status *int8) (int64, error) {
	var totalCount int64

	// 遍历所有分表
	for i := 0; i < 10; i++ {
		tableName := fmt.Sprintf("order_%d", i)
		var count int64

		query := d.db.WithContext(ctx).Table(tableName)
		if status != nil {
			query = query.Where("status = ?", *status)
		}

		if err := query.Count(&count).Error; err != nil {
			return 0, err
		}

		totalCount += count
	}

	return totalCount, nil
}
