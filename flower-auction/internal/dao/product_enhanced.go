package dao

import (
	"context"
	"errors"
	"fmt"
	"strings"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// ProductEnhancedDAO 商品增强功能数据访问接口
type ProductEnhancedDAO interface {
	// 商品图片管理
	CreateProductImage(ctx context.Context, image *model.ProductImage) error
	UpdateProductImage(ctx context.Context, image *model.ProductImage) error
	DeleteProductImage(ctx context.Context, id int64) error
	FindProductImages(ctx context.Context, productID int64, imageType int8) ([]*model.ProductImage, error)
	DeleteProductImages(ctx context.Context, productID int64) error

	// 商品规格管理
	CreateProductSpec(ctx context.Context, spec *model.ProductSpec) error
	UpdateProductSpec(ctx context.Context, spec *model.ProductSpec) error
	DeleteProductSpec(ctx context.Context, id int64) error
	FindProductSpecs(ctx context.Context, productID int64) ([]*model.ProductSpec, error)
	FindProductSpecByID(ctx context.Context, id int64) (*model.ProductSpec, error)
	DeleteProductSpecs(ctx context.Context, productID int64) error

	// 商品库存管理
	CreateProductInventory(ctx context.Context, inventory *model.ProductInventory) error
	UpdateProductInventory(ctx context.Context, inventory *model.ProductInventory) error
	FindProductInventory(ctx context.Context, productID int64, specID *int64) (*model.ProductInventory, error)
	UpdateStock(ctx context.Context, productID int64, specID *int64, quantity int, operation string) error
	CheckStock(ctx context.Context, productID int64, specID *int64, quantity int) (bool, error)

	// 商品审核管理
	CreateProductAudit(ctx context.Context, audit *model.ProductAudit) error
	UpdateProductAudit(ctx context.Context, audit *model.ProductAudit) error
	FindProductAudit(ctx context.Context, productID int64) (*model.ProductAudit, error)
	ListPendingAudits(ctx context.Context, offset, limit int) ([]*model.ProductAudit, error)
	CountPendingAudits(ctx context.Context) (int64, error)

	// 商品详细信息
	FindProductWithDetails(ctx context.Context, productID int64) (*model.ProductWithDetails, error)

	// 商品搜索
	SearchProducts(ctx context.Context, query *model.ProductSearchQuery) ([]*model.Product, int64, error)
}

// productEnhancedDAO 商品增强功能数据访问实现
type productEnhancedDAO struct {
	db *gorm.DB
}

// NewProductEnhancedDAO 创建商品增强功能数据访问实例
func NewProductEnhancedDAO() ProductEnhancedDAO {
	return &productEnhancedDAO{
		db: GetProductDB(),
	}
}

// CreateProductImage 创建商品图片
func (d *productEnhancedDAO) CreateProductImage(ctx context.Context, image *model.ProductImage) error {
	return d.db.WithContext(ctx).Create(image).Error
}

// UpdateProductImage 更新商品图片
func (d *productEnhancedDAO) UpdateProductImage(ctx context.Context, image *model.ProductImage) error {
	return d.db.WithContext(ctx).Save(image).Error
}

// DeleteProductImage 删除商品图片
func (d *productEnhancedDAO) DeleteProductImage(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ProductImage{}, id).Error
}

// FindProductImages 查找商品图片
func (d *productEnhancedDAO) FindProductImages(ctx context.Context, productID int64, imageType int8) ([]*model.ProductImage, error) {
	var images []*model.ProductImage
	query := d.db.WithContext(ctx).Where("product_id = ?", productID)
	if imageType >= 0 {
		query = query.Where("image_type = ?", imageType)
	}
	err := query.Order("sort_order ASC, created_at ASC").Find(&images).Error
	return images, err
}

// DeleteProductImages 删除商品所有图片
func (d *productEnhancedDAO) DeleteProductImages(ctx context.Context, productID int64) error {
	return d.db.WithContext(ctx).Where("product_id = ?", productID).Delete(&model.ProductImage{}).Error
}

// CreateProductSpec 创建商品规格
func (d *productEnhancedDAO) CreateProductSpec(ctx context.Context, spec *model.ProductSpec) error {
	return d.db.WithContext(ctx).Create(spec).Error
}

// UpdateProductSpec 更新商品规格
func (d *productEnhancedDAO) UpdateProductSpec(ctx context.Context, spec *model.ProductSpec) error {
	return d.db.WithContext(ctx).Save(spec).Error
}

// DeleteProductSpec 删除商品规格
func (d *productEnhancedDAO) DeleteProductSpec(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ProductSpec{}, id).Error
}

// FindProductSpecs 查找商品规格
func (d *productEnhancedDAO) FindProductSpecs(ctx context.Context, productID int64) ([]*model.ProductSpec, error) {
	var specs []*model.ProductSpec
	err := d.db.WithContext(ctx).Where("product_id = ? AND status = 1", productID).Order("created_at ASC").Find(&specs).Error
	return specs, err
}

// FindProductSpecByID 根据ID查找商品规格
func (d *productEnhancedDAO) FindProductSpecByID(ctx context.Context, id int64) (*model.ProductSpec, error) {
	var spec model.ProductSpec
	err := d.db.WithContext(ctx).First(&spec, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &spec, nil
}

// DeleteProductSpecs 删除商品所有规格
func (d *productEnhancedDAO) DeleteProductSpecs(ctx context.Context, productID int64) error {
	return d.db.WithContext(ctx).Where("product_id = ?", productID).Delete(&model.ProductSpec{}).Error
}

// CreateProductInventory 创建商品库存
func (d *productEnhancedDAO) CreateProductInventory(ctx context.Context, inventory *model.ProductInventory) error {
	return d.db.WithContext(ctx).Create(inventory).Error
}

// UpdateProductInventory 更新商品库存
func (d *productEnhancedDAO) UpdateProductInventory(ctx context.Context, inventory *model.ProductInventory) error {
	return d.db.WithContext(ctx).Save(inventory).Error
}

// FindProductInventory 查找商品库存
func (d *productEnhancedDAO) FindProductInventory(ctx context.Context, productID int64, specID *int64) (*model.ProductInventory, error) {
	var inventory model.ProductInventory
	query := d.db.WithContext(ctx).Where("product_id = ?", productID)
	if specID != nil {
		query = query.Where("spec_id = ?", *specID)
	} else {
		query = query.Where("spec_id IS NULL")
	}
	
	err := query.First(&inventory).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &inventory, nil
}

// UpdateStock 更新库存
func (d *productEnhancedDAO) UpdateStock(ctx context.Context, productID int64, specID *int64, quantity int, operation string) error {
	return d.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 查找库存记录
		var inventory model.ProductInventory
		query := tx.Where("product_id = ?", productID)
		if specID != nil {
			query = query.Where("spec_id = ?", *specID)
		} else {
			query = query.Where("spec_id IS NULL")
		}
		
		err := query.First(&inventory).Error
		if err != nil {
			return err
		}

		// 根据操作类型更新库存
		switch operation {
		case "add":
			inventory.TotalStock += quantity
			inventory.AvailableStock += quantity
		case "reserve":
			if inventory.AvailableStock < quantity {
				return errors.New("库存不足")
			}
			inventory.AvailableStock -= quantity
			inventory.ReservedStock += quantity
		case "release":
			inventory.ReservedStock -= quantity
			inventory.AvailableStock += quantity
		case "sell":
			if inventory.ReservedStock < quantity {
				return errors.New("预留库存不足")
			}
			inventory.ReservedStock -= quantity
			inventory.SoldStock += quantity
		case "return":
			inventory.SoldStock -= quantity
			inventory.AvailableStock += quantity
		default:
			return errors.New("无效的操作类型")
		}

		return tx.Save(&inventory).Error
	})
}

// CheckStock 检查库存
func (d *productEnhancedDAO) CheckStock(ctx context.Context, productID int64, specID *int64, quantity int) (bool, error) {
	inventory, err := d.FindProductInventory(ctx, productID, specID)
	if err != nil {
		return false, err
	}
	if inventory == nil {
		return false, nil
	}
	return inventory.AvailableStock >= quantity, nil
}

// CreateProductAudit 创建商品审核
func (d *productEnhancedDAO) CreateProductAudit(ctx context.Context, audit *model.ProductAudit) error {
	return d.db.WithContext(ctx).Create(audit).Error
}

// UpdateProductAudit 更新商品审核
func (d *productEnhancedDAO) UpdateProductAudit(ctx context.Context, audit *model.ProductAudit) error {
	return d.db.WithContext(ctx).Save(audit).Error
}

// FindProductAudit 查找商品审核
func (d *productEnhancedDAO) FindProductAudit(ctx context.Context, productID int64) (*model.ProductAudit, error) {
	var audit model.ProductAudit
	err := d.db.WithContext(ctx).Where("product_id = ?", productID).Order("created_at DESC").First(&audit).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &audit, nil
}

// ListPendingAudits 查询待审核商品列表
func (d *productEnhancedDAO) ListPendingAudits(ctx context.Context, offset, limit int) ([]*model.ProductAudit, error) {
	var audits []*model.ProductAudit
	err := d.db.WithContext(ctx).Where("status = 0").Offset(offset).Limit(limit).Order("created_at ASC").Find(&audits).Error
	return audits, err
}

// CountPendingAudits 统计待审核商品数量
func (d *productEnhancedDAO) CountPendingAudits(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ProductAudit{}).Where("status = 0").Count(&count).Error
	return count, err
}

// FindProductWithDetails 查找商品详细信息
func (d *productEnhancedDAO) FindProductWithDetails(ctx context.Context, productID int64) (*model.ProductWithDetails, error) {
	// 查找商品基本信息
	productDAO := NewProductDAO()
	product, err := productDAO.FindByID(ctx, productID)
	if err != nil {
		return nil, err
	}
	if product == nil {
		return nil, nil
	}

	result := &model.ProductWithDetails{
		Product: *product,
	}

	// 查找分类信息
	if product.CategoryID > 0 {
		category, err := productDAO.FindCategoryByID(ctx, product.CategoryID)
		if err == nil && category != nil {
			result.Category = category
		}
	}

	// 查找图片
	images, err := d.FindProductImages(ctx, productID, -1)
	if err == nil && len(images) > 0 {
		result.Images = make([]model.ProductImage, len(images))
		for i, img := range images {
			result.Images[i] = *img
		}
	}

	// 查找规格
	specs, err := d.FindProductSpecs(ctx, productID)
	if err == nil && len(specs) > 0 {
		result.Specs = make([]model.ProductSpec, len(specs))
		for i, spec := range specs {
			result.Specs[i] = *spec
		}
	}

	// 查找库存
	inventory, err := d.FindProductInventory(ctx, productID, nil)
	if err == nil && inventory != nil {
		result.Inventory = inventory
	}

	// 查找审核信息
	audit, err := d.FindProductAudit(ctx, productID)
	if err == nil && audit != nil {
		result.Audit = audit
	}

	return result, nil
}

// SearchProducts 搜索商品
func (d *productEnhancedDAO) SearchProducts(ctx context.Context, query *model.ProductSearchQuery) ([]*model.Product, int64, error) {
	var products []*model.Product
	
	// 构建查询
	db := d.db.WithContext(ctx).Model(&model.Product{})
	
	// 关键词搜索
	if query.Keyword != "" {
		keyword := "%" + query.Keyword + "%"
		db = db.Where("name LIKE ? OR description LIKE ?", keyword, keyword)
	}
	
	// 分类筛选
	if query.CategoryID > 0 {
		db = db.Where("category_id = ?", query.CategoryID)
	}
	
	// 价格范围（这里需要关联规格表）
	if query.MinPrice > 0 || query.MaxPrice > 0 {
		subQuery := d.db.Model(&model.ProductSpec{}).Select("product_id")
		if query.MinPrice > 0 {
			subQuery = subQuery.Where("price >= ?", query.MinPrice)
		}
		if query.MaxPrice > 0 {
			subQuery = subQuery.Where("price <= ?", query.MaxPrice)
		}
		db = db.Where("id IN (?)", subQuery)
	}
	
	// 状态筛选
	if query.Status >= 0 {
		db = db.Where("status = ?", query.Status)
	}
	
	// 供应商筛选
	if query.SupplierID > 0 {
		db = db.Where("supplier_id = ?", query.SupplierID)
	}
	
	// 时间范围
	if !query.StartTime.IsZero() {
		db = db.Where("created_at >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("created_at <= ?", query.EndTime)
	}
	
	// 统计总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, 0, err
	}
	
	// 排序
	orderBy := "created_at DESC"
	if query.SortBy != "" {
		direction := "ASC"
		if strings.ToUpper(query.SortOrder) == "DESC" {
			direction = "DESC"
		}
		orderBy = fmt.Sprintf("%s %s", query.SortBy, direction)
	}
	
	// 分页查询
	offset := (query.Page - 1) * query.Size
	err := db.Order(orderBy).Offset(offset).Limit(query.Size).Find(&products).Error
	
	return products, total, err
}
