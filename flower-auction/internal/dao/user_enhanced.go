package dao

import (
	"context"
	"errors"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// UserEnhancedDAO 用户增强功能数据访问接口
type UserEnhancedDAO interface {
	// 实名认证
	CreateVerification(ctx context.Context, verification *model.UserVerification) error
	UpdateVerification(ctx context.Context, verification *model.UserVerification) error
	FindVerificationByUserID(ctx context.Context, userID int64) (*model.UserVerification, error)
	ListVerifications(ctx context.Context, status int8, offset, limit int) ([]*model.UserVerification, error)
	CountVerifications(ctx context.Context, status int8) (int64, error)

	// 子账号管理
	CreateSubAccount(ctx context.Context, subAccount *model.SubAccount) error
	UpdateSubAccount(ctx context.Context, subAccount *model.SubAccount) error
	DeleteSubAccount(ctx context.Context, id int64) error
	FindSubAccountByID(ctx context.Context, id int64) (*model.SubAccount, error)
	FindSubAccountByUsername(ctx context.Context, username string) (*model.SubAccount, error)
	ListSubAccountsByParent(ctx context.Context, parentID int64) ([]*model.SubAccount, error)
	CountSubAccountsByParent(ctx context.Context, parentID int64) (int64, error)

	// 用户档案
	CreateProfile(ctx context.Context, profile *model.UserProfile) error
	UpdateProfile(ctx context.Context, profile *model.UserProfile) error
	FindProfileByUserID(ctx context.Context, userID int64) (*model.UserProfile, error)

	// 用户完整信息
	FindUserWithProfile(ctx context.Context, userID int64) (*model.UserWithProfile, error)
}

// userEnhancedDAO 用户增强功能数据访问实现
type userEnhancedDAO struct {
	db *gorm.DB
}

// NewUserEnhancedDAO 创建用户增强功能数据访问实例
func NewUserEnhancedDAO() UserEnhancedDAO {
	return &userEnhancedDAO{
		db: GetUserDB(),
	}
}

// CreateVerification 创建实名认证
func (d *userEnhancedDAO) CreateVerification(ctx context.Context, verification *model.UserVerification) error {
	return d.db.WithContext(ctx).Create(verification).Error
}

// UpdateVerification 更新实名认证
func (d *userEnhancedDAO) UpdateVerification(ctx context.Context, verification *model.UserVerification) error {
	return d.db.WithContext(ctx).Save(verification).Error
}

// FindVerificationByUserID 根据用户ID查找实名认证
func (d *userEnhancedDAO) FindVerificationByUserID(ctx context.Context, userID int64) (*model.UserVerification, error) {
	var verification model.UserVerification
	err := d.db.WithContext(ctx).Where("user_id = ?", userID).First(&verification).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &verification, nil
}

// ListVerifications 查询实名认证列表
func (d *userEnhancedDAO) ListVerifications(ctx context.Context, status int8, offset, limit int) ([]*model.UserVerification, error) {
	var verifications []*model.UserVerification
	query := d.db.WithContext(ctx)
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Offset(offset).Limit(limit).Order("created_at DESC").Find(&verifications).Error
	return verifications, err
}

// CountVerifications 统计实名认证数量
func (d *userEnhancedDAO) CountVerifications(ctx context.Context, status int8) (int64, error) {
	var count int64
	query := d.db.WithContext(ctx).Model(&model.UserVerification{})
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Count(&count).Error
	return count, err
}

// CreateSubAccount 创建子账号
func (d *userEnhancedDAO) CreateSubAccount(ctx context.Context, subAccount *model.SubAccount) error {
	return d.db.WithContext(ctx).Create(subAccount).Error
}

// UpdateSubAccount 更新子账号
func (d *userEnhancedDAO) UpdateSubAccount(ctx context.Context, subAccount *model.SubAccount) error {
	return d.db.WithContext(ctx).Save(subAccount).Error
}

// DeleteSubAccount 删除子账号
func (d *userEnhancedDAO) DeleteSubAccount(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.SubAccount{}, id).Error
}

// FindSubAccountByID 根据ID查找子账号
func (d *userEnhancedDAO) FindSubAccountByID(ctx context.Context, id int64) (*model.SubAccount, error) {
	var subAccount model.SubAccount
	err := d.db.WithContext(ctx).First(&subAccount, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &subAccount, nil
}

// FindSubAccountByUsername 根据用户名查找子账号
func (d *userEnhancedDAO) FindSubAccountByUsername(ctx context.Context, username string) (*model.SubAccount, error) {
	var subAccount model.SubAccount
	err := d.db.WithContext(ctx).Where("username = ?", username).First(&subAccount).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &subAccount, nil
}

// ListSubAccountsByParent 根据主账号查询子账号列表
func (d *userEnhancedDAO) ListSubAccountsByParent(ctx context.Context, parentID int64) ([]*model.SubAccount, error) {
	var subAccounts []*model.SubAccount
	err := d.db.WithContext(ctx).Where("parent_id = ?", parentID).Order("created_at DESC").Find(&subAccounts).Error
	return subAccounts, err
}

// CountSubAccountsByParent 统计主账号的子账号数量
func (d *userEnhancedDAO) CountSubAccountsByParent(ctx context.Context, parentID int64) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.SubAccount{}).Where("parent_id = ?", parentID).Count(&count).Error
	return count, err
}

// CreateProfile 创建用户档案
func (d *userEnhancedDAO) CreateProfile(ctx context.Context, profile *model.UserProfile) error {
	return d.db.WithContext(ctx).Create(profile).Error
}

// UpdateProfile 更新用户档案
func (d *userEnhancedDAO) UpdateProfile(ctx context.Context, profile *model.UserProfile) error {
	return d.db.WithContext(ctx).Save(profile).Error
}

// FindProfileByUserID 根据用户ID查找用户档案
func (d *userEnhancedDAO) FindProfileByUserID(ctx context.Context, userID int64) (*model.UserProfile, error) {
	var profile model.UserProfile
	err := d.db.WithContext(ctx).Where("user_id = ?", userID).First(&profile).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &profile, nil
}

// FindUserWithProfile 查找用户完整信息
func (d *userEnhancedDAO) FindUserWithProfile(ctx context.Context, userID int64) (*model.UserWithProfile, error) {
	// 查找用户基本信息
	userDAO := NewUserDAO()
	user, err := userDAO.FindByID(ctx, userID)
	if err != nil {
		return nil, err
	}
	if user == nil {
		return nil, nil
	}

	result := &model.UserWithProfile{
		User: *user,
	}

	// 查找用户档案
	profile, err := d.FindProfileByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	result.Profile = profile

	// 查找实名认证信息
	verification, err := d.FindVerificationByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	result.Verification = verification

	// 查找子账号列表
	subAccounts, err := d.ListSubAccountsByParent(ctx, userID)
	if err != nil {
		return nil, err
	}
	if len(subAccounts) > 0 {
		result.SubAccounts = make([]model.SubAccount, len(subAccounts))
		for i, sub := range subAccounts {
			result.SubAccounts[i] = *sub
		}
	}

	return result, nil
}
