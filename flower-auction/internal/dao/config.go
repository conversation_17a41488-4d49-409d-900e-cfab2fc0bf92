package dao

// DBConfig 数据库配置
type DBConfig struct {
	Host         string `yaml:"host"`
	Port         int    `yaml:"port"`
	User         string `yaml:"user"`
	Password     string `yaml:"password"`
	Database     string `yaml:"database"`
	MaxIdleConns int    `yaml:"maxIdleConns"`
	MaxOpenConns int    `yaml:"maxOpenConns"`
}

// Config 数据库配置集合
type Config struct {
	UserDB    DBConfig `yaml:"userDB"`
	ProductDB DBConfig `yaml:"productDB"`
	AuctionDB DBConfig `yaml:"auctionDB"`
	OrderDB   DBConfig `yaml:"orderDB"`
}
