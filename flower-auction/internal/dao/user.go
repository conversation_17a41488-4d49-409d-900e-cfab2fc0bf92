package dao

import (
	"context"
	"errors"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// UserDAO 用户数据访问接口
type UserDAO interface {
	Create(ctx context.Context, user *model.User) error
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*model.User, error)
	FindByUsername(ctx context.Context, username string) (*model.User, error)
	FindByPhone(ctx context.Context, phone string) (*model.User, error)
	FindWithRoles(ctx context.Context, id int64) (*model.UserWithRoles, error)
	List(ctx context.Context, offset, limit int) ([]*model.User, error)
	Count(ctx context.Context) (int64, error)
}

// userDAO 用户数据访问实现
type userDAO struct {
	db *gorm.DB
}

// NewUserDAO 创建用户数据访问实例
func NewUserDAO() UserDAO {
	return &userDAO{
		db: GetUserDB(),
	}
}

// Create 创建用户
func (d *userDAO) Create(ctx context.Context, user *model.User) error {
	return d.db.WithContext(ctx).Create(user).Error
}

// Update 更新用户
func (d *userDAO) Update(ctx context.Context, user *model.User) error {
	return d.db.WithContext(ctx).Save(user).Error
}

// Delete 删除用户
func (d *userDAO) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.User{}, id).Error
}

// FindByID 根据ID查找用户
func (d *userDAO) FindByID(ctx context.Context, id int64) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByUsername 根据用户名查找用户
func (d *userDAO) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByPhone 根据手机号查找用户
func (d *userDAO) FindByPhone(ctx context.Context, phone string) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).Where("phone = ?", phone).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindWithRoles 查找用户及其角色
func (d *userDAO) FindWithRoles(ctx context.Context, id int64) (*model.UserWithRoles, error) {
	var user model.User
	if err := d.db.WithContext(ctx).First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	var roles []model.Role
	if err := d.db.WithContext(ctx).
		Joins("JOIN user_role ON user_role.role_id = role.id").
		Where("user_role.user_id = ?", id).
		Find(&roles).Error; err != nil {
		return nil, err
	}

	return &model.UserWithRoles{
		User:  user,
		Roles: roles,
	}, nil
}

// List 查询用户列表
func (d *userDAO) List(ctx context.Context, offset, limit int) ([]*model.User, error) {
	var users []*model.User
	if err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

// Count 统计用户总数
func (d *userDAO) Count(ctx context.Context) (int64, error) {
	var count int64
	if err := d.db.WithContext(ctx).Model(&model.User{}).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
