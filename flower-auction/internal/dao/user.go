package dao

import (
	"context"
	"errors"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// UserDAO 用户数据访问接口
type UserDAO interface {
	Create(ctx context.Context, user *model.User) error
	Update(ctx context.Context, user *model.User) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (*model.User, error)
	FindByUsername(ctx context.Context, username string) (*model.User, error)
	FindByPhone(ctx context.Context, phone string) (*model.User, error)
	FindWithRoles(ctx context.Context, id int64) (*model.UserWithRoles, error)
	List(ctx context.Context, params *UserListParams, offset, limit int) ([]*model.User, error)
	Count(ctx context.Context, params *UserListParams) (int64, error)
}

// userDAO 用户数据访问实现
type userDAO struct {
	db *gorm.DB
}

// NewUserDAO 创建用户数据访问实例
func NewUserDAO() UserDAO {
	return &userDAO{
		db: GetUserDB(),
	}
}

// Create 创建用户
func (d *userDAO) Create(ctx context.Context, user *model.User) error {
	return d.db.WithContext(ctx).Create(user).Error
}

// Update 更新用户
func (d *userDAO) Update(ctx context.Context, user *model.User) error {
	return d.db.WithContext(ctx).Save(user).Error
}

// Delete 删除用户
func (d *userDAO) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.User{}, id).Error
}

// FindByID 根据ID查找用户
func (d *userDAO) FindByID(ctx context.Context, id int64) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByUsername 根据用户名查找用户
func (d *userDAO) FindByUsername(ctx context.Context, username string) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindByPhone 根据手机号查找用户
func (d *userDAO) FindByPhone(ctx context.Context, phone string) (*model.User, error) {
	var user model.User
	if err := d.db.WithContext(ctx).Where("phone = ?", phone).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

// FindWithRoles 查找用户及其角色
func (d *userDAO) FindWithRoles(ctx context.Context, id int64) (*model.UserWithRoles, error) {
	var user model.User
	if err := d.db.WithContext(ctx).First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	var roles []model.Role
	if err := d.db.WithContext(ctx).
		Joins("JOIN user_role ON user_role.role_id = role.id").
		Where("user_role.user_id = ?", id).
		Find(&roles).Error; err != nil {
		return nil, err
	}

	return &model.UserWithRoles{
		User:  user,
		Roles: roles,
	}, nil
}

// UserListParams 用户列表查询参数
type UserListParams struct {
	Username  string `json:"username"`
	RealName  string `json:"realName"`
	Phone     string `json:"phone"`
	UserType  int    `json:"userType"`
	Status    *int   `json:"status"`    // 使用指针，nil表示不过滤状态
	HasStatus bool   `json:"hasStatus"` // 标记是否有状态过滤条件
}

// List 查询用户列表
func (d *userDAO) List(ctx context.Context, params *UserListParams, offset, limit int) ([]*model.User, error) {
	var users []*model.User
	query := d.db.WithContext(ctx)

	// 添加搜索条件
	if params != nil {
		if params.Username != "" {
			query = query.Where("username LIKE ?", "%"+params.Username+"%")
		}
		if params.RealName != "" {
			query = query.Where("real_name LIKE ?", "%"+params.RealName+"%")
		}
		if params.Phone != "" {
			query = query.Where("phone LIKE ?", "%"+params.Phone+"%")
		}
		if params.UserType > 0 {
			query = query.Where("user_type = ?", params.UserType)
		}
		if params.HasStatus && params.Status != nil {
			query = query.Where("status = ?", *params.Status)
		}
	}

	if err := query.Offset(offset).Limit(limit).Find(&users).Error; err != nil {
		return nil, err
	}
	return users, nil
}

// Count 统计用户总数
func (d *userDAO) Count(ctx context.Context, params *UserListParams) (int64, error) {
	var count int64
	query := d.db.WithContext(ctx).Model(&model.User{})

	// 添加搜索条件
	if params != nil {
		if params.Username != "" {
			query = query.Where("username LIKE ?", "%"+params.Username+"%")
		}
		if params.RealName != "" {
			query = query.Where("real_name LIKE ?", "%"+params.RealName+"%")
		}
		if params.Phone != "" {
			query = query.Where("phone LIKE ?", "%"+params.Phone+"%")
		}
		if params.UserType > 0 {
			query = query.Where("user_type = ?", params.UserType)
		}
		if params.HasStatus && params.Status != nil {
			query = query.Where("status = ?", *params.Status)
		}
	}

	if err := query.Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}
