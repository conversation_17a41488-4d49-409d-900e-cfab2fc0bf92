package dao

import (
	"context"
	"errors"

	"github.com/putonghao/flower-auction/internal/model"
	"gorm.io/gorm"
)

// QualityDAO 质检数据访问接口
type QualityDAO interface {
	// 批次管理
	CreateBatch(ctx context.Context, batch *model.Batch) error
	UpdateBatch(ctx context.Context, batch *model.Batch) error
	FindBatchByID(ctx context.Context, id int64) (*model.Batch, error)
	FindBatchByNo(ctx context.Context, batchNo string) (*model.Batch, error)
	FindBatchWithQualityByID(ctx context.Context, id int64) (*model.BatchWithQuality, error)
	FindBatchWithQualityByNo(ctx context.Context, batchNo string) (*model.BatchWithQuality, error)
	ListBatchesWithQuality(ctx context.Context, status int8, offset, limit int) ([]*model.BatchWithQuality, error)
	CountBatches(ctx context.Context, status int8) (int64, error)

	// 质检管理
	CreateQualityCheck(ctx context.Context, check *model.QualityCheck) error
	UpdateQualityCheck(ctx context.Context, check *model.QualityCheck) error
	FindQualityCheckByID(ctx context.Context, id int64) (*model.QualityCheck, error)
	FindQualityCheckByBatch(ctx context.Context, batchID int64) (*model.QualityCheck, error)
	FindQualityCheckDetailByID(ctx context.Context, id int64) (*model.QualityCheckDetail, error)
	ListQualityCheckDetails(ctx context.Context, status int8, offset, limit int) ([]*model.QualityCheckDetail, error)
	CountQualityChecks(ctx context.Context, status int8) (int64, error)

	// 瑕疵管理
	CreateQualityDefect(ctx context.Context, defect *model.QualityDefect) error
	ListQualityDefectsByCheck(ctx context.Context, checkID int64) ([]*model.QualityDefect, error)

	// 质检标准管理
	CreateQualityStandard(ctx context.Context, standard *model.QualityStandard) error
	UpdateQualityStandard(ctx context.Context, standard *model.QualityStandard) error
	FindQualityStandardByID(ctx context.Context, id int64) (*model.QualityStandard, error)
	FindQualityStandardByCategoryAndLevel(ctx context.Context, categoryID int64, level int8) (*model.QualityStandard, error)
	ListQualityStandardsByCategory(ctx context.Context, categoryID int64) ([]*model.QualityStandard, error)
}

// qualityDAO 质检数据访问实现
type qualityDAO struct {
	db *gorm.DB
}

// NewQualityDAO 创建质检数据访问实例
func NewQualityDAO() QualityDAO {
	return &qualityDAO{
		db: GetProductDB(), // 质检数据存储在商品数据库中
	}
}

// CreateBatch 创建批次
func (d *qualityDAO) CreateBatch(ctx context.Context, batch *model.Batch) error {
	return d.db.WithContext(ctx).Create(batch).Error
}

// UpdateBatch 更新批次
func (d *qualityDAO) UpdateBatch(ctx context.Context, batch *model.Batch) error {
	return d.db.WithContext(ctx).Save(batch).Error
}

// FindBatchByID 根据ID查找批次
func (d *qualityDAO) FindBatchByID(ctx context.Context, id int64) (*model.Batch, error) {
	var batch model.Batch
	err := d.db.WithContext(ctx).First(&batch, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &batch, nil
}

// FindBatchByNo 根据批次号查找批次
func (d *qualityDAO) FindBatchByNo(ctx context.Context, batchNo string) (*model.Batch, error) {
	var batch model.Batch
	err := d.db.WithContext(ctx).Where("batch_no = ?", batchNo).First(&batch).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &batch, nil
}

// FindBatchWithQualityByID 根据ID查找批次及质检信息
func (d *qualityDAO) FindBatchWithQualityByID(ctx context.Context, id int64) (*model.BatchWithQuality, error) {
	var batch model.Batch
	err := d.db.WithContext(ctx).First(&batch, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	// 查询商品信息
	var product model.Product
	d.db.WithContext(ctx).First(&product, batch.ProductID)

	// 查询质检信息
	var qualityCheck model.QualityCheck
	d.db.WithContext(ctx).Where("batch_id = ?", batch.ID).First(&qualityCheck)

	return &model.BatchWithQuality{
		Batch:        batch,
		Product:      product,
		QualityCheck: qualityCheck,
	}, nil
}

// FindBatchWithQualityByNo 根据批次号查找批次及质检信息
func (d *qualityDAO) FindBatchWithQualityByNo(ctx context.Context, batchNo string) (*model.BatchWithQuality, error) {
	var batch model.Batch
	err := d.db.WithContext(ctx).Where("batch_no = ?", batchNo).First(&batch).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	// 查询商品信息
	var product model.Product
	d.db.WithContext(ctx).First(&product, batch.ProductID)

	// 查询质检信息
	var qualityCheck model.QualityCheck
	d.db.WithContext(ctx).Where("batch_id = ?", batch.ID).First(&qualityCheck)

	return &model.BatchWithQuality{
		Batch:        batch,
		Product:      product,
		QualityCheck: qualityCheck,
	}, nil
}

// ListBatchesWithQuality 查询批次列表及质检信息
func (d *qualityDAO) ListBatchesWithQuality(ctx context.Context, status int8, offset, limit int) ([]*model.BatchWithQuality, error) {
	var batches []*model.Batch
	query := d.db.WithContext(ctx)
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Offset(offset).Limit(limit).Find(&batches).Error
	if err != nil {
		return nil, err
	}

	var result []*model.BatchWithQuality
	for _, batch := range batches {
		// 查询商品信息
		var product model.Product
		d.db.WithContext(ctx).First(&product, batch.ProductID)

		// 查询质检信息
		var qualityCheck model.QualityCheck
		d.db.WithContext(ctx).Where("batch_id = ?", batch.ID).First(&qualityCheck)

		result = append(result, &model.BatchWithQuality{
			Batch:        *batch,
			Product:      product,
			QualityCheck: qualityCheck,
		})
	}

	return result, nil
}

// CountBatches 统计批次数量
func (d *qualityDAO) CountBatches(ctx context.Context, status int8) (int64, error) {
	var count int64
	query := d.db.WithContext(ctx).Model(&model.Batch{})
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Count(&count).Error
	return count, err
}

// CreateQualityCheck 创建质检记录
func (d *qualityDAO) CreateQualityCheck(ctx context.Context, check *model.QualityCheck) error {
	return d.db.WithContext(ctx).Create(check).Error
}

// UpdateQualityCheck 更新质检记录
func (d *qualityDAO) UpdateQualityCheck(ctx context.Context, check *model.QualityCheck) error {
	return d.db.WithContext(ctx).Save(check).Error
}

// FindQualityCheckByID 根据ID查找质检记录
func (d *qualityDAO) FindQualityCheckByID(ctx context.Context, id int64) (*model.QualityCheck, error) {
	var check model.QualityCheck
	err := d.db.WithContext(ctx).First(&check, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &check, nil
}

// FindQualityCheckByBatch 根据批次ID查找质检记录
func (d *qualityDAO) FindQualityCheckByBatch(ctx context.Context, batchID int64) (*model.QualityCheck, error) {
	var check model.QualityCheck
	err := d.db.WithContext(ctx).Where("batch_id = ?", batchID).First(&check).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &check, nil
}

// FindQualityCheckDetailByID 根据ID查找质检详情
func (d *qualityDAO) FindQualityCheckDetailByID(ctx context.Context, id int64) (*model.QualityCheckDetail, error) {
	var check model.QualityCheck
	err := d.db.WithContext(ctx).First(&check, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}

	// 查询关联信息
	var product model.Product
	var batch model.Batch
	var defects []model.QualityDefect
	var standard model.QualityStandard

	d.db.WithContext(ctx).First(&product, check.ProductID)
	d.db.WithContext(ctx).First(&batch, check.BatchID)
	d.db.WithContext(ctx).Where("check_id = ?", check.ID).Find(&defects)
	d.db.WithContext(ctx).Where("category_id = ? AND level = ?", product.CategoryID, check.QualityLevel).First(&standard)

	return &model.QualityCheckDetail{
		QualityCheck: check,
		Product:      product,
		Batch:        batch,
		Defects:      defects,
		Standard:     standard,
	}, nil
}

// ListQualityCheckDetails 查询质检详情列表
func (d *qualityDAO) ListQualityCheckDetails(ctx context.Context, status int8, offset, limit int) ([]*model.QualityCheckDetail, error) {
	var checks []*model.QualityCheck
	query := d.db.WithContext(ctx)
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Offset(offset).Limit(limit).Find(&checks).Error
	if err != nil {
		return nil, err
	}

	var result []*model.QualityCheckDetail
	for _, check := range checks {
		detail, err := d.FindQualityCheckDetailByID(ctx, check.ID)
		if err != nil {
			continue
		}
		result = append(result, detail)
	}

	return result, nil
}

// CountQualityChecks 统计质检记录数量
func (d *qualityDAO) CountQualityChecks(ctx context.Context, status int8) (int64, error) {
	var count int64
	query := d.db.WithContext(ctx).Model(&model.QualityCheck{})
	if status >= 0 {
		query = query.Where("status = ?", status)
	}
	err := query.Count(&count).Error
	return count, err
}

// CreateQualityDefect 创建瑕疵记录
func (d *qualityDAO) CreateQualityDefect(ctx context.Context, defect *model.QualityDefect) error {
	return d.db.WithContext(ctx).Create(defect).Error
}

// ListQualityDefectsByCheck 根据质检ID查询瑕疵列表
func (d *qualityDAO) ListQualityDefectsByCheck(ctx context.Context, checkID int64) ([]*model.QualityDefect, error) {
	var defects []*model.QualityDefect
	err := d.db.WithContext(ctx).Where("check_id = ?", checkID).Find(&defects).Error
	return defects, err
}

// CreateQualityStandard 创建质检标准
func (d *qualityDAO) CreateQualityStandard(ctx context.Context, standard *model.QualityStandard) error {
	return d.db.WithContext(ctx).Create(standard).Error
}

// UpdateQualityStandard 更新质检标准
func (d *qualityDAO) UpdateQualityStandard(ctx context.Context, standard *model.QualityStandard) error {
	return d.db.WithContext(ctx).Save(standard).Error
}

// FindQualityStandardByID 根据ID查找质检标准
func (d *qualityDAO) FindQualityStandardByID(ctx context.Context, id int64) (*model.QualityStandard, error) {
	var standard model.QualityStandard
	err := d.db.WithContext(ctx).First(&standard, id).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &standard, nil
}

// FindQualityStandardByCategoryAndLevel 根据分类和等级查找质检标准
func (d *qualityDAO) FindQualityStandardByCategoryAndLevel(ctx context.Context, categoryID int64, level int8) (*model.QualityStandard, error) {
	var standard model.QualityStandard
	err := d.db.WithContext(ctx).Where("category_id = ? AND level = ?", categoryID, level).First(&standard).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, nil
		}
		return nil, err
	}
	return &standard, nil
}

// ListQualityStandardsByCategory 根据分类查询质检标准列表
func (d *qualityDAO) ListQualityStandardsByCategory(ctx context.Context, categoryID int64) ([]*model.QualityStandard, error) {
	var standards []*model.QualityStandard
	err := d.db.WithContext(ctx).Where("category_id = ?", categoryID).Order("level ASC").Find(&standards).Error
	return standards, err
}
