#!/bin/bash

# 昆明花卉拍卖系统 API 功能验证脚本
# 测试所有主要功能的API接口

BASE_URL="http://localhost:8081"
API_BASE="$BASE_URL/api/v1"

echo "🌸 昆明花卉拍卖系统 API 功能验证"
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试结果统计
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# 测试函数
test_api() {
    local name="$1"
    local method="$2"
    local url="$3"
    local data="$4"
    local expected_status="$5"
    local use_auth="$6"

    TOTAL_TESTS=$((TOTAL_TESTS + 1))

    echo -n "测试 $name ... "

    # 构建curl命令
    local curl_cmd="curl -s -w \"\\n%{http_code}\" -X \"$method\" \"$url\""

    if [ -n "$data" ]; then
        curl_cmd="$curl_cmd -H \"Content-Type: application/json\" -d '$data'"
    fi

    if [ "$use_auth" = "true" ] && [ -n "$TOKEN" ]; then
        curl_cmd="$curl_cmd -H \"Authorization: Bearer $TOKEN\""
    fi

    # 执行curl命令
    response=$(eval $curl_cmd)

    # 分离响应体和状态码
    status_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | sed '$d')

    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✓ PASS${NC} (HTTP $status_code)"
        PASSED_TESTS=$((PASSED_TESTS + 1))
    else
        echo -e "${RED}✗ FAIL${NC} (HTTP $status_code, expected $expected_status)"
        if [ ${#response_body} -lt 200 ]; then
            echo "  Response: $response_body"
        else
            echo "  Response: ${response_body:0:200}..."
        fi
        FAILED_TESTS=$((FAILED_TESTS + 1))
    fi
}

# 获取认证Token
echo -e "\n${BLUE}1. 认证系统测试${NC}"
echo "=================="

# 登录获取Token
login_response=$(curl -s -X POST "$API_BASE/auth/login" \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123"}')

TOKEN=$(echo "$login_response" | grep -o '"token":"[^"]*"' | cut -d'"' -f4)

if [ -n "$TOKEN" ]; then
    echo -e "${GREEN}✓ 登录成功，获取到Token${NC}"
    AUTH_HEADER="-H \"Authorization: Bearer $TOKEN\""
else
    echo -e "${RED}✗ 登录失败，无法获取Token${NC}"
    exit 1
fi

# 测试认证相关API
test_api "用户登录" "POST" "$API_BASE/auth/login" '{"username":"admin","password":"admin123"}' "200" "false"
test_api "获取用户信息" "GET" "$API_BASE/auth/me" "" "200" "true"

echo -e "\n${BLUE}2. 用户管理测试${NC}"
echo "=================="

# 测试用户管理API
test_api "获取用户列表" "GET" "$API_BASE/users?page=1&pageSize=10" "" "200" "true"
test_api "创建用户" "POST" "$API_BASE/users" '{"username":"testuser3","password":"password123","realName":"测试用户3","phone":"13800138002","email":"<EMAIL>","userType":1}' "200" "true"
test_api "导出用户" "GET" "$API_BASE/users/export?page=1&pageSize=10" "" "200" "true"

echo -e "\n${BLUE}3. 商品管理测试${NC}"
echo "=================="

# 测试商品管理API
test_api "获取商品列表" "GET" "$API_BASE/products?page=1&pageSize=10" "" "200" "true"
test_api "获取商品统计" "GET" "$API_BASE/products/statistics" "" "200" "true"
test_api "获取分类树" "GET" "$API_BASE/categories/tree" "" "200" "true"

echo -e "\n${BLUE}4. 拍卖系统测试${NC}"
echo "=================="

# 测试拍卖系统API
test_api "获取拍卖列表" "GET" "$API_BASE/auctions?page=1&pageSize=10" "" "200" "true"
test_api "获取拍卖统计" "GET" "$API_BASE/auctions/statistics" "" "200" "true"

echo -e "\n${BLUE}5. 订单管理测试${NC}"
echo "=================="

# 测试订单管理API
test_api "获取订单列表" "GET" "$API_BASE/orders?page=1&pageSize=10" "" "200" "true"
test_api "获取订单统计" "GET" "$API_BASE/orders/statistics" "" "200" "true"

echo -e "\n${BLUE}6. 权限管理测试${NC}"
echo "=================="

# 测试权限管理API
test_api "获取权限列表" "GET" "$API_BASE/permissions?page=1&pageSize=10" "" "200" "true"
test_api "获取角色列表" "GET" "$API_BASE/roles" "" "200" "false"

echo -e "\n${BLUE}7. 财务管理测试${NC}"
echo "=================="

# 测试财务管理API
test_api "获取收入统计" "GET" "$API_BASE/finance/statistics/income" "" "200" "true"
test_api "获取日报表" "GET" "$API_BASE/finance/report/daily" "" "200" "true"
test_api "获取月报表" "GET" "$API_BASE/finance/report/monthly" "" "200" "true"

echo -e "\n${BLUE}8. 文件上传测试${NC}"
echo "=================="

# 测试文件上传API（这里只测试接口是否存在）
test_api "文件信息接口" "GET" "$API_BASE/upload/file/info?filename=test.jpg" "" "200" "true"

echo -e "\n${YELLOW}测试结果统计${NC}"
echo "=================="
echo -e "总测试数: ${BLUE}$TOTAL_TESTS${NC}"
echo -e "通过测试: ${GREEN}$PASSED_TESTS${NC}"
echo -e "失败测试: ${RED}$FAILED_TESTS${NC}"

if [ $FAILED_TESTS -eq 0 ]; then
    echo -e "\n${GREEN}🎉 所有测试通过！系统功能正常！${NC}"
    exit 0
else
    echo -e "\n${RED}⚠️  有 $FAILED_TESTS 个测试失败，请检查相关功能${NC}"
    exit 1
fi
