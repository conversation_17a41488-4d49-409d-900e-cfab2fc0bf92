#!/bin/bash

# 昆明花卉拍卖系统 - 启动所有服务

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        log_warn "端口 $port 已被占用，可能 $service 已在运行"
        return 1
    fi
    return 0
}

# 等待服务启动
wait_for_service() {
    local url=$1
    local service=$2
    local max_attempts=30
    local attempt=1
    
    log_info "等待 $service 启动..."
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url/health" > /dev/null 2>&1; then
            log_info "$service 启动成功"
            return 0
        fi
        
        log_debug "尝试 $attempt/$max_attempts: $service 尚未就绪"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    log_error "$service 启动失败或超时"
    return 1
}

# 启动服务
start_service() {
    local service=$1
    local port=$2
    local config=$3
    
    log_info "启动 $service..."
    
    # 检查端口
    if ! check_port $port $service; then
        return 1
    fi
    
    # 创建日志目录
    mkdir -p logs
    
    # 启动服务
    nohup go run cmd/$service/main.go -config=$config > logs/$service.log 2>&1 &
    local pid=$!
    
    # 保存PID
    echo $pid > logs/$service.pid
    
    log_info "$service 已启动，PID: $pid"
    
    # 等待服务就绪
    wait_for_service "http://localhost:$port" "$service"
    
    return $?
}

# 主函数
main() {
    log_info "开始启动昆明花卉拍卖系统所有服务..."
    
    # 检查Go环境
    if ! command -v go &> /dev/null; then
        log_error "Go 环境未安装或未配置"
        exit 1
    fi
    
    # 检查依赖
    log_info "检查依赖..."
    go mod tidy
    
    # 创建必要的目录
    mkdir -p logs configs
    
    # 启动服务（按依赖顺序）
    
    # 1. 认证服务
    if start_service "auth-service" 8081 "configs/auth-service.yaml"; then
        log_info "✓ 认证服务启动成功"
    else
        log_error "✗ 认证服务启动失败"
        exit 1
    fi
    
    # 2. 用户服务
    if start_service "user-service" 8082 "configs/user-service.yaml"; then
        log_info "✓ 用户服务启动成功"
    else
        log_error "✗ 用户服务启动失败"
        exit 1
    fi
    
    # 3. 商品服务
    # if start_service "product-service" 8083 "configs/product-service.yaml"; then
    #     log_info "✓ 商品服务启动成功"
    # else
    #     log_error "✗ 商品服务启动失败"
    #     exit 1
    # fi
    
    # 4. 上传服务
    # if start_service "upload-service" 8084 "configs/upload-service.yaml"; then
    #     log_info "✓ 上传服务启动成功"
    # else
    #     log_error "✗ 上传服务启动失败"
    #     exit 1
    # fi
    
    # 5. API网关
    if start_service "gateway" 8080 "configs/gateway.yaml"; then
        log_info "✓ API网关启动成功"
    else
        log_error "✗ API网关启动失败"
        exit 1
    fi
    
    log_info "所有服务启动完成！"
    log_info ""
    log_info "服务访问地址："
    log_info "  API网关:    http://localhost:8080"
    log_info "  认证服务:   http://localhost:8081"
    log_info "  用户服务:   http://localhost:8082"
    # log_info "  商品服务:   http://localhost:8083"
    # log_info "  上传服务:   http://localhost:8084"
    log_info ""
    log_info "健康检查："
    log_info "  curl http://localhost:8080/health"
    log_info ""
    log_info "查看日志："
    log_info "  tail -f logs/gateway.log"
    log_info "  tail -f logs/auth-service.log"
    log_info "  tail -f logs/user-service.log"
    log_info ""
    log_info "停止所有服务："
    log_info "  ./scripts/stop-all.sh"
}

# 执行主函数
main "$@"
