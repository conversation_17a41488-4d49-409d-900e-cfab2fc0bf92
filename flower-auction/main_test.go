package main

import (
	"fmt"
	"testing"

	"golang.org/x/crypto/bcrypt"
)

func TestMain(t *testing.T) {
	main()
}

func TestGereratePassword(t *testing.T) {
	password := "admin123"

	// 生成bcrypt哈希
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		fmt.Printf("生成密码哈希失败: %v\n", err)
		return
	}

	fmt.Printf("密码: %s\n", password)
	fmt.Printf("哈希: %s\n", string(hashedPassword))

	// 验证哈希是否正确
	err = bcrypt.CompareHashAndPassword(hashedPassword, []byte(password))
	if err == nil {
		fmt.Println("✅ 密码验证成功")
	} else {
		fmt.Printf("❌ 密码验证失败: %v\n", err)
	}
}
