# 昆明花卉拍卖后端web系统

这是一个基于Go语言开发的花卉拍卖系统，支持多种拍卖模式，实现供应商、购买商、拍卖师等多角色协同工作。

## 项目结构

```
flower/
├── configs/                    # 配置文件
│   ├── config.yaml            # 主配置文件
│   └── database.yaml          # 数据库配置
├── docs/                      # 文档目录
│   ├── requirements/          # 需求文档
│   ├── tech_analysis/         # 技术分析文档
│   ├── architecture/          # 架构图
│   └── database/             # 数据库文档
├── internal/                  # 内部代码
│   ├── api/                  # API层
│   ├── service/              # 业务逻辑层
│   ├── dao/                  # 数据访问层
│   └── model/                # 数据模型
├── main.go                   # 程序入口
├── go.mod                    # Go模块文件
└── README.md                 # 项目说明
```

## 功能模块

### 已实现功能

1. **用户管理**
   - 用户注册、登录
   - 用户信息管理
   - 角色权限管理

2. **商品管理**
   - 商品CRUD操作
   - 商品分类管理
   - 分类树结构

3. **质检管理**
   - 批次管理
   - 质检流程
   - 瑕疵记录
   - 质检标准

4. **拍卖管理**
   - 拍卖会管理
   - 拍卖商品管理
   - 竞价功能

### 待实现功能

1. **实时竞价系统**
   - WebSocket支持
   - 实时价格推送
   - 高并发处理

2. **支付系统**
   - 订单管理
   - 支付集成
   - 结算管理

3. **消息队列**
   - RabbitMQ集成
   - 异步处理
   - 事件驱动

4. **缓存系统**
   - Redis集成
   - 数据缓存
   - 会话管理

## 技术栈

- **后端**: Go 1.21, Gin框架
- **数据库**: MySQL 8.0
- **ORM**: GORM
- **配置管理**: Viper
- **日志**: 标准库log
- **API文档**: Swagger注释

## 快速开始

### 环境要求

- Go 1.21+
- MySQL 8.0+
- Redis 6.0+ (可选)
- RabbitMQ 3.8+ (可选)

### 安装依赖

```bash
go mod download
```

### 配置数据库

1. 创建数据库：
```sql
CREATE DATABASE user_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE product_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE auction_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE DATABASE order_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 修改配置文件 `configs/config.yaml` 中的数据库连接信息

### 运行项目

```bash
go run main.go
```

服务器将在 `http://localhost:8080` 启动

### 健康检查

访问 `http://localhost:8080/health` 检查服务状态

## API文档

### 用户管理

- `POST /api/v1/users/register` - 用户注册
- `POST /api/v1/users/login` - 用户登录
- `PUT /api/v1/users/password` - 修改密码
- `PUT /api/v1/users/profile` - 更新用户信息
- `GET /api/v1/users/info/:id` - 获取用户信息
- `PUT /api/v1/users/status/:id` - 更新用户状态
- `GET /api/v1/users` - 查询用户列表

### 商品管理

- `POST /api/v1/products` - 创建商品
- `PUT /api/v1/products/:id` - 更新商品
- `GET /api/v1/products/:id` - 获取商品信息
- `GET /api/v1/products` - 查询商品列表
- `PUT /api/v1/products/status/:id` - 更新商品状态

### 分类管理

- `POST /api/v1/categories` - 创建分类
- `PUT /api/v1/categories/:id` - 更新分类
- `DELETE /api/v1/categories/:id` - 删除分类
- `GET /api/v1/categories/tree` - 获取分类树

### 质检管理

- `POST /api/v1/batches` - 创建批次
- `PUT /api/v1/batches/:id` - 更新批次
- `GET /api/v1/batches/:id` - 获取批次信息
- `GET /api/v1/batches/no/:batchNo` - 根据批次号获取批次
- `GET /api/v1/batches` - 查询批次列表

### 拍卖管理

- `POST /api/v1/auctions` - 创建拍卖会
- `PUT /api/v1/auctions/:id` - 更新拍卖会
- `GET /api/v1/auctions/:id` - 获取拍卖会信息
- `GET /api/v1/auctions` - 查询拍卖会列表

## 开发指南

### 代码结构

项目采用分层架构：

1. **API层** (`internal/api/`): 处理HTTP请求和响应
2. **Service层** (`internal/service/`): 业务逻辑处理
3. **DAO层** (`internal/dao/`): 数据访问操作
4. **Model层** (`internal/model/`): 数据模型定义

### 添加新功能

1. 在 `internal/model/` 中定义数据模型
2. 在 `internal/dao/` 中实现数据访问接口
3. 在 `internal/service/` 中实现业务逻辑
4. 在 `internal/api/` 中实现HTTP接口
5. 在 `main.go` 中注册路由

### 数据库迁移

使用 `docs/database/migration/` 目录下的SQL文件进行数据库迁移

## 部署

### Docker部署

```bash
# 构建镜像
docker build -t flower-auction .

# 运行容器
docker run -p 8080:8080 flower-auction
```

### 生产环境配置

1. 修改 `configs/config.yaml` 中的配置
2. 设置环境变量 `GIN_MODE=release`
3. 配置反向代理（如Nginx）
4. 设置日志轮转
5. 配置监控和告警

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request

## 许可证

MIT License

## 联系方式

如有问题，请联系项目维护者。
