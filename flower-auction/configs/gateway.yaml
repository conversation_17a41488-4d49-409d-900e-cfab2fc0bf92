# API网关配置
server:
  port: 8080
  mode: debug
  read_timeout: 60
  write_timeout: 60

# 服务发现配置
services:
  auth-service:
    url: http://localhost:8081
    health_check: /health
  user-service:
    url: http://localhost:8082
    health_check: /health
  product-service:
    url: http://localhost:8083
    health_check: /health
  upload-service:
    url: http://localhost:8084
    health_check: /health

# 负载均衡配置
load_balancer:
  strategy: round_robin # round_robin, least_connections, ip_hash

# 限流配置
rate_limit:
  enabled: true
  requests_per_second: 100
  burst: 200

log:
  level: info
  filename: ./logs/gateway.log
  max_size: 100
  max_age: 30
  max_backups: 10
  compress: true
