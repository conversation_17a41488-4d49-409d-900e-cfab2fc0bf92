# 昆明花卉拍卖系统配置文件

# 服务器配置
server:
  port: 8080
  mode: debug  # debug, release

# 用户数据库配置
userDB:
  host: localhost
  port: 3306
  user: root
  password: root
  database: user_db
  maxIdleConns: 10
  maxOpenConns: 100

# 商品数据库配置
productDB:
  host: localhost
  port: 3306
  user: root
  password: root
  database: product_db
  maxIdleConns: 10
  maxOpenConns: 100

# 拍卖数据库配置
auctionDB:
  host: localhost
  port: 3306
  user: root
  password: root
  database: auction_db
  maxIdleConns: 10
  maxOpenConns: 100

# 订单数据库配置
orderDB:
  host: localhost
  port: 3306
  user: root
  password: root
  database: order_db
  maxIdleConns: 10
  maxOpenConns: 100

# Redis配置
redis:
  host: localhost
  port: 6379
  password: ""
  db: 0

# RabbitMQ配置
rabbitmq:
  url: "amqp://guest:guest@localhost:5672/"
  exchange: "flower_auction"
  
# JWT配置
jwt:
  secret: "flower_auction_secret_key"
  expireHours: 24

# 日志配置
log:
  level: info
  file: "./logs/app.log"
  maxSize: 100  # MB
  maxBackups: 3
  maxAge: 28    # days
