@menuPrefixCls: rc-menu;

@font-face {
  font-family: 'FontAwesome';
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/fontawesome-webfont.eot');
  src: url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/fontawesome-webfont.eot?#iefix')
      format('embedded-opentype'),
    url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/fontawesome-webfont.woff')
      format('woff'),
    url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/fontawesome-webfont.ttf')
      format('truetype'),
    url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.2.0/fonts/fontawesome-webfont.svg?#fontawesomeregular')
      format('svg');
  font-weight: normal;
  font-style: normal;
}

.@{menuPrefixCls} {
  outline: none;
  margin-bottom: 0;
  padding-left: 0; // Override default ul/ol
  padding-right: 0; // Override default ul/ol in RTL direction
  list-style: none;
  border: 1px solid #d9d9d9;
  box-shadow: 0 0 4px #d9d9d9;
  border-radius: 3px;
  color: #666;

  &-rtl {
    direction: rtl;
  }

  &-hidden,
  &-submenu-hidden {
    display: none;
  }

  &-collapse {
    overflow: hidden;
    // &-active {
    transition: height 0.3s ease-out;
    // }
  }

  &-item-group-list {
    margin: 0;
    padding: 0;
  }

  &-item-group-title {
    color: #999;
    line-height: 1.5;
    padding: 8px 10px;
    border-bottom: 1px solid #dedede;
  }

  &-item-active,
  &-submenu-active > &-submenu-title {
    background-color: #eaf8fe;
  }

  &-item-selected {
    background-color: #eaf8fe;
    // fix chrome render bug
    transform: translateZ(0);
  }

  &-submenu-selected {
    background-color: #eaf8fe;
  }

  & > li&-submenu {
    padding: 0;
  }

  &-horizontal&-sub,
  &-vertical&-sub,
  &-vertical-left&-sub,
  &-vertical-right&-sub {
    min-width: 160px;
    margin-top: 0;
  }

  &-item,
  &-submenu-title {
    margin: 0;
    position: relative;
    display: block;
    padding: 7px 7px 7px 16px;
    white-space: nowrap;

    .@{menuPrefixCls}-rtl & {
      padding: 7px 16px 7px 7px;
    }

    // Disabled state sets text to gray and nukes hover/tab effects
    &.@{menuPrefixCls}-item-disabled,
    &.@{menuPrefixCls}-submenu-disabled {
      color: #777 !important;
    }
  }

  &-item {
    display: flex;
    align-items: center;

    .@{menuPrefixCls}-item-extra {
      margin-left: auto;
      font-size: 14px;
    }
  }

  &-item-divider {
    height: 1px;
    margin: 1px 0;
    overflow: hidden;
    padding: 0;
    line-height: 0;
    background-color: #e5e5e5;
  }

  &-submenu {
    &-popup {
      position: absolute;

      .submenu-title-wrapper {
        padding-right: 20px;

        .@{menuPrefixCls}-submenu-rtl&,
        .@{menuPrefixCls}-submenu-rtl & {
          padding-right: 0;
          padding-left: 20px;
        }
      }
    }
    > .@{menuPrefixCls} {
      background-color: #fff;
    }
  }

  .@{menuPrefixCls}-submenu-title,
  .@{menuPrefixCls}-item {
    .anticon {
      width: 14px;
      height: 14px;
      margin-right: 8px;
      top: -1px;

      .@{menuPrefixCls}-rtl & {
        margin-right: 0;
        margin-left: 8px;
      }
    }
  }

  &-horizontal {
    background-color: #f3f5f7;
    border: none;
    border-bottom: 1px solid #d9d9d9;
    box-shadow: none;
    white-space: nowrap;
    overflow: hidden;

    & > .@{menuPrefixCls}-item,
    & > .@{menuPrefixCls}-submenu > .@{menuPrefixCls}-submenu-title {
      padding: 15px 20px;
    }

    & > .@{menuPrefixCls}-submenu,
    & > .@{menuPrefixCls}-item {
      border-bottom: 2px solid transparent;
      display: inline-block;
      vertical-align: bottom;

      &-active {
        border-bottom: 2px solid #2db7f5;
        background-color: #f3f5f7;
        color: #2baee9;
      }
    }

    &:after {
      content: '\20';
      display: block;
      height: 0;
      clear: both;
    }
  }

  &-vertical,
  &-vertical-left,
  &-vertical-right,
  &-inline {
    padding: 12px 0;
    & > .@{menuPrefixCls}-item,
    & > .@{menuPrefixCls}-submenu > .@{menuPrefixCls}-submenu-title {
      padding: 12px 8px 12px 24px;

      .@{menuPrefixCls}-rtl& {
        padding: 12px 24px 12px 8px;
      }
    }
    .@{menuPrefixCls}-submenu-arrow {
      display: inline-block;
      font: normal normal normal 14px/1 FontAwesome;
      font-size: inherit;
      vertical-align: baseline;
      text-align: center;
      text-transform: none;
      text-rendering: auto;
      position: absolute;
      right: 16px;
      line-height: 1.5em;
      &:before {
        content: '\f0da';

        .@{menuPrefixCls}-rtl&,
        .@{menuPrefixCls}-submenu-rtl & {
          content: '\f0d9';
        }
      }

      .@{menuPrefixCls}-rtl&,
      .@{menuPrefixCls}-submenu-rtl & {
        right: auto;
        left: 16px;
      }
    }
  }
  &-inline {
    .@{menuPrefixCls}-submenu-arrow {
      transform: rotate(90deg);
      transition: transform 0.3s;
    }
    & .@{menuPrefixCls}-submenu-open > .@{menuPrefixCls}-submenu-title {
      .@{menuPrefixCls}-submenu-arrow {
        transform: rotate(-90deg);
      }
    }
  }

  &-vertical&-sub,
  &-vertical-left&-sub,
  &-vertical-right&-sub {
    padding: 0;

    .@{menuPrefixCls}-submenu-rtl & {
      direction: rtl;
    }
  }

  &-sub&-inline {
    padding: 0;
    border: none;
    border-radius: 0;
    box-shadow: none;

    & > .@{menuPrefixCls}-item,
    & > .@{menuPrefixCls}-submenu > .@{menuPrefixCls}-submenu-title {
      padding-top: 8px;
      padding-bottom: 8px;
      padding-right: 0;

      .@{menuPrefixCls}-rtl & {
        padding-left: 0;
      }
    }
  }

  .effect() {
    animation-duration: 0.3s;
    animation-fill-mode: both;
    transform-origin: 0 0;
  }

  &-open {
    &-slide-up-enter,
    &-slide-up-appear {
      .effect();
      opacity: 0;
      animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
      animation-play-state: paused;
    }

    &-slide-up-leave {
      .effect();
      opacity: 1;
      animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
      animation-play-state: paused;
    }

    &-slide-up-enter&-slide-up-enter-active,
    &-slide-up-appear&-slide-up-appear-active {
      animation-name: rcMenuOpenSlideUpIn;
      animation-play-state: running;
    }

    &-slide-up-leave&-slide-up-leave-active {
      animation-name: rcMenuOpenSlideUpOut;
      animation-play-state: running;
    }

    @keyframes rcMenuOpenSlideUpIn {
      0% {
        opacity: 0;
        transform-origin: 0% 0%;
        transform: scaleY(0);
      }
      100% {
        opacity: 1;
        transform-origin: 0% 0%;
        transform: scaleY(1);
      }
    }
    @keyframes rcMenuOpenSlideUpOut {
      0% {
        opacity: 1;
        transform-origin: 0% 0%;
        transform: scaleY(1);
      }
      100% {
        opacity: 0;
        transform-origin: 0% 0%;
        transform: scaleY(0);
      }
    }

    &-zoom-enter,
    &-zoom-appear {
      opacity: 0;
      .effect();
      animation-timing-function: cubic-bezier(0.08, 0.82, 0.17, 1);
      animation-play-state: paused;
    }

    &-zoom-leave {
      .effect();
      animation-timing-function: cubic-bezier(0.6, 0.04, 0.98, 0.34);
      animation-play-state: paused;
    }

    &-zoom-enter&-zoom-enter-active,
    &-zoom-appear&-zoom-appear-active {
      animation-name: rcMenuOpenZoomIn;
      animation-play-state: running;
    }

    &-zoom-leave&-zoom-leave-active {
      animation-name: rcMenuOpenZoomOut;
      animation-play-state: running;
    }

    &-zoom-enter,
    &-zoom-appear,
    &-zoom-leave {
      .@{menuPrefixCls}-submenu-rtl&,
      .@{menuPrefixCls}-submenu-rtl & {
        transform-origin: top right !important;
      }
    }

    @keyframes rcMenuOpenZoomIn {
      0% {
        opacity: 0;
        transform: scale(0, 0);
      }
      100% {
        opacity: 1;
        transform: scale(1, 1);
      }
    }
    @keyframes rcMenuOpenZoomOut {
      0% {
        transform: scale(1, 1);
      }
      100% {
        opacity: 0;
        transform: scale(0, 0);
      }
    }
  }
}
