"use strict";

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

// Generated by CoffeeScript 2.5.1
var _BlockAppendor;

module.exports = _BlockAppendor = /*#__PURE__*/function () {
  function _BlockAppendor(_config) {
    _classCallCheck(this, _BlockAppendor);

    this._config = _config;
  }

  _createClass(_BlockAppendor, [{
    key: "render",
    value: function render(options) {
      return this._render(options);
    }
  }]);

  return _BlockAppendor;
}();