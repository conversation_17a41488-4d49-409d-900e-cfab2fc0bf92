import _objectSpread from "@babel/runtime/helpers/esm/objectSpread2";
import _extends from "@babel/runtime/helpers/esm/extends";
import _typeof from "@babel/runtime/helpers/esm/typeof";
import _slicedToArray from "@babel/runtime/helpers/esm/slicedToArray";
import _objectWithoutProperties from "@babel/runtime/helpers/esm/objectWithoutProperties";
var _excluded = ["prefixCls", "steps", "defaultCurrent", "current", "onChange", "onClose", "onFinish", "open", "mask", "arrow", "rootClassName", "placement", "renderPanel", "gap", "animated", "scrollIntoViewOptions", "zIndex", "closeIcon", "closable", "builtinPlacements", "disabledInteraction"];
import * as React from 'react';
import Portal from '@rc-component/portal';
import Trigger from '@rc-component/trigger';
import classNames from 'classnames';
import useLayoutEffect from "rc-util/es/hooks/useLayoutEffect";
import useMergedState from "rc-util/es/hooks/useMergedState";
import { useMemo } from 'react';
import { useClosable } from "./hooks/useClosable";
import useTarget from "./hooks/useTarget";
import Mask from "./Mask";
import { getPlacements } from "./placements";
import TourStep from "./TourStep";
import { getPlacement } from "./util";
var CENTER_PLACEHOLDER = {
  left: '50%',
  top: '50%',
  width: 1,
  height: 1
};
var defaultScrollIntoViewOptions = {
  block: 'center',
  inline: 'center'
};
var Tour = function Tour(props) {
  var _props$prefixCls = props.prefixCls,
    prefixCls = _props$prefixCls === void 0 ? 'rc-tour' : _props$prefixCls,
    _props$steps = props.steps,
    steps = _props$steps === void 0 ? [] : _props$steps,
    defaultCurrent = props.defaultCurrent,
    current = props.current,
    onChange = props.onChange,
    onClose = props.onClose,
    _onFinish = props.onFinish,
    open = props.open,
    _props$mask = props.mask,
    mask = _props$mask === void 0 ? true : _props$mask,
    _props$arrow = props.arrow,
    arrow = _props$arrow === void 0 ? true : _props$arrow,
    rootClassName = props.rootClassName,
    placement = props.placement,
    renderPanel = props.renderPanel,
    gap = props.gap,
    animated = props.animated,
    _props$scrollIntoView = props.scrollIntoViewOptions,
    scrollIntoViewOptions = _props$scrollIntoView === void 0 ? defaultScrollIntoViewOptions : _props$scrollIntoView,
    _props$zIndex = props.zIndex,
    zIndex = _props$zIndex === void 0 ? 1001 : _props$zIndex,
    closeIcon = props.closeIcon,
    closable = props.closable,
    builtinPlacements = props.builtinPlacements,
    disabledInteraction = props.disabledInteraction,
    restProps = _objectWithoutProperties(props, _excluded);
  var triggerRef = React.useRef();
  var _useMergedState = useMergedState(0, {
      value: current,
      defaultValue: defaultCurrent
    }),
    _useMergedState2 = _slicedToArray(_useMergedState, 2),
    mergedCurrent = _useMergedState2[0],
    setMergedCurrent = _useMergedState2[1];
  var _useMergedState3 = useMergedState(undefined, {
      value: open,
      postState: function postState(origin) {
        return mergedCurrent < 0 || mergedCurrent >= steps.length ? false : origin !== null && origin !== void 0 ? origin : true;
      }
    }),
    _useMergedState4 = _slicedToArray(_useMergedState3, 2),
    mergedOpen = _useMergedState4[0],
    setMergedOpen = _useMergedState4[1];

  // Record if already rended in the DOM to avoid `findDOMNode` issue
  var _React$useState = React.useState(mergedOpen),
    _React$useState2 = _slicedToArray(_React$useState, 2),
    hasOpened = _React$useState2[0],
    setHasOpened = _React$useState2[1];
  var openRef = React.useRef(mergedOpen);
  useLayoutEffect(function () {
    if (mergedOpen) {
      if (!openRef.current) {
        setMergedCurrent(0);
      }
      setHasOpened(true);
    }
    openRef.current = mergedOpen;
  }, [mergedOpen]);
  var _ref = steps[mergedCurrent] || {},
    target = _ref.target,
    stepPlacement = _ref.placement,
    stepStyle = _ref.style,
    stepArrow = _ref.arrow,
    stepClassName = _ref.className,
    stepMask = _ref.mask,
    _ref$scrollIntoViewOp = _ref.scrollIntoViewOptions,
    stepScrollIntoViewOptions = _ref$scrollIntoViewOp === void 0 ? defaultScrollIntoViewOptions : _ref$scrollIntoViewOp,
    stepCloseIcon = _ref.closeIcon,
    stepClosable = _ref.closable;
  var mergedClosable = useClosable(stepClosable, stepCloseIcon, closable, closeIcon);
  var mergedMask = mergedOpen && (stepMask !== null && stepMask !== void 0 ? stepMask : mask);
  var mergedScrollIntoViewOptions = stepScrollIntoViewOptions !== null && stepScrollIntoViewOptions !== void 0 ? stepScrollIntoViewOptions : scrollIntoViewOptions;
  var _useTarget = useTarget(target, open, gap, mergedScrollIntoViewOptions),
    _useTarget2 = _slicedToArray(_useTarget, 2),
    posInfo = _useTarget2[0],
    targetElement = _useTarget2[1];
  var mergedPlacement = getPlacement(targetElement, placement, stepPlacement);

  // ========================= arrow =========================
  var mergedArrow = targetElement ? typeof stepArrow === 'undefined' ? arrow : stepArrow : false;
  var arrowPointAtCenter = _typeof(mergedArrow) === 'object' ? mergedArrow.pointAtCenter : false;
  useLayoutEffect(function () {
    var _triggerRef$current;
    (_triggerRef$current = triggerRef.current) === null || _triggerRef$current === void 0 ? void 0 : _triggerRef$current.forceAlign();
  }, [arrowPointAtCenter, mergedCurrent]);

  // ========================= Change =========================
  var onInternalChange = function onInternalChange(nextCurrent) {
    setMergedCurrent(nextCurrent);
    onChange === null || onChange === void 0 ? void 0 : onChange(nextCurrent);
  };
  var mergedBuiltinPlacements = useMemo(function () {
    if (builtinPlacements) {
      return typeof builtinPlacements === 'function' ? builtinPlacements({
        arrowPointAtCenter: arrowPointAtCenter
      }) : builtinPlacements;
    }
    return getPlacements(arrowPointAtCenter);
  }, [builtinPlacements, arrowPointAtCenter]);

  // ========================= Render =========================
  // Skip if not init yet
  if (targetElement === undefined || !hasOpened) {
    return null;
  }
  var handleClose = function handleClose() {
    setMergedOpen(false);
    onClose === null || onClose === void 0 ? void 0 : onClose(mergedCurrent);
  };
  var getPopupElement = function getPopupElement() {
    return /*#__PURE__*/React.createElement(TourStep, _extends({
      arrow: mergedArrow,
      key: "content",
      prefixCls: prefixCls,
      total: steps.length,
      renderPanel: renderPanel,
      onPrev: function onPrev() {
        onInternalChange(mergedCurrent - 1);
      },
      onNext: function onNext() {
        onInternalChange(mergedCurrent + 1);
      },
      onClose: handleClose,
      current: mergedCurrent,
      onFinish: function onFinish() {
        handleClose();
        _onFinish === null || _onFinish === void 0 ? void 0 : _onFinish();
      }
    }, steps[mergedCurrent], {
      closable: mergedClosable
    }));
  };
  var mergedShowMask = typeof mergedMask === 'boolean' ? mergedMask : !!mergedMask;
  var mergedMaskStyle = typeof mergedMask === 'boolean' ? undefined : mergedMask;

  // when targetElement is not exist, use body as triggerDOMNode
  var getTriggerDOMNode = function getTriggerDOMNode(node) {
    return node || targetElement || document.body;
  };
  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Mask, {
    zIndex: zIndex,
    prefixCls: prefixCls,
    pos: posInfo,
    showMask: mergedShowMask,
    style: mergedMaskStyle === null || mergedMaskStyle === void 0 ? void 0 : mergedMaskStyle.style,
    fill: mergedMaskStyle === null || mergedMaskStyle === void 0 ? void 0 : mergedMaskStyle.color,
    open: mergedOpen,
    animated: animated,
    rootClassName: rootClassName,
    disabledInteraction: disabledInteraction
  }), /*#__PURE__*/React.createElement(Trigger, _extends({}, restProps, {
    builtinPlacements: mergedBuiltinPlacements,
    ref: triggerRef,
    popupStyle: stepStyle,
    popupPlacement: mergedPlacement,
    popupVisible: mergedOpen,
    popupClassName: classNames(rootClassName, stepClassName),
    prefixCls: prefixCls,
    popup: getPopupElement,
    forceRender: false,
    destroyPopupOnHide: true,
    zIndex: zIndex,
    getTriggerDOMNode: getTriggerDOMNode,
    arrow: !!mergedArrow
  }), /*#__PURE__*/React.createElement(Portal, {
    open: mergedOpen,
    autoLock: true
  }, /*#__PURE__*/React.createElement("div", {
    className: classNames(rootClassName, "".concat(prefixCls, "-target-placeholder")),
    style: _objectSpread(_objectSpread({}, posInfo || CENTER_PLACEHOLDER), {}, {
      position: 'fixed',
      pointerEvents: 'none'
    })
  }))));
};
export default Tour;