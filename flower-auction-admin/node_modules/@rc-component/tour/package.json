{"name": "@rc-component/tour", "version": "1.15.1", "description": "React tour Component", "keywords": ["react", "react-component", "react-tour"], "homepage": "https://github.com/react-component/tour", "bugs": {"url": "https://github.com/react-component/tour/issues"}, "repository": {"type": "git", "url": "https://github.com/react-component/tour.git"}, "license": "MIT", "author": "<EMAIL>", "main": "./lib/index", "module": "./es/index", "typings": "es/index.d.ts", "files": ["lib", "es"], "scripts": {"compile": "father build", "deploy": "npm run docs:build && npm run docs:deploy", "docs:build": "dumi build", "docs:deploy": "gh-pages -d docs-dist", "lint": "eslint src/ --ext .tsx,.ts", "lint:tsc": "tsc -p tsconfig.json --noEmit", "now-build": "npm run docs:build", "prepare": "dumi setup", "prepublishOnly": "npm run compile && np --no-cleanup --yolo --no-publish", "prettier": "prettier --write \"**/*.{js,jsx,tsx,ts,less,md,json}\"", "start": "dumi dev", "test": "umi-test", "watch": "father dev"}, "dependencies": {"@babel/runtime": "^7.18.0", "@rc-component/portal": "^1.0.0-9", "@rc-component/trigger": "^2.0.0", "classnames": "^2.3.2", "rc-util": "^5.24.4"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^15.0.0", "@types/jest": "^29.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@umijs/fabric": "^2.5.2", "dumi": "^2.1.2", "eslint": "^7.18.0", "father": "^4.0.0-rc.8", "gh-pages": "^3.1.0", "np": "^5.0.3", "prettier": "^3.0.0", "react": "^18.3.1", "react-dom": "^18.3.1", "typescript": "^5.0.0", "cheerio": "1.0.0-rc.12", "umi-test": "^1.9.7"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}, "engines": {"node": ">=8.x"}}