{"name": "@rc-component/util", "version": "1.2.1", "description": "Common Utils For React Component", "keywords": ["react", "util"], "homepage": "http://github.com/react-component/util", "bugs": {"url": "http://github.com/react-component/util/issues"}, "repository": {"type": "git", "url": "**************:react-component/util.git"}, "license": "MIT", "main": "./lib/index", "module": "./es/index", "files": ["lib", "es"], "scripts": {"build": "dumi build", "compile": "father build", "coverage": "npm test -- --coverage", "lint": "eslint src/ --ext .tsx,.ts & eslint tests/ --ext .tsx,.ts", "prepare": "husky install", "prepublishOnly": "npm run compile && rc-np", "start": "dumi dev", "test": "rc-test"}, "lint-staged": {"**/*.{js,jsx,tsx,ts,md,json}": ["prettier --write", "git add"]}, "dependencies": {"react-is": "^18.2.0"}, "devDependencies": {"@rc-component/father-plugin": "^2.0.1", "@rc-component/np": "^1.0.3", "@testing-library/react": "^16.0.0", "@types/jest": "^29.4.0", "@types/node": "^22.5.5", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@types/react-is": "^19.0.0", "@types/responselike": "^1.0.0", "@types/warning": "^3.0.0", "@umijs/fabric": "^3.0.0", "cross-env": "^7.0.2", "dumi": "^2.1.3", "eslint": "^8.54.0", "eslint-plugin-jest": "^28.2.0", "eslint-plugin-unicorn": "^56.0.1", "father": "^4.1.3", "glob": "^9.2.1", "husky": "^9.1.6", "lint-staged": "^15.1.0", "prettier": "^3.3.2", "rc-test": "^7.0.14", "react": "^18.0.0", "react-19": "npm:react@19.0.0", "react-dom": "^18.0.0", "react-dom-19": "npm:react-dom@19.0.0", "typescript": "^5.3.2"}, "peerDependencies": {"react": ">=18.0.0", "react-dom": ">=18.0.0"}}