.@{triggerPrefixCls} {
  &-mask {
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background-color: rgb(55, 55, 55);
    background-color: rgba(55, 55, 55, 0.6);
    height: 100%;
    filter: alpha(opacity=50);
    z-index: 1050;

    &-hidden {
      display: none;
    }
  }

  .fade-effect() {
    animation-duration: 0.3s;
    animation-fill-mode: both;
    animation-timing-function: cubic-bezier(0.55, 0, 0.55, 0.2);
  }

  &-fade-enter,&-fade-appear {
    opacity: 0;
    .fade-effect();
    animation-play-state: paused;
  }

  &-fade-leave {
    .fade-effect();
    animation-play-state: paused;
  }

  &-fade-enter&-fade-enter-active,&-fade-appear&-fade-appear-active  {
    animation-name: rcTriggerMaskFadeIn;
    animation-play-state: running;
  }

  &-fade-leave&-fade-leave-active {
    animation-name: rcDialogFadeOut;
    animation-play-state: running;
  }

  @keyframes rcTriggerMaskFadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }

  @keyframes rcDialogFadeOut {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0;
    }
  }
}
