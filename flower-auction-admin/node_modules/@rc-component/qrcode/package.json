{"name": "@rc-component/qrcode", "version": "1.0.0", "description": "base abstract trigger component for react", "engines": {"node": ">=8.x"}, "keywords": ["react", "react-component", "react-qrcode", "qrcode"], "homepage": "https://github.com/react-component/qrcode", "author": "", "repository": {"type": "git", "url": "https://github.com/react-component/qrcode.git"}, "bugs": {"url": "https://github.com/react-component/qrcode/issues"}, "files": ["es", "lib", "assets/**/*.css", "assets/**/*.less"], "license": "MIT", "main": "./lib/index", "module": "./es/index", "scripts": {"start": "dumi dev", "build": "dumi build", "compile": "father build && lessc assets/index.less assets/index.css", "bakpublishOnly": "npm run compile && np --yolo --no-publish", "prepublishOnly": "npm run compile", "lint": "eslint src/ docs/examples/ --ext .tsx,.ts,.jsx,.js", "test": "umi-test", "coverage": "rc-test --coverage", "now-build": "npm run build"}, "devDependencies": {"@ant-design/tools": "^18.0.2", "@rc-component/father-plugin": "^1.0.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^15.0.4", "@types/classnames": "^2.2.10", "@types/jest": "^29.5.2", "@types/node": "^20.11.6", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.11", "@umijs/fabric": "^4.0.1", "cross-env": "^7.0.1", "dumi": "^2.1.0", "eslint": "^8.51.0", "father": "^4.0.0", "jest-canvas-mock": "^2.5.2", "less": "^4.2.0", "np": "^10.0.5", "rc-test": "^7.0.13", "react": "^18.0.0", "react-dom": "^18.0.0", "regenerator-runtime": "^0.14.0", "ts-jest": "^29.1.4", "typescript": "^5.1.6", "umi-test": "^1.9.7"}, "dependencies": {"@babel/runtime": "^7.24.7", "classnames": "^2.3.2", "rc-util": "^5.38.0"}, "peerDependencies": {"react": ">=16.9.0", "react-dom": ">=16.9.0"}}