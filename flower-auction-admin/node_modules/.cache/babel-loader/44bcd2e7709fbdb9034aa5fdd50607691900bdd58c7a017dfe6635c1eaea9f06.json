{"ast": null, "code": "export default function contains(root, n) {\n  if (!root) {\n    return false;\n  }\n\n  // Use native if support\n  if (root.contains) {\n    return root.contains(n);\n  }\n\n  // `document.contains` not support with IE11\n  let node = n;\n  while (node) {\n    if (node === root) {\n      return true;\n    }\n    node = node.parentNode;\n  }\n  return false;\n}", "map": {"version": 3, "names": ["contains", "root", "n", "node", "parentNode"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@rc-component/util/es/Dom/contains.js"], "sourcesContent": ["export default function contains(root, n) {\n  if (!root) {\n    return false;\n  }\n\n  // Use native if support\n  if (root.contains) {\n    return root.contains(n);\n  }\n\n  // `document.contains` not support with IE11\n  let node = n;\n  while (node) {\n    if (node === root) {\n      return true;\n    }\n    node = node.parentNode;\n  }\n  return false;\n}"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,IAAI,EAAEC,CAAC,EAAE;EACxC,IAAI,CAACD,IAAI,EAAE;IACT,OAAO,KAAK;EACd;;EAEA;EACA,IAAIA,IAAI,CAACD,QAAQ,EAAE;IACjB,OAAOC,IAAI,CAACD,QAAQ,CAACE,CAAC,CAAC;EACzB;;EAEA;EACA,IAAIC,IAAI,GAAGD,CAAC;EACZ,OAAOC,IAAI,EAAE;IACX,IAAIA,IAAI,KAAKF,IAAI,EAAE;MACjB,OAAO,IAAI;IACb;IACAE,IAAI,GAAGA,IAAI,CAACC,UAAU;EACxB;EACA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}