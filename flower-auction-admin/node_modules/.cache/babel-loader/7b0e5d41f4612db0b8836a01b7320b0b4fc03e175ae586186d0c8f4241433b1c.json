{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0\n};\nexport default function useOffsets(tabs, tabSizes, holderScrollWidth) {\n  return useMemo(function () {\n    var _tabs$;\n    var map = new Map();\n    var lastOffset = tabSizes.get((_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key) || DEFAULT_SIZE;\n    var rightOffset = lastOffset.left + lastOffset.width;\n    for (var i = 0; i < tabs.length; i += 1) {\n      var key = tabs[i].key;\n      var data = tabSizes.get(key);\n\n      // Reuse last one when not exist yet\n      if (!data) {\n        var _tabs;\n        data = tabSizes.get((_tabs = tabs[i - 1]) === null || _tabs === void 0 ? void 0 : _tabs.key) || DEFAULT_SIZE;\n      }\n      var entity = map.get(key) || _objectSpread({}, data);\n\n      // Right\n      entity.right = rightOffset - entity.left - entity.width;\n\n      // Update entity\n      map.set(key, entity);\n    }\n    return map;\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), tabSizes, holderScrollWidth]);\n}", "map": {"version": 3, "names": ["_objectSpread", "useMemo", "DEFAULT_SIZE", "width", "height", "left", "top", "useOffsets", "tabs", "tabSizes", "holder<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_tabs$", "map", "Map", "lastOffset", "get", "key", "rightOffset", "i", "length", "data", "_tabs", "entity", "right", "set", "tab", "join"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/rc-tabs/es/hooks/useOffsets.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport { useMemo } from 'react';\nvar DEFAULT_SIZE = {\n  width: 0,\n  height: 0,\n  left: 0,\n  top: 0\n};\nexport default function useOffsets(tabs, tabSizes, holderScrollWidth) {\n  return useMemo(function () {\n    var _tabs$;\n    var map = new Map();\n    var lastOffset = tabSizes.get((_tabs$ = tabs[0]) === null || _tabs$ === void 0 ? void 0 : _tabs$.key) || DEFAULT_SIZE;\n    var rightOffset = lastOffset.left + lastOffset.width;\n    for (var i = 0; i < tabs.length; i += 1) {\n      var key = tabs[i].key;\n      var data = tabSizes.get(key);\n\n      // Reuse last one when not exist yet\n      if (!data) {\n        var _tabs;\n        data = tabSizes.get((_tabs = tabs[i - 1]) === null || _tabs === void 0 ? void 0 : _tabs.key) || DEFAULT_SIZE;\n      }\n      var entity = map.get(key) || _objectSpread({}, data);\n\n      // Right\n      entity.right = rightOffset - entity.left - entity.width;\n\n      // Update entity\n      map.set(key, entity);\n    }\n    return map;\n  }, [tabs.map(function (tab) {\n    return tab.key;\n  }).join('_'), tabSizes, holderScrollWidth]);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,SAASC,OAAO,QAAQ,OAAO;AAC/B,IAAIC,YAAY,GAAG;EACjBC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,GAAG,EAAE;AACP,CAAC;AACD,eAAe,SAASC,UAAUA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,iBAAiB,EAAE;EACpE,OAAOT,OAAO,CAAC,YAAY;IACzB,IAAIU,MAAM;IACV,IAAIC,GAAG,GAAG,IAAIC,GAAG,CAAC,CAAC;IACnB,IAAIC,UAAU,GAAGL,QAAQ,CAACM,GAAG,CAAC,CAACJ,MAAM,GAAGH,IAAI,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIG,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACK,GAAG,CAAC,IAAId,YAAY;IACrH,IAAIe,WAAW,GAAGH,UAAU,CAACT,IAAI,GAAGS,UAAU,CAACX,KAAK;IACpD,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGV,IAAI,CAACW,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACvC,IAAIF,GAAG,GAAGR,IAAI,CAACU,CAAC,CAAC,CAACF,GAAG;MACrB,IAAII,IAAI,GAAGX,QAAQ,CAACM,GAAG,CAACC,GAAG,CAAC;;MAE5B;MACA,IAAI,CAACI,IAAI,EAAE;QACT,IAAIC,KAAK;QACTD,IAAI,GAAGX,QAAQ,CAACM,GAAG,CAAC,CAACM,KAAK,GAAGb,IAAI,CAACU,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,IAAIG,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACL,GAAG,CAAC,IAAId,YAAY;MAC9G;MACA,IAAIoB,MAAM,GAAGV,GAAG,CAACG,GAAG,CAACC,GAAG,CAAC,IAAIhB,aAAa,CAAC,CAAC,CAAC,EAAEoB,IAAI,CAAC;;MAEpD;MACAE,MAAM,CAACC,KAAK,GAAGN,WAAW,GAAGK,MAAM,CAACjB,IAAI,GAAGiB,MAAM,CAACnB,KAAK;;MAEvD;MACAS,GAAG,CAACY,GAAG,CAACR,GAAG,EAAEM,MAAM,CAAC;IACtB;IACA,OAAOV,GAAG;EACZ,CAAC,EAAE,CAACJ,IAAI,CAACI,GAAG,CAAC,UAAUa,GAAG,EAAE;IAC1B,OAAOA,GAAG,CAACT,GAAG;EAChB,CAAC,CAAC,CAACU,IAAI,CAAC,GAAG,CAAC,EAAEjB,QAAQ,EAAEC,iBAAiB,CAAC,CAAC;AAC7C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}