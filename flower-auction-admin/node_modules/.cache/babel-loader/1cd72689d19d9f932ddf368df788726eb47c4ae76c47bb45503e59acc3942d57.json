{"ast": null, "code": "import { apiClient } from './apiClient';\nexport const authService = {\n  // 用户登录\n  login: async credentials => {\n    try {\n      const response = await apiClient.post('/auth/login', credentials);\n\n      // 后端返回的数据结构：{ success: true, data: { user: {...}, token: \"...\", refreshToken: \"...\" } }\n      console.log('Login response:', response.data);\n      if (response.data && response.data.success && response.data.data) {\n        const {\n          data\n        } = response.data;\n        return {\n          success: true,\n          data: {\n            token: data.token,\n            refreshToken: data.refreshToken,\n            user: {\n              id: data.user.id,\n              username: data.user.username,\n              email: data.user.email || '',\n              role: data.user.userType === 3 ? 'admin' : 'user',\n              realName: data.user.realName\n            }\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '登录响应数据格式错误'\n        };\n      }\n    } catch (error) {\n      var _error$response, _error$response$data;\n      console.error('Login error:', error);\n      return {\n        success: false,\n        message: ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || error.message || '登录失败'\n      };\n    }\n  },\n  // 用户登出\n  logout: async () => {\n    try {\n      const response = await apiClient.post('/auth/logout');\n      return response.data;\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      throw new Error(((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '登出失败');\n    }\n  },\n  // 获取用户信息\n  getUserInfo: async () => {\n    try {\n      const response = await apiClient.get('/auth/me');\n\n      // 后端返回的数据结构需要转换\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          data: {\n            id: response.data.data.id,\n            username: response.data.data.username,\n            email: response.data.data.email || '',\n            role: response.data.data.role || 'user',\n            realName: response.data.data.realName\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '获取用户信息失败'\n        };\n      }\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      return {\n        success: false,\n        message: ((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.error) || error.message || '获取用户信息失败'\n      };\n    }\n  },\n  // 刷新token\n  refreshToken: async refreshToken => {\n    try {\n      const response = await apiClient.post('/auth/refresh', {\n        refreshToken\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '刷新token失败');\n    }\n  },\n  // 修改密码\n  changePassword: async (oldPassword, newPassword) => {\n    try {\n      const response = await apiClient.post('/auth/change-password', {\n        oldPassword,\n        newPassword\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || '修改密码失败');\n    }\n  }\n};", "map": {"version": 3, "names": ["apiClient", "authService", "login", "credentials", "response", "post", "console", "log", "data", "success", "token", "refreshToken", "user", "id", "username", "email", "role", "userType", "realName", "message", "error", "_error$response", "_error$response$data", "logout", "_error$response2", "_error$response2$data", "Error", "getUserInfo", "get", "_error$response3", "_error$response3$data", "_error$response4", "_error$response4$data", "changePassword", "oldPassword", "newPassword", "_error$response5", "_error$response5$data"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/authService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\nimport { LoginCredentials, User } from '../hooks/useAuth';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n}\n\nexport interface LoginResponse {\n  token: string;\n  refreshToken: string;\n  user: User;\n}\n\nexport const authService = {\n  // 用户登录\n  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {\n    try {\n      const response = await apiClient.post('/auth/login', credentials);\n\n      // 后端返回的数据结构：{ success: true, data: { user: {...}, token: \"...\", refreshToken: \"...\" } }\n      console.log('Login response:', response.data);\n\n      if (response.data && response.data.success && response.data.data) {\n        const { data } = response.data;\n        return {\n          success: true,\n          data: {\n            token: data.token,\n            refreshToken: data.refreshToken,\n            user: {\n              id: data.user.id,\n              username: data.user.username,\n              email: data.user.email || '',\n              role: data.user.userType === 3 ? 'admin' : 'user',\n              realName: data.user.realName,\n            }\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '登录响应数据格式错误'\n        };\n      }\n    } catch (error: any) {\n      console.error('Login error:', error);\n      return {\n        success: false,\n        message: error.response?.data?.error || error.message || '登录失败'\n      };\n    }\n  },\n\n  // 用户登出\n  logout: async (): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.post('/auth/logout');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '登出失败');\n    }\n  },\n\n  // 获取用户信息\n  getUserInfo: async (): Promise<ApiResponse<User>> => {\n    try {\n      const response = await apiClient.get('/auth/me');\n\n      // 后端返回的数据结构需要转换\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          data: {\n            id: response.data.data.id,\n            username: response.data.data.username,\n            email: response.data.data.email || '',\n            role: response.data.data.role || 'user',\n            realName: response.data.data.realName,\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '获取用户信息失败'\n        };\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        message: error.response?.data?.error || error.message || '获取用户信息失败'\n      };\n    }\n  },\n\n  // 刷新token\n  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {\n    try {\n      const response = await apiClient.post('/auth/refresh', { refreshToken });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '刷新token失败');\n    }\n  },\n\n  // 修改密码\n  changePassword: async (oldPassword: string, newPassword: string): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.post('/auth/change-password', {\n        oldPassword,\n        newPassword,\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '修改密码失败');\n    }\n  },\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAevC,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,KAAK,EAAE,MAAOC,WAA6B,IAA0C;IACnF,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,aAAa,EAAEF,WAAW,CAAC;;MAEjE;MACAG,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEH,QAAQ,CAACI,IAAI,CAAC;MAE7C,IAAIJ,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,IAAIL,QAAQ,CAACI,IAAI,CAACA,IAAI,EAAE;QAChE,MAAM;UAAEA;QAAK,CAAC,GAAGJ,QAAQ,CAACI,IAAI;QAC9B,OAAO;UACLC,OAAO,EAAE,IAAI;UACbD,IAAI,EAAE;YACJE,KAAK,EAAEF,IAAI,CAACE,KAAK;YACjBC,YAAY,EAAEH,IAAI,CAACG,YAAY;YAC/BC,IAAI,EAAE;cACJC,EAAE,EAAEL,IAAI,CAACI,IAAI,CAACC,EAAE;cAChBC,QAAQ,EAAEN,IAAI,CAACI,IAAI,CAACE,QAAQ;cAC5BC,KAAK,EAAEP,IAAI,CAACI,IAAI,CAACG,KAAK,IAAI,EAAE;cAC5BC,IAAI,EAAER,IAAI,CAACI,IAAI,CAACK,QAAQ,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM;cACjDC,QAAQ,EAAEV,IAAI,CAACI,IAAI,CAACM;YACtB;UACF;QACF,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLT,OAAO,EAAE,KAAK;UACdU,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA;MACnBhB,OAAO,CAACc,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;MACpC,OAAO;QACLX,OAAO,EAAE,KAAK;QACdU,OAAO,EAAE,EAAAE,eAAA,GAAAD,KAAK,CAAChB,QAAQ,cAAAiB,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBb,IAAI,cAAAc,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,KAAIA,KAAK,CAACD,OAAO,IAAI;MAC3D,CAAC;IACH;EACF,CAAC;EAED;EACAI,MAAM,EAAE,MAAAA,CAAA,KAAkC;IACxC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,cAAc,CAAC;MACrD,OAAOD,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOY,KAAU,EAAE;MAAA,IAAAI,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIC,KAAK,CAAC,EAAAF,gBAAA,GAAAJ,KAAK,CAAChB,QAAQ,cAAAoB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhB,IAAI,cAAAiB,qBAAA,uBAApBA,qBAAA,CAAsBN,OAAO,KAAI,MAAM,CAAC;IAC1D;EACF,CAAC;EAED;EACAQ,WAAW,EAAE,MAAAA,CAAA,KAAwC;IACnD,IAAI;MACF,MAAMvB,QAAQ,GAAG,MAAMJ,SAAS,CAAC4B,GAAG,CAAC,UAAU,CAAC;;MAEhD;MACA,IAAIxB,QAAQ,CAACI,IAAI,IAAIJ,QAAQ,CAACI,IAAI,CAACC,OAAO,EAAE;QAC1C,OAAO;UACLA,OAAO,EAAE,IAAI;UACbD,IAAI,EAAE;YACJK,EAAE,EAAET,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACK,EAAE;YACzBC,QAAQ,EAAEV,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACM,QAAQ;YACrCC,KAAK,EAAEX,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACO,KAAK,IAAI,EAAE;YACrCC,IAAI,EAAEZ,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACQ,IAAI,IAAI,MAAM;YACvCE,QAAQ,EAAEd,QAAQ,CAACI,IAAI,CAACA,IAAI,CAACU;UAC/B;QACF,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLT,OAAO,EAAE,KAAK;UACdU,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAS,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QACLrB,OAAO,EAAE,KAAK;QACdU,OAAO,EAAE,EAAAU,gBAAA,GAAAT,KAAK,CAAChB,QAAQ,cAAAyB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBV,KAAK,KAAIA,KAAK,CAACD,OAAO,IAAI;MAC3D,CAAC;IACH;EACF,CAAC;EAED;EACAR,YAAY,EAAE,MAAOA,YAAoB,IAAoE;IAC3G,IAAI;MACF,MAAMP,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,eAAe,EAAE;QAAEM;MAAa,CAAC,CAAC;MACxE,OAAOP,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOY,KAAU,EAAE;MAAA,IAAAW,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIN,KAAK,CAAC,EAAAK,gBAAA,GAAAX,KAAK,CAAChB,QAAQ,cAAA2B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvB,IAAI,cAAAwB,qBAAA,uBAApBA,qBAAA,CAAsBb,OAAO,KAAI,WAAW,CAAC;IAC/D;EACF,CAAC;EAED;EACAc,cAAc,EAAE,MAAAA,CAAOC,WAAmB,EAAEC,WAAmB,KAA2B;IACxF,IAAI;MACF,MAAM/B,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,uBAAuB,EAAE;QAC7D6B,WAAW;QACXC;MACF,CAAC,CAAC;MACF,OAAO/B,QAAQ,CAACI,IAAI;IACtB,CAAC,CAAC,OAAOY,KAAU,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIX,KAAK,CAAC,EAAAU,gBAAA,GAAAhB,KAAK,CAAChB,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBlB,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}