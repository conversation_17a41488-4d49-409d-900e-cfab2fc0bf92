{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Modal, Form, message, Popconfirm, Typography, Row, Col, Tree, Switch, Tag, Descriptions, Select } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, SettingOutlined, ReloadOutlined, UserOutlined } from '@ant-design/icons';\nimport { roleService } from '../../../services/roleService';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\n\n// 角色数据接口\n\n// 权限数据接口\n\n// 查询参数接口\n\nconst RoleManagement = () => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  const [permissions, setPermissions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);\n  const [editingRole, setEditingRole] = useState(null);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [checkedPermissions, setCheckedPermissions] = useState([]);\n  const [expandedKeys, setExpandedKeys] = useState([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const response = await roleService.getRoleList(queryParams);\n      if (response.success) {\n        // 处理后端返回的数据，添加缺失的字段\n        const processedRoles = response.data.list.map(role => ({\n          ...role,\n          userCount: role.userCount || 0,\n          permissions: role.permissions || []\n        }));\n        setRoles(processedRoles);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取角色列表失败');\n        setRoles([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      console.error('获取角色列表失败:', error);\n      let errorMsg = '获取角色列表失败';\n      if (error.response) {\n        const {\n          status\n        } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问角色列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setRoles([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取权限列表\n  const fetchPermissions = async () => {\n    try {\n      const response = await roleService.getPermissionList();\n      if (response.success) {\n        // 处理后端返回的权限数据，转换为前端期望的格式\n        const rawData = response.data;\n        const permissionList = rawData.list || rawData || [];\n        const processedPermissions = permissionList.map(permission => ({\n          id: permission.id,\n          name: permission.name,\n          code: permission.code,\n          type: permission.type || 'menu',\n          parentId: permission.parent_id,\n          path: permission.path,\n          children: []\n        }));\n\n        // 构建树形结构\n        const permissionTree = buildPermissionTree(processedPermissions);\n        setPermissions(permissionTree);\n      }\n    } catch (error) {\n      console.error('获取权限列表失败:', error);\n    }\n  };\n\n  // 构建权限树形结构\n  const buildPermissionTree = permissions => {\n    const map = new Map();\n    const roots = [];\n\n    // 创建映射\n    permissions.forEach(permission => {\n      map.set(permission.id, {\n        ...permission,\n        children: []\n      });\n    });\n\n    // 构建树形结构\n    permissions.forEach(permission => {\n      const node = map.get(permission.id);\n      if (permission.parentId && map.has(permission.parentId)) {\n        map.get(permission.parentId).children.push(node);\n      } else {\n        roots.push(node);\n      }\n    });\n    return roots;\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增角色\n  const handleAdd = () => {\n    setEditingRole(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑角色\n  const handleEdit = role => {\n    setEditingRole(role);\n    form.setFieldsValue({\n      ...role,\n      status: role.status === 1\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除角色\n  const handleDelete = async id => {\n    try {\n      const response = await roleService.deleteRole(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchRoles();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存角色\n  const handleSave = async values => {\n    try {\n      const roleData = {\n        ...values,\n        status: values.status ? 1 : 0\n      };\n      let response;\n      if (editingRole) {\n        response = await roleService.updateRole(editingRole.id, roleData);\n      } else {\n        response = await roleService.createRole(roleData);\n      }\n      if (response.success) {\n        message.success(editingRole ? '更新成功' : '创建成功');\n        setIsModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '保存失败');\n      }\n    } catch (error) {\n      message.error(error.message || '保存失败');\n    }\n  };\n\n  // 配置权限\n  const handleConfigPermissions = role => {\n    setSelectedRole(role);\n    setCheckedPermissions(role.permissions || []);\n    setIsPermissionModalVisible(true);\n  };\n\n  // 保存权限配置\n  const handleSavePermissions = async () => {\n    if (!selectedRole) return;\n    try {\n      const response = await roleService.updateRolePermissions(selectedRole.id, checkedPermissions);\n      if (response.success) {\n        message.success('权限配置成功');\n        setIsPermissionModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '权限配置失败');\n      }\n    } catch (error) {\n      message.error(error.message || '权限配置失败');\n    }\n  };\n\n  // 转换权限数据为树形结构\n  const convertPermissionsToTreeData = permissions => {\n    return permissions.map(permission => ({\n      title: permission.name,\n      key: permission.id,\n      children: permission.children ? convertPermissionsToTreeData(permission.children) : undefined\n    }));\n  };\n\n  // 权限树选择处理\n  const handlePermissionCheck = checkedKeys => {\n    if (Array.isArray(checkedKeys)) {\n      setCheckedPermissions(checkedKeys);\n    } else {\n      setCheckedPermissions(checkedKeys.checked);\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '角色名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 120\n  }, {\n    title: '角色编码',\n    dataIndex: 'code',\n    key: 'code',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 330,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    width: 200,\n    render: text => text || '-'\n  }, {\n    title: '用户数量',\n    dataIndex: 'userCount',\n    key: 'userCount',\n    width: 100,\n    render: count => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 346,\n        columnNumber: 11\n      }, this), count]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 345,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 1 ? 'green' : 'red',\n      children: status === 1 ? '启用' : '禁用'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 379,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleConfigPermissions(record),\n        children: \"\\u914D\\u7F6E\\u6743\\u9650\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 387,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u89D2\\u8272\\u5417\\uFF1F\",\n        description: \"\\u5220\\u9664\\u540E\\u8BE5\\u89D2\\u8272\\u4E0B\\u7684\\u7528\\u6237\\u5C06\\u5931\\u53BB\\u76F8\\u5E94\\u6743\\u9650\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 21\n          }, this),\n          disabled: record.userCount > 0,\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 399,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 392,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 375,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"role-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u89D2\\u8272\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 416,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u540D\\u79F0\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 428,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 427,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 426,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u7F16\\u7801\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 433,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 432,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 431,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: 1,\n                  children: \"\\u542F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 439,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 0,\n                  children: \"\\u7981\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 440,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 447,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 447,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 450,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 450,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 446,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 425,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 420,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 466,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u65B0\\u589E\\u89D2\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 464,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 463,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 21\n            }, this),\n            onClick: fetchRoles,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 472,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 462,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 461,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: roles,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1000\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingRole ? '编辑角色' : '新增角色',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入角色名称'\n              }, {\n                max: 50,\n                message: '角色名称不能超过50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 536,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入角色编码'\n              }, {\n                pattern: /^[A-Z_]+$/,\n                message: '角色编码只能包含大写字母和下划线'\n              }, {\n                max: 50,\n                message: '角色编码不能超过50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u7F16\\u7801\",\n                disabled: !!editingRole\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 549,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 540,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 539,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u89D2\\u8272\\u63CF\\u8FF0\",\n          rules: [{\n            max: 200,\n            message: '角色描述不能超过200个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u63CF\\u8FF0\",\n            rows: 4,\n            showCount: true,\n            maxLength: 200\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 554,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Switch, {\n            checkedChildren: \"\\u542F\\u7528\",\n            unCheckedChildren: \"\\u7981\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 579,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingRole ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 578,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 577,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 520,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 512,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `配置权限 - ${selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name}`,\n      open: isPermissionModalVisible,\n      onCancel: () => setIsPermissionModalVisible(false),\n      onOk: handleSavePermissions,\n      width: 800,\n      destroyOnClose: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Descriptions, {\n          size: \"small\",\n          column: 2,\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 601,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.code\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7528\\u6237\\u6570\\u91CF\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.userCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5F53\\u524D\\u6743\\u9650\\u6570\\u91CF\",\n            children: checkedPermissions.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 604,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 600,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 599,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: '1px solid #d9d9d9',\n          borderRadius: 6,\n          padding: 16,\n          maxHeight: 400,\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tree, {\n          checkable: true,\n          checkedKeys: checkedPermissions,\n          expandedKeys: expandedKeys,\n          onCheck: handlePermissionCheck,\n          onExpand: setExpandedKeys,\n          treeData: convertPermissionsToTreeData(permissions),\n          height: 350\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 609,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 608,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 591,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 415,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleManagement, \"pWX0hyl+c2/X5FvYRQEgQ/DVS6A=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = RoleManagement;\nexport default RoleManagement;\nvar _c;\n$RefreshReg$(_c, \"RoleManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Modal", "Form", "message", "Popconfirm", "Typography", "Row", "Col", "Tree", "Switch", "Tag", "Descriptions", "Select", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "SettingOutlined", "ReloadOutlined", "UserOutlined", "roleService", "jsxDEV", "_jsxDEV", "Title", "Option", "TextArea", "RoleManagement", "_s", "roles", "setRoles", "permissions", "setPermissions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "isPermissionModalVisible", "setIsPermissionModalVisible", "editingRole", "setEditingRole", "selectedR<PERSON>", "setSelectedRole", "checkedPermissions", "setCheckedPermissions", "expandedKeys", "setExpandedKeys", "saving", "setSaving", "form", "useForm", "searchForm", "fetchRoles", "response", "getRoleList", "success", "processedRoles", "data", "list", "map", "role", "userCount", "error", "console", "errorMsg", "status", "fetchPermissions", "getPermissionList", "rawData", "permissionList", "processedPermissions", "permission", "id", "name", "code", "type", "parentId", "parent_id", "path", "children", "permissionTree", "buildPermissionTree", "Map", "roots", "for<PERSON>ach", "set", "node", "get", "has", "push", "handleSearch", "values", "handleReset", "resetFields", "handleAdd", "handleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "deleteRole", "handleSave", "roleData", "updateRole", "createRole", "handleConfigPermissions", "handleSavePermissions", "updateRolePermissions", "convertPermissionsToTreeData", "title", "key", "undefined", "handlePermissionCheck", "checked<PERSON>eys", "Array", "isArray", "checked", "columns", "dataIndex", "width", "render", "text", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "count", "Date", "toLocaleString", "fixed", "_", "record", "size", "icon", "onClick", "description", "onConfirm", "okText", "cancelText", "danger", "disabled", "className", "level", "layout", "onFinish", "autoComplete", "gutter", "style", "xs", "sm", "md", "<PERSON><PERSON>", "label", "placeholder", "allowClear", "value", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "open", "onCancel", "footer", "destroyOnClose", "span", "rules", "required", "max", "pattern", "rows", "showCount", "max<PERSON><PERSON><PERSON>", "valuePropName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "justifyContent", "onOk", "marginBottom", "column", "length", "border", "borderRadius", "padding", "maxHeight", "overflow", "checkable", "onCheck", "onExpand", "treeData", "height", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  Tree,\n  Switch,\n  Tag,\n  Descriptions,\n  Select,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SettingOutlined,\n  ReloadOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { DataNode } from 'antd/es/tree';\nimport { roleService } from '../../../services/roleService';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// 角色数据接口\nexport interface Role {\n  id: number;\n  name: string;\n  code: string;\n  description?: string;\n  status: number;\n  userCount: number;\n  permissions: number[];\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 权限数据接口\nexport interface Permission {\n  id: number;\n  name: string;\n  code: string;\n  type: 'menu' | 'button' | 'api';\n  parentId?: number;\n  path?: string;\n  children?: Permission[];\n}\n\n// 查询参数接口\ninterface RoleQueryParams {\n  name?: string;\n  code?: string;\n  status?: number;\n  page: number;\n  pageSize: number;\n}\n\nconst RoleManagement: React.FC = () => {\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<RoleQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null);\n  const [checkedPermissions, setCheckedPermissions] = useState<number[]>([]);\n  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const response = await roleService.getRoleList(queryParams);\n      if (response.success) {\n        // 处理后端返回的数据，添加缺失的字段\n        const processedRoles = response.data.list.map((role: any) => ({\n          ...role,\n          userCount: role.userCount || 0,\n          permissions: role.permissions || [],\n        }));\n        setRoles(processedRoles);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取角色列表失败');\n        setRoles([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取角色列表失败:', error);\n      let errorMsg = '获取角色列表失败';\n      if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问角色列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setRoles([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取权限列表\n  const fetchPermissions = async () => {\n    try {\n      const response = await roleService.getPermissionList();\n      if (response.success) {\n        // 处理后端返回的权限数据，转换为前端期望的格式\n        const rawData = response.data as any;\n        const permissionList = rawData.list || rawData || [];\n        const processedPermissions = permissionList.map((permission: any) => ({\n          id: permission.id,\n          name: permission.name,\n          code: permission.code,\n          type: permission.type || 'menu',\n          parentId: permission.parent_id,\n          path: permission.path,\n          children: [],\n        }));\n\n        // 构建树形结构\n        const permissionTree = buildPermissionTree(processedPermissions);\n        setPermissions(permissionTree);\n      }\n    } catch (error: any) {\n      console.error('获取权限列表失败:', error);\n    }\n  };\n\n  // 构建权限树形结构\n  const buildPermissionTree = (permissions: any[]): Permission[] => {\n    const map = new Map();\n    const roots: Permission[] = [];\n\n    // 创建映射\n    permissions.forEach(permission => {\n      map.set(permission.id, { ...permission, children: [] });\n    });\n\n    // 构建树形结构\n    permissions.forEach(permission => {\n      const node = map.get(permission.id);\n      if (permission.parentId && map.has(permission.parentId)) {\n        map.get(permission.parentId).children.push(node);\n      } else {\n        roots.push(node);\n      }\n    });\n\n    return roots;\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增角色\n  const handleAdd = () => {\n    setEditingRole(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑角色\n  const handleEdit = (role: Role) => {\n    setEditingRole(role);\n    form.setFieldsValue({\n      ...role,\n      status: role.status === 1,\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除角色\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await roleService.deleteRole(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchRoles();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存角色\n  const handleSave = async (values: any) => {\n    try {\n      const roleData = {\n        ...values,\n        status: values.status ? 1 : 0,\n      };\n\n      let response;\n      if (editingRole) {\n        response = await roleService.updateRole(editingRole.id, roleData);\n      } else {\n        response = await roleService.createRole(roleData);\n      }\n\n      if (response.success) {\n        message.success(editingRole ? '更新成功' : '创建成功');\n        setIsModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '保存失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '保存失败');\n    }\n  };\n\n  // 配置权限\n  const handleConfigPermissions = (role: Role) => {\n    setSelectedRole(role);\n    setCheckedPermissions(role.permissions || []);\n    setIsPermissionModalVisible(true);\n  };\n\n  // 保存权限配置\n  const handleSavePermissions = async () => {\n    if (!selectedRole) return;\n\n    try {\n      const response = await roleService.updateRolePermissions(\n        selectedRole.id,\n        checkedPermissions\n      );\n      if (response.success) {\n        message.success('权限配置成功');\n        setIsPermissionModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '权限配置失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '权限配置失败');\n    }\n  };\n\n  // 转换权限数据为树形结构\n  const convertPermissionsToTreeData = (permissions: Permission[]): DataNode[] => {\n    return permissions.map(permission => ({\n      title: permission.name,\n      key: permission.id,\n      children: permission.children ? convertPermissionsToTreeData(permission.children) : undefined,\n    }));\n  };\n\n  // 权限树选择处理\n  const handlePermissionCheck = (checkedKeys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {\n    if (Array.isArray(checkedKeys)) {\n      setCheckedPermissions(checkedKeys as number[]);\n    } else {\n      setCheckedPermissions(checkedKeys.checked as number[]);\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Role> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '角色名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 120,\n    },\n    {\n      title: '角色编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 120,\n      render: (text: string) => <Tag color=\"blue\">{text}</Tag>,\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      width: 200,\n      render: (text: string) => text || '-',\n    },\n    {\n      title: '用户数量',\n      dataIndex: 'userCount',\n      key: 'userCount',\n      width: 100,\n      render: (count: number) => (\n        <Space>\n          <UserOutlined />\n          {count}\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: number) => (\n        <Tag color={status === 1 ? 'green' : 'red'}>\n          {status === 1 ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right',\n      render: (_, record: Role) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<SettingOutlined />}\n            onClick={() => handleConfigPermissions(record)}\n          >\n            配置权限\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个角色吗？\"\n            description=\"删除后该角色下的用户将失去相应权限\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              disabled={record.userCount > 0}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"role-management-container\">\n      <Title level={2}>角色管理</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"name\" label=\"角色名称\">\n                <Input placeholder=\"请输入角色名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"code\" label=\"角色编码\">\n                <Input placeholder=\"请输入角色编码\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"状态\">\n                <Select placeholder=\"请选择状态\" allowClear>\n                  <Option value={1}>启用</Option>\n                  <Option value={0}>禁用</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              新增角色\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchRoles}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 角色列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={roles}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1000 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 角色编辑模态框 */}\n      <Modal\n        title={editingRole ? '编辑角色' : '新增角色'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"角色名称\"\n                rules={[\n                  { required: true, message: '请输入角色名称' },\n                  { max: 50, message: '角色名称不能超过50个字符' },\n                ]}\n              >\n                <Input placeholder=\"请输入角色名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"code\"\n                label=\"角色编码\"\n                rules={[\n                  { required: true, message: '请输入角色编码' },\n                  { pattern: /^[A-Z_]+$/, message: '角色编码只能包含大写字母和下划线' },\n                  { max: 50, message: '角色编码不能超过50个字符' },\n                ]}\n              >\n                <Input placeholder=\"请输入角色编码\" disabled={!!editingRole} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"角色描述\"\n            rules={[\n              { max: 200, message: '角色描述不能超过200个字符' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入角色描述\"\n              rows={4}\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            valuePropName=\"checked\"\n          >\n            <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n          </Form.Item>\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setIsModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingRole ? '更新' : '创建'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 权限配置模态框 */}\n      <Modal\n        title={`配置权限 - ${selectedRole?.name}`}\n        open={isPermissionModalVisible}\n        onCancel={() => setIsPermissionModalVisible(false)}\n        onOk={handleSavePermissions}\n        width={800}\n        destroyOnClose\n      >\n        <div style={{ marginBottom: 16 }}>\n          <Descriptions size=\"small\" column={2}>\n            <Descriptions.Item label=\"角色名称\">{selectedRole?.name}</Descriptions.Item>\n            <Descriptions.Item label=\"角色编码\">{selectedRole?.code}</Descriptions.Item>\n            <Descriptions.Item label=\"用户数量\">{selectedRole?.userCount}</Descriptions.Item>\n            <Descriptions.Item label=\"当前权限数量\">{checkedPermissions.length}</Descriptions.Item>\n          </Descriptions>\n        </div>\n\n        <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16, maxHeight: 400, overflow: 'auto' }}>\n          <Tree\n            checkable\n            checkedKeys={checkedPermissions}\n            expandedKeys={expandedKeys}\n            onCheck={handlePermissionCheck}\n            onExpand={setExpandedKeys}\n            treeData={convertPermissionsToTreeData(permissions)}\n            height={350}\n          />\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default RoleManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,YAAY,EACZC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,YAAY,QACP,mBAAmB;AAG1B,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGlB,UAAU;AAC5B,MAAM;EAAEmB;AAAO,CAAC,GAAGZ,MAAM;AACzB,MAAM;EAAEa;AAAS,CAAC,GAAGzB,KAAK;;AAE1B;;AAaA;;AAWA;;AASA,MAAM0B,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAkB;IAC9D4C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACsD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvD,QAAQ,CAAW,EAAE,CAAC;EAC1E,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAc,EAAE,CAAC;EACjE,MAAM,CAAC0D,MAAM,EAAEC,SAAS,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC4D,IAAI,CAAC,GAAGpD,IAAI,CAACqD,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAGtD,IAAI,CAACqD,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMyB,QAAQ,GAAG,MAAMtC,WAAW,CAACuC,WAAW,CAACvB,WAAW,CAAC;MAC3D,IAAIsB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAEC,IAAS,KAAM;UAC5D,GAAGA,IAAI;UACPC,SAAS,EAAED,IAAI,CAACC,SAAS,IAAI,CAAC;UAC9BpC,WAAW,EAAEmC,IAAI,CAACnC,WAAW,IAAI;QACnC,CAAC,CAAC,CAAC;QACHD,QAAQ,CAACgC,cAAc,CAAC;QACxB1B,QAAQ,CAACuB,QAAQ,CAACI,IAAI,CAAC5B,KAAK,CAAC;MAC/B,CAAC,MAAM;QACL/B,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,UAAU,CAAC;QAC7C0B,QAAQ,CAAC,EAAE,CAAC;QACZM,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIE,QAAQ,GAAG,UAAU;MACzB,IAAIF,KAAK,CAACT,QAAQ,EAAE;QAClB,MAAM;UAAEY;QAAO,CAAC,GAAGH,KAAK,CAACT,QAAQ;QACjC,IAAIY,MAAM,KAAK,GAAG,EAAE;UAClBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,YAAY;QACzB,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,eAAe;QAC5B;MACF;MACAlE,OAAO,CAACgE,KAAK,CAACE,QAAQ,CAAC;MACvBxC,QAAQ,CAAC,EAAE,CAAC;MACZM,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMtC,WAAW,CAACoD,iBAAiB,CAAC,CAAC;MACtD,IAAId,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMa,OAAO,GAAGf,QAAQ,CAACI,IAAW;QACpC,MAAMY,cAAc,GAAGD,OAAO,CAACV,IAAI,IAAIU,OAAO,IAAI,EAAE;QACpD,MAAME,oBAAoB,GAAGD,cAAc,CAACV,GAAG,CAAEY,UAAe,KAAM;UACpEC,EAAE,EAAED,UAAU,CAACC,EAAE;UACjBC,IAAI,EAAEF,UAAU,CAACE,IAAI;UACrBC,IAAI,EAAEH,UAAU,CAACG,IAAI;UACrBC,IAAI,EAAEJ,UAAU,CAACI,IAAI,IAAI,MAAM;UAC/BC,QAAQ,EAAEL,UAAU,CAACM,SAAS;UAC9BC,IAAI,EAAEP,UAAU,CAACO,IAAI;UACrBC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,cAAc,GAAGC,mBAAmB,CAACX,oBAAoB,CAAC;QAChE5C,cAAc,CAACsD,cAAc,CAAC;MAChC;IACF,CAAC,CAAC,OAAOlB,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMmB,mBAAmB,GAAIxD,WAAkB,IAAmB;IAChE,MAAMkC,GAAG,GAAG,IAAIuB,GAAG,CAAC,CAAC;IACrB,MAAMC,KAAmB,GAAG,EAAE;;IAE9B;IACA1D,WAAW,CAAC2D,OAAO,CAACb,UAAU,IAAI;MAChCZ,GAAG,CAAC0B,GAAG,CAACd,UAAU,CAACC,EAAE,EAAE;QAAE,GAAGD,UAAU;QAAEQ,QAAQ,EAAE;MAAG,CAAC,CAAC;IACzD,CAAC,CAAC;;IAEF;IACAtD,WAAW,CAAC2D,OAAO,CAACb,UAAU,IAAI;MAChC,MAAMe,IAAI,GAAG3B,GAAG,CAAC4B,GAAG,CAAChB,UAAU,CAACC,EAAE,CAAC;MACnC,IAAID,UAAU,CAACK,QAAQ,IAAIjB,GAAG,CAAC6B,GAAG,CAACjB,UAAU,CAACK,QAAQ,CAAC,EAAE;QACvDjB,GAAG,CAAC4B,GAAG,CAAChB,UAAU,CAACK,QAAQ,CAAC,CAACG,QAAQ,CAACU,IAAI,CAACH,IAAI,CAAC;MAClD,CAAC,MAAM;QACLH,KAAK,CAACM,IAAI,CAACH,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,OAAOH,KAAK;EACd,CAAC;;EAED;EACA7F,SAAS,CAAC,MAAM;IACd8D,UAAU,CAAC,CAAC;IACZc,gBAAgB,CAAC,CAAC;IACpB;EACA,CAAC,EAAE,CAACnC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM2D,YAAY,GAAIC,MAAW,IAAK;IACpC3D,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAG4D,MAAM;MACT1D,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2D,WAAW,GAAGA,CAAA,KAAM;IACxBzC,UAAU,CAAC0C,WAAW,CAAC,CAAC;IACxB7D,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4D,SAAS,GAAGA,CAAA,KAAM;IACtBtD,cAAc,CAAC,IAAI,CAAC;IACpBS,IAAI,CAAC4C,WAAW,CAAC,CAAC;IAClBzD,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM2D,UAAU,GAAInC,IAAU,IAAK;IACjCpB,cAAc,CAACoB,IAAI,CAAC;IACpBX,IAAI,CAAC+C,cAAc,CAAC;MAClB,GAAGpC,IAAI;MACPK,MAAM,EAAEL,IAAI,CAACK,MAAM,KAAK;IAC1B,CAAC,CAAC;IACF7B,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM6D,YAAY,GAAG,MAAOzB,EAAU,IAAK;IACzC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMtC,WAAW,CAACmF,UAAU,CAAC1B,EAAE,CAAC;MACjD,IAAInB,QAAQ,CAACE,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAC,MAAM,CAAC;QACvBH,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLtD,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMqG,UAAU,GAAG,MAAOR,MAAW,IAAK;IACxC,IAAI;MACF,MAAMS,QAAQ,GAAG;QACf,GAAGT,MAAM;QACT1B,MAAM,EAAE0B,MAAM,CAAC1B,MAAM,GAAG,CAAC,GAAG;MAC9B,CAAC;MAED,IAAIZ,QAAQ;MACZ,IAAId,WAAW,EAAE;QACfc,QAAQ,GAAG,MAAMtC,WAAW,CAACsF,UAAU,CAAC9D,WAAW,CAACiC,EAAE,EAAE4B,QAAQ,CAAC;MACnE,CAAC,MAAM;QACL/C,QAAQ,GAAG,MAAMtC,WAAW,CAACuF,UAAU,CAACF,QAAQ,CAAC;MACnD;MAEA,IAAI/C,QAAQ,CAACE,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAChB,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC;QAC9CH,iBAAiB,CAAC,KAAK,CAAC;QACxBgB,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLtD,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMyG,uBAAuB,GAAI3C,IAAU,IAAK;IAC9ClB,eAAe,CAACkB,IAAI,CAAC;IACrBhB,qBAAqB,CAACgB,IAAI,CAACnC,WAAW,IAAI,EAAE,CAAC;IAC7Ca,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;;EAED;EACA,MAAMkE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAAC/D,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMtC,WAAW,CAAC0F,qBAAqB,CACtDhE,YAAY,CAAC+B,EAAE,EACf7B,kBACF,CAAC;MACD,IAAIU,QAAQ,CAACE,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAC,QAAQ,CAAC;QACzBjB,2BAA2B,CAAC,KAAK,CAAC;QAClCc,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLtD,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAM4G,4BAA4B,GAAIjF,WAAyB,IAAiB;IAC9E,OAAOA,WAAW,CAACkC,GAAG,CAACY,UAAU,KAAK;MACpCoC,KAAK,EAAEpC,UAAU,CAACE,IAAI;MACtBmC,GAAG,EAAErC,UAAU,CAACC,EAAE;MAClBO,QAAQ,EAAER,UAAU,CAACQ,QAAQ,GAAG2B,4BAA4B,CAACnC,UAAU,CAACQ,QAAQ,CAAC,GAAG8B;IACtF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,WAA6E,IAAK;IAC/G,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;MAC9BnE,qBAAqB,CAACmE,WAAuB,CAAC;IAChD,CAAC,MAAM;MACLnE,qBAAqB,CAACmE,WAAW,CAACG,OAAmB,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMC,OAA0B,GAAG,CACjC;IACER,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,IAAI;IACfR,GAAG,EAAE,IAAI;IACTS,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,MAAM;IACjBR,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,MAAM;IACjBR,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBAAKtG,OAAA,CAACZ,GAAG;MAACmH,KAAK,EAAC,MAAM;MAAAzC,QAAA,EAAEwC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACzD,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,aAAa;IACxBR,GAAG,EAAE,aAAa;IAClBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAKA,IAAI,IAAI;EACpC,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,WAAW;IACtBR,GAAG,EAAE,WAAW;IAChBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGO,KAAa,iBACpB5G,OAAA,CAACvB,KAAK;MAAAqF,QAAA,gBACJ9D,OAAA,CAACH,YAAY;QAAA2G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACfC,KAAK;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,QAAQ;IACnBR,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGrD,MAAc,iBACrBhD,OAAA,CAACZ,GAAG;MAACmH,KAAK,EAAEvD,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,KAAM;MAAAc,QAAA,EACxCd,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG;IAAI;MAAAwD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,WAAW;IACtBR,GAAG,EAAE,WAAW;IAChBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAK,IAAIO,IAAI,CAACP,IAAI,CAAC,CAACQ,cAAc,CAAC;EAC1D,CAAC,EACD;IACEpB,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,GAAG;IACVW,KAAK,EAAE,OAAO;IACdV,MAAM,EAAEA,CAACW,CAAC,EAAEC,MAAY,kBACtBjH,OAAA,CAACvB,KAAK;MAACyI,IAAI,EAAC,OAAO;MAAApD,QAAA,gBACjB9D,OAAA,CAACxB,MAAM;QACLkF,IAAI,EAAC,MAAM;QACXwD,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEnH,OAAA,CAACL,eAAe;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1BS,OAAO,EAAEA,CAAA,KAAM9B,uBAAuB,CAAC2B,MAAM,CAAE;QAAAnD,QAAA,EAChD;MAED;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3G,OAAA,CAACxB,MAAM;QACLkF,IAAI,EAAC,MAAM;QACXwD,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEnH,OAAA,CAACP,YAAY;UAAA+G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBS,OAAO,EAAEA,CAAA,KAAMtC,UAAU,CAACmC,MAAM,CAAE;QAAAnD,QAAA,EACnC;MAED;QAAA0C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT3G,OAAA,CAAClB,UAAU;QACT4G,KAAK,EAAC,oEAAa;QACnB2B,WAAW,EAAC,wGAAmB;QAC/BC,SAAS,EAAEA,CAAA,KAAMtC,YAAY,CAACiC,MAAM,CAAC1D,EAAE,CAAE;QACzCgE,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAA1D,QAAA,eAEf9D,OAAA,CAACxB,MAAM;UACLkF,IAAI,EAAC,MAAM;UACXwD,IAAI,EAAC,OAAO;UACZO,MAAM;UACNN,IAAI,eAAEnH,OAAA,CAACN,cAAc;YAAA8G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBe,QAAQ,EAAET,MAAM,CAACrE,SAAS,GAAG,CAAE;UAAAkB,QAAA,EAChC;QAED;UAAA0C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE3G,OAAA;IAAK2H,SAAS,EAAC,2BAA2B;IAAA7D,QAAA,gBACxC9D,OAAA,CAACC,KAAK;MAAC2H,KAAK,EAAE,CAAE;MAAA9D,QAAA,EAAC;IAAI;MAAA0C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7B3G,OAAA,CAAC1B,IAAI;MAACqJ,SAAS,EAAC,aAAa;MAACT,IAAI,EAAC,OAAO;MAAApD,QAAA,eACxC9D,OAAA,CAACpB,IAAI;QACHiJ,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAErD,YAAa;QACvBsD,YAAY,EAAC,KAAK;QAAAjE,QAAA,eAElB9D,OAAA,CAAChB,GAAG;UAACgJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACC,KAAK,EAAE;YAAE7B,KAAK,EAAE;UAAO,CAAE;UAAAtC,QAAA,gBAC9C9D,OAAA,CAACf,GAAG;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtE,QAAA,eACzB9D,OAAA,CAACpB,IAAI,CAACyJ,IAAI;cAAC7E,IAAI,EAAC,MAAM;cAAC8E,KAAK,EAAC,0BAAM;cAAAxE,QAAA,eACjC9D,OAAA,CAACtB,KAAK;gBAAC6J,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN3G,OAAA,CAACf,GAAG;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtE,QAAA,eACzB9D,OAAA,CAACpB,IAAI,CAACyJ,IAAI;cAAC7E,IAAI,EAAC,MAAM;cAAC8E,KAAK,EAAC,0BAAM;cAAAxE,QAAA,eACjC9D,OAAA,CAACtB,KAAK;gBAAC6J,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN3G,OAAA,CAACf,GAAG;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtE,QAAA,eACzB9D,OAAA,CAACpB,IAAI,CAACyJ,IAAI;cAAC7E,IAAI,EAAC,QAAQ;cAAC8E,KAAK,EAAC,cAAI;cAAAxE,QAAA,eACjC9D,OAAA,CAACV,MAAM;gBAACiJ,WAAW,EAAC,gCAAO;gBAACC,UAAU;gBAAA1E,QAAA,gBACpC9D,OAAA,CAACE,MAAM;kBAACuI,KAAK,EAAE,CAAE;kBAAA3E,QAAA,EAAC;gBAAE;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7B3G,OAAA,CAACE,MAAM;kBAACuI,KAAK,EAAE,CAAE;kBAAA3E,QAAA,EAAC;gBAAE;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN3G,OAAA,CAACf,GAAG;YAACiJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAtE,QAAA,eACzB9D,OAAA,CAACpB,IAAI,CAACyJ,IAAI;cAAAvE,QAAA,eACR9D,OAAA,CAACvB,KAAK;gBAAAqF,QAAA,gBACJ9D,OAAA,CAACxB,MAAM;kBAACkF,IAAI,EAAC,SAAS;kBAACgF,QAAQ,EAAC,QAAQ;kBAACvB,IAAI,eAAEnH,OAAA,CAACR,cAAc;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAA7C,QAAA,EAAC;gBAEnE;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT3G,OAAA,CAACxB,MAAM;kBAAC4I,OAAO,EAAEzC,WAAY;kBAACwC,IAAI,eAAEnH,OAAA,CAACJ,cAAc;oBAAA4G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAA7C,QAAA,EAAC;gBAExD;kBAAA0C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP3G,OAAA,CAAC1B,IAAI;MAACqJ,SAAS,EAAC,aAAa;MAACT,IAAI,EAAC,OAAO;MAAApD,QAAA,eACxC9D,OAAA,CAAChB,GAAG;QAAC2J,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAA9E,QAAA,gBACzC9D,OAAA,CAACf,GAAG;UAAA6E,QAAA,eACF9D,OAAA,CAACxB,MAAM;YACLkF,IAAI,EAAC,SAAS;YACdyD,IAAI,eAAEnH,OAAA,CAACT,YAAY;cAAAiH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAEvC,SAAU;YAAAf,QAAA,EACpB;UAED;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN3G,OAAA,CAACf,GAAG;UAAA6E,QAAA,eACF9D,OAAA,CAACxB,MAAM;YACL2I,IAAI,eAAEnH,OAAA,CAACJ,cAAc;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBS,OAAO,EAAEjF,UAAW;YACpBzB,OAAO,EAAEA,OAAQ;YAAAoD,QAAA,EAClB;UAED;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP3G,OAAA,CAAC1B,IAAI;MAAAwF,QAAA,eACH9D,OAAA,CAACzB,KAAK;QACJ2H,OAAO,EAAEA,OAAQ;QACjB2C,UAAU,EAAEvI,KAAM;QAClBwI,MAAM,EAAC,IAAI;QACXpI,OAAO,EAAEA,OAAQ;QACjBqI,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAEpI,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZuI,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACzI,KAAK,EAAE0I,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ1I,KAAK,IAAI;UAC5C2I,QAAQ,EAAEA,CAACvI,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAAuF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP3G,OAAA,CAACrB,KAAK;MACJ+G,KAAK,EAAEpE,WAAW,GAAG,MAAM,GAAG,MAAO;MACrCkI,IAAI,EAAEtI,cAAe;MACrBuI,QAAQ,EAAEA,CAAA,KAAMtI,iBAAiB,CAAC,KAAK,CAAE;MACzCuI,MAAM,EAAE,IAAK;MACbtD,KAAK,EAAE,GAAI;MACXuD,cAAc;MAAA7F,QAAA,eAEd9D,OAAA,CAACpB,IAAI;QACHoD,IAAI,EAAEA,IAAK;QACX6F,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE5C,UAAW;QACrB6C,YAAY,EAAC,KAAK;QAAAjE,QAAA,gBAElB9D,OAAA,CAAChB,GAAG;UAACgJ,MAAM,EAAE,EAAG;UAAAlE,QAAA,gBACd9D,OAAA,CAACf,GAAG;YAAC2K,IAAI,EAAE,EAAG;YAAA9F,QAAA,eACZ9D,OAAA,CAACpB,IAAI,CAACyJ,IAAI;cACR7E,IAAI,EAAC,MAAM;cACX8E,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEkL,GAAG,EAAE,EAAE;gBAAElL,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAAiF,QAAA,eAEF9D,OAAA,CAACtB,KAAK;gBAAC6J,WAAW,EAAC;cAAS;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN3G,OAAA,CAACf,GAAG;YAAC2K,IAAI,EAAE,EAAG;YAAA9F,QAAA,eACZ9D,OAAA,CAACpB,IAAI,CAACyJ,IAAI;cACR7E,IAAI,EAAC,MAAM;cACX8E,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEmL,OAAO,EAAE,WAAW;gBAAEnL,OAAO,EAAE;cAAmB,CAAC,EACrD;gBAAEkL,GAAG,EAAE,EAAE;gBAAElL,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAAiF,QAAA,eAEF9D,OAAA,CAACtB,KAAK;gBAAC6J,WAAW,EAAC,4CAAS;gBAACb,QAAQ,EAAE,CAAC,CAACpG;cAAY;gBAAAkF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN3G,OAAA,CAACpB,IAAI,CAACyJ,IAAI;UACR7E,IAAI,EAAC,aAAa;UAClB8E,KAAK,EAAC,0BAAM;UACZuB,KAAK,EAAE,CACL;YAAEE,GAAG,EAAE,GAAG;YAAElL,OAAO,EAAE;UAAiB,CAAC,CACvC;UAAAiF,QAAA,eAEF9D,OAAA,CAACG,QAAQ;YACPoI,WAAW,EAAC,4CAAS;YACrB0B,IAAI,EAAE,CAAE;YACRC,SAAS;YACTC,SAAS,EAAE;UAAI;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ3G,OAAA,CAACpB,IAAI,CAACyJ,IAAI;UACR7E,IAAI,EAAC,QAAQ;UACb8E,KAAK,EAAC,cAAI;UACV8B,aAAa,EAAC,SAAS;UAAAtG,QAAA,eAEvB9D,OAAA,CAACb,MAAM;YAACkL,eAAe,EAAC,cAAI;YAACC,iBAAiB,EAAC;UAAI;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEZ3G,OAAA,CAACpB,IAAI,CAACyJ,IAAI;UAAAvE,QAAA,eACR9D,OAAA,CAACvB,KAAK;YAACwJ,KAAK,EAAE;cAAE7B,KAAK,EAAE,MAAM;cAAEmE,cAAc,EAAE;YAAW,CAAE;YAAAzG,QAAA,gBAC1D9D,OAAA,CAACxB,MAAM;cAAC4I,OAAO,EAAEA,CAAA,KAAMjG,iBAAiB,CAAC,KAAK,CAAE;cAAA2C,QAAA,EAAC;YAEjD;cAAA0C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT3G,OAAA,CAACxB,MAAM;cAACkF,IAAI,EAAC,SAAS;cAACgF,QAAQ,EAAC,QAAQ;cAAA5E,QAAA,EACrCxC,WAAW,GAAG,IAAI,GAAG;YAAI;cAAAkF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR3G,OAAA,CAACrB,KAAK;MACJ+G,KAAK,EAAE,UAAUlE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgC,IAAI,EAAG;MACtCgG,IAAI,EAAEpI,wBAAyB;MAC/BqI,QAAQ,EAAEA,CAAA,KAAMpI,2BAA2B,CAAC,KAAK,CAAE;MACnDmJ,IAAI,EAAEjF,qBAAsB;MAC5Ba,KAAK,EAAE,GAAI;MACXuD,cAAc;MAAA7F,QAAA,gBAEd9D,OAAA;QAAKiI,KAAK,EAAE;UAAEwC,YAAY,EAAE;QAAG,CAAE;QAAA3G,QAAA,eAC/B9D,OAAA,CAACX,YAAY;UAAC6H,IAAI,EAAC,OAAO;UAACwD,MAAM,EAAE,CAAE;UAAA5G,QAAA,gBACnC9D,OAAA,CAACX,YAAY,CAACgJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAxE,QAAA,EAAEtC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgC;UAAI;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACxE3G,OAAA,CAACX,YAAY,CAACgJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAxE,QAAA,EAAEtC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiC;UAAI;YAAA+C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACxE3G,OAAA,CAACX,YAAY,CAACgJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAAxE,QAAA,EAAEtC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoB;UAAS;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC7E3G,OAAA,CAACX,YAAY,CAACgJ,IAAI;YAACC,KAAK,EAAC,sCAAQ;YAAAxE,QAAA,EAAEpC,kBAAkB,CAACiJ;UAAM;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEN3G,OAAA;QAAKiI,KAAK,EAAE;UAAE2C,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE,CAAC;UAAEC,OAAO,EAAE,EAAE;UAAEC,SAAS,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAlH,QAAA,eAC1G9D,OAAA,CAACd,IAAI;UACH+L,SAAS;UACTnF,WAAW,EAAEpE,kBAAmB;UAChCE,YAAY,EAAEA,YAAa;UAC3BsJ,OAAO,EAAErF,qBAAsB;UAC/BsF,QAAQ,EAAEtJ,eAAgB;UAC1BuJ,QAAQ,EAAE3F,4BAA4B,CAACjF,WAAW,CAAE;UACpD6K,MAAM,EAAE;QAAI;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACtG,EAAA,CAtiBID,cAAwB;EAAA,QAgBbxB,IAAI,CAACqD,OAAO,EACNrD,IAAI,CAACqD,OAAO;AAAA;AAAAqJ,EAAA,GAjB7BlL,cAAwB;AAwiB9B,eAAeA,cAAc;AAAC,IAAAkL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}