{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Typography, Row, Col, DatePicker, Statistic, Badge } from 'antd';\nimport { PlusOutlined, SearchOutlined, EyeOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined, PauseCircleOutlined, StopOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { auctionService } from '../../../services/auctionService';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\n\n// 拍卖会状态枚举\nexport let AuctionStatus = /*#__PURE__*/function (AuctionStatus) {\n  AuctionStatus[AuctionStatus[\"DRAFT\"] = 1] = \"DRAFT\";\n  // 草稿\n  AuctionStatus[AuctionStatus[\"SCHEDULED\"] = 2] = \"SCHEDULED\";\n  // 已安排\n  AuctionStatus[AuctionStatus[\"ONGOING\"] = 3] = \"ONGOING\";\n  // 进行中\n  AuctionStatus[AuctionStatus[\"PAUSED\"] = 4] = \"PAUSED\";\n  // 已暂停\n  AuctionStatus[AuctionStatus[\"COMPLETED\"] = 5] = \"COMPLETED\";\n  // 已完成\n  AuctionStatus[AuctionStatus[\"CANCELLED\"] = 6] = \"CANCELLED\"; // 已取消\n  return AuctionStatus;\n}({});\n\n// 拍卖会数据接口\n\n// 查询参数接口\n\nconst AuctionList = () => {\n  _s();\n  const [auctions, setAuctions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingAuction, setEditingAuction] = useState(null);\n  const [saving, setSaving] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalAuctions: 0,\n    ongoingAuctions: 0,\n    todayAuctions: 0,\n    totalParticipants: 0\n  });\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 拍卖会状态映射\n  const auctionStatusMap = {\n    [AuctionStatus.DRAFT]: {\n      label: '草稿',\n      color: 'default'\n    },\n    [AuctionStatus.SCHEDULED]: {\n      label: '已安排',\n      color: 'blue'\n    },\n    [AuctionStatus.ONGOING]: {\n      label: '进行中',\n      color: 'green'\n    },\n    [AuctionStatus.PAUSED]: {\n      label: '已暂停',\n      color: 'orange'\n    },\n    [AuctionStatus.COMPLETED]: {\n      label: '已完成',\n      color: 'purple'\n    },\n    [AuctionStatus.CANCELLED]: {\n      label: '已取消',\n      color: 'red'\n    }\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    setLoading(true);\n    try {\n      const response = await auctionService.getAuctionList(queryParams);\n      if (response.success) {\n        setAuctions(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取拍卖会列表失败');\n        setAuctions([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      console.error('获取拍卖会列表失败:', error);\n      let errorMsg = '获取拍卖会列表失败';\n      if (error.response) {\n        const {\n          status\n        } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问拍卖会列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setAuctions([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取拍卖会统计\n  const fetchStatistics = async () => {\n    try {\n      const response = await auctionService.getAuctionStatistics();\n      if (response.success) {\n        setStatistics(response.data);\n      }\n    } catch (error) {\n      console.error('获取拍卖会统计失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchAuctions();\n    fetchStatistics();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增拍卖会\n  const handleAdd = () => {\n    setEditingAuction(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑拍卖会\n  const handleEdit = auction => {\n    setEditingAuction(auction);\n    form.setFieldsValue({\n      ...auction,\n      timeRange: [new Date(auction.startTime), new Date(auction.endTime)]\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖会\n  const handleDelete = async id => {\n    try {\n      const response = await auctionService.deleteAuction(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖会\n  const handleSave = async values => {\n    setSaving(true);\n    try {\n      const auctionData = {\n        ...values,\n        startTime: values.timeRange[0].toISOString(),\n        endTime: values.timeRange[1].toISOString()\n      };\n      delete auctionData.timeRange;\n      let response;\n      if (editingAuction) {\n        response = await auctionService.updateAuction(editingAuction.id, auctionData);\n      } else {\n        response = await auctionService.createAuction(auctionData);\n      }\n      if (response.success) {\n        const successMsg = editingAuction ? '拍卖会信息更新成功！' : '拍卖会创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3\n        });\n        setIsModalVisible(false);\n        form.resetFields();\n        setEditingAuction(null);\n        fetchAuctions();\n        fetchStatistics(); // 刷新统计数据\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('title')) {\n            errorMsg = '拍卖会标题已存在，请使用其他标题';\n          } else if (response.message.includes('time')) {\n            errorMsg = '拍卖时间设置不正确，请检查时间范围';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n        message.error({\n          content: errorMsg,\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('保存拍卖会失败:', error);\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const {\n          status,\n          data\n        } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('title')) {\n            errorMsg = '拍卖会标题格式不正确或已存在';\n          } else if (data.error && data.error.includes('time')) {\n            errorMsg = '拍卖时间设置无效，请重新选择';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '拍卖会信息冲突，标题可能已存在';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n      message.error({\n        content: errorMsg,\n        duration: 5\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 开始拍卖\n  const handleStart = async id => {\n    try {\n      const response = await auctionService.startAuction(id);\n      if (response.success) {\n        message.success('拍卖会已开始');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '开始拍卖失败');\n      }\n    } catch (error) {\n      message.error(error.message || '开始拍卖失败');\n    }\n  };\n\n  // 暂停拍卖\n  const handlePause = async id => {\n    try {\n      const response = await auctionService.pauseAuction(id);\n      if (response.success) {\n        message.success('拍卖会已暂停');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '暂停拍卖失败');\n      }\n    } catch (error) {\n      message.error(error.message || '暂停拍卖失败');\n    }\n  };\n\n  // 结束拍卖\n  const handleEnd = async id => {\n    try {\n      const response = await auctionService.endAuction(id);\n      if (response.success) {\n        message.success('拍卖会已结束');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '结束拍卖失败');\n      }\n    } catch (error) {\n      message.error(error.message || '结束拍卖失败');\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '拍卖会标题',\n    dataIndex: 'title',\n    key: 'title',\n    width: 200,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontWeight: 500\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 353,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '拍卖状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => {\n      const statusInfo = auctionStatusMap[status];\n      return /*#__PURE__*/_jsxDEV(Badge, {\n        status: status === AuctionStatus.ONGOING ? 'processing' : status === AuctionStatus.COMPLETED ? 'success' : status === AuctionStatus.CANCELLED ? 'error' : 'default',\n        text: /*#__PURE__*/_jsxDEV(Tag, {\n          color: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || 'default',\n          children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.label) || '未知'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 364,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '商品数量',\n    dataIndex: 'totalItems',\n    key: 'totalItems',\n    width: 100,\n    render: (total, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: [total, \"\\u4EF6\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 12,\n          color: '#999'\n        },\n        children: [\"\\u5DF2\\u552E: \", record.soldItems]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '成交金额',\n    dataIndex: 'totalAmount',\n    key: 'totalAmount',\n    width: 120,\n    render: amount => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontWeight: 500,\n        color: '#f50'\n      },\n      children: [\"\\xA5\", amount.toFixed(2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '参与人数',\n    dataIndex: 'participantCount',\n    key: 'participantCount',\n    width: 100\n  }, {\n    title: '创建人',\n    dataIndex: 'creatorName',\n    key: 'creatorName',\n    width: 100\n  }, {\n    title: '拍卖时间',\n    dataIndex: 'startTime',\n    key: 'startTime',\n    width: 160,\n    render: (text, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        children: new Date(text).toLocaleString()\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 423,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: 12,\n          color: '#999'\n        },\n        children: [\"\\u81F3 \", new Date(record.endTime).toLocaleString()]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 424,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 422,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 440,\n          columnNumber: 19\n        }, this),\n        onClick: () => {/* 查看详情 */},\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 437,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 448,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 11\n      }, this), record.status === AuctionStatus.SCHEDULED && /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 21\n        }, this),\n        onClick: () => handleStart(record.id),\n        children: \"\\u5F00\\u59CB\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 454,\n        columnNumber: 13\n      }, this), record.status === AuctionStatus.ONGOING && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(PauseCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 23\n          }, this),\n          onClick: () => handlePause(record.id),\n          children: \"\\u6682\\u505C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 476,\n            columnNumber: 23\n          }, this),\n          onClick: () => handleEnd(record.id),\n          children: \"\\u7ED3\\u675F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 473,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        danger: true,\n        icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleDelete(record.id),\n        children: \"\\u5220\\u9664\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 483,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 436,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"auction-list-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u62CD\\u5356\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 499,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u62CD\\u5356\\u4F1A\",\n            value: statistics.totalAuctions,\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 505,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 504,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8FDB\\u884C\\u4E2D\",\n            value: statistics.ongoingAuctions,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 514,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 513,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u4ECA\\u65E5\\u62CD\\u5356\",\n            value: statistics.todayAuctions,\n            valueStyle: {\n              color: '#cf1322'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 522,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 521,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u53C2\\u4E0E\\u4EBA\\u6570\",\n            value: statistics.totalParticipants,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 532,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 531,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 530,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 502,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"title\",\n              label: \"\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 551,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 550,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 549,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u62CD\\u5356\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.DRAFT,\n                  children: \"\\u8349\\u7A3F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 557,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.SCHEDULED,\n                  children: \"\\u5DF2\\u5B89\\u6392\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 558,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.ONGOING,\n                  children: \"\\u8FDB\\u884C\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.PAUSED,\n                  children: \"\\u5DF2\\u6682\\u505C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 560,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.COMPLETED,\n                  children: \"\\u5DF2\\u5B8C\\u6210\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 561,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionStatus.CANCELLED,\n                  children: \"\\u5DF2\\u53D6\\u6D88\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 562,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 556,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 555,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"creatorName\",\n              label: \"\\u521B\\u5EFA\\u4EBA\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u521B\\u5EFA\\u4EBA\\u59D3\\u540D\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 568,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dateRange\",\n              label: \"\\u62CD\\u5356\\u65F6\\u95F4\",\n              children: /*#__PURE__*/_jsxDEV(RangePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 573,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 571,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 579,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 582,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 582,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 578,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 577,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 576,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 548,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 543,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 542,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u65B0\\u589E\\u62CD\\u5356\\u4F1A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 596,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 21\n            }, this),\n            onClick: fetchAuctions,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 605,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 604,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 594,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 593,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: auctions,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 618,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 617,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingAuction ? '编辑拍卖会' : '新增拍卖会',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"title\",\n          label: \"\\u62CD\\u5356\\u4F1A\\u6807\\u9898\",\n          rules: [{\n            required: true,\n            message: '请输入拍卖会标题'\n          }, {\n            max: 100,\n            message: '标题不能超过100个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u6807\\u9898\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 665,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 657,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u62CD\\u5356\\u4F1A\\u63CF\\u8FF0\",\n          rules: [{\n            max: 500,\n            message: '描述不能超过500个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input.TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u4F1A\\u63CF\\u8FF0\",\n            rows: 4,\n            showCount: true,\n            maxLength: 500\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 675,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 668,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"timeRange\",\n          label: \"\\u62CD\\u5356\\u65F6\\u95F4\",\n          rules: [{\n            required: true,\n            message: '请选择拍卖时间'\n          }],\n          children: /*#__PURE__*/_jsxDEV(RangePicker, {\n            showTime: true,\n            style: {\n              width: '100%'\n            },\n            placeholder: ['开始时间', '结束时间']\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 688,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 683,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"location\",\n          label: \"\\u62CD\\u5356\\u5730\\u70B9\",\n          rules: [{\n            required: true,\n            message: '请输入拍卖地点'\n          }, {\n            max: 200,\n            message: '地点不能超过200个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Input, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u62CD\\u5356\\u5730\\u70B9\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 703,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 695,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 708,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingAuction ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 711,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 707,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 706,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 651,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 644,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 498,\n    columnNumber: 5\n  }, this);\n};\n_s(AuctionList, \"8/Er0jK6nksOWiR2sg2wbwoDNLU=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = AuctionList;\nexport default AuctionList;\nvar _c;\n$RefreshReg$(_c, \"AuctionList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Typography", "Row", "Col", "DatePicker", "Statistic", "Badge", "PlusOutlined", "SearchOutlined", "EyeOutlined", "EditOutlined", "DeleteOutlined", "PlayCircleOutlined", "PauseCircleOutlined", "StopOutlined", "ReloadOutlined", "auctionService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Title", "Option", "RangePicker", "AuctionStatus", "AuctionList", "_s", "auctions", "setAuctions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "editingAuction", "setEditingAuction", "saving", "setSaving", "statistics", "setStatistics", "totalAuctions", "ongoingAuctions", "todayAuctions", "totalParticipants", "form", "useForm", "searchForm", "auctionStatusMap", "DRAFT", "label", "color", "SCHEDULED", "ONGOING", "PAUSED", "COMPLETED", "CANCELLED", "fetchAuctions", "response", "getAuctionList", "success", "data", "list", "error", "console", "errorMsg", "status", "fetchStatistics", "getAuctionStatistics", "handleSearch", "values", "handleReset", "resetFields", "handleAdd", "handleEdit", "auction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "timeRange", "Date", "startTime", "endTime", "handleDelete", "id", "deleteAuction", "handleSave", "auctionData", "toISOString", "updateAuction", "createAuction", "successMsg", "content", "duration", "includes", "handleStart", "startAuction", "handlePause", "pauseAuction", "handleEnd", "endAuction", "columns", "title", "dataIndex", "key", "width", "render", "text", "style", "fontWeight", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "statusInfo", "record", "fontSize", "soldItems", "amount", "toFixed", "toLocaleString", "fixed", "_", "size", "type", "icon", "onClick", "danger", "className", "level", "gutter", "marginBottom", "xs", "sm", "md", "value", "valueStyle", "layout", "onFinish", "autoComplete", "<PERSON><PERSON>", "name", "placeholder", "allowClear", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "open", "onCancel", "footer", "rules", "required", "max", "TextArea", "rows", "showCount", "max<PERSON><PERSON><PERSON>", "showTime", "justifyContent", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Typography,\n  Row,\n  Col,\n  DatePicker,\n  Statistic,\n  Progress,\n  Badge,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EyeOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { auctionService } from '../../../services/auctionService';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\n// 拍卖会状态枚举\nexport enum AuctionStatus {\n  DRAFT = 1,        // 草稿\n  SCHEDULED = 2,    // 已安排\n  ONGOING = 3,      // 进行中\n  PAUSED = 4,       // 已暂停\n  COMPLETED = 5,    // 已完成\n  CANCELLED = 6,    // 已取消\n}\n\n// 拍卖会数据接口\nexport interface Auction {\n  id: number;\n  title: string;\n  description?: string;\n  startTime: string;\n  endTime: string;\n  status: AuctionStatus;\n  totalItems: number;\n  soldItems: number;\n  totalAmount: number;\n  participantCount: number;\n  creatorName: string;\n  location: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数接口\ninterface AuctionQueryParams {\n  title?: string;\n  status?: AuctionStatus;\n  creatorName?: string;\n  dateRange?: [string, string];\n  page: number;\n  pageSize: number;\n}\n\nconst AuctionList: React.FC = () => {\n  const [auctions, setAuctions] = useState<Auction[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<AuctionQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingAuction, setEditingAuction] = useState<Auction | null>(null);\n  const [saving, setSaving] = useState(false);\n  const [statistics, setStatistics] = useState({\n    totalAuctions: 0,\n    ongoingAuctions: 0,\n    todayAuctions: 0,\n    totalParticipants: 0,\n  });\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 拍卖会状态映射\n  const auctionStatusMap = {\n    [AuctionStatus.DRAFT]: { label: '草稿', color: 'default' },\n    [AuctionStatus.SCHEDULED]: { label: '已安排', color: 'blue' },\n    [AuctionStatus.ONGOING]: { label: '进行中', color: 'green' },\n    [AuctionStatus.PAUSED]: { label: '已暂停', color: 'orange' },\n    [AuctionStatus.COMPLETED]: { label: '已完成', color: 'purple' },\n    [AuctionStatus.CANCELLED]: { label: '已取消', color: 'red' },\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    setLoading(true);\n    try {\n      const response = await auctionService.getAuctionList(queryParams);\n      if (response.success) {\n        setAuctions(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取拍卖会列表失败');\n        setAuctions([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会列表失败:', error);\n      let errorMsg = '获取拍卖会列表失败';\n      if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问拍卖会列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setAuctions([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取拍卖会统计\n  const fetchStatistics = async () => {\n    try {\n      const response = await auctionService.getAuctionStatistics();\n      if (response.success) {\n        setStatistics(response.data);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会统计失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchAuctions();\n    fetchStatistics();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增拍卖会\n  const handleAdd = () => {\n    setEditingAuction(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑拍卖会\n  const handleEdit = (auction: Auction) => {\n    setEditingAuction(auction);\n    form.setFieldsValue({\n      ...auction,\n      timeRange: [new Date(auction.startTime), new Date(auction.endTime)],\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖会\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await auctionService.deleteAuction(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖会\n  const handleSave = async (values: any) => {\n    setSaving(true);\n\n    try {\n      const auctionData = {\n        ...values,\n        startTime: values.timeRange[0].toISOString(),\n        endTime: values.timeRange[1].toISOString(),\n      };\n      delete auctionData.timeRange;\n\n      let response;\n      if (editingAuction) {\n        response = await auctionService.updateAuction(editingAuction.id, auctionData);\n      } else {\n        response = await auctionService.createAuction(auctionData);\n      }\n\n      if (response.success) {\n        const successMsg = editingAuction ? '拍卖会信息更新成功！' : '拍卖会创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3,\n        });\n        setIsModalVisible(false);\n        form.resetFields();\n        setEditingAuction(null);\n        fetchAuctions();\n        fetchStatistics(); // 刷新统计数据\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('title')) {\n            errorMsg = '拍卖会标题已存在，请使用其他标题';\n          } else if (response.message.includes('time')) {\n            errorMsg = '拍卖时间设置不正确，请检查时间范围';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n\n        message.error({\n          content: errorMsg,\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('保存拍卖会失败:', error);\n\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const { status, data } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('title')) {\n            errorMsg = '拍卖会标题格式不正确或已存在';\n          } else if (data.error && data.error.includes('time')) {\n            errorMsg = '拍卖时间设置无效，请重新选择';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '拍卖会信息冲突，标题可能已存在';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n\n      message.error({\n        content: errorMsg,\n        duration: 5,\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 开始拍卖\n  const handleStart = async (id: number) => {\n    try {\n      const response = await auctionService.startAuction(id);\n      if (response.success) {\n        message.success('拍卖会已开始');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '开始拍卖失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '开始拍卖失败');\n    }\n  };\n\n  // 暂停拍卖\n  const handlePause = async (id: number) => {\n    try {\n      const response = await auctionService.pauseAuction(id);\n      if (response.success) {\n        message.success('拍卖会已暂停');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '暂停拍卖失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '暂停拍卖失败');\n    }\n  };\n\n  // 结束拍卖\n  const handleEnd = async (id: number) => {\n    try {\n      const response = await auctionService.endAuction(id);\n      if (response.success) {\n        message.success('拍卖会已结束');\n        fetchAuctions();\n      } else {\n        message.error(response.message || '结束拍卖失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '结束拍卖失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Auction> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '拍卖会标题',\n      dataIndex: 'title',\n      key: 'title',\n      width: 200,\n      render: (text: string) => (\n        <div style={{ fontWeight: 500 }}>{text}</div>\n      ),\n    },\n    {\n      title: '拍卖状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: AuctionStatus) => {\n        const statusInfo = auctionStatusMap[status];\n        return (\n          <Badge\n            status={\n              status === AuctionStatus.ONGOING ? 'processing' :\n              status === AuctionStatus.COMPLETED ? 'success' :\n              status === AuctionStatus.CANCELLED ? 'error' : 'default'\n            }\n            text={\n              <Tag color={statusInfo?.color || 'default'}>\n                {statusInfo?.label || '未知'}\n              </Tag>\n            }\n          />\n        );\n      },\n    },\n    {\n      title: '商品数量',\n      dataIndex: 'totalItems',\n      key: 'totalItems',\n      width: 100,\n      render: (total: number, record: Auction) => (\n        <div>\n          <div>{total}件</div>\n          <div style={{ fontSize: 12, color: '#999' }}>\n            已售: {record.soldItems}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '成交金额',\n      dataIndex: 'totalAmount',\n      key: 'totalAmount',\n      width: 120,\n      render: (amount: number) => (\n        <div style={{ fontWeight: 500, color: '#f50' }}>\n          ¥{amount.toFixed(2)}\n        </div>\n      ),\n    },\n    {\n      title: '参与人数',\n      dataIndex: 'participantCount',\n      key: 'participantCount',\n      width: 100,\n    },\n    {\n      title: '创建人',\n      dataIndex: 'creatorName',\n      key: 'creatorName',\n      width: 100,\n    },\n    {\n      title: '拍卖时间',\n      dataIndex: 'startTime',\n      key: 'startTime',\n      width: 160,\n      render: (text: string, record: Auction) => (\n        <div>\n          <div>{new Date(text).toLocaleString()}</div>\n          <div style={{ fontSize: 12, color: '#999' }}>\n            至 {new Date(record.endTime).toLocaleString()}\n          </div>\n        </div>\n      ),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right',\n      render: (_, record: Auction) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => {/* 查看详情 */}}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          {record.status === AuctionStatus.SCHEDULED && (\n            <Button\n              type=\"link\"\n              size=\"small\"\n              icon={<PlayCircleOutlined />}\n              onClick={() => handleStart(record.id)}\n            >\n              开始\n            </Button>\n          )}\n          {record.status === AuctionStatus.ONGOING && (\n            <>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<PauseCircleOutlined />}\n                onClick={() => handlePause(record.id)}\n              >\n                暂停\n              </Button>\n              <Button\n                type=\"link\"\n                size=\"small\"\n                icon={<StopOutlined />}\n                onClick={() => handleEnd(record.id)}\n              >\n                结束\n              </Button>\n            </>\n          )}\n          <Button\n            type=\"link\"\n            size=\"small\"\n            danger\n            icon={<DeleteOutlined />}\n            onClick={() => handleDelete(record.id)}\n          >\n            删除\n          </Button>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"auction-list-container\">\n      <Title level={2}>拍卖管理</Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总拍卖会\"\n              value={statistics.totalAuctions}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"进行中\"\n              value={statistics.ongoingAuctions}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"今日拍卖\"\n              value={statistics.todayAuctions}\n              valueStyle={{ color: '#cf1322' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总参与人数\"\n              value={statistics.totalParticipants}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"title\" label=\"拍卖会标题\">\n                <Input placeholder=\"请输入拍卖会标题\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"拍卖状态\">\n                <Select placeholder=\"请选择拍卖状态\" allowClear>\n                  <Option value={AuctionStatus.DRAFT}>草稿</Option>\n                  <Option value={AuctionStatus.SCHEDULED}>已安排</Option>\n                  <Option value={AuctionStatus.ONGOING}>进行中</Option>\n                  <Option value={AuctionStatus.PAUSED}>已暂停</Option>\n                  <Option value={AuctionStatus.COMPLETED}>已完成</Option>\n                  <Option value={AuctionStatus.CANCELLED}>已取消</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"creatorName\" label=\"创建人\">\n                <Input placeholder=\"请输入创建人姓名\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"dateRange\" label=\"拍卖时间\">\n                <RangePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={4}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              新增拍卖会\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchAuctions}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 拍卖会列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={auctions}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 拍卖会编辑模态框 */}\n      <Modal\n        title={editingAuction ? '编辑拍卖会' : '新增拍卖会'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Form.Item\n            name=\"title\"\n            label=\"拍卖会标题\"\n            rules={[\n              { required: true, message: '请输入拍卖会标题' },\n              { max: 100, message: '标题不能超过100个字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入拍卖会标题\" />\n          </Form.Item>\n\n          <Form.Item\n            name=\"description\"\n            label=\"拍卖会描述\"\n            rules={[\n              { max: 500, message: '描述不能超过500个字符' },\n            ]}\n          >\n            <Input.TextArea\n              placeholder=\"请输入拍卖会描述\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"timeRange\"\n            label=\"拍卖时间\"\n            rules={[{ required: true, message: '请选择拍卖时间' }]}\n          >\n            <RangePicker\n              showTime\n              style={{ width: '100%' }}\n              placeholder={['开始时间', '结束时间']}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"location\"\n            label=\"拍卖地点\"\n            rules={[\n              { required: true, message: '请输入拍卖地点' },\n              { max: 200, message: '地点不能超过200个字符' },\n            ]}\n          >\n            <Input placeholder=\"请输入拍卖地点\" />\n          </Form.Item>\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setIsModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingAuction ? '更新' : '创建'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AuctionList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,SAAS,EAETC,KAAK,QACA,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,YAAY,EACZC,cAAc,EACdC,kBAAkB,EAClBC,mBAAmB,EACnBC,YAAY,EACZC,cAAc,QACT,mBAAmB;AAE1B,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGpB,UAAU;AAC5B,MAAM;EAAEqB;AAAO,CAAC,GAAG1B,MAAM;AACzB,MAAM;EAAE2B;AAAY,CAAC,GAAGnB,UAAU;;AAElC;AACA,WAAYoB,aAAa,0BAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EACL;EADRA,aAAa,CAAbA,aAAa;EAEL;EAFRA,aAAa,CAAbA,aAAa;EAGL;EAHRA,aAAa,CAAbA,aAAa;EAIL;EAJRA,aAAa,CAAbA,aAAa;EAKL;EALRA,aAAa,CAAbA,aAAa,kCAML;EAAA,OANRA,aAAa;AAAA;;AASzB;;AAkBA;;AAUA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACwC,OAAO,EAAEC,UAAU,CAAC,GAAGzC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0C,KAAK,EAAEC,QAAQ,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC4C,WAAW,EAAEC,cAAc,CAAC,GAAG7C,QAAQ,CAAqB;IACjE8C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACkD,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACoD,MAAM,EAAEC,SAAS,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC;IAC3CwD,aAAa,EAAE,CAAC;IAChBC,eAAe,EAAE,CAAC;IAClBC,aAAa,EAAE,CAAC;IAChBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,CAAC,GAAGlD,IAAI,CAACmD,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAGpD,IAAI,CAACmD,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAME,gBAAgB,GAAG;IACvB,CAAC5B,aAAa,CAAC6B,KAAK,GAAG;MAAEC,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAU,CAAC;IACxD,CAAC/B,aAAa,CAACgC,SAAS,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC1D,CAAC/B,aAAa,CAACiC,OAAO,GAAG;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACzD,CAAC/B,aAAa,CAACkC,MAAM,GAAG;MAAEJ,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS,CAAC;IACzD,CAAC/B,aAAa,CAACmC,SAAS,GAAG;MAAEL,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC5D,CAAC/B,aAAa,CAACoC,SAAS,GAAG;MAAEN,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM;EAC1D,CAAC;;EAED;EACA,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC/B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMgC,QAAQ,GAAG,MAAM9C,cAAc,CAAC+C,cAAc,CAAC9B,WAAW,CAAC;MACjE,IAAI6B,QAAQ,CAACE,OAAO,EAAE;QACpBpC,WAAW,CAACkC,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;QAC/BlC,QAAQ,CAAC8B,QAAQ,CAACG,IAAI,CAAClC,KAAK,CAAC;MAC/B,CAAC,MAAM;QACL/B,OAAO,CAACmE,KAAK,CAACL,QAAQ,CAAC9D,OAAO,IAAI,WAAW,CAAC;QAC9C4B,WAAW,CAAC,EAAE,CAAC;QACfI,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOmC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,IAAIE,QAAQ,GAAG,WAAW;MAC1B,IAAIF,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM;UAAEQ;QAAO,CAAC,GAAGH,KAAK,CAACL,QAAQ;QACjC,IAAIQ,MAAM,KAAK,GAAG,EAAE;UAClBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,eAAe;QAC5B;MACF;MACArE,OAAO,CAACmE,KAAK,CAACE,QAAQ,CAAC;MACvBzC,WAAW,CAAC,EAAE,CAAC;MACfI,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMyC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAM9C,cAAc,CAACwD,oBAAoB,CAAC,CAAC;MAC5D,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpBpB,aAAa,CAACkB,QAAQ,CAACG,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC;EACF,CAAC;;EAED;EACA7E,SAAS,CAAC,MAAM;IACduE,aAAa,CAAC,CAAC;IACfU,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACtC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMwC,YAAY,GAAIC,MAAW,IAAK;IACpCxC,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAGyC,MAAM;MACTvC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwC,WAAW,GAAGA,CAAA,KAAM;IACxBxB,UAAU,CAACyB,WAAW,CAAC,CAAC;IACxB1C,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyC,SAAS,GAAGA,CAAA,KAAM;IACtBrC,iBAAiB,CAAC,IAAI,CAAC;IACvBS,IAAI,CAAC2B,WAAW,CAAC,CAAC;IAClBtC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMwC,UAAU,GAAIC,OAAgB,IAAK;IACvCvC,iBAAiB,CAACuC,OAAO,CAAC;IAC1B9B,IAAI,CAAC+B,cAAc,CAAC;MAClB,GAAGD,OAAO;MACVE,SAAS,EAAE,CAAC,IAAIC,IAAI,CAACH,OAAO,CAACI,SAAS,CAAC,EAAE,IAAID,IAAI,CAACH,OAAO,CAACK,OAAO,CAAC;IACpE,CAAC,CAAC;IACF9C,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM+C,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAM9C,cAAc,CAACuE,aAAa,CAACD,EAAE,CAAC;MACvD,IAAIxB,QAAQ,CAACE,OAAO,EAAE;QACpBhE,OAAO,CAACgE,OAAO,CAAC,MAAM,CAAC;QACvBH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL7D,OAAO,CAACmE,KAAK,CAACL,QAAQ,CAAC9D,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOmE,KAAU,EAAE;MACnBnE,OAAO,CAACmE,KAAK,CAACA,KAAK,CAACnE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMwF,UAAU,GAAG,MAAOd,MAAW,IAAK;IACxChC,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAM+C,WAAW,GAAG;QAClB,GAAGf,MAAM;QACTS,SAAS,EAAET,MAAM,CAACO,SAAS,CAAC,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;QAC5CN,OAAO,EAAEV,MAAM,CAACO,SAAS,CAAC,CAAC,CAAC,CAACS,WAAW,CAAC;MAC3C,CAAC;MACD,OAAOD,WAAW,CAACR,SAAS;MAE5B,IAAInB,QAAQ;MACZ,IAAIvB,cAAc,EAAE;QAClBuB,QAAQ,GAAG,MAAM9C,cAAc,CAAC2E,aAAa,CAACpD,cAAc,CAAC+C,EAAE,EAAEG,WAAW,CAAC;MAC/E,CAAC,MAAM;QACL3B,QAAQ,GAAG,MAAM9C,cAAc,CAAC4E,aAAa,CAACH,WAAW,CAAC;MAC5D;MAEA,IAAI3B,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAM6B,UAAU,GAAGtD,cAAc,GAAG,YAAY,GAAG,UAAU;QAC7DvC,OAAO,CAACgE,OAAO,CAAC;UACd8B,OAAO,EAAED,UAAU;UACnBE,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFzD,iBAAiB,CAAC,KAAK,CAAC;QACxBW,IAAI,CAAC2B,WAAW,CAAC,CAAC;QAClBpC,iBAAiB,CAAC,IAAI,CAAC;QACvBqB,aAAa,CAAC,CAAC;QACfU,eAAe,CAAC,CAAC,CAAC,CAAC;MACrB,CAAC,MAAM;QACL;QACA,IAAIF,QAAQ,GAAG,MAAM;QACrB,IAAIP,QAAQ,CAAC9D,OAAO,EAAE;UACpB,IAAI8D,QAAQ,CAAC9D,OAAO,CAACgG,QAAQ,CAAC,OAAO,CAAC,EAAE;YACtC3B,QAAQ,GAAG,kBAAkB;UAC/B,CAAC,MAAM,IAAIP,QAAQ,CAAC9D,OAAO,CAACgG,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC5C3B,QAAQ,GAAG,mBAAmB;UAChC,CAAC,MAAM,IAAIP,QAAQ,CAAC9D,OAAO,CAACgG,QAAQ,CAAC,YAAY,CAAC,EAAE;YAClD3B,QAAQ,GAAG,kBAAkB;UAC/B,CAAC,MAAM;YACLA,QAAQ,GAAGP,QAAQ,CAAC9D,OAAO;UAC7B;QACF;QAEAA,OAAO,CAACmE,KAAK,CAAC;UACZ2B,OAAO,EAAEzB,QAAQ;UACjB0B,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO5B,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAEhC,IAAIE,QAAQ,GAAG,YAAY;MAC3B,IAAIF,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM;UAAEQ,MAAM;UAAEL;QAAK,CAAC,GAAGE,KAAK,CAACL,QAAQ;QACvC,IAAIQ,MAAM,KAAK,GAAG,EAAE;UAClB,IAAIL,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAAC6B,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC9C3B,QAAQ,GAAG,gBAAgB;UAC7B,CAAC,MAAM,IAAIJ,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAAC6B,QAAQ,CAAC,MAAM,CAAC,EAAE;YACpD3B,QAAQ,GAAG,gBAAgB;UAC7B,CAAC,MAAM;YACLA,QAAQ,GAAGJ,IAAI,CAACE,KAAK,IAAI,gBAAgB;UAC3C;QACF,CAAC,MAAM,IAAIG,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,iBAAiB;QAC9B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,gBAAgB;QAC7B,CAAC,MAAM;UACLA,QAAQ,GAAG,SAASC,MAAM,SAAS;QACrC;MACF,CAAC,MAAM,IAAIH,KAAK,CAACnE,OAAO,EAAE;QACxBqE,QAAQ,GAAGF,KAAK,CAACnE,OAAO;MAC1B;MAEAA,OAAO,CAACmE,KAAK,CAAC;QACZ2B,OAAO,EAAEzB,QAAQ;QACjB0B,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRrD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMuD,WAAW,GAAG,MAAOX,EAAU,IAAK;IACxC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAM9C,cAAc,CAACkF,YAAY,CAACZ,EAAE,CAAC;MACtD,IAAIxB,QAAQ,CAACE,OAAO,EAAE;QACpBhE,OAAO,CAACgE,OAAO,CAAC,QAAQ,CAAC;QACzBH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL7D,OAAO,CAACmE,KAAK,CAACL,QAAQ,CAAC9D,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOmE,KAAU,EAAE;MACnBnE,OAAO,CAACmE,KAAK,CAACA,KAAK,CAACnE,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMmG,WAAW,GAAG,MAAOb,EAAU,IAAK;IACxC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAM9C,cAAc,CAACoF,YAAY,CAACd,EAAE,CAAC;MACtD,IAAIxB,QAAQ,CAACE,OAAO,EAAE;QACpBhE,OAAO,CAACgE,OAAO,CAAC,QAAQ,CAAC;QACzBH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL7D,OAAO,CAACmE,KAAK,CAACL,QAAQ,CAAC9D,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOmE,KAAU,EAAE;MACnBnE,OAAO,CAACmE,KAAK,CAACA,KAAK,CAACnE,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMqG,SAAS,GAAG,MAAOf,EAAU,IAAK;IACtC,IAAI;MACF,MAAMxB,QAAQ,GAAG,MAAM9C,cAAc,CAACsF,UAAU,CAAChB,EAAE,CAAC;MACpD,IAAIxB,QAAQ,CAACE,OAAO,EAAE;QACpBhE,OAAO,CAACgE,OAAO,CAAC,QAAQ,CAAC;QACzBH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL7D,OAAO,CAACmE,KAAK,CAACL,QAAQ,CAAC9D,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOmE,KAAU,EAAE;MACnBnE,OAAO,CAACmE,KAAK,CAACA,KAAK,CAACnE,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMuG,OAA6B,GAAG,CACpC;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnB3F,OAAA;MAAK4F,KAAK,EAAE;QAAEC,UAAU,EAAE;MAAI,CAAE;MAAAC,QAAA,EAAEH;IAAI;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEhD,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGtC,MAAqB,IAAK;MACjC,MAAM+C,UAAU,GAAGjE,gBAAgB,CAACkB,MAAM,CAAC;MAC3C,oBACEpD,OAAA,CAACZ,KAAK;QACJgE,MAAM,EACJA,MAAM,KAAK9C,aAAa,CAACiC,OAAO,GAAG,YAAY,GAC/Ca,MAAM,KAAK9C,aAAa,CAACmC,SAAS,GAAG,SAAS,GAC9CW,MAAM,KAAK9C,aAAa,CAACoC,SAAS,GAAG,OAAO,GAAG,SAChD;QACDiD,IAAI,eACF3F,OAAA,CAACrB,GAAG;UAAC0D,KAAK,EAAE,CAAA8D,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE9D,KAAK,KAAI,SAAU;UAAAyD,QAAA,EACxC,CAAAK,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE/D,KAAK,KAAI;QAAI;UAAA2D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAEN;EACF,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC7E,KAAa,EAAEuF,MAAe,kBACrCpG,OAAA;MAAA8F,QAAA,gBACE9F,OAAA;QAAA8F,QAAA,GAAMjF,KAAK,EAAC,QAAC;MAAA;QAAAkF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC,eACnBlG,OAAA;QAAK4F,KAAK,EAAE;UAAES,QAAQ,EAAE,EAAE;UAAEhE,KAAK,EAAE;QAAO,CAAE;QAAAyD,QAAA,GAAC,gBACvC,EAACM,MAAM,CAACE,SAAS;MAAA;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGa,MAAc,iBACrBvG,OAAA;MAAK4F,KAAK,EAAE;QAAEC,UAAU,EAAE,GAAG;QAAExD,KAAK,EAAE;MAAO,CAAE;MAAAyD,QAAA,GAAC,MAC7C,EAACS,MAAM,CAACC,OAAO,CAAC,CAAC,CAAC;IAAA;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB;EAET,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,kBAAkB;IAC7BC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACC,IAAY,EAAES,MAAe,kBACpCpG,OAAA;MAAA8F,QAAA,gBACE9F,OAAA;QAAA8F,QAAA,EAAM,IAAI9B,IAAI,CAAC2B,IAAI,CAAC,CAACc,cAAc,CAAC;MAAC;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAC5ClG,OAAA;QAAK4F,KAAK,EAAE;UAAES,QAAQ,EAAE,EAAE;UAAEhE,KAAK,EAAE;QAAO,CAAE;QAAAyD,QAAA,GAAC,SACzC,EAAC,IAAI9B,IAAI,CAACoC,MAAM,CAAClC,OAAO,CAAC,CAACuC,cAAc,CAAC,CAAC;MAAA;QAAAV,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAET,CAAC,EACD;IACEZ,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACViB,KAAK,EAAE,OAAO;IACdhB,MAAM,EAAEA,CAACiB,CAAC,EAAEP,MAAe,kBACzBpG,OAAA,CAACxB,KAAK;MAACoI,IAAI,EAAC,OAAO;MAAAd,QAAA,gBACjB9F,OAAA,CAACzB,MAAM;QACLsI,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE9G,OAAA,CAACT,WAAW;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBa,OAAO,EAAEA,CAAA,KAAM,CAAC,WAAY;QAAAjB,QAAA,EAC7B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTlG,OAAA,CAACzB,MAAM;QACLsI,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE9G,OAAA,CAACR,YAAY;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBa,OAAO,EAAEA,CAAA,KAAMnD,UAAU,CAACwC,MAAM,CAAE;QAAAN,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,EACRE,MAAM,CAAChD,MAAM,KAAK9C,aAAa,CAACgC,SAAS,iBACxCtC,OAAA,CAACzB,MAAM;QACLsI,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE9G,OAAA,CAACN,kBAAkB;UAAAqG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC7Ba,OAAO,EAAEA,CAAA,KAAMhC,WAAW,CAACqB,MAAM,CAAChC,EAAE,CAAE;QAAA0B,QAAA,EACvC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT,EACAE,MAAM,CAAChD,MAAM,KAAK9C,aAAa,CAACiC,OAAO,iBACtCvC,OAAA,CAAAE,SAAA;QAAA4F,QAAA,gBACE9F,OAAA,CAACzB,MAAM;UACLsI,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZE,IAAI,eAAE9G,OAAA,CAACL,mBAAmB;YAAAoG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC9Ba,OAAO,EAAEA,CAAA,KAAM9B,WAAW,CAACmB,MAAM,CAAChC,EAAE,CAAE;UAAA0B,QAAA,EACvC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTlG,OAAA,CAACzB,MAAM;UACLsI,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZE,IAAI,eAAE9G,OAAA,CAACJ,YAAY;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACvBa,OAAO,EAAEA,CAAA,KAAM5B,SAAS,CAACiB,MAAM,CAAChC,EAAE,CAAE;UAAA0B,QAAA,EACrC;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA,eACT,CACH,eACDlG,OAAA,CAACzB,MAAM;QACLsI,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZI,MAAM;QACNF,IAAI,eAAE9G,OAAA,CAACP,cAAc;UAAAsG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACzBa,OAAO,EAAEA,CAAA,KAAM5C,YAAY,CAACiC,MAAM,CAAChC,EAAE,CAAE;QAAA0B,QAAA,EACxC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAEX,CAAC,CACF;EAED,oBACElG,OAAA;IAAKiH,SAAS,EAAC,wBAAwB;IAAAnB,QAAA,gBACrC9F,OAAA,CAACG,KAAK;MAAC+G,KAAK,EAAE,CAAE;MAAApB,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7BlG,OAAA,CAAChB,GAAG;MAACmI,MAAM,EAAE,EAAG;MAACvB,KAAK,EAAE;QAAEwB,YAAY,EAAE;MAAG,CAAE;MAAAtB,QAAA,gBAC3C9F,OAAA,CAACf,GAAG;QAACoI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACzB9F,OAAA,CAAC3B,IAAI;UAAAyH,QAAA,eACH9F,OAAA,CAACb,SAAS;YACRmG,KAAK,EAAC,0BAAM;YACZkC,KAAK,EAAE/F,UAAU,CAACE,aAAc;YAChC8F,UAAU,EAAE;cAAEpF,KAAK,EAAE;YAAU;UAAE;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlG,OAAA,CAACf,GAAG;QAACoI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACzB9F,OAAA,CAAC3B,IAAI;UAAAyH,QAAA,eACH9F,OAAA,CAACb,SAAS;YACRmG,KAAK,EAAC,oBAAK;YACXkC,KAAK,EAAE/F,UAAU,CAACG,eAAgB;YAClC6F,UAAU,EAAE;cAAEpF,KAAK,EAAE;YAAU;UAAE;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlG,OAAA,CAACf,GAAG;QAACoI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACzB9F,OAAA,CAAC3B,IAAI;UAAAyH,QAAA,eACH9F,OAAA,CAACb,SAAS;YACRmG,KAAK,EAAC,0BAAM;YACZkC,KAAK,EAAE/F,UAAU,CAACI,aAAc;YAChC4F,UAAU,EAAE;cAAEpF,KAAK,EAAE;YAAU;UAAE;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNlG,OAAA,CAACf,GAAG;QAACoI,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACzB9F,OAAA,CAAC3B,IAAI;UAAAyH,QAAA,eACH9F,OAAA,CAACb,SAAS;YACRmG,KAAK,EAAC,gCAAO;YACbkC,KAAK,EAAE/F,UAAU,CAACK,iBAAkB;YACpC2F,UAAU,EAAE;cAAEpF,KAAK,EAAE;YAAU;UAAE;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlG,OAAA,CAAC3B,IAAI;MAAC4I,SAAS,EAAC,aAAa;MAACL,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxC9F,OAAA,CAACnB,IAAI;QACH6I,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEpE,YAAa;QACvBqE,YAAY,EAAC,KAAK;QAAA9B,QAAA,eAElB9F,OAAA,CAAChB,GAAG;UAACmI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACvB,KAAK,EAAE;YAAEH,KAAK,EAAE;UAAO,CAAE;UAAAK,QAAA,gBAC9C9F,OAAA,CAACf,GAAG;YAACoI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzB9F,OAAA,CAACnB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,OAAO;cAAC1F,KAAK,EAAC,gCAAO;cAAA0D,QAAA,eACnC9F,OAAA,CAACvB,KAAK;gBAACsJ,WAAW,EAAC,kDAAU;gBAACC,UAAU;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlG,OAAA,CAACf,GAAG;YAACoI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzB9F,OAAA,CAACnB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,QAAQ;cAAC1F,KAAK,EAAC,0BAAM;cAAA0D,QAAA,eACnC9F,OAAA,CAACtB,MAAM;gBAACqJ,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAlC,QAAA,gBACtC9F,OAAA,CAACI,MAAM;kBAACoH,KAAK,EAAElH,aAAa,CAAC6B,KAAM;kBAAA2D,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/ClG,OAAA,CAACI,MAAM;kBAACoH,KAAK,EAAElH,aAAa,CAACgC,SAAU;kBAAAwD,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDlG,OAAA,CAACI,MAAM;kBAACoH,KAAK,EAAElH,aAAa,CAACiC,OAAQ;kBAAAuD,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAClDlG,OAAA,CAACI,MAAM;kBAACoH,KAAK,EAAElH,aAAa,CAACkC,MAAO;kBAAAsD,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjDlG,OAAA,CAACI,MAAM;kBAACoH,KAAK,EAAElH,aAAa,CAACmC,SAAU;kBAAAqD,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpDlG,OAAA,CAACI,MAAM;kBAACoH,KAAK,EAAElH,aAAa,CAACoC,SAAU;kBAAAoD,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlG,OAAA,CAACf,GAAG;YAACoI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzB9F,OAAA,CAACnB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,aAAa;cAAC1F,KAAK,EAAC,oBAAK;cAAA0D,QAAA,eACvC9F,OAAA,CAACvB,KAAK;gBAACsJ,WAAW,EAAC,kDAAU;gBAACC,UAAU;cAAA;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlG,OAAA,CAACf,GAAG;YAACoI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzB9F,OAAA,CAACnB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,WAAW;cAAC1F,KAAK,EAAC,0BAAM;cAAA0D,QAAA,eACtC9F,OAAA,CAACK,WAAW;gBAACuF,KAAK,EAAE;kBAAEH,KAAK,EAAE;gBAAO;cAAE;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNlG,OAAA,CAACf,GAAG;YAACoI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAzB,QAAA,eACzB9F,OAAA,CAACnB,IAAI,CAACgJ,IAAI;cAAA/B,QAAA,eACR9F,OAAA,CAACxB,KAAK;gBAAAsH,QAAA,gBACJ9F,OAAA,CAACzB,MAAM;kBAACsI,IAAI,EAAC,SAAS;kBAACoB,QAAQ,EAAC,QAAQ;kBAACnB,IAAI,eAAE9G,OAAA,CAACV,cAAc;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAEnE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTlG,OAAA,CAACzB,MAAM;kBAACwI,OAAO,EAAEtD,WAAY;kBAACqD,IAAI,eAAE9G,OAAA,CAACH,cAAc;oBAAAkG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAExD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPlG,OAAA,CAAC3B,IAAI;MAAC4I,SAAS,EAAC,aAAa;MAACL,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxC9F,OAAA,CAAChB,GAAG;QAACkJ,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAArC,QAAA,gBACzC9F,OAAA,CAACf,GAAG;UAAA6G,QAAA,eACF9F,OAAA,CAACzB,MAAM;YACLsI,IAAI,EAAC,SAAS;YACdC,IAAI,eAAE9G,OAAA,CAACX,YAAY;cAAA0G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBa,OAAO,EAAEpD,SAAU;YAAAmC,QAAA,EACpB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlG,OAAA,CAACf,GAAG;UAAA6G,QAAA,eACF9F,OAAA,CAACzB,MAAM;YACLuI,IAAI,eAAE9G,OAAA,CAACH,cAAc;cAAAkG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBa,OAAO,EAAEpE,aAAc;YACvBhC,OAAO,EAAEA,OAAQ;YAAAmF,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPlG,OAAA,CAAC3B,IAAI;MAAAyH,QAAA,eACH9F,OAAA,CAAC1B,KAAK;QACJ+G,OAAO,EAAEA,OAAQ;QACjB+C,UAAU,EAAE3H,QAAS;QACrB4H,MAAM,EAAC,IAAI;QACX1H,OAAO,EAAEA,OAAQ;QACjB2H,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAE1H,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZ6H,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC/H,KAAK,EAAEgI,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQhI,KAAK,IAAI;UAC5CiI,QAAQ,EAAEA,CAAC7H,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPlG,OAAA,CAACpB,KAAK;MACJ0G,KAAK,EAAEjE,cAAc,GAAG,OAAO,GAAG,OAAQ;MAC1C0H,IAAI,EAAE5H,cAAe;MACrB6H,QAAQ,EAAEA,CAAA,KAAM5H,iBAAiB,CAAC,KAAK,CAAE;MACzC6H,MAAM,EAAE,IAAK;MACbxD,KAAK,EAAE,GAAI;MAAAK,QAAA,eAEX9F,OAAA,CAACnB,IAAI;QACHkD,IAAI,EAAEA,IAAK;QACX2F,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAErD,UAAW;QACrBsD,YAAY,EAAC,KAAK;QAAA9B,QAAA,gBAElB9F,OAAA,CAACnB,IAAI,CAACgJ,IAAI;UACRC,IAAI,EAAC,OAAO;UACZ1F,KAAK,EAAC,gCAAO;UACb8G,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAErK,OAAO,EAAE;UAAW,CAAC,EACvC;YAAEsK,GAAG,EAAE,GAAG;YAAEtK,OAAO,EAAE;UAAe,CAAC,CACrC;UAAAgH,QAAA,eAEF9F,OAAA,CAACvB,KAAK;YAACsJ,WAAW,EAAC;UAAU;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvB,CAAC,eAEZlG,OAAA,CAACnB,IAAI,CAACgJ,IAAI;UACRC,IAAI,EAAC,aAAa;UAClB1F,KAAK,EAAC,gCAAO;UACb8G,KAAK,EAAE,CACL;YAAEE,GAAG,EAAE,GAAG;YAAEtK,OAAO,EAAE;UAAe,CAAC,CACrC;UAAAgH,QAAA,eAEF9F,OAAA,CAACvB,KAAK,CAAC4K,QAAQ;YACbtB,WAAW,EAAC,kDAAU;YACtBuB,IAAI,EAAE,CAAE;YACRC,SAAS;YACTC,SAAS,EAAE;UAAI;YAAAzD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlG,OAAA,CAACnB,IAAI,CAACgJ,IAAI;UACRC,IAAI,EAAC,WAAW;UAChB1F,KAAK,EAAC,0BAAM;UACZ8G,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAErK,OAAO,EAAE;UAAU,CAAC,CAAE;UAAAgH,QAAA,eAEhD9F,OAAA,CAACK,WAAW;YACVoJ,QAAQ;YACR7D,KAAK,EAAE;cAAEH,KAAK,EAAE;YAAO,CAAE;YACzBsC,WAAW,EAAE,CAAC,MAAM,EAAE,MAAM;UAAE;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZlG,OAAA,CAACnB,IAAI,CAACgJ,IAAI;UACRC,IAAI,EAAC,UAAU;UACf1F,KAAK,EAAC,0BAAM;UACZ8G,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAErK,OAAO,EAAE;UAAU,CAAC,EACtC;YAAEsK,GAAG,EAAE,GAAG;YAAEtK,OAAO,EAAE;UAAe,CAAC,CACrC;UAAAgH,QAAA,eAEF9F,OAAA,CAACvB,KAAK;YAACsJ,WAAW,EAAC;UAAS;YAAAhC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACtB,CAAC,eAEZlG,OAAA,CAACnB,IAAI,CAACgJ,IAAI;UAAA/B,QAAA,eACR9F,OAAA,CAACxB,KAAK;YAACoH,KAAK,EAAE;cAAEH,KAAK,EAAE,MAAM;cAAEiE,cAAc,EAAE;YAAW,CAAE;YAAA5D,QAAA,gBAC1D9F,OAAA,CAACzB,MAAM;cAACwI,OAAO,EAAEA,CAAA,KAAM3F,iBAAiB,CAAC,KAAK,CAAE;cAAA0E,QAAA,EAAC;YAEjD;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTlG,OAAA,CAACzB,MAAM;cAACsI,IAAI,EAAC,SAAS;cAACoB,QAAQ,EAAC,QAAQ;cAAAnC,QAAA,EACrCzE,cAAc,GAAG,IAAI,GAAG;YAAI;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1F,EAAA,CAloBID,WAAqB;EAAA,QAiBV1B,IAAI,CAACmD,OAAO,EACNnD,IAAI,CAACmD,OAAO;AAAA;AAAA2H,EAAA,GAlB7BpJ,WAAqB;AAooB3B,eAAeA,WAAW;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}