{"ast": null, "code": "import { format } from \"../util\";\nvar pattern = function pattern(rule, value, source, errors, options) {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    } else if (typeof rule.pattern === 'string') {\n      var _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    }\n  }\n};\nexport default pattern;", "map": {"version": 3, "names": ["format", "pattern", "rule", "value", "source", "errors", "options", "RegExp", "lastIndex", "test", "push", "messages", "mismatch", "fullField", "_pattern"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@rc-component/async-validator/es/rule/pattern.js"], "sourcesContent": ["import { format } from \"../util\";\nvar pattern = function pattern(rule, value, source, errors, options) {\n  if (rule.pattern) {\n    if (rule.pattern instanceof RegExp) {\n      // if a RegExp instance is passed, reset `lastIndex` in case its `global`\n      // flag is accidentally set to `true`, which in a validation scenario\n      // is not necessary and the result might be misleading\n      rule.pattern.lastIndex = 0;\n      if (!rule.pattern.test(value)) {\n        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    } else if (typeof rule.pattern === 'string') {\n      var _pattern = new RegExp(rule.pattern);\n      if (!_pattern.test(value)) {\n        errors.push(format(options.messages.pattern.mismatch, rule.fullField, value, rule.pattern));\n      }\n    }\n  }\n};\nexport default pattern;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,SAAS;AAChC,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,MAAM,EAAEC,OAAO,EAAE;EACnE,IAAIJ,IAAI,CAACD,OAAO,EAAE;IAChB,IAAIC,IAAI,CAACD,OAAO,YAAYM,MAAM,EAAE;MAClC;MACA;MACA;MACAL,IAAI,CAACD,OAAO,CAACO,SAAS,GAAG,CAAC;MAC1B,IAAI,CAACN,IAAI,CAACD,OAAO,CAACQ,IAAI,CAACN,KAAK,CAAC,EAAE;QAC7BE,MAAM,CAACK,IAAI,CAACV,MAAM,CAACM,OAAO,CAACK,QAAQ,CAACV,OAAO,CAACW,QAAQ,EAAEV,IAAI,CAACW,SAAS,EAAEV,KAAK,EAAED,IAAI,CAACD,OAAO,CAAC,CAAC;MAC7F;IACF,CAAC,MAAM,IAAI,OAAOC,IAAI,CAACD,OAAO,KAAK,QAAQ,EAAE;MAC3C,IAAIa,QAAQ,GAAG,IAAIP,MAAM,CAACL,IAAI,CAACD,OAAO,CAAC;MACvC,IAAI,CAACa,QAAQ,CAACL,IAAI,CAACN,KAAK,CAAC,EAAE;QACzBE,MAAM,CAACK,IAAI,CAACV,MAAM,CAACM,OAAO,CAACK,QAAQ,CAACV,OAAO,CAACW,QAAQ,EAAEV,IAAI,CAACW,SAAS,EAAEV,KAAK,EAAED,IAAI,CAACD,OAAO,CAAC,CAAC;MAC7F;IACF;EACF;AACF,CAAC;AACD,eAAeA,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}