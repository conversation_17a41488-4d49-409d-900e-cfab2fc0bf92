{"ast": null, "code": "import * as React from 'react';\n// recursion (flat tree structure)\nfunction fillRecords(list, record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  list.push({\n    record: record,\n    indent: indent,\n    index: index\n  });\n  var key = getRowKey(record);\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      fillRecords(list, record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n    }\n  }\n}\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys !== null && expandedKeys !== void 0 && expandedKeys.size) {\n      var list = [];\n\n      // collect flattened record\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var record = data[i];\n\n        // using array.push or spread operator may cause \"Maximum call stack size exceeded\" exception if array size is big enough.\n        fillRecords(list, record, 0, childrenColumnName, expandedKeys, getRowKey, i);\n      }\n      return list;\n    }\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}", "map": {"version": 3, "names": ["React", "fillRecords", "list", "record", "indent", "childrenColumnName", "expandedKeys", "getRowKey", "index", "push", "key", "expanded", "has", "Array", "isArray", "i", "length", "useFlattenRecords", "data", "arr", "useMemo", "size", "map", "item"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/rc-table/es/hooks/useFlattenRecords.js"], "sourcesContent": ["import * as React from 'react';\n// recursion (flat tree structure)\nfunction fillRecords(list, record, indent, childrenColumnName, expandedKeys, getRowKey, index) {\n  list.push({\n    record: record,\n    indent: indent,\n    index: index\n  });\n  var key = getRowKey(record);\n  var expanded = expandedKeys === null || expandedKeys === void 0 ? void 0 : expandedKeys.has(key);\n  if (record && Array.isArray(record[childrenColumnName]) && expanded) {\n    // expanded state, flat record\n    for (var i = 0; i < record[childrenColumnName].length; i += 1) {\n      fillRecords(list, record[childrenColumnName][i], indent + 1, childrenColumnName, expandedKeys, getRowKey, i);\n    }\n  }\n}\n/**\n * flat tree data on expanded state\n *\n * @export\n * @template T\n * @param {*} data : table data\n * @param {string} childrenColumnName : 指定树形结构的列名\n * @param {Set<Key>} expandedKeys : 展开的行对应的keys\n * @param {GetRowKey<T>} getRowKey  : 获取当前rowKey的方法\n * @returns flattened data\n */\nexport default function useFlattenRecords(data, childrenColumnName, expandedKeys, getRowKey) {\n  var arr = React.useMemo(function () {\n    if (expandedKeys !== null && expandedKeys !== void 0 && expandedKeys.size) {\n      var list = [];\n\n      // collect flattened record\n      for (var i = 0; i < (data === null || data === void 0 ? void 0 : data.length); i += 1) {\n        var record = data[i];\n\n        // using array.push or spread operator may cause \"Maximum call stack size exceeded\" exception if array size is big enough.\n        fillRecords(list, record, 0, childrenColumnName, expandedKeys, getRowKey, i);\n      }\n      return list;\n    }\n    return data === null || data === void 0 ? void 0 : data.map(function (item, index) {\n      return {\n        record: item,\n        indent: 0,\n        index: index\n      };\n    });\n  }, [data, childrenColumnName, expandedKeys, getRowKey]);\n  return arr;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA,SAASC,WAAWA,CAACC,IAAI,EAAEC,MAAM,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAE;EAC7FN,IAAI,CAACO,IAAI,CAAC;IACRN,MAAM,EAAEA,MAAM;IACdC,MAAM,EAAEA,MAAM;IACdI,KAAK,EAAEA;EACT,CAAC,CAAC;EACF,IAAIE,GAAG,GAAGH,SAAS,CAACJ,MAAM,CAAC;EAC3B,IAAIQ,QAAQ,GAAGL,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACM,GAAG,CAACF,GAAG,CAAC;EAChG,IAAIP,MAAM,IAAIU,KAAK,CAACC,OAAO,CAACX,MAAM,CAACE,kBAAkB,CAAC,CAAC,IAAIM,QAAQ,EAAE;IACnE;IACA,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGZ,MAAM,CAACE,kBAAkB,CAAC,CAACW,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MAC7Dd,WAAW,CAACC,IAAI,EAAEC,MAAM,CAACE,kBAAkB,CAAC,CAACU,CAAC,CAAC,EAAEX,MAAM,GAAG,CAAC,EAAEC,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAEQ,CAAC,CAAC;IAC9G;EACF;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASE,iBAAiBA,CAACC,IAAI,EAAEb,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAE;EAC3F,IAAIY,GAAG,GAAGnB,KAAK,CAACoB,OAAO,CAAC,YAAY;IAClC,IAAId,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,IAAIA,YAAY,CAACe,IAAI,EAAE;MACzE,IAAInB,IAAI,GAAG,EAAE;;MAEb;MACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAIG,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACF,MAAM,CAAC,EAAED,CAAC,IAAI,CAAC,EAAE;QACrF,IAAIZ,MAAM,GAAGe,IAAI,CAACH,CAAC,CAAC;;QAEpB;QACAd,WAAW,CAACC,IAAI,EAAEC,MAAM,EAAE,CAAC,EAAEE,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,EAAEQ,CAAC,CAAC;MAC9E;MACA,OAAOb,IAAI;IACb;IACA,OAAOgB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACI,GAAG,CAAC,UAAUC,IAAI,EAAEf,KAAK,EAAE;MACjF,OAAO;QACLL,MAAM,EAAEoB,IAAI;QACZnB,MAAM,EAAE,CAAC;QACTI,KAAK,EAAEA;MACT,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAACU,IAAI,EAAEb,kBAAkB,EAAEC,YAAY,EAAEC,SAAS,CAAC,CAAC;EACvD,OAAOY,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}