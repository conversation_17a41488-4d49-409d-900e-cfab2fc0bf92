{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Form, Input, Button, Card, message } from 'antd';\nimport { UserOutlined, LockOutlined, WifiOutlined, DisconnectOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [networkStatus, setNetworkStatus] = useState('checking');\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取重定向路径\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/dashboard';\n\n  // 检测网络和服务器状态\n  useEffect(() => {\n    const checkServerStatus = async () => {\n      try {\n        // 尝试访问后端健康检查接口\n        const response = await fetch('http://localhost:8081/health', {\n          method: 'GET',\n          timeout: 5000\n        });\n        if (response.ok) {\n          setNetworkStatus('online');\n        } else {\n          setNetworkStatus('offline');\n        }\n      } catch (error) {\n        setNetworkStatus('offline');\n      }\n    };\n    checkServerStatus();\n\n    // 监听网络状态变化\n    const handleOnline = () => checkServerStatus();\n    const handleOffline = () => setNetworkStatus('offline');\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  }, []);\n  const handleSubmit = async values => {\n    setLoading(true);\n    try {\n      const result = await login(values);\n      if (result.success) {\n        message.success({\n          content: '登录成功！正在跳转...',\n          duration: 2\n        });\n\n        // 延迟一下让用户看到成功提示\n        setTimeout(() => {\n          navigate(from, {\n            replace: true\n          });\n        }, 500);\n      } else {\n        // 根据错误类型显示不同的提示\n        let errorMessage = result.message || '登录失败';\n        if (errorMessage.includes('Network Error') || errorMessage.includes('timeout')) {\n          errorMessage = '服务器连接超时，请检查网络连接或稍后重试';\n        } else if (errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503')) {\n          errorMessage = '服务器暂时不可用，请稍后重试';\n        } else if (errorMessage.includes('404')) {\n          errorMessage = '登录服务不可用，请联系系统管理员';\n        } else if (errorMessage.includes('用户名') || errorMessage.includes('密码')) {\n          errorMessage = '用户名或密码错误，请重新输入';\n        }\n        message.error({\n          content: errorMessage,\n          duration: 6\n        });\n      }\n    } catch (error) {\n      var _error$message, _error$message2, _error$response, _error$response2;\n      console.error('登录异常:', error);\n\n      // 处理网络错误\n      let errorMessage = '登录失败，请稍后重试';\n      if (error.code === 'NETWORK_ERROR' || (_error$message = error.message) !== null && _error$message !== void 0 && _error$message.includes('Network Error')) {\n        errorMessage = '无法连接到服务器，请检查网络连接';\n      } else if (error.code === 'TIMEOUT_ERROR' || (_error$message2 = error.message) !== null && _error$message2 !== void 0 && _error$message2.includes('timeout')) {\n        errorMessage = '请求超时，请检查网络连接或稍后重试';\n      } else if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) >= 500) {\n        errorMessage = '服务器内部错误，请稍后重试或联系系统管理员';\n      } else if (((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : _error$response2.status) === 404) {\n        errorMessage = '登录服务不可用，请联系系统管理员';\n      }\n      message.error({\n        content: errorMessage,\n        duration: 6\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-content\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"login-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"login-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"\\u6606\\u660E\\u82B1\\u5349\\u62CD\\u5356\\u7CFB\\u7EDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u7BA1\\u7406\\u540E\\u53F0\\u767B\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                marginTop: '8px',\n                fontSize: '12px',\n                color: networkStatus === 'online' ? '#52c41a' : '#ff4d4f'\n              },\n              children: networkStatus === 'checking' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(WifiOutlined, {\n                  style: {\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 138,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u68C0\\u6D4B\\u670D\\u52A1\\u5668\\u72B6\\u6001...\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 139,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : networkStatus === 'online' ? /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(WifiOutlined, {\n                  style: {\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 143,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u670D\\u52A1\\u5668\\u8FDE\\u63A5\\u6B63\\u5E38\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n                children: [/*#__PURE__*/_jsxDEV(DisconnectOutlined, {\n                  style: {\n                    marginRight: '4px'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 148,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"\\u670D\\u52A1\\u5668\\u8FDE\\u63A5\\u5F02\\u5E38\\uFF0C\\u8BF7\\u68C0\\u67E5\\u7F51\\u7EDC\\u6216\\u8054\\u7CFB\\u7BA1\\u7406\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 149,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            name: \"login\",\n            onFinish: handleSubmit,\n            autoComplete: \"off\",\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 3,\n                message: '用户名至少3个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 170,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u7528\\u6237\\u540D\",\n                autoComplete: \"username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 169,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 162,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                message: '密码至少6个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 184,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u5BC6\\u7801\",\n                autoComplete: \"current-password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 176,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                disabled: loading || networkStatus === 'offline',\n                block: true,\n                size: \"large\",\n                style: {\n                  height: '48px',\n                  fontSize: '16px',\n                  fontWeight: '500'\n                },\n                children: loading ? '正在登录...' : networkStatus === 'offline' ? '服务器连接异常' : '立即登录'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 191,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 190,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                textAlign: 'center',\n                marginTop: '16px',\n                padding: '12px',\n                backgroundColor: '#f6f8fa',\n                borderRadius: '6px',\n                fontSize: '14px',\n                color: '#666'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginBottom: '4px',\n                  fontWeight: '500'\n                },\n                children: \"\\u6D4B\\u8BD5\\u8D26\\u53F7\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"\\u7528\\u6237\\u540D\\uFF1Aadmin\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 221,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: \"\\u5BC6\\u7801\\uFF1Aadmin123\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 222,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"login-footer\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2024 \\u6606\\u660E\\u82B1\\u5349\\u62CD\\u5356\\u7CFB\\u7EDF. All rights reserved.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 227,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 226,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 121,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 120,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 119,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"9HhdG1udJ4eeTqGbrI5Riz5w9rA=\", false, function () {\n  return [Form.useForm, useAuth, useNavigate, useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Form", "Input", "<PERSON><PERSON>", "Card", "message", "UserOutlined", "LockOutlined", "WifiOutlined", "DisconnectOutlined", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "<PERSON><PERSON>", "_s", "_location$state", "_location$state$from", "form", "useForm", "loading", "setLoading", "networkStatus", "setNetworkStatus", "login", "navigate", "location", "from", "state", "pathname", "checkServerStatus", "response", "fetch", "method", "timeout", "ok", "error", "handleOnline", "handleOffline", "window", "addEventListener", "removeEventListener", "handleSubmit", "values", "result", "success", "content", "duration", "setTimeout", "replace", "errorMessage", "includes", "_error$message", "_error$message2", "_error$response", "_error$response2", "console", "code", "status", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "display", "alignItems", "justifyContent", "marginTop", "fontSize", "color", "marginRight", "name", "onFinish", "autoComplete", "size", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "Password", "type", "htmlType", "disabled", "block", "height", "fontWeight", "textAlign", "padding", "backgroundColor", "borderRadius", "marginBottom", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Form, Input, Button, Card, message } from 'antd';\nimport { UserOutlined, LockOutlined, WifiOutlined, DisconnectOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\n\ninterface LoginFormValues {\n  username: string;\n  password: string;\n}\n\nconst Login: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [networkStatus, setNetworkStatus] = useState<'checking' | 'online' | 'offline'>('checking');\n  const { login } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取重定向路径\n  const from = (location.state as any)?.from?.pathname || '/dashboard';\n\n  // 检测网络和服务器状态\n  useEffect(() => {\n    const checkServerStatus = async () => {\n      try {\n        // 尝试访问后端健康检查接口\n        const response = await fetch('http://localhost:8081/health', {\n          method: 'GET',\n          timeout: 5000,\n        } as any);\n\n        if (response.ok) {\n          setNetworkStatus('online');\n        } else {\n          setNetworkStatus('offline');\n        }\n      } catch (error) {\n        setNetworkStatus('offline');\n      }\n    };\n\n    checkServerStatus();\n\n    // 监听网络状态变化\n    const handleOnline = () => checkServerStatus();\n    const handleOffline = () => setNetworkStatus('offline');\n\n    window.addEventListener('online', handleOnline);\n    window.addEventListener('offline', handleOffline);\n\n    return () => {\n      window.removeEventListener('online', handleOnline);\n      window.removeEventListener('offline', handleOffline);\n    };\n  }, []);\n\n  const handleSubmit = async (values: LoginFormValues) => {\n    setLoading(true);\n    try {\n      const result = await login(values);\n\n      if (result.success) {\n        message.success({\n          content: '登录成功！正在跳转...',\n          duration: 2,\n        });\n\n        // 延迟一下让用户看到成功提示\n        setTimeout(() => {\n          navigate(from, { replace: true });\n        }, 500);\n      } else {\n        // 根据错误类型显示不同的提示\n        let errorMessage = result.message || '登录失败';\n\n        if (errorMessage.includes('Network Error') || errorMessage.includes('timeout')) {\n          errorMessage = '服务器连接超时，请检查网络连接或稍后重试';\n        } else if (errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503')) {\n          errorMessage = '服务器暂时不可用，请稍后重试';\n        } else if (errorMessage.includes('404')) {\n          errorMessage = '登录服务不可用，请联系系统管理员';\n        } else if (errorMessage.includes('用户名') || errorMessage.includes('密码')) {\n          errorMessage = '用户名或密码错误，请重新输入';\n        }\n\n        message.error({\n          content: errorMessage,\n          duration: 6,\n        });\n      }\n    } catch (error: any) {\n      console.error('登录异常:', error);\n\n      // 处理网络错误\n      let errorMessage = '登录失败，请稍后重试';\n\n      if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {\n        errorMessage = '无法连接到服务器，请检查网络连接';\n      } else if (error.code === 'TIMEOUT_ERROR' || error.message?.includes('timeout')) {\n        errorMessage = '请求超时，请检查网络连接或稍后重试';\n      } else if (error.response?.status >= 500) {\n        errorMessage = '服务器内部错误，请稍后重试或联系系统管理员';\n      } else if (error.response?.status === 404) {\n        errorMessage = '登录服务不可用，请联系系统管理员';\n      }\n\n      message.error({\n        content: errorMessage,\n        duration: 6,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <div className=\"login-background\">\n        <div className=\"login-content\">\n          <Card className=\"login-card\">\n            <div className=\"login-header\">\n              <h1>昆明花卉拍卖系统</h1>\n              <p>管理后台登录</p>\n\n              {/* 网络状态指示器 */}\n              <div style={{\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                marginTop: '8px',\n                fontSize: '12px',\n                color: networkStatus === 'online' ? '#52c41a' : '#ff4d4f'\n              }}>\n                {networkStatus === 'checking' ? (\n                  <>\n                    <WifiOutlined style={{ marginRight: '4px' }} />\n                    <span>检测服务器状态...</span>\n                  </>\n                ) : networkStatus === 'online' ? (\n                  <>\n                    <WifiOutlined style={{ marginRight: '4px' }} />\n                    <span>服务器连接正常</span>\n                  </>\n                ) : (\n                  <>\n                    <DisconnectOutlined style={{ marginRight: '4px' }} />\n                    <span>服务器连接异常，请检查网络或联系管理员</span>\n                  </>\n                )}\n              </div>\n            </div>\n\n            <Form\n              form={form}\n              name=\"login\"\n              onFinish={handleSubmit}\n              autoComplete=\"off\"\n              size=\"large\"\n            >\n              <Form.Item\n                name=\"username\"\n                rules={[\n                  { required: true, message: '请输入用户名' },\n                  { min: 3, message: '用户名至少3个字符' },\n                ]}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  placeholder=\"用户名\"\n                  autoComplete=\"username\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"password\"\n                rules={[\n                  { required: true, message: '请输入密码' },\n                  { min: 6, message: '密码至少6个字符' },\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"密码\"\n                  autoComplete=\"current-password\"\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  disabled={loading || networkStatus === 'offline'}\n                  block\n                  size=\"large\"\n                  style={{\n                    height: '48px',\n                    fontSize: '16px',\n                    fontWeight: '500',\n                  }}\n                >\n                  {loading ? '正在登录...' :\n                   networkStatus === 'offline' ? '服务器连接异常' :\n                   '立即登录'}\n                </Button>\n              </Form.Item>\n\n              {/* 测试账号提示 */}\n              <div style={{\n                textAlign: 'center',\n                marginTop: '16px',\n                padding: '12px',\n                backgroundColor: '#f6f8fa',\n                borderRadius: '6px',\n                fontSize: '14px',\n                color: '#666'\n              }}>\n                <div style={{ marginBottom: '4px', fontWeight: '500' }}>测试账号</div>\n                <div>用户名：admin</div>\n                <div>密码：admin123</div>\n              </div>\n            </Form>\n\n            <div className=\"login-footer\">\n              <p>© 2024 昆明花卉拍卖系统. All rights reserved.</p>\n            </div>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,QAAQ,MAAM;AACzD,SAASC,YAAY,EAAEC,YAAY,EAAEC,YAAY,EAAEC,kBAAkB,QAAQ,mBAAmB;AAChG,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAOrB,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EAC5B,MAAM,CAACC,IAAI,CAAC,GAAGpB,IAAI,CAACqB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGzB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC0B,aAAa,EAAEC,gBAAgB,CAAC,GAAG3B,QAAQ,CAAoC,UAAU,CAAC;EACjG,MAAM;IAAE4B;EAAM,CAAC,GAAGf,OAAO,CAAC,CAAC;EAC3B,MAAMgB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;EAC9B,MAAMmB,QAAQ,GAAGlB,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMmB,IAAI,GAAG,EAAAX,eAAA,GAACU,QAAQ,CAACE,KAAK,cAAAZ,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBW,IAAI,cAAAV,oBAAA,uBAA7BA,oBAAA,CAA+BY,QAAQ,KAAI,YAAY;;EAEpE;EACAhC,SAAS,CAAC,MAAM;IACd,MAAMiC,iBAAiB,GAAG,MAAAA,CAAA,KAAY;MACpC,IAAI;QACF;QACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,8BAA8B,EAAE;UAC3DC,MAAM,EAAE,KAAK;UACbC,OAAO,EAAE;QACX,CAAQ,CAAC;QAET,IAAIH,QAAQ,CAACI,EAAE,EAAE;UACfZ,gBAAgB,CAAC,QAAQ,CAAC;QAC5B,CAAC,MAAM;UACLA,gBAAgB,CAAC,SAAS,CAAC;QAC7B;MACF,CAAC,CAAC,OAAOa,KAAK,EAAE;QACdb,gBAAgB,CAAC,SAAS,CAAC;MAC7B;IACF,CAAC;IAEDO,iBAAiB,CAAC,CAAC;;IAEnB;IACA,MAAMO,YAAY,GAAGA,CAAA,KAAMP,iBAAiB,CAAC,CAAC;IAC9C,MAAMQ,aAAa,GAAGA,CAAA,KAAMf,gBAAgB,CAAC,SAAS,CAAC;IAEvDgB,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEH,YAAY,CAAC;IAC/CE,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEF,aAAa,CAAC;IAEjD,OAAO,MAAM;MACXC,MAAM,CAACE,mBAAmB,CAAC,QAAQ,EAAEJ,YAAY,CAAC;MAClDE,MAAM,CAACE,mBAAmB,CAAC,SAAS,EAAEH,aAAa,CAAC;IACtD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,YAAY,GAAG,MAAOC,MAAuB,IAAK;IACtDtB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMuB,MAAM,GAAG,MAAMpB,KAAK,CAACmB,MAAM,CAAC;MAElC,IAAIC,MAAM,CAACC,OAAO,EAAE;QAClB3C,OAAO,CAAC2C,OAAO,CAAC;UACdC,OAAO,EAAE,cAAc;UACvBC,QAAQ,EAAE;QACZ,CAAC,CAAC;;QAEF;QACAC,UAAU,CAAC,MAAM;UACfvB,QAAQ,CAACE,IAAI,EAAE;YAAEsB,OAAO,EAAE;UAAK,CAAC,CAAC;QACnC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL;QACA,IAAIC,YAAY,GAAGN,MAAM,CAAC1C,OAAO,IAAI,MAAM;QAE3C,IAAIgD,YAAY,CAACC,QAAQ,CAAC,eAAe,CAAC,IAAID,YAAY,CAACC,QAAQ,CAAC,SAAS,CAAC,EAAE;UAC9ED,YAAY,GAAG,sBAAsB;QACvC,CAAC,MAAM,IAAIA,YAAY,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,YAAY,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,YAAY,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;UACvGD,YAAY,GAAG,gBAAgB;QACjC,CAAC,MAAM,IAAIA,YAAY,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE;UACvCD,YAAY,GAAG,kBAAkB;QACnC,CAAC,MAAM,IAAIA,YAAY,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAID,YAAY,CAACC,QAAQ,CAAC,IAAI,CAAC,EAAE;UACtED,YAAY,GAAG,gBAAgB;QACjC;QAEAhD,OAAO,CAACkC,KAAK,CAAC;UACZU,OAAO,EAAEI,YAAY;UACrBH,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOX,KAAU,EAAE;MAAA,IAAAgB,cAAA,EAAAC,eAAA,EAAAC,eAAA,EAAAC,gBAAA;MACnBC,OAAO,CAACpB,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;;MAE7B;MACA,IAAIc,YAAY,GAAG,YAAY;MAE/B,IAAId,KAAK,CAACqB,IAAI,KAAK,eAAe,KAAAL,cAAA,GAAIhB,KAAK,CAAClC,OAAO,cAAAkD,cAAA,eAAbA,cAAA,CAAeD,QAAQ,CAAC,eAAe,CAAC,EAAE;QAC9ED,YAAY,GAAG,kBAAkB;MACnC,CAAC,MAAM,IAAId,KAAK,CAACqB,IAAI,KAAK,eAAe,KAAAJ,eAAA,GAAIjB,KAAK,CAAClC,OAAO,cAAAmD,eAAA,eAAbA,eAAA,CAAeF,QAAQ,CAAC,SAAS,CAAC,EAAE;QAC/ED,YAAY,GAAG,mBAAmB;MACpC,CAAC,MAAM,IAAI,EAAAI,eAAA,GAAAlB,KAAK,CAACL,QAAQ,cAAAuB,eAAA,uBAAdA,eAAA,CAAgBI,MAAM,KAAI,GAAG,EAAE;QACxCR,YAAY,GAAG,uBAAuB;MACxC,CAAC,MAAM,IAAI,EAAAK,gBAAA,GAAAnB,KAAK,CAACL,QAAQ,cAAAwB,gBAAA,uBAAdA,gBAAA,CAAgBG,MAAM,MAAK,GAAG,EAAE;QACzCR,YAAY,GAAG,kBAAkB;MACnC;MAEAhD,OAAO,CAACkC,KAAK,CAAC;QACZU,OAAO,EAAEI,YAAY;QACrBH,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACR1B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACEV,OAAA;IAAKgD,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9BjD,OAAA;MAAKgD,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/BjD,OAAA;QAAKgD,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5BjD,OAAA,CAACV,IAAI;UAAC0D,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC1BjD,OAAA;YAAKgD,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BjD,OAAA;cAAAiD,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjBrD,OAAA;cAAAiD,QAAA,EAAG;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eAGbrD,OAAA;cAAKsD,KAAK,EAAE;gBACVC,OAAO,EAAE,MAAM;gBACfC,UAAU,EAAE,QAAQ;gBACpBC,cAAc,EAAE,QAAQ;gBACxBC,SAAS,EAAE,KAAK;gBAChBC,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAEjD,aAAa,KAAK,QAAQ,GAAG,SAAS,GAAG;cAClD,CAAE;cAAAsC,QAAA,EACCtC,aAAa,KAAK,UAAU,gBAC3BX,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,gBACEjD,OAAA,CAACN,YAAY;kBAAC4D,KAAK,EAAE;oBAAEO,WAAW,EAAE;kBAAM;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CrD,OAAA;kBAAAiD,QAAA,EAAM;gBAAU;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACvB,CAAC,GACD1C,aAAa,KAAK,QAAQ,gBAC5BX,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,gBACEjD,OAAA,CAACN,YAAY;kBAAC4D,KAAK,EAAE;oBAAEO,WAAW,EAAE;kBAAM;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAC/CrD,OAAA;kBAAAiD,QAAA,EAAM;gBAAO;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eACpB,CAAC,gBAEHrD,OAAA,CAAAE,SAAA;gBAAA+C,QAAA,gBACEjD,OAAA,CAACL,kBAAkB;kBAAC2D,KAAK,EAAE;oBAAEO,WAAW,EAAE;kBAAM;gBAAE;kBAAAX,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACrDrD,OAAA;kBAAAiD,QAAA,EAAM;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA,eAChC;YACH;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENrD,OAAA,CAACb,IAAI;YACHoB,IAAI,EAAEA,IAAK;YACXuD,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAEhC,YAAa;YACvBiC,YAAY,EAAC,KAAK;YAClBC,IAAI,EAAC,OAAO;YAAAhB,QAAA,gBAEZjD,OAAA,CAACb,IAAI,CAAC+E,IAAI;cACRJ,IAAI,EAAC,UAAU;cACfK,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7E,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE8E,GAAG,EAAE,CAAC;gBAAE9E,OAAO,EAAE;cAAY,CAAC,CAChC;cAAA0D,QAAA,eAEFjD,OAAA,CAACZ,KAAK;gBACJkF,MAAM,eAAEtE,OAAA,CAACR,YAAY;kBAAA0D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBkB,WAAW,EAAC,oBAAK;gBACjBP,YAAY,EAAC;cAAU;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZrD,OAAA,CAACb,IAAI,CAAC+E,IAAI;cACRJ,IAAI,EAAC,UAAU;cACfK,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7E,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAE8E,GAAG,EAAE,CAAC;gBAAE9E,OAAO,EAAE;cAAW,CAAC,CAC/B;cAAA0D,QAAA,eAEFjD,OAAA,CAACZ,KAAK,CAACoF,QAAQ;gBACbF,MAAM,eAAEtE,OAAA,CAACP,YAAY;kBAAAyD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBkB,WAAW,EAAC,cAAI;gBAChBP,YAAY,EAAC;cAAkB;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZrD,OAAA,CAACb,IAAI,CAAC+E,IAAI;cAAAjB,QAAA,eACRjD,OAAA,CAACX,MAAM;gBACLoF,IAAI,EAAC,SAAS;gBACdC,QAAQ,EAAC,QAAQ;gBACjBjE,OAAO,EAAEA,OAAQ;gBACjBkE,QAAQ,EAAElE,OAAO,IAAIE,aAAa,KAAK,SAAU;gBACjDiE,KAAK;gBACLX,IAAI,EAAC,OAAO;gBACZX,KAAK,EAAE;kBACLuB,MAAM,EAAE,MAAM;kBACdlB,QAAQ,EAAE,MAAM;kBAChBmB,UAAU,EAAE;gBACd,CAAE;gBAAA7B,QAAA,EAEDxC,OAAO,GAAG,SAAS,GACnBE,aAAa,KAAK,SAAS,GAAG,SAAS,GACvC;cAAM;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC,eAGZrD,OAAA;cAAKsD,KAAK,EAAE;gBACVyB,SAAS,EAAE,QAAQ;gBACnBrB,SAAS,EAAE,MAAM;gBACjBsB,OAAO,EAAE,MAAM;gBACfC,eAAe,EAAE,SAAS;gBAC1BC,YAAY,EAAE,KAAK;gBACnBvB,QAAQ,EAAE,MAAM;gBAChBC,KAAK,EAAE;cACT,CAAE;cAAAX,QAAA,gBACAjD,OAAA;gBAAKsD,KAAK,EAAE;kBAAE6B,YAAY,EAAE,KAAK;kBAAEL,UAAU,EAAE;gBAAM,CAAE;gBAAA7B,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAClErD,OAAA;gBAAAiD,QAAA,EAAK;cAAS;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACpBrD,OAAA;gBAAAiD,QAAA,EAAK;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAEPrD,OAAA;YAAKgD,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3BjD,OAAA;cAAAiD,QAAA,EAAG;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACjD,EAAA,CA7NID,KAAe;EAAA,QACJhB,IAAI,CAACqB,OAAO,EAGTV,OAAO,EACRF,WAAW,EACXC,WAAW;AAAA;AAAAuF,EAAA,GANxBjF,KAAe;AA+NrB,eAAeA,KAAK;AAAC,IAAAiF,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}