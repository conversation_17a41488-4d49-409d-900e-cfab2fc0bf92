{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Popconfirm, Typography, Row, Col, Upload, Image, Descriptions, Switch } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ExportOutlined, ReloadOutlined, UploadOutlined } from '@ant-design/icons';\nimport { productService } from '../../../services/productService';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\n\n// 商品质量等级枚举\nexport let QualityLevel = /*#__PURE__*/function (QualityLevel) {\n  QualityLevel[QualityLevel[\"EXCELLENT\"] = 1] = \"EXCELLENT\";\n  // 优\n  QualityLevel[QualityLevel[\"GOOD\"] = 2] = \"GOOD\";\n  // 良\n  QualityLevel[QualityLevel[\"MEDIUM\"] = 3] = \"MEDIUM\"; // 中\n  return QualityLevel;\n}({});\n\n// 商品状态枚举\nexport let ProductStatus = /*#__PURE__*/function (ProductStatus) {\n  ProductStatus[ProductStatus[\"OFFLINE\"] = 0] = \"OFFLINE\";\n  // 下架\n  ProductStatus[ProductStatus[\"ONLINE\"] = 1] = \"ONLINE\"; // 上架\n  return ProductStatus;\n}({});\n\n// 商品数据接口\n\n// 商品类别接口\n\n// 查询参数接口\n\nconst ProductList = () => {\n  _s();\n  var _qualityLevelMap$view, _qualityLevelMap$view2;\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isDetailVisible, setIsDetailVisible] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [viewingProduct, setViewingProduct] = useState(null);\n  const [fileList, setFileList] = useState([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 质量等级映射\n  const qualityLevelMap = {\n    [QualityLevel.EXCELLENT]: {\n      label: '优',\n      color: 'green'\n    },\n    [QualityLevel.GOOD]: {\n      label: '良',\n      color: 'blue'\n    },\n    [QualityLevel.MEDIUM]: {\n      label: '中',\n      color: 'orange'\n    }\n  };\n\n  // 获取商品列表\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await productService.getProductList(queryParams);\n      if (response.success) {\n        setProducts(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取商品列表失败');\n        setProducts([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      let errorMsg = '获取商品列表失败';\n      if (error.response) {\n        const {\n          status\n        } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问商品列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setProducts([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品类别\n  const fetchCategories = async () => {\n    try {\n      const response = await productService.getCategoryList();\n      if (response.success) {\n        setCategories(response.data);\n      } else {\n        console.error('获取商品类别失败:', response.message);\n        message.warning('获取商品分类失败，部分功能可能受限');\n        setCategories([]);\n      }\n    } catch (error) {\n      console.error('获取商品类别失败:', error);\n      message.error('获取商品分类失败，请刷新页面重试');\n      setCategories([]);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    // 过滤掉空值\n    const filteredValues = Object.keys(values).reduce((acc, key) => {\n      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {\n        acc[key] = values[key];\n      }\n      return acc;\n    }, {});\n    setQueryParams({\n      ...queryParams,\n      ...filteredValues,\n      page: 1\n    });\n    message.info(`正在搜索商品...`);\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    const resetParams = {\n      page: 1,\n      pageSize: queryParams.pageSize // 保持当前页面大小\n    };\n    setQueryParams(resetParams);\n    message.success('搜索条件已重置');\n  };\n\n  // 快速搜索（输入框回车）\n  const handleQuickSearch = value => {\n    if (value.trim()) {\n      searchForm.setFieldsValue({\n        name: value.trim()\n      });\n      handleSearch({\n        name: value.trim()\n      });\n    }\n  };\n\n  // 新增商品\n  const handleAdd = () => {\n    setEditingProduct(null);\n    form.resetFields();\n    setFileList([]);\n    setIsModalVisible(true);\n  };\n\n  // 编辑商品\n  const handleEdit = product => {\n    setEditingProduct(product);\n    form.setFieldsValue({\n      ...product,\n      status: product.status === ProductStatus.ONLINE\n    });\n\n    // 设置图片列表\n    if (product.images && product.images.length > 0) {\n      const imageFiles = product.images.map((url, index) => ({\n        uid: `${index}`,\n        name: `image-${index}`,\n        status: 'done',\n        url: url\n      }));\n      setFileList(imageFiles);\n    } else {\n      setFileList([]);\n    }\n    setIsModalVisible(true);\n  };\n\n  // 查看商品详情\n  const handleView = product => {\n    setViewingProduct(product);\n    setIsDetailVisible(true);\n  };\n\n  // 删除商品\n  const handleDelete = async id => {\n    try {\n      const response = await productService.deleteProduct(id);\n      if (response.success) {\n        message.success({\n          content: '商品删除成功！',\n          duration: 3\n        });\n        // 如果当前页没有数据了，回到上一页\n        if (products.length === 1 && queryParams.page > 1) {\n          setQueryParams({\n            ...queryParams,\n            page: queryParams.page - 1\n          });\n        } else {\n          fetchProducts();\n        }\n      } else {\n        message.error({\n          content: response.message || '删除失败，请稍后重试',\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('删除商品失败:', error);\n      message.error({\n        content: '删除失败，请检查网络连接后重试',\n        duration: 5\n      });\n    }\n  };\n\n  // 保存商品\n  const handleSave = async values => {\n    setSaving(true);\n    try {\n      const productData = {\n        ...values,\n        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,\n        images: fileList.map(file => {\n          var _file$response;\n          return file.url || ((_file$response = file.response) === null || _file$response === void 0 ? void 0 : _file$response.url);\n        }).filter(Boolean)\n      };\n      let response;\n      if (editingProduct) {\n        response = await productService.updateProduct(editingProduct.id, productData);\n      } else {\n        response = await productService.createProduct(productData);\n      }\n      if (response.success) {\n        const successMsg = editingProduct ? '商品信息更新成功！' : '商品创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3\n        });\n        setIsModalVisible(false);\n        form.resetFields();\n        setFileList([]);\n        fetchProducts();\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('name')) {\n            errorMsg = '商品名称已存在，请使用其他名称';\n          } else if (response.message.includes('category')) {\n            errorMsg = '商品分类不存在，请重新选择';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n        message.error({\n          content: errorMsg,\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('保存商品失败:', error);\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const {\n          status,\n          data\n        } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('name')) {\n            errorMsg = '商品名称格式不正确或已存在';\n          } else if (data.error && data.error.includes('category')) {\n            errorMsg = '商品分类无效，请重新选择';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '商品信息冲突，商品名称可能已存在';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n      message.error({\n        content: errorMsg,\n        duration: 5\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 切换商品状态\n  const handleToggleStatus = async product => {\n    const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;\n    const statusText = newStatus === ProductStatus.ONLINE ? '上架' : '下架';\n    try {\n      const response = await productService.updateProductStatus(product.id, newStatus);\n      if (response.success) {\n        message.success({\n          content: `商品\"${product.name}\"已成功${statusText}！`,\n          duration: 3\n        });\n        fetchProducts();\n      } else {\n        message.error({\n          content: response.message || `${statusText}失败，请稍后重试`,\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('更新商品状态失败:', error);\n      message.error({\n        content: `${statusText}失败，请检查网络连接后重试`,\n        duration: 5\n      });\n    }\n  };\n\n  // 图片上传处理\n  const handleUploadChange = ({\n    fileList: newFileList\n  }) => {\n    setFileList(newFileList);\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '商品图片',\n    dataIndex: 'images',\n    key: 'images',\n    width: 100,\n    render: images => images && images.length > 0 ? /*#__PURE__*/_jsxDEV(Image, {\n      width: 60,\n      height: 60,\n      src: images[0],\n      style: {\n        objectFit: 'cover',\n        borderRadius: 4\n      },\n      fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 413,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: 60,\n        height: 60,\n        background: '#f5f5f5',\n        borderRadius: 4,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: \"\\u65E0\\u56FE\\u7247\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '商品名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontWeight: 500\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 433,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '分类',\n    dataIndex: 'categoryName',\n    key: 'categoryName',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '质量等级',\n    dataIndex: 'qualityLevel',\n    key: 'qualityLevel',\n    width: 100,\n    render: level => {\n      const levelInfo = qualityLevelMap[level];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: (levelInfo === null || levelInfo === void 0 ? void 0 : levelInfo.color) || 'default',\n        children: (levelInfo === null || levelInfo === void 0 ? void 0 : levelInfo.label) || '未知'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 451,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '产地',\n    dataIndex: 'origin',\n    key: 'origin',\n    width: 120\n  }, {\n    title: '供应商',\n    dataIndex: 'supplierName',\n    key: 'supplierName',\n    width: 120\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: (status, record) => /*#__PURE__*/_jsxDEV(Switch, {\n      checked: status === ProductStatus.ONLINE,\n      onChange: () => handleToggleStatus(record),\n      checkedChildren: \"\\u4E0A\\u67B6\",\n      unCheckedChildren: \"\\u4E0B\\u67B6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 475,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 180,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleView(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 497,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 505,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontWeight: 'bold',\n              marginBottom: '4px'\n            },\n            children: [\"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u5546\\u54C1\\\"\", record.name, \"\\\"\\u5417\\uFF1F\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 516,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#999'\n            },\n            children: \"\\u5220\\u9664\\u540E\\u5C06\\u65E0\\u6CD5\\u6062\\u590D\\uFF0C\\u8BF7\\u8C28\\u614E\\u64CD\\u4F5C\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 519,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 515,\n          columnNumber: 15\n        }, this),\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\\u5220\\u9664\",\n        cancelText: \"\\u53D6\\u6D88\",\n        okButtonProps: {\n          danger: true\n        },\n        placement: \"topRight\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 534,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 530,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 513,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-list-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u5546\\u54C1\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 546,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: searchForm,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",\n                allowClear: true,\n                onPressEnter: e => handleQuickSearch(e.target.value),\n                suffix: /*#__PURE__*/_jsxDEV(SearchOutlined, {\n                  style: {\n                    color: '#1890ff',\n                    cursor: 'pointer'\n                  },\n                  onClick: () => {\n                    const value = searchForm.getFieldValue('name');\n                    if (value) handleQuickSearch(value);\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 564,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 559,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 558,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"categoryId\",\n              label: \"\\u5546\\u54C1\\u5206\\u7C7B\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\\u5206\\u7C7B\",\n                allowClear: true,\n                children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 579,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 577,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 576,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"qualityLevel\",\n              label: \"\\u8D28\\u91CF\\u7B49\\u7EA7\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u8D28\\u91CF\\u7B49\\u7EA7\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.EXCELLENT,\n                  children: \"\\u4F18\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 589,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.GOOD,\n                  children: \"\\u826F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 590,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.MEDIUM,\n                  children: \"\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 591,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 588,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 587,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u5546\\u54C1\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: ProductStatus.ONLINE,\n                  children: \"\\u4E0A\\u67B6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 598,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: ProductStatus.OFFLINE,\n                  children: \"\\u4E0B\\u67B6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 599,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 597,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 595,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"origin\",\n              label: \"\\u4EA7\\u5730\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u5730\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 605,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 603,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 614,\n                    columnNumber: 27\n                  }, this),\n                  loading: loading,\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 611,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 621,\n                    columnNumber: 27\n                  }, this),\n                  disabled: loading,\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 619,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 610,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 609,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              label: \"\\u641C\\u7D22\\u7ED3\\u679C\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  color: '#666',\n                  fontSize: '14px'\n                },\n                children: loading ? '搜索中...' : `共找到 ${total} 条商品`\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 631,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 630,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 556,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 550,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 549,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 647,\n                columnNumber: 23\n              }, this),\n              onClick: handleAdd,\n              size: \"middle\",\n              children: \"\\u65B0\\u589E\\u5546\\u54C1\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 654,\n                columnNumber: 23\n              }, this),\n              onClick: () => message.info('导出功能开发中...'),\n              disabled: loading || total === 0,\n              children: \"\\u5BFC\\u51FA\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 653,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#666',\n                fontSize: '14px'\n              },\n              children: products.length > 0 && `显示第 ${(queryParams.page - 1) * queryParams.pageSize + 1}-${Math.min(queryParams.page * queryParams.pageSize, total)} 条`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 664,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 670,\n                columnNumber: 23\n              }, this),\n              onClick: fetchProducts,\n              loading: loading,\n              title: \"\\u5237\\u65B0\\u6570\\u636E\",\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 669,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 663,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 662,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 642,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 641,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: products,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        locale: {\n          emptyText: loading ? '加载中...' : /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: '40px 0',\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '16px',\n                color: '#999',\n                marginBottom: '8px'\n              },\n              children: \"\\u6682\\u65E0\\u5546\\u54C1\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                color: '#ccc'\n              },\n              children: Object.keys(queryParams).some(key => key !== 'page' && key !== 'pageSize' && queryParams[key]) ? '请尝试调整搜索条件' : '点击\"新增商品\"按钮添加第一个商品'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 696,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 692,\n            columnNumber: 15\n          }, this)\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => {\n            if (total === 0) return '暂无数据';\n            return `第 ${range[0]}-${range[1]} 条/共 ${total} 条`;\n          },\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          },\n          onShowSizeChange: (current, size) => {\n            setQueryParams({\n              ...queryParams,\n              page: 1,\n              // 改变页面大小时回到第一页\n              pageSize: size\n            });\n          },\n          pageSizeOptions: ['10', '20', '50', '100'],\n          size: 'default'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 684,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 683,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [editingProduct ? /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 31\n        }, this) : /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 739,\n          columnNumber: 50\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: editingProduct ? `编辑商品 - ${editingProduct.name}` : '新增商品'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 740,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 738,\n        columnNumber: 11\n      }, this),\n      open: isModalVisible,\n      onCancel: () => {\n        if (saving) {\n          message.warning('正在保存中，请稍候...');\n          return;\n        }\n        setIsModalVisible(false);\n        form.resetFields();\n        setFileList([]);\n        setEditingProduct(null);\n      },\n      footer: null,\n      width: 800,\n      destroyOnClose: true,\n      maskClosable: !saving,\n      closable: !saving,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入商品名称'\n              }, {\n                min: 2,\n                max: 50,\n                message: '商品名称长度为2-50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",\n                showCount: true,\n                maxLength: 50\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 776,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 767,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"categoryId\",\n              label: \"\\u5546\\u54C1\\u5206\\u7C7B\",\n              rules: [{\n                required: true,\n                message: '请选择商品分类'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\\u5206\\u7C7B\",\n                children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 791,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 789,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 784,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 783,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 766,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"qualityLevel\",\n              label: \"\\u8D28\\u91CF\\u7B49\\u7EA7\",\n              rules: [{\n                required: true,\n                message: '请选择质量等级'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u8D28\\u91CF\\u7B49\\u7EA7\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.EXCELLENT,\n                  children: \"\\u4F18\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 808,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.GOOD,\n                  children: \"\\u826F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 809,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.MEDIUM,\n                  children: \"\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 810,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 807,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 802,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 801,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"origin\",\n              label: \"\\u4EA7\\u5730\",\n              rules: [{\n                required: true,\n                message: '请输入产地'\n              }, {\n                max: 30,\n                message: '产地名称不能超过30个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u5730\",\n                showCount: true,\n                maxLength: 30\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 823,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 815,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 814,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 800,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"supplierId\",\n              label: \"\\u4F9B\\u5E94\\u5546\",\n              rules: [{\n                required: true,\n                message: '请选择供应商'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u4F9B\\u5E94\\u5546\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: 1,\n                  children: \"\\u4F9B\\u5E94\\u5546A\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 841,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 2,\n                  children: \"\\u4F9B\\u5E94\\u5546B\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 842,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 3,\n                  children: \"\\u4F9B\\u5E94\\u5546C\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 843,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 839,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u5546\\u54C1\\u72B6\\u6001\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checkedChildren: \"\\u4E0A\\u67B6\",\n                unCheckedChildren: \"\\u4E0B\\u67B6\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 853,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 848,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 847,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u5546\\u54C1\\u63CF\\u8FF0\",\n          rules: [{\n            max: 500,\n            message: '商品描述不能超过500个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u63CF\\u8FF0\",\n            rows: 4,\n            showCount: true,\n            maxLength: 500\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 865,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 858,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"images\",\n          label: \"\\u5546\\u54C1\\u56FE\\u7247\",\n          extra: \"\\u652F\\u6301jpg\\u3001png\\u683C\\u5F0F\\uFF0C\\u5355\\u5F20\\u56FE\\u7247\\u4E0D\\u8D85\\u8FC75MB\\uFF0C\\u6700\\u591A\\u4E0A\\u4F205\\u5F20\",\n          children: /*#__PURE__*/_jsxDEV(Upload, {\n            listType: \"picture-card\",\n            fileList: fileList,\n            onChange: handleUploadChange,\n            beforeUpload: () => false // 阻止自动上传\n            ,\n            maxCount: 5,\n            accept: \"image/*\",\n            children: fileList.length >= 5 ? null : /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(UploadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 888,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  marginTop: 8\n                },\n                children: \"\\u4E0A\\u4F20\\u56FE\\u7247\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 889,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 887,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 878,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 873,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          style: {\n            marginBottom: 0,\n            marginTop: '24px'\n          },\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              borderTop: '1px solid #f0f0f0',\n              paddingTop: '16px',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#666',\n                fontSize: '12px'\n              },\n              children: editingProduct ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建商品'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 903,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              children: [/*#__PURE__*/_jsxDEV(Button, {\n                onClick: () => {\n                  if (saving) {\n                    message.warning('正在保存中，请稍候...');\n                    return;\n                  }\n                  setIsModalVisible(false);\n                  form.resetFields();\n                  setFileList([]);\n                  setEditingProduct(null);\n                },\n                disabled: saving,\n                size: \"middle\",\n                children: \"\\u53D6\\u6D88\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 907,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: saving,\n                disabled: saving,\n                size: \"middle\",\n                icon: editingProduct ? /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 42\n                }, this) : /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 929,\n                  columnNumber: 61\n                }, this),\n                children: saving ? '保存中...' : editingProduct ? '更新商品' : '创建商品'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 923,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 906,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 896,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 895,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 760,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 736,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 943,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u5546\\u54C1\\u8BE6\\u60C5\", viewingProduct ? ` - ${viewingProduct.name}` : '']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 944,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 942,\n        columnNumber: 11\n      }, this),\n      open: isDetailVisible,\n      onCancel: () => setIsDetailVisible(false),\n      footer: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"primary\",\n        onClick: () => {\n          if (viewingProduct) {\n            setIsDetailVisible(false);\n            handleEdit(viewingProduct);\n          }\n        },\n        children: \"\\u7F16\\u8F91\\u5546\\u54C1\"\n      }, \"edit\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 950,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        onClick: () => setIsDetailVisible(false),\n        children: \"\\u5173\\u95ED\"\n      }, \"close\", false, {\n        fileName: _jsxFileName,\n        lineNumber: 958,\n        columnNumber: 11\n      }, this)],\n      width: 800,\n      children: viewingProduct && /*#__PURE__*/_jsxDEV(Descriptions, {\n        column: 2,\n        bordered: true,\n        children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1ID\",\n          children: viewingProduct.id\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 966,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n          children: viewingProduct.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 967,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u5206\\u7C7B\",\n          children: viewingProduct.categoryName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 968,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u8D28\\u91CF\\u7B49\\u7EA7\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: (_qualityLevelMap$view = qualityLevelMap[viewingProduct.qualityLevel]) === null || _qualityLevelMap$view === void 0 ? void 0 : _qualityLevelMap$view.color,\n            children: (_qualityLevelMap$view2 = qualityLevelMap[viewingProduct.qualityLevel]) === null || _qualityLevelMap$view2 === void 0 ? void 0 : _qualityLevelMap$view2.label\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 970,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 969,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4EA7\\u5730\",\n          children: viewingProduct.origin\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 974,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u4F9B\\u5E94\\u5546\",\n          children: viewingProduct.supplierName\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 975,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u72B6\\u6001\",\n          children: /*#__PURE__*/_jsxDEV(Tag, {\n            color: viewingProduct.status === ProductStatus.ONLINE ? 'green' : 'red',\n            children: viewingProduct.status === ProductStatus.ONLINE ? '上架' : '下架'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 977,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n          children: new Date(viewingProduct.createdAt).toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 981,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u63CF\\u8FF0\",\n          span: 2,\n          children: viewingProduct.description || '暂无描述'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 984,\n          columnNumber: 13\n        }, this), viewingProduct.images && viewingProduct.images.length > 0 && /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n          label: \"\\u5546\\u54C1\\u56FE\\u7247\",\n          span: 2,\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            wrap: true,\n            children: viewingProduct.images.map((image, index) => /*#__PURE__*/_jsxDEV(Image, {\n              width: 100,\n              height: 100,\n              src: image,\n              style: {\n                objectFit: 'cover',\n                borderRadius: 4\n              }\n            }, index, false, {\n              fileName: _jsxFileName,\n              lineNumber: 991,\n              columnNumber: 21\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 989,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 988,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 965,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 940,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 545,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductList, \"/UVt+8d1LaaRiDcxHYyYDJb/iPc=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = ProductList;\nexport default ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Popconfirm", "Typography", "Row", "Col", "Upload", "Image", "Descriptions", "Switch", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ExportOutlined", "ReloadOutlined", "UploadOutlined", "productService", "jsxDEV", "_jsxDEV", "Title", "Option", "TextArea", "QualityLevel", "ProductStatus", "ProductList", "_s", "_qualityLevelMap$view", "_qualityLevelMap$view2", "products", "setProducts", "categories", "setCategories", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "isDetailVisible", "setIsDetailVisible", "editingProduct", "setEditingProduct", "viewingProduct", "setViewingProduct", "fileList", "setFileList", "saving", "setSaving", "form", "useForm", "searchForm", "qualityLevelMap", "EXCELLENT", "label", "color", "GOOD", "MEDIUM", "fetchProducts", "response", "getProductList", "success", "data", "list", "error", "console", "errorMsg", "status", "fetchCategories", "getCategoryList", "warning", "handleSearch", "values", "filteredValues", "Object", "keys", "reduce", "acc", "key", "undefined", "info", "handleReset", "resetFields", "resetParams", "handleQuickSearch", "value", "trim", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "name", "handleAdd", "handleEdit", "product", "ONLINE", "images", "length", "imageFiles", "map", "url", "index", "uid", "handleView", "handleDelete", "id", "deleteProduct", "content", "duration", "handleSave", "productData", "OFFLINE", "file", "_file$response", "filter", "Boolean", "updateProduct", "createProduct", "successMsg", "includes", "handleToggleStatus", "newStatus", "statusText", "updateProductStatus", "handleUploadChange", "newFileList", "columns", "title", "dataIndex", "width", "render", "height", "src", "style", "objectFit", "borderRadius", "fallback", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "display", "alignItems", "justifyContent", "children", "text", "fontWeight", "level", "levelInfo", "record", "checked", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "Date", "toLocaleString", "fixed", "_", "size", "type", "icon", "onClick", "marginBottom", "fontSize", "onConfirm", "okText", "cancelText", "okButtonProps", "danger", "placement", "className", "layout", "onFinish", "autoComplete", "gutter", "xs", "sm", "md", "<PERSON><PERSON>", "placeholder", "allowClear", "onPressEnter", "e", "target", "suffix", "cursor", "getFieldValue", "category", "htmlType", "disabled", "justify", "align", "Math", "min", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "locale", "emptyText", "padding", "textAlign", "some", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onShowSizeChange", "pageSizeOptions", "gap", "open", "onCancel", "footer", "destroyOnClose", "maskClosable", "closable", "span", "rules", "required", "max", "showCount", "max<PERSON><PERSON><PERSON>", "valuePropName", "rows", "extra", "listType", "beforeUpload", "maxCount", "accept", "marginTop", "borderTop", "paddingTop", "column", "bordered", "categoryName", "qualityLevel", "origin", "supplierName", "createdAt", "description", "wrap", "image", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  Upload,\n  Image,\n  InputNumber,\n  Descriptions,\n  Switch,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  UploadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { UploadFile } from 'antd/es/upload/interface';\nimport { productService } from '../../../services/productService';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// 商品质量等级枚举\nexport enum QualityLevel {\n  EXCELLENT = 1, // 优\n  GOOD = 2,      // 良\n  MEDIUM = 3,    // 中\n}\n\n// 商品状态枚举\nexport enum ProductStatus {\n  OFFLINE = 0, // 下架\n  ONLINE = 1,  // 上架\n}\n\n// 商品数据接口\nexport interface Product {\n  id: number;\n  name: string;\n  categoryId: number;\n  categoryName: string;\n  description?: string;\n  qualityLevel: QualityLevel;\n  origin: string;\n  supplierId: number;\n  supplierName: string;\n  status: ProductStatus;\n  images?: string[];\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 商品类别接口\nexport interface Category {\n  id: number;\n  name: string;\n  parentId?: number;\n  level: number;\n}\n\n// 查询参数接口\ninterface ProductQueryParams {\n  name?: string;\n  categoryId?: number;\n  qualityLevel?: QualityLevel;\n  status?: ProductStatus;\n  origin?: string;\n  page: number;\n  pageSize: number;\n}\n\nconst ProductList: React.FC = () => {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<ProductQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isDetailVisible, setIsDetailVisible] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);\n  const [fileList, setFileList] = useState<UploadFile[]>([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 质量等级映射\n  const qualityLevelMap = {\n    [QualityLevel.EXCELLENT]: { label: '优', color: 'green' },\n    [QualityLevel.GOOD]: { label: '良', color: 'blue' },\n    [QualityLevel.MEDIUM]: { label: '中', color: 'orange' },\n  };\n\n  // 获取商品列表\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await productService.getProductList(queryParams);\n      if (response.success) {\n        setProducts(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取商品列表失败');\n        setProducts([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取商品列表失败:', error);\n      let errorMsg = '获取商品列表失败';\n      if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问商品列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setProducts([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品类别\n  const fetchCategories = async () => {\n    try {\n      const response = await productService.getCategoryList();\n      if (response.success) {\n        setCategories(response.data);\n      } else {\n        console.error('获取商品类别失败:', response.message);\n        message.warning('获取商品分类失败，部分功能可能受限');\n        setCategories([]);\n      }\n    } catch (error: any) {\n      console.error('获取商品类别失败:', error);\n      message.error('获取商品分类失败，请刷新页面重试');\n      setCategories([]);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    // 过滤掉空值\n    const filteredValues = Object.keys(values).reduce((acc, key) => {\n      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {\n        acc[key] = values[key];\n      }\n      return acc;\n    }, {} as any);\n\n    setQueryParams({\n      ...queryParams,\n      ...filteredValues,\n      page: 1,\n    });\n\n    message.info(`正在搜索商品...`);\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    const resetParams = {\n      page: 1,\n      pageSize: queryParams.pageSize, // 保持当前页面大小\n    };\n    setQueryParams(resetParams);\n    message.success('搜索条件已重置');\n  };\n\n  // 快速搜索（输入框回车）\n  const handleQuickSearch = (value: string) => {\n    if (value.trim()) {\n      searchForm.setFieldsValue({ name: value.trim() });\n      handleSearch({ name: value.trim() });\n    }\n  };\n\n  // 新增商品\n  const handleAdd = () => {\n    setEditingProduct(null);\n    form.resetFields();\n    setFileList([]);\n    setIsModalVisible(true);\n  };\n\n  // 编辑商品\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product);\n    form.setFieldsValue({\n      ...product,\n      status: product.status === ProductStatus.ONLINE,\n    });\n\n    // 设置图片列表\n    if (product.images && product.images.length > 0) {\n      const imageFiles = product.images.map((url, index) => ({\n        uid: `${index}`,\n        name: `image-${index}`,\n        status: 'done' as const,\n        url: url,\n      }));\n      setFileList(imageFiles);\n    } else {\n      setFileList([]);\n    }\n\n    setIsModalVisible(true);\n  };\n\n  // 查看商品详情\n  const handleView = (product: Product) => {\n    setViewingProduct(product);\n    setIsDetailVisible(true);\n  };\n\n  // 删除商品\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await productService.deleteProduct(id);\n      if (response.success) {\n        message.success({\n          content: '商品删除成功！',\n          duration: 3,\n        });\n        // 如果当前页没有数据了，回到上一页\n        if (products.length === 1 && queryParams.page > 1) {\n          setQueryParams({\n            ...queryParams,\n            page: queryParams.page - 1,\n          });\n        } else {\n          fetchProducts();\n        }\n      } else {\n        message.error({\n          content: response.message || '删除失败，请稍后重试',\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('删除商品失败:', error);\n      message.error({\n        content: '删除失败，请检查网络连接后重试',\n        duration: 5,\n      });\n    }\n  };\n\n  // 保存商品\n  const handleSave = async (values: any) => {\n    setSaving(true);\n\n    try {\n      const productData = {\n        ...values,\n        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,\n        images: fileList.map(file => file.url || file.response?.url).filter(Boolean),\n      };\n\n      let response;\n      if (editingProduct) {\n        response = await productService.updateProduct(editingProduct.id, productData);\n      } else {\n        response = await productService.createProduct(productData);\n      }\n\n      if (response.success) {\n        const successMsg = editingProduct ? '商品信息更新成功！' : '商品创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3,\n        });\n        setIsModalVisible(false);\n        form.resetFields();\n        setFileList([]);\n        fetchProducts();\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('name')) {\n            errorMsg = '商品名称已存在，请使用其他名称';\n          } else if (response.message.includes('category')) {\n            errorMsg = '商品分类不存在，请重新选择';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n\n        message.error({\n          content: errorMsg,\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('保存商品失败:', error);\n\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const { status, data } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('name')) {\n            errorMsg = '商品名称格式不正确或已存在';\n          } else if (data.error && data.error.includes('category')) {\n            errorMsg = '商品分类无效，请重新选择';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '商品信息冲突，商品名称可能已存在';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n\n      message.error({\n        content: errorMsg,\n        duration: 5,\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 切换商品状态\n  const handleToggleStatus = async (product: Product) => {\n    const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;\n    const statusText = newStatus === ProductStatus.ONLINE ? '上架' : '下架';\n\n    try {\n      const response = await productService.updateProductStatus(product.id, newStatus);\n      if (response.success) {\n        message.success({\n          content: `商品\"${product.name}\"已成功${statusText}！`,\n          duration: 3,\n        });\n        fetchProducts();\n      } else {\n        message.error({\n          content: response.message || `${statusText}失败，请稍后重试`,\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('更新商品状态失败:', error);\n      message.error({\n        content: `${statusText}失败，请检查网络连接后重试`,\n        duration: 5,\n      });\n    }\n  };\n\n  // 图片上传处理\n  const handleUploadChange = ({ fileList: newFileList }: any) => {\n    setFileList(newFileList);\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Product> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '商品图片',\n      dataIndex: 'images',\n      key: 'images',\n      width: 100,\n      render: (images: string[]) => (\n        images && images.length > 0 ? (\n          <Image\n            width={60}\n            height={60}\n            src={images[0]}\n            style={{ objectFit: 'cover', borderRadius: 4 }}\n            fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n          />\n        ) : (\n          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            无图片\n          </div>\n        )\n      ),\n    },\n    {\n      title: '商品名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontWeight: 500 }}>{text}</div>\n      ),\n    },\n    {\n      title: '分类',\n      dataIndex: 'categoryName',\n      key: 'categoryName',\n      width: 120,\n      render: (text: string) => <Tag color=\"blue\">{text}</Tag>,\n    },\n    {\n      title: '质量等级',\n      dataIndex: 'qualityLevel',\n      key: 'qualityLevel',\n      width: 100,\n      render: (level: QualityLevel) => {\n        const levelInfo = qualityLevelMap[level];\n        return (\n          <Tag color={levelInfo?.color || 'default'}>\n            {levelInfo?.label || '未知'}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '产地',\n      dataIndex: 'origin',\n      key: 'origin',\n      width: 120,\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplierName',\n      key: 'supplierName',\n      width: 120,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: ProductStatus, record: Product) => (\n        <Switch\n          checked={status === ProductStatus.ONLINE}\n          onChange={() => handleToggleStatus(record)}\n          checkedChildren=\"上架\"\n          unCheckedChildren=\"下架\"\n        />\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 180,\n      fixed: 'right',\n      render: (_, record: Product) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleView(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title={\n              <div>\n                <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>\n                  确定要删除商品\"{record.name}\"吗？\n                </div>\n                <div style={{ fontSize: '12px', color: '#999' }}>\n                  删除后将无法恢复，请谨慎操作\n                </div>\n              </div>\n            }\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定删除\"\n            cancelText=\"取消\"\n            okButtonProps={{ danger: true }}\n            placement=\"topRight\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"product-list-container\">\n      <Title level={2}>商品管理</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          form={searchForm}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"name\" label=\"商品名称\">\n                <Input\n                  placeholder=\"请输入商品名称\"\n                  allowClear\n                  onPressEnter={(e) => handleQuickSearch((e.target as HTMLInputElement).value)}\n                  suffix={\n                    <SearchOutlined\n                      style={{ color: '#1890ff', cursor: 'pointer' }}\n                      onClick={() => {\n                        const value = searchForm.getFieldValue('name');\n                        if (value) handleQuickSearch(value);\n                      }}\n                    />\n                  }\n                />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"categoryId\" label=\"商品分类\">\n                <Select placeholder=\"请选择商品分类\" allowClear>\n                  {categories.map(category => (\n                    <Option key={category.id} value={category.id}>\n                      {category.name}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"qualityLevel\" label=\"质量等级\">\n                <Select placeholder=\"请选择质量等级\" allowClear>\n                  <Option value={QualityLevel.EXCELLENT}>优</Option>\n                  <Option value={QualityLevel.GOOD}>良</Option>\n                  <Option value={QualityLevel.MEDIUM}>中</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"商品状态\">\n                <Select placeholder=\"请选择商品状态\" allowClear>\n                  <Option value={ProductStatus.ONLINE}>上架</Option>\n                  <Option value={ProductStatus.OFFLINE}>下架</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"origin\" label=\"产地\">\n                <Input placeholder=\"请输入产地\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button\n                    type=\"primary\"\n                    htmlType=\"submit\"\n                    icon={<SearchOutlined />}\n                    loading={loading}\n                  >\n                    搜索\n                  </Button>\n                  <Button\n                    onClick={handleReset}\n                    icon={<ReloadOutlined />}\n                    disabled={loading}\n                  >\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item label=\"搜索结果\">\n                <div style={{ color: '#666', fontSize: '14px' }}>\n                  {loading ? '搜索中...' : `共找到 ${total} 条商品`}\n                </div>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={handleAdd}\n                size=\"middle\"\n              >\n                新增商品\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={() => message.info('导出功能开发中...')}\n                disabled={loading || total === 0}\n              >\n                导出数据\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <div style={{ color: '#666', fontSize: '14px' }}>\n                {products.length > 0 && (\n                  `显示第 ${(queryParams.page - 1) * queryParams.pageSize + 1}-${Math.min(queryParams.page * queryParams.pageSize, total)} 条`\n                )}\n              </div>\n              <Button\n                icon={<ReloadOutlined />}\n                onClick={fetchProducts}\n                loading={loading}\n                title=\"刷新数据\"\n              >\n                刷新\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 商品列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={products}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          locale={{\n            emptyText: loading ? '加载中...' : (\n              <div style={{ padding: '40px 0', textAlign: 'center' }}>\n                <div style={{ fontSize: '16px', color: '#999', marginBottom: '8px' }}>\n                  暂无商品数据\n                </div>\n                <div style={{ fontSize: '14px', color: '#ccc' }}>\n                  {Object.keys(queryParams).some(key => key !== 'page' && key !== 'pageSize' && queryParams[key as keyof ProductQueryParams])\n                    ? '请尝试调整搜索条件'\n                    : '点击\"新增商品\"按钮添加第一个商品'\n                  }\n                </div>\n              </div>\n            )\n          }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) => {\n              if (total === 0) return '暂无数据';\n              return `第 ${range[0]}-${range[1]} 条/共 ${total} 条`;\n            },\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n            onShowSizeChange: (current, size) => {\n              setQueryParams({\n                ...queryParams,\n                page: 1, // 改变页面大小时回到第一页\n                pageSize: size,\n              });\n            },\n            pageSizeOptions: ['10', '20', '50', '100'],\n            size: 'default',\n          }}\n        />\n      </Card>\n\n      {/* 商品编辑模态框 */}\n      <Modal\n        title={\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            {editingProduct ? <EditOutlined /> : <PlusOutlined />}\n            <span>{editingProduct ? `编辑商品 - ${editingProduct.name}` : '新增商品'}</span>\n          </div>\n        }\n        open={isModalVisible}\n        onCancel={() => {\n          if (saving) {\n            message.warning('正在保存中，请稍候...');\n            return;\n          }\n          setIsModalVisible(false);\n          form.resetFields();\n          setFileList([]);\n          setEditingProduct(null);\n        }}\n        footer={null}\n        width={800}\n        destroyOnClose\n        maskClosable={!saving}\n        closable={!saving}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"商品名称\"\n                rules={[\n                  { required: true, message: '请输入商品名称' },\n                  { min: 2, max: 50, message: '商品名称长度为2-50个字符' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入商品名称\"\n                  showCount\n                  maxLength={50}\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"categoryId\"\n                label=\"商品分类\"\n                rules={[{ required: true, message: '请选择商品分类' }]}\n              >\n                <Select placeholder=\"请选择商品分类\">\n                  {categories.map(category => (\n                    <Option key={category.id} value={category.id}>\n                      {category.name}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"qualityLevel\"\n                label=\"质量等级\"\n                rules={[{ required: true, message: '请选择质量等级' }]}\n              >\n                <Select placeholder=\"请选择质量等级\">\n                  <Option value={QualityLevel.EXCELLENT}>优</Option>\n                  <Option value={QualityLevel.GOOD}>良</Option>\n                  <Option value={QualityLevel.MEDIUM}>中</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"origin\"\n                label=\"产地\"\n                rules={[\n                  { required: true, message: '请输入产地' },\n                  { max: 30, message: '产地名称不能超过30个字符' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入产地\"\n                  showCount\n                  maxLength={30}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"supplierId\"\n                label=\"供应商\"\n                rules={[{ required: true, message: '请选择供应商' }]}\n              >\n                <Select placeholder=\"请选择供应商\">\n                  {/* TODO: 从供应商服务获取数据 */}\n                  <Option value={1}>供应商A</Option>\n                  <Option value={2}>供应商B</Option>\n                  <Option value={3}>供应商C</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"商品状态\"\n                valuePropName=\"checked\"\n              >\n                <Switch checkedChildren=\"上架\" unCheckedChildren=\"下架\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"商品描述\"\n            rules={[\n              { max: 500, message: '商品描述不能超过500个字符' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入商品描述\"\n              rows={4}\n              showCount\n              maxLength={500}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"images\"\n            label=\"商品图片\"\n            extra=\"支持jpg、png格式，单张图片不超过5MB，最多上传5张\"\n          >\n            <Upload\n              listType=\"picture-card\"\n              fileList={fileList}\n              onChange={handleUploadChange}\n              beforeUpload={() => false} // 阻止自动上传\n              maxCount={5}\n              accept=\"image/*\"\n            >\n              {fileList.length >= 5 ? null : (\n                <div>\n                  <UploadOutlined />\n                  <div style={{ marginTop: 8 }}>上传图片</div>\n                </div>\n              )}\n            </Upload>\n          </Form.Item>\n\n          <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>\n            <div style={{\n              borderTop: '1px solid #f0f0f0',\n              paddingTop: '16px',\n              display: 'flex',\n              justifyContent: 'space-between',\n              alignItems: 'center'\n            }}>\n              <div style={{ color: '#666', fontSize: '12px' }}>\n                {editingProduct ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建商品'}\n              </div>\n              <Space>\n                <Button\n                  onClick={() => {\n                    if (saving) {\n                      message.warning('正在保存中，请稍候...');\n                      return;\n                    }\n                    setIsModalVisible(false);\n                    form.resetFields();\n                    setFileList([]);\n                    setEditingProduct(null);\n                  }}\n                  disabled={saving}\n                  size=\"middle\"\n                >\n                  取消\n                </Button>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={saving}\n                  disabled={saving}\n                  size=\"middle\"\n                  icon={editingProduct ? <EditOutlined /> : <PlusOutlined />}\n                >\n                  {saving ? '保存中...' : (editingProduct ? '更新商品' : '创建商品')}\n                </Button>\n              </Space>\n            </div>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 商品详情模态框 */}\n      <Modal\n        title={\n          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n            <EyeOutlined />\n            <span>商品详情{viewingProduct ? ` - ${viewingProduct.name}` : ''}</span>\n          </div>\n        }\n        open={isDetailVisible}\n        onCancel={() => setIsDetailVisible(false)}\n        footer={[\n          <Button key=\"edit\" type=\"primary\" onClick={() => {\n            if (viewingProduct) {\n              setIsDetailVisible(false);\n              handleEdit(viewingProduct);\n            }\n          }}>\n            编辑商品\n          </Button>,\n          <Button key=\"close\" onClick={() => setIsDetailVisible(false)}>\n            关闭\n          </Button>\n        ]}\n        width={800}\n      >\n        {viewingProduct && (\n          <Descriptions column={2} bordered>\n            <Descriptions.Item label=\"商品ID\">{viewingProduct.id}</Descriptions.Item>\n            <Descriptions.Item label=\"商品名称\">{viewingProduct.name}</Descriptions.Item>\n            <Descriptions.Item label=\"商品分类\">{viewingProduct.categoryName}</Descriptions.Item>\n            <Descriptions.Item label=\"质量等级\">\n              <Tag color={qualityLevelMap[viewingProduct.qualityLevel]?.color}>\n                {qualityLevelMap[viewingProduct.qualityLevel]?.label}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"产地\">{viewingProduct.origin}</Descriptions.Item>\n            <Descriptions.Item label=\"供应商\">{viewingProduct.supplierName}</Descriptions.Item>\n            <Descriptions.Item label=\"状态\">\n              <Tag color={viewingProduct.status === ProductStatus.ONLINE ? 'green' : 'red'}>\n                {viewingProduct.status === ProductStatus.ONLINE ? '上架' : '下架'}\n              </Tag>\n            </Descriptions.Item>\n            <Descriptions.Item label=\"创建时间\">\n              {new Date(viewingProduct.createdAt).toLocaleString()}\n            </Descriptions.Item>\n            <Descriptions.Item label=\"商品描述\" span={2}>\n              {viewingProduct.description || '暂无描述'}\n            </Descriptions.Item>\n            {viewingProduct.images && viewingProduct.images.length > 0 && (\n              <Descriptions.Item label=\"商品图片\" span={2}>\n                <Space wrap>\n                  {viewingProduct.images.map((image, index) => (\n                    <Image\n                      key={index}\n                      width={100}\n                      height={100}\n                      src={image}\n                      style={{ objectFit: 'cover', borderRadius: 4 }}\n                    />\n                  ))}\n                </Space>\n              </Descriptions.Item>\n            )}\n          </Descriptions>\n        )}\n      </Modal>\n    </div>\n  );\n};\n\nexport default ProductList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EAELC,YAAY,EACZC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,cAAc,EACdC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAG1B,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGlB,UAAU;AAC5B,MAAM;EAAEmB;AAAO,CAAC,GAAGzB,MAAM;AACzB,MAAM;EAAE0B;AAAS,CAAC,GAAG3B,KAAK;;AAE1B;AACA,WAAY4B,YAAY,0BAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EACP;EADLA,YAAY,CAAZA,YAAY;EAEP;EAFLA,YAAY,CAAZA,YAAY,4BAGP;EAAA,OAHLA,YAAY;AAAA;;AAMxB;AACA,WAAYC,aAAa,0BAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EACV;EADHA,aAAa,CAAbA,aAAa,4BAEV;EAAA,OAFHA,aAAa;AAAA;;AAKzB;;AAiBA;;AAQA;;AAWA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,qBAAA,EAAAC,sBAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAAC0C,UAAU,EAAEC,aAAa,CAAC,GAAG3C,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAAC4C,OAAO,EAAEC,UAAU,CAAC,GAAG7C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC8C,KAAK,EAAEC,QAAQ,CAAC,GAAG/C,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACgD,WAAW,EAAEC,cAAc,CAAC,GAAGjD,QAAQ,CAAqB;IACjEkD,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACsD,eAAe,EAAEC,kBAAkB,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACwD,cAAc,EAAEC,iBAAiB,CAAC,GAAGzD,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC0D,cAAc,EAAEC,iBAAiB,CAAC,GAAG3D,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC4D,QAAQ,EAAEC,WAAW,CAAC,GAAG7D,QAAQ,CAAe,EAAE,CAAC;EAC1D,MAAM,CAAC8D,MAAM,EAAEC,SAAS,CAAC,GAAG/D,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACgE,IAAI,CAAC,GAAGtD,IAAI,CAACuD,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAGxD,IAAI,CAACuD,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAME,eAAe,GAAG;IACtB,CAACjC,YAAY,CAACkC,SAAS,GAAG;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACxD,CAACpC,YAAY,CAACqC,IAAI,GAAG;MAAEF,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAO,CAAC;IAClD,CAACpC,YAAY,CAACsC,MAAM,GAAG;MAAEH,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAS;EACvD,CAAC;;EAED;EACA,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC5B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM6B,QAAQ,GAAG,MAAM9C,cAAc,CAAC+C,cAAc,CAAC3B,WAAW,CAAC;MACjE,IAAI0B,QAAQ,CAACE,OAAO,EAAE;QACpBnC,WAAW,CAACiC,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;QAC/B/B,QAAQ,CAAC2B,QAAQ,CAACG,IAAI,CAAC/B,KAAK,CAAC;MAC/B,CAAC,MAAM;QACLnC,OAAO,CAACoE,KAAK,CAACL,QAAQ,CAAC/D,OAAO,IAAI,UAAU,CAAC;QAC7C8B,WAAW,CAAC,EAAE,CAAC;QACfM,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIE,QAAQ,GAAG,UAAU;MACzB,IAAIF,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM;UAAEQ;QAAO,CAAC,GAAGH,KAAK,CAACL,QAAQ;QACjC,IAAIQ,MAAM,KAAK,GAAG,EAAE;UAClBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,YAAY;QACzB,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,eAAe;QAC5B;MACF;MACAtE,OAAO,CAACoE,KAAK,CAACE,QAAQ,CAAC;MACvBxC,WAAW,CAAC,EAAE,CAAC;MACfM,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAM9C,cAAc,CAACwD,eAAe,CAAC,CAAC;MACvD,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpBjC,aAAa,CAAC+B,QAAQ,CAACG,IAAI,CAAC;MAC9B,CAAC,MAAM;QACLG,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEL,QAAQ,CAAC/D,OAAO,CAAC;QAC5CA,OAAO,CAAC0E,OAAO,CAAC,mBAAmB,CAAC;QACpC1C,aAAa,CAAC,EAAE,CAAC;MACnB;IACF,CAAC,CAAC,OAAOoC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCpE,OAAO,CAACoE,KAAK,CAAC,kBAAkB,CAAC;MACjCpC,aAAa,CAAC,EAAE,CAAC;IACnB;EACF,CAAC;;EAED;EACA1C,SAAS,CAAC,MAAM;IACdwE,aAAa,CAAC,CAAC;IACfU,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACnC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMsC,YAAY,GAAIC,MAAW,IAAK;IACpC;IACA,MAAMC,cAAc,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;MAC9D,IAAIN,MAAM,CAACM,GAAG,CAAC,KAAKC,SAAS,IAAIP,MAAM,CAACM,GAAG,CAAC,KAAK,IAAI,IAAIN,MAAM,CAACM,GAAG,CAAC,KAAK,EAAE,EAAE;QAC3ED,GAAG,CAACC,GAAG,CAAC,GAAGN,MAAM,CAACM,GAAG,CAAC;MACxB;MACA,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAQ,CAAC;IAEb3C,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAGwC,cAAc;MACjBtC,IAAI,EAAE;IACR,CAAC,CAAC;IAEFvC,OAAO,CAACoF,IAAI,CAAC,WAAW,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB9B,UAAU,CAAC+B,WAAW,CAAC,CAAC;IACxB,MAAMC,WAAW,GAAG;MAClBhD,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAEH,WAAW,CAACG,QAAQ,CAAE;IAClC,CAAC;IACDF,cAAc,CAACiD,WAAW,CAAC;IAC3BvF,OAAO,CAACiE,OAAO,CAAC,SAAS,CAAC;EAC5B,CAAC;;EAED;EACA,MAAMuB,iBAAiB,GAAIC,KAAa,IAAK;IAC3C,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,EAAE;MAChBnC,UAAU,CAACoC,cAAc,CAAC;QAAEC,IAAI,EAAEH,KAAK,CAACC,IAAI,CAAC;MAAE,CAAC,CAAC;MACjDf,YAAY,CAAC;QAAEiB,IAAI,EAAEH,KAAK,CAACC,IAAI,CAAC;MAAE,CAAC,CAAC;IACtC;EACF,CAAC;;EAED;EACA,MAAMG,SAAS,GAAGA,CAAA,KAAM;IACtB/C,iBAAiB,CAAC,IAAI,CAAC;IACvBO,IAAI,CAACiC,WAAW,CAAC,CAAC;IAClBpC,WAAW,CAAC,EAAE,CAAC;IACfR,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMoD,UAAU,GAAIC,OAAgB,IAAK;IACvCjD,iBAAiB,CAACiD,OAAO,CAAC;IAC1B1C,IAAI,CAACsC,cAAc,CAAC;MAClB,GAAGI,OAAO;MACVxB,MAAM,EAAEwB,OAAO,CAACxB,MAAM,KAAK/C,aAAa,CAACwE;IAC3C,CAAC,CAAC;;IAEF;IACA,IAAID,OAAO,CAACE,MAAM,IAAIF,OAAO,CAACE,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAMC,UAAU,GAAGJ,OAAO,CAACE,MAAM,CAACG,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;QACrDC,GAAG,EAAE,GAAGD,KAAK,EAAE;QACfV,IAAI,EAAE,SAASU,KAAK,EAAE;QACtB/B,MAAM,EAAE,MAAe;QACvB8B,GAAG,EAAEA;MACP,CAAC,CAAC,CAAC;MACHnD,WAAW,CAACiD,UAAU,CAAC;IACzB,CAAC,MAAM;MACLjD,WAAW,CAAC,EAAE,CAAC;IACjB;IAEAR,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM8D,UAAU,GAAIT,OAAgB,IAAK;IACvC/C,iBAAiB,CAAC+C,OAAO,CAAC;IAC1BnD,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAM6D,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAM3C,QAAQ,GAAG,MAAM9C,cAAc,CAAC0F,aAAa,CAACD,EAAE,CAAC;MACvD,IAAI3C,QAAQ,CAACE,OAAO,EAAE;QACpBjE,OAAO,CAACiE,OAAO,CAAC;UACd2C,OAAO,EAAE,SAAS;UAClBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF;QACA,IAAIhF,QAAQ,CAACqE,MAAM,KAAK,CAAC,IAAI7D,WAAW,CAACE,IAAI,GAAG,CAAC,EAAE;UACjDD,cAAc,CAAC;YACb,GAAGD,WAAW;YACdE,IAAI,EAAEF,WAAW,CAACE,IAAI,GAAG;UAC3B,CAAC,CAAC;QACJ,CAAC,MAAM;UACLuB,aAAa,CAAC,CAAC;QACjB;MACF,CAAC,MAAM;QACL9D,OAAO,CAACoE,KAAK,CAAC;UACZwC,OAAO,EAAE7C,QAAQ,CAAC/D,OAAO,IAAI,YAAY;UACzC6G,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOzC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BpE,OAAO,CAACoE,KAAK,CAAC;QACZwC,OAAO,EAAE,iBAAiB;QAC1BC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMC,UAAU,GAAG,MAAOlC,MAAW,IAAK;IACxCxB,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAM2D,WAAW,GAAG;QAClB,GAAGnC,MAAM;QACTL,MAAM,EAAEK,MAAM,CAACL,MAAM,GAAG/C,aAAa,CAACwE,MAAM,GAAGxE,aAAa,CAACwF,OAAO;QACpEf,MAAM,EAAEhD,QAAQ,CAACmD,GAAG,CAACa,IAAI;UAAA,IAAAC,cAAA;UAAA,OAAID,IAAI,CAACZ,GAAG,MAAAa,cAAA,GAAID,IAAI,CAAClD,QAAQ,cAAAmD,cAAA,uBAAbA,cAAA,CAAeb,GAAG;QAAA,EAAC,CAACc,MAAM,CAACC,OAAO;MAC7E,CAAC;MAED,IAAIrD,QAAQ;MACZ,IAAIlB,cAAc,EAAE;QAClBkB,QAAQ,GAAG,MAAM9C,cAAc,CAACoG,aAAa,CAACxE,cAAc,CAAC6D,EAAE,EAAEK,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLhD,QAAQ,GAAG,MAAM9C,cAAc,CAACqG,aAAa,CAACP,WAAW,CAAC;MAC5D;MAEA,IAAIhD,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMsD,UAAU,GAAG1E,cAAc,GAAG,WAAW,GAAG,SAAS;QAC3D7C,OAAO,CAACiE,OAAO,CAAC;UACd2C,OAAO,EAAEW,UAAU;UACnBV,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFnE,iBAAiB,CAAC,KAAK,CAAC;QACxBW,IAAI,CAACiC,WAAW,CAAC,CAAC;QAClBpC,WAAW,CAAC,EAAE,CAAC;QACfY,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL;QACA,IAAIQ,QAAQ,GAAG,MAAM;QACrB,IAAIP,QAAQ,CAAC/D,OAAO,EAAE;UACpB,IAAI+D,QAAQ,CAAC/D,OAAO,CAACwH,QAAQ,CAAC,MAAM,CAAC,EAAE;YACrClD,QAAQ,GAAG,iBAAiB;UAC9B,CAAC,MAAM,IAAIP,QAAQ,CAAC/D,OAAO,CAACwH,QAAQ,CAAC,UAAU,CAAC,EAAE;YAChDlD,QAAQ,GAAG,eAAe;UAC5B,CAAC,MAAM,IAAIP,QAAQ,CAAC/D,OAAO,CAACwH,QAAQ,CAAC,YAAY,CAAC,EAAE;YAClDlD,QAAQ,GAAG,kBAAkB;UAC/B,CAAC,MAAM;YACLA,QAAQ,GAAGP,QAAQ,CAAC/D,OAAO;UAC7B;QACF;QAEAA,OAAO,CAACoE,KAAK,CAAC;UACZwC,OAAO,EAAEtC,QAAQ;UACjBuC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOzC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAE/B,IAAIE,QAAQ,GAAG,YAAY;MAC3B,IAAIF,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM;UAAEQ,MAAM;UAAEL;QAAK,CAAC,GAAGE,KAAK,CAACL,QAAQ;QACvC,IAAIQ,MAAM,KAAK,GAAG,EAAE;UAClB,IAAIL,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAACoD,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC7ClD,QAAQ,GAAG,eAAe;UAC5B,CAAC,MAAM,IAAIJ,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAACoD,QAAQ,CAAC,UAAU,CAAC,EAAE;YACxDlD,QAAQ,GAAG,cAAc;UAC3B,CAAC,MAAM;YACLA,QAAQ,GAAGJ,IAAI,CAACE,KAAK,IAAI,gBAAgB;UAC3C;QACF,CAAC,MAAM,IAAIG,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,kBAAkB;QAC/B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,gBAAgB;QAC7B,CAAC,MAAM;UACLA,QAAQ,GAAG,SAASC,MAAM,SAAS;QACrC;MACF,CAAC,MAAM,IAAIH,KAAK,CAACpE,OAAO,EAAE;QACxBsE,QAAQ,GAAGF,KAAK,CAACpE,OAAO;MAC1B;MAEAA,OAAO,CAACoE,KAAK,CAAC;QACZwC,OAAO,EAAEtC,QAAQ;QACjBuC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRzD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMqE,kBAAkB,GAAG,MAAO1B,OAAgB,IAAK;IACrD,MAAM2B,SAAS,GAAG3B,OAAO,CAACxB,MAAM,KAAK/C,aAAa,CAACwE,MAAM,GAAGxE,aAAa,CAACwF,OAAO,GAAGxF,aAAa,CAACwE,MAAM;IACxG,MAAM2B,UAAU,GAAGD,SAAS,KAAKlG,aAAa,CAACwE,MAAM,GAAG,IAAI,GAAG,IAAI;IAEnE,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAM9C,cAAc,CAAC2G,mBAAmB,CAAC7B,OAAO,CAACW,EAAE,EAAEgB,SAAS,CAAC;MAChF,IAAI3D,QAAQ,CAACE,OAAO,EAAE;QACpBjE,OAAO,CAACiE,OAAO,CAAC;UACd2C,OAAO,EAAE,MAAMb,OAAO,CAACH,IAAI,OAAO+B,UAAU,GAAG;UAC/Cd,QAAQ,EAAE;QACZ,CAAC,CAAC;QACF/C,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACL9D,OAAO,CAACoE,KAAK,CAAC;UACZwC,OAAO,EAAE7C,QAAQ,CAAC/D,OAAO,IAAI,GAAG2H,UAAU,UAAU;UACpDd,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOzC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCpE,OAAO,CAACoE,KAAK,CAAC;QACZwC,OAAO,EAAE,GAAGe,UAAU,eAAe;QACrCd,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC;;EAED;EACA,MAAMgB,kBAAkB,GAAGA,CAAC;IAAE5E,QAAQ,EAAE6E;EAAiB,CAAC,KAAK;IAC7D5E,WAAW,CAAC4E,WAAW,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,OAA6B,GAAG,CACpC;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACf/C,GAAG,EAAE,IAAI;IACTgD,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnB/C,GAAG,EAAE,QAAQ;IACbgD,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGlC,MAAgB,IACvBA,MAAM,IAAIA,MAAM,CAACC,MAAM,GAAG,CAAC,gBACzB/E,OAAA,CAACb,KAAK;MACJ4H,KAAK,EAAE,EAAG;MACVE,MAAM,EAAE,EAAG;MACXC,GAAG,EAAEpC,MAAM,CAAC,CAAC,CAAE;MACfqC,KAAK,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,YAAY,EAAE;MAAE,CAAE;MAC/CC,QAAQ,EAAC;IAAgoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1oB,CAAC,gBAEF1H,OAAA;MAAKmH,KAAK,EAAE;QAAEJ,KAAK,EAAE,EAAE;QAAEE,MAAM,EAAE,EAAE;QAAEU,UAAU,EAAE,SAAS;QAAEN,YAAY,EAAE,CAAC;QAAEO,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,EAAC;IAEhJ;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAGX,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjB/C,GAAG,EAAE,MAAM;IACXgD,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,iBACnBhI,OAAA;MAAKmH,KAAK,EAAE;QAAEc,UAAU,EAAE;MAAI,CAAE;MAAAF,QAAA,EAAEC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEhD,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,cAAc;IACzB/C,GAAG,EAAE,cAAc;IACnBgD,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,iBAAKhI,OAAA,CAACtB,GAAG;MAAC8D,KAAK,EAAC,MAAM;MAAAuF,QAAA,EAAEC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACzD,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzB/C,GAAG,EAAE,cAAc;IACnBgD,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGkB,KAAmB,IAAK;MAC/B,MAAMC,SAAS,GAAG9F,eAAe,CAAC6F,KAAK,CAAC;MACxC,oBACElI,OAAA,CAACtB,GAAG;QAAC8D,KAAK,EAAE,CAAA2F,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE3F,KAAK,KAAI,SAAU;QAAAuF,QAAA,EACvC,CAAAI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE5F,KAAK,KAAI;MAAI;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAEV;EACF,CAAC,EACD;IACEb,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnB/C,GAAG,EAAE,QAAQ;IACbgD,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,cAAc;IACzB/C,GAAG,EAAE,cAAc;IACnBgD,KAAK,EAAE;EACT,CAAC,EACD;IACEF,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnB/C,GAAG,EAAE,QAAQ;IACbgD,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC5D,MAAqB,EAAEgF,MAAe,kBAC7CpI,OAAA,CAACX,MAAM;MACLgJ,OAAO,EAAEjF,MAAM,KAAK/C,aAAa,CAACwE,MAAO;MACzCyD,QAAQ,EAAEA,CAAA,KAAMhC,kBAAkB,CAAC8B,MAAM,CAAE;MAC3CG,eAAe,EAAC,cAAI;MACpBC,iBAAiB,EAAC;IAAI;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB;EAEL,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtB/C,GAAG,EAAE,WAAW;IAChBgD,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,IAAK,IAAIS,IAAI,CAACT,IAAI,CAAC,CAACU,cAAc,CAAC;EAC1D,CAAC,EACD;IACE7B,KAAK,EAAE,IAAI;IACX9C,GAAG,EAAE,QAAQ;IACbgD,KAAK,EAAE,GAAG;IACV4B,KAAK,EAAE,OAAO;IACd3B,MAAM,EAAEA,CAAC4B,CAAC,EAAER,MAAe,kBACzBpI,OAAA,CAACzB,KAAK;MAACsK,IAAI,EAAC,OAAO;MAAAd,QAAA,gBACjB/H,OAAA,CAAC1B,MAAM;QACLwK,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE/I,OAAA,CAACN,WAAW;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBsB,OAAO,EAAEA,CAAA,KAAM3D,UAAU,CAAC+C,MAAM,CAAE;QAAAL,QAAA,EACnC;MAED;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1H,OAAA,CAAC1B,MAAM;QACLwK,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE/I,OAAA,CAACR,YAAY;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBsB,OAAO,EAAEA,CAAA,KAAMrE,UAAU,CAACyD,MAAM,CAAE;QAAAL,QAAA,EACnC;MAED;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1H,OAAA,CAAClB,UAAU;QACT+H,KAAK,eACH7G,OAAA;UAAA+H,QAAA,gBACE/H,OAAA;YAAKmH,KAAK,EAAE;cAAEc,UAAU,EAAE,MAAM;cAAEgB,YAAY,EAAE;YAAM,CAAE;YAAAlB,QAAA,GAAC,8CAC/C,EAACK,MAAM,CAAC3D,IAAI,EAAC,gBACvB;UAAA;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eACN1H,OAAA;YAAKmH,KAAK,EAAE;cAAE+B,QAAQ,EAAE,MAAM;cAAE1G,KAAK,EAAE;YAAO,CAAE;YAAAuF,QAAA,EAAC;UAEjD;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;QACDyB,SAAS,EAAEA,CAAA,KAAM7D,YAAY,CAAC8C,MAAM,CAAC7C,EAAE,CAAE;QACzC6D,MAAM,EAAC,0BAAM;QACbC,UAAU,EAAC,cAAI;QACfC,aAAa,EAAE;UAAEC,MAAM,EAAE;QAAK,CAAE;QAChCC,SAAS,EAAC,UAAU;QAAAzB,QAAA,eAEpB/H,OAAA,CAAC1B,MAAM;UACLwK,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZU,MAAM;UACNR,IAAI,eAAE/I,OAAA,CAACP,cAAc;YAAA8H,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAK,QAAA,EAC1B;QAED;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE1H,OAAA;IAAKyJ,SAAS,EAAC,wBAAwB;IAAA1B,QAAA,gBACrC/H,OAAA,CAACC,KAAK;MAACiI,KAAK,EAAE,CAAE;MAAAH,QAAA,EAAC;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7B1H,OAAA,CAAC5B,IAAI;MAACqL,SAAS,EAAC,aAAa;MAACZ,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxC/H,OAAA,CAACpB,IAAI;QACHsD,IAAI,EAAEE,UAAW;QACjBsH,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEnG,YAAa;QACvBoG,YAAY,EAAC,KAAK;QAAA7B,QAAA,eAElB/H,OAAA,CAAChB,GAAG;UAAC6K,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAC1C,KAAK,EAAE;YAAEJ,KAAK,EAAE;UAAO,CAAE;UAAAgB,QAAA,gBAC9C/H,OAAA,CAACf,GAAG;YAAC6K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACzB/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cAACxF,IAAI,EAAC,MAAM;cAAClC,KAAK,EAAC,0BAAM;cAAAwF,QAAA,eACjC/H,OAAA,CAACxB,KAAK;gBACJ0L,WAAW,EAAC,4CAAS;gBACrBC,UAAU;gBACVC,YAAY,EAAGC,CAAC,IAAKhG,iBAAiB,CAAEgG,CAAC,CAACC,MAAM,CAAsBhG,KAAK,CAAE;gBAC7EiG,MAAM,eACJvK,OAAA,CAACT,cAAc;kBACb4H,KAAK,EAAE;oBAAE3E,KAAK,EAAE,SAAS;oBAAEgI,MAAM,EAAE;kBAAU,CAAE;kBAC/CxB,OAAO,EAAEA,CAAA,KAAM;oBACb,MAAM1E,KAAK,GAAGlC,UAAU,CAACqI,aAAa,CAAC,MAAM,CAAC;oBAC9C,IAAInG,KAAK,EAAED,iBAAiB,CAACC,KAAK,CAAC;kBACrC;gBAAE;kBAAAiD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cACF;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAAC6K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACzB/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cAACxF,IAAI,EAAC,YAAY;cAAClC,KAAK,EAAC,0BAAM;cAAAwF,QAAA,eACvC/H,OAAA,CAACvB,MAAM;gBAACyL,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAApC,QAAA,EACrCnH,UAAU,CAACqE,GAAG,CAACyF,QAAQ,iBACtB1K,OAAA,CAACE,MAAM;kBAAmBoE,KAAK,EAAEoG,QAAQ,CAACnF,EAAG;kBAAAwC,QAAA,EAC1C2C,QAAQ,CAACjG;gBAAI,GADHiG,QAAQ,CAACnF,EAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAAC6K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACzB/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cAACxF,IAAI,EAAC,cAAc;cAAClC,KAAK,EAAC,0BAAM;cAAAwF,QAAA,eACzC/H,OAAA,CAACvB,MAAM;gBAACyL,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAApC,QAAA,gBACtC/H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAElE,YAAY,CAACkC,SAAU;kBAAAyF,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD1H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAElE,YAAY,CAACqC,IAAK;kBAAAsF,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C1H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAElE,YAAY,CAACsC,MAAO;kBAAAqF,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAAC6K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACzB/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cAACxF,IAAI,EAAC,QAAQ;cAAClC,KAAK,EAAC,0BAAM;cAAAwF,QAAA,eACnC/H,OAAA,CAACvB,MAAM;gBAACyL,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAApC,QAAA,gBACtC/H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAEjE,aAAa,CAACwE,MAAO;kBAAAkD,QAAA,EAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChD1H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAEjE,aAAa,CAACwF,OAAQ;kBAAAkC,QAAA,EAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAAC6K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACzB/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cAACxF,IAAI,EAAC,QAAQ;cAAClC,KAAK,EAAC,cAAI;cAAAwF,QAAA,eACjC/H,OAAA,CAACxB,KAAK;gBAAC0L,WAAW,EAAC,gCAAO;gBAACC,UAAU;cAAA;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAAC6K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACzB/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cAAAlC,QAAA,eACR/H,OAAA,CAACzB,KAAK;gBAAAwJ,QAAA,gBACJ/H,OAAA,CAAC1B,MAAM;kBACLwK,IAAI,EAAC,SAAS;kBACd6B,QAAQ,EAAC,QAAQ;kBACjB5B,IAAI,eAAE/I,OAAA,CAACT,cAAc;oBAAAgI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzB5G,OAAO,EAAEA,OAAQ;kBAAAiH,QAAA,EAClB;gBAED;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1H,OAAA,CAAC1B,MAAM;kBACL0K,OAAO,EAAE9E,WAAY;kBACrB6E,IAAI,eAAE/I,OAAA,CAACJ,cAAc;oBAAA2H,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzBkD,QAAQ,EAAE9J,OAAQ;kBAAAiH,QAAA,EACnB;gBAED;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAAC6K,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAjC,QAAA,eACzB/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cAAC1H,KAAK,EAAC,0BAAM;cAAAwF,QAAA,eACrB/H,OAAA;gBAAKmH,KAAK,EAAE;kBAAE3E,KAAK,EAAE,MAAM;kBAAE0G,QAAQ,EAAE;gBAAO,CAAE;gBAAAnB,QAAA,EAC7CjH,OAAO,GAAG,QAAQ,GAAG,OAAOE,KAAK;cAAM;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACrC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1H,OAAA,CAAC5B,IAAI;MAACqL,SAAS,EAAC,aAAa;MAACZ,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxC/H,OAAA,CAAChB,GAAG;QAAC6L,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAA/C,QAAA,gBACzC/H,OAAA,CAACf,GAAG;UAAA8I,QAAA,eACF/H,OAAA,CAACzB,KAAK;YAAAwJ,QAAA,gBACJ/H,OAAA,CAAC1B,MAAM;cACLwK,IAAI,EAAC,SAAS;cACdC,IAAI,eAAE/I,OAAA,CAACV,YAAY;gBAAAiI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBsB,OAAO,EAAEtE,SAAU;cACnBmE,IAAI,EAAC,QAAQ;cAAAd,QAAA,EACd;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1H,OAAA,CAAC1B,MAAM;cACLyK,IAAI,eAAE/I,OAAA,CAACL,cAAc;gBAAA4H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBsB,OAAO,EAAEA,CAAA,KAAMnK,OAAO,CAACoF,IAAI,CAAC,YAAY,CAAE;cAC1C2G,QAAQ,EAAE9J,OAAO,IAAIE,KAAK,KAAK,CAAE;cAAA+G,QAAA,EAClC;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACN1H,OAAA,CAACf,GAAG;UAAA8I,QAAA,eACF/H,OAAA,CAACzB,KAAK;YAAAwJ,QAAA,gBACJ/H,OAAA;cAAKmH,KAAK,EAAE;gBAAE3E,KAAK,EAAE,MAAM;gBAAE0G,QAAQ,EAAE;cAAO,CAAE;cAAAnB,QAAA,EAC7CrH,QAAQ,CAACqE,MAAM,GAAG,CAAC,IAClB,OAAO,CAAC7D,WAAW,CAACE,IAAI,GAAG,CAAC,IAAIF,WAAW,CAACG,QAAQ,GAAG,CAAC,IAAI0J,IAAI,CAACC,GAAG,CAAC9J,WAAW,CAACE,IAAI,GAAGF,WAAW,CAACG,QAAQ,EAAEL,KAAK,CAAC;YACrH;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,eACN1H,OAAA,CAAC1B,MAAM;cACLyK,IAAI,eAAE/I,OAAA,CAACJ,cAAc;gBAAA2H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBsB,OAAO,EAAErG,aAAc;cACvB7B,OAAO,EAAEA,OAAQ;cACjB+F,KAAK,EAAC,0BAAM;cAAAkB,QAAA,EACb;YAED;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP1H,OAAA,CAAC5B,IAAI;MAAA2J,QAAA,eACH/H,OAAA,CAAC3B,KAAK;QACJuI,OAAO,EAAEA,OAAQ;QACjBqE,UAAU,EAAEvK,QAAS;QACrBwK,MAAM,EAAC,IAAI;QACXpK,OAAO,EAAEA,OAAQ;QACjBqK,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,MAAM,EAAE;UACNC,SAAS,EAAExK,OAAO,GAAG,QAAQ,gBAC3Bd,OAAA;YAAKmH,KAAK,EAAE;cAAEoE,OAAO,EAAE,QAAQ;cAAEC,SAAS,EAAE;YAAS,CAAE;YAAAzD,QAAA,gBACrD/H,OAAA;cAAKmH,KAAK,EAAE;gBAAE+B,QAAQ,EAAE,MAAM;gBAAE1G,KAAK,EAAE,MAAM;gBAAEyG,YAAY,EAAE;cAAM,CAAE;cAAAlB,QAAA,EAAC;YAEtE;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACN1H,OAAA;cAAKmH,KAAK,EAAE;gBAAE+B,QAAQ,EAAE,MAAM;gBAAE1G,KAAK,EAAE;cAAO,CAAE;cAAAuF,QAAA,EAC7CpE,MAAM,CAACC,IAAI,CAAC1C,WAAW,CAAC,CAACuK,IAAI,CAAC1H,GAAG,IAAIA,GAAG,KAAK,MAAM,IAAIA,GAAG,KAAK,UAAU,IAAI7C,WAAW,CAAC6C,GAAG,CAA6B,CAAC,GACvH,WAAW,GACX;YAAmB;cAAAwD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAET,CAAE;QACFgE,UAAU,EAAE;UACVC,OAAO,EAAEzK,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZ4K,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC9K,KAAK,EAAE+K,KAAK,KAAK;YAC3B,IAAI/K,KAAK,KAAK,CAAC,EAAE,OAAO,MAAM;YAC9B,OAAO,KAAK+K,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ/K,KAAK,IAAI;UACnD,CAAC;UACDsH,QAAQ,EAAEA,CAAClH,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ,CAAC;UACD2K,gBAAgB,EAAEA,CAACL,OAAO,EAAE9C,IAAI,KAAK;YACnC1H,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI,EAAE,CAAC;cAAE;cACTC,QAAQ,EAAEwH;YACZ,CAAC,CAAC;UACJ,CAAC;UACDoD,eAAe,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC;UAC1CpD,IAAI,EAAE;QACR;MAAE;QAAAtB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP1H,OAAA,CAACrB,KAAK;MACJkI,KAAK,eACH7G,OAAA;QAAKmH,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEqE,GAAG,EAAE;QAAM,CAAE;QAAAnE,QAAA,GAC/DrG,cAAc,gBAAG1B,OAAA,CAACR,YAAY;UAAA+H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAG1H,OAAA,CAACV,YAAY;UAAAiI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACrD1H,OAAA;UAAA+H,QAAA,EAAOrG,cAAc,GAAG,UAAUA,cAAc,CAAC+C,IAAI,EAAE,GAAG;QAAM;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrE,CACN;MACDyE,IAAI,EAAE7K,cAAe;MACrB8K,QAAQ,EAAEA,CAAA,KAAM;QACd,IAAIpK,MAAM,EAAE;UACVnD,OAAO,CAAC0E,OAAO,CAAC,cAAc,CAAC;UAC/B;QACF;QACAhC,iBAAiB,CAAC,KAAK,CAAC;QACxBW,IAAI,CAACiC,WAAW,CAAC,CAAC;QAClBpC,WAAW,CAAC,EAAE,CAAC;QACfJ,iBAAiB,CAAC,IAAI,CAAC;MACzB,CAAE;MACF0K,MAAM,EAAE,IAAK;MACbtF,KAAK,EAAE,GAAI;MACXuF,cAAc;MACdC,YAAY,EAAE,CAACvK,MAAO;MACtBwK,QAAQ,EAAE,CAACxK,MAAO;MAAA+F,QAAA,eAElB/H,OAAA,CAACpB,IAAI;QACHsD,IAAI,EAAEA,IAAK;QACXwH,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEhE,UAAW;QACrBiE,YAAY,EAAC,KAAK;QAAA7B,QAAA,gBAElB/H,OAAA,CAAChB,GAAG;UAAC6K,MAAM,EAAE,EAAG;UAAA9B,QAAA,gBACd/H,OAAA,CAACf,GAAG;YAACwN,IAAI,EAAE,EAAG;YAAA1E,QAAA,eACZ/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cACRxF,IAAI,EAAC,MAAM;cACXlC,KAAK,EAAC,0BAAM;cACZmK,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9N,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEmM,GAAG,EAAE,CAAC;gBAAE4B,GAAG,EAAE,EAAE;gBAAE/N,OAAO,EAAE;cAAiB,CAAC,CAC9C;cAAAkJ,QAAA,eAEF/H,OAAA,CAACxB,KAAK;gBACJ0L,WAAW,EAAC,4CAAS;gBACrB2C,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAACwN,IAAI,EAAE,EAAG;YAAA1E,QAAA,eACZ/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cACRxF,IAAI,EAAC,YAAY;cACjBlC,KAAK,EAAC,0BAAM;cACZmK,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9N,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAkJ,QAAA,eAEhD/H,OAAA,CAACvB,MAAM;gBAACyL,WAAW,EAAC,4CAAS;gBAAAnC,QAAA,EAC1BnH,UAAU,CAACqE,GAAG,CAACyF,QAAQ,iBACtB1K,OAAA,CAACE,MAAM;kBAAmBoE,KAAK,EAAEoG,QAAQ,CAACnF,EAAG;kBAAAwC,QAAA,EAC1C2C,QAAQ,CAACjG;gBAAI,GADHiG,QAAQ,CAACnF,EAAE;kBAAAgC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1H,OAAA,CAAChB,GAAG;UAAC6K,MAAM,EAAE,EAAG;UAAA9B,QAAA,gBACd/H,OAAA,CAACf,GAAG;YAACwN,IAAI,EAAE,EAAG;YAAA1E,QAAA,eACZ/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cACRxF,IAAI,EAAC,cAAc;cACnBlC,KAAK,EAAC,0BAAM;cACZmK,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9N,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAkJ,QAAA,eAEhD/H,OAAA,CAACvB,MAAM;gBAACyL,WAAW,EAAC,4CAAS;gBAAAnC,QAAA,gBAC3B/H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAElE,YAAY,CAACkC,SAAU;kBAAAyF,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjD1H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAElE,YAAY,CAACqC,IAAK;kBAAAsF,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5C1H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAElE,YAAY,CAACsC,MAAO;kBAAAqF,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAACwN,IAAI,EAAE,EAAG;YAAA1E,QAAA,eACZ/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cACRxF,IAAI,EAAC,QAAQ;cACblC,KAAK,EAAC,cAAI;cACVmK,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9N,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAE+N,GAAG,EAAE,EAAE;gBAAE/N,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAAkJ,QAAA,eAEF/H,OAAA,CAACxB,KAAK;gBACJ0L,WAAW,EAAC,gCAAO;gBACnB2C,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAAvF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1H,OAAA,CAAChB,GAAG;UAAC6K,MAAM,EAAE,EAAG;UAAA9B,QAAA,gBACd/H,OAAA,CAACf,GAAG;YAACwN,IAAI,EAAE,EAAG;YAAA1E,QAAA,eACZ/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cACRxF,IAAI,EAAC,YAAY;cACjBlC,KAAK,EAAC,oBAAK;cACXmK,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE9N,OAAO,EAAE;cAAS,CAAC,CAAE;cAAAkJ,QAAA,eAE/C/H,OAAA,CAACvB,MAAM;gBAACyL,WAAW,EAAC,sCAAQ;gBAAAnC,QAAA,gBAE1B/H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAE,CAAE;kBAAAyD,QAAA,EAAC;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/B1H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAE,CAAE;kBAAAyD,QAAA,EAAC;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC/B1H,OAAA,CAACE,MAAM;kBAACoE,KAAK,EAAE,CAAE;kBAAAyD,QAAA,EAAC;gBAAI;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1H,OAAA,CAACf,GAAG;YAACwN,IAAI,EAAE,EAAG;YAAA1E,QAAA,eACZ/H,OAAA,CAACpB,IAAI,CAACqL,IAAI;cACRxF,IAAI,EAAC,QAAQ;cACblC,KAAK,EAAC,0BAAM;cACZwK,aAAa,EAAC,SAAS;cAAAhF,QAAA,eAEvB/H,OAAA,CAACX,MAAM;gBAACkJ,eAAe,EAAC,cAAI;gBAACC,iBAAiB,EAAC;cAAI;gBAAAjB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1H,OAAA,CAACpB,IAAI,CAACqL,IAAI;UACRxF,IAAI,EAAC,aAAa;UAClBlC,KAAK,EAAC,0BAAM;UACZmK,KAAK,EAAE,CACL;YAAEE,GAAG,EAAE,GAAG;YAAE/N,OAAO,EAAE;UAAiB,CAAC,CACvC;UAAAkJ,QAAA,eAEF/H,OAAA,CAACG,QAAQ;YACP+J,WAAW,EAAC,4CAAS;YACrB8C,IAAI,EAAE,CAAE;YACRH,SAAS;YACTC,SAAS,EAAE;UAAI;YAAAvF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1H,OAAA,CAACpB,IAAI,CAACqL,IAAI;UACRxF,IAAI,EAAC,QAAQ;UACblC,KAAK,EAAC,0BAAM;UACZ0K,KAAK,EAAC,8HAA+B;UAAAlF,QAAA,eAErC/H,OAAA,CAACd,MAAM;YACLgO,QAAQ,EAAC,cAAc;YACvBpL,QAAQ,EAAEA,QAAS;YACnBwG,QAAQ,EAAE5B,kBAAmB;YAC7ByG,YAAY,EAAEA,CAAA,KAAM,KAAM,CAAC;YAAA;YAC3BC,QAAQ,EAAE,CAAE;YACZC,MAAM,EAAC,SAAS;YAAAtF,QAAA,EAEfjG,QAAQ,CAACiD,MAAM,IAAI,CAAC,GAAG,IAAI,gBAC1B/E,OAAA;cAAA+H,QAAA,gBACE/H,OAAA,CAACH,cAAc;gBAAA0H,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eAClB1H,OAAA;gBAAKmH,KAAK,EAAE;kBAAEmG,SAAS,EAAE;gBAAE,CAAE;gBAAAvF,QAAA,EAAC;cAAI;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC;UACN;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ1H,OAAA,CAACpB,IAAI,CAACqL,IAAI;UAAC9C,KAAK,EAAE;YAAE8B,YAAY,EAAE,CAAC;YAAEqE,SAAS,EAAE;UAAO,CAAE;UAAAvF,QAAA,eACvD/H,OAAA;YAAKmH,KAAK,EAAE;cACVoG,SAAS,EAAE,mBAAmB;cAC9BC,UAAU,EAAE,MAAM;cAClB5F,OAAO,EAAE,MAAM;cACfE,cAAc,EAAE,eAAe;cAC/BD,UAAU,EAAE;YACd,CAAE;YAAAE,QAAA,gBACA/H,OAAA;cAAKmH,KAAK,EAAE;gBAAE3E,KAAK,EAAE,MAAM;gBAAE0G,QAAQ,EAAE;cAAO,CAAE;cAAAnB,QAAA,EAC7CrG,cAAc,GAAG,gBAAgB,GAAG;YAAgB;cAAA6F,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClD,CAAC,eACN1H,OAAA,CAACzB,KAAK;cAAAwJ,QAAA,gBACJ/H,OAAA,CAAC1B,MAAM;gBACL0K,OAAO,EAAEA,CAAA,KAAM;kBACb,IAAIhH,MAAM,EAAE;oBACVnD,OAAO,CAAC0E,OAAO,CAAC,cAAc,CAAC;oBAC/B;kBACF;kBACAhC,iBAAiB,CAAC,KAAK,CAAC;kBACxBW,IAAI,CAACiC,WAAW,CAAC,CAAC;kBAClBpC,WAAW,CAAC,EAAE,CAAC;kBACfJ,iBAAiB,CAAC,IAAI,CAAC;gBACzB,CAAE;gBACFiJ,QAAQ,EAAE5I,MAAO;gBACjB6G,IAAI,EAAC,QAAQ;gBAAAd,QAAA,EACd;cAED;gBAAAR,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eACT1H,OAAA,CAAC1B,MAAM;gBACLwK,IAAI,EAAC,SAAS;gBACd6B,QAAQ,EAAC,QAAQ;gBACjB7J,OAAO,EAAEkB,MAAO;gBAChB4I,QAAQ,EAAE5I,MAAO;gBACjB6G,IAAI,EAAC,QAAQ;gBACbE,IAAI,EAAErH,cAAc,gBAAG1B,OAAA,CAACR,YAAY;kBAAA+H,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAAG1H,OAAA,CAACV,YAAY;kBAAAiI,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAAAK,QAAA,EAE1D/F,MAAM,GAAG,QAAQ,GAAIN,cAAc,GAAG,MAAM,GAAG;cAAO;gBAAA6F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR1H,OAAA,CAACrB,KAAK;MACJkI,KAAK,eACH7G,OAAA;QAAKmH,KAAK,EAAE;UAAES,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEqE,GAAG,EAAE;QAAM,CAAE;QAAAnE,QAAA,gBAChE/H,OAAA,CAACN,WAAW;UAAA6H,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACf1H,OAAA;UAAA+H,QAAA,GAAM,0BAAI,EAACnG,cAAc,GAAG,MAAMA,cAAc,CAAC6C,IAAI,EAAE,GAAG,EAAE;QAAA;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CACN;MACDyE,IAAI,EAAE3K,eAAgB;MACtB4K,QAAQ,EAAEA,CAAA,KAAM3K,kBAAkB,CAAC,KAAK,CAAE;MAC1C4K,MAAM,EAAE,cACNrM,OAAA,CAAC1B,MAAM;QAAYwK,IAAI,EAAC,SAAS;QAACE,OAAO,EAAEA,CAAA,KAAM;UAC/C,IAAIpH,cAAc,EAAE;YAClBH,kBAAkB,CAAC,KAAK,CAAC;YACzBkD,UAAU,CAAC/C,cAAc,CAAC;UAC5B;QACF,CAAE;QAAAmG,QAAA,EAAC;MAEH,GAPY,MAAM;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAOV,CAAC,eACT1H,OAAA,CAAC1B,MAAM;QAAa0K,OAAO,EAAEA,CAAA,KAAMvH,kBAAkB,CAAC,KAAK,CAAE;QAAAsG,QAAA,EAAC;MAE9D,GAFY,OAAO;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAEX,CAAC,CACT;MACFX,KAAK,EAAE,GAAI;MAAAgB,QAAA,EAEVnG,cAAc,iBACb5B,OAAA,CAACZ,YAAY;QAACqO,MAAM,EAAE,CAAE;QAACC,QAAQ;QAAA3F,QAAA,gBAC/B/H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,gBAAM;UAAAwF,QAAA,EAAEnG,cAAc,CAAC2D;QAAE;UAAAgC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACvE1H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,0BAAM;UAAAwF,QAAA,EAAEnG,cAAc,CAAC6C;QAAI;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACzE1H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,0BAAM;UAAAwF,QAAA,EAAEnG,cAAc,CAAC+L;QAAY;UAAApG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACjF1H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,0BAAM;UAAAwF,QAAA,eAC7B/H,OAAA,CAACtB,GAAG;YAAC8D,KAAK,GAAAhC,qBAAA,GAAE6B,eAAe,CAACT,cAAc,CAACgM,YAAY,CAAC,cAAApN,qBAAA,uBAA5CA,qBAAA,CAA8CgC,KAAM;YAAAuF,QAAA,GAAAtH,sBAAA,GAC7D4B,eAAe,CAACT,cAAc,CAACgM,YAAY,CAAC,cAAAnN,sBAAA,uBAA5CA,sBAAA,CAA8C8B;UAAK;YAAAgF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpB1H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,cAAI;UAAAwF,QAAA,EAAEnG,cAAc,CAACiM;QAAM;UAAAtG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eACzE1H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,oBAAK;UAAAwF,QAAA,EAAEnG,cAAc,CAACkM;QAAY;UAAAvG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAoB,CAAC,eAChF1H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,cAAI;UAAAwF,QAAA,eAC3B/H,OAAA,CAACtB,GAAG;YAAC8D,KAAK,EAAEZ,cAAc,CAACwB,MAAM,KAAK/C,aAAa,CAACwE,MAAM,GAAG,OAAO,GAAG,KAAM;YAAAkD,QAAA,EAC1EnG,cAAc,CAACwB,MAAM,KAAK/C,aAAa,CAACwE,MAAM,GAAG,IAAI,GAAG;UAAI;YAAA0C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACW,CAAC,eACpB1H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,0BAAM;UAAAwF,QAAA,EAC5B,IAAIU,IAAI,CAAC7G,cAAc,CAACmM,SAAS,CAAC,CAACrF,cAAc,CAAC;QAAC;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eACpB1H,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,0BAAM;UAACkK,IAAI,EAAE,CAAE;UAAA1E,QAAA,EACrCnG,cAAc,CAACoM,WAAW,IAAI;QAAM;UAAAzG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,EACnB9F,cAAc,CAACkD,MAAM,IAAIlD,cAAc,CAACkD,MAAM,CAACC,MAAM,GAAG,CAAC,iBACxD/E,OAAA,CAACZ,YAAY,CAAC6K,IAAI;UAAC1H,KAAK,EAAC,0BAAM;UAACkK,IAAI,EAAE,CAAE;UAAA1E,QAAA,eACtC/H,OAAA,CAACzB,KAAK;YAAC0P,IAAI;YAAAlG,QAAA,EACRnG,cAAc,CAACkD,MAAM,CAACG,GAAG,CAAC,CAACiJ,KAAK,EAAE/I,KAAK,kBACtCnF,OAAA,CAACb,KAAK;cAEJ4H,KAAK,EAAE,GAAI;cACXE,MAAM,EAAE,GAAI;cACZC,GAAG,EAAEgH,KAAM;cACX/G,KAAK,EAAE;gBAAEC,SAAS,EAAE,OAAO;gBAAEC,YAAY,EAAE;cAAE;YAAE,GAJ1ClC,KAAK;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAKX,CACF;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACS,CACpB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACW;IACf;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACnH,EAAA,CAp5BID,WAAqB;EAAA,QAeV1B,IAAI,CAACuD,OAAO,EACNvD,IAAI,CAACuD,OAAO;AAAA;AAAAgM,EAAA,GAhB7B7N,WAAqB;AAs5B3B,eAAeA,WAAW;AAAC,IAAA6N,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}