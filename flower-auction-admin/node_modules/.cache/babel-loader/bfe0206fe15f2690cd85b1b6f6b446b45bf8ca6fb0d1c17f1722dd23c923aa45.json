{"ast": null, "code": "import { apiClient } from './apiClient';\nexport const roleService = {\n  // 获取角色列表\n  getRoleList: async params => {\n    try {\n      const response = await apiClient.get('/roles', {\n        params\n      });\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '获取角色列表失败';\n      return {\n        success: false,\n        data: {\n          list: [],\n          total: 0,\n          page: 1,\n          pageSize: 10\n        },\n        message: errorMessage\n      };\n    }\n  },\n  // 创建角色\n  createRole: async roleData => {\n    try {\n      const response = await apiClient.post('/roles', roleData);\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '创建角色失败');\n    }\n  },\n  // 更新角色\n  updateRole: async (id, roleData) => {\n    try {\n      const response = await apiClient.put(`/roles/${id}`, roleData);\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '更新角色失败');\n    }\n  },\n  // 删除角色\n  deleteRole: async id => {\n    try {\n      const response = await apiClient.delete(`/roles/${id}`);\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || '删除角色失败');\n    }\n  },\n  // 获取角色详情\n  getRoleDetail: async id => {\n    try {\n      const response = await apiClient.get(`/roles/${id}`);\n      return response.data;\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      throw new Error(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || '获取角色详情失败');\n    }\n  },\n  // 获取权限列表\n  getPermissionList: async () => {\n    try {\n      const response = await apiClient.get('/permissions');\n      return response.data;\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      throw new Error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || '获取权限列表失败');\n    }\n  },\n  // 更新角色权限\n  updateRolePermissions: async (roleId, permissionIds) => {\n    try {\n      const response = await apiClient.put(`/roles/${roleId}/permissions`, {\n        permissionIds\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      throw new Error(((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.message) || '更新角色权限失败');\n    }\n  },\n  // 获取角色权限\n  getRolePermissions: async roleId => {\n    try {\n      const response = await apiClient.get(`/roles/${roleId}/permissions`);\n      return response.data;\n    } catch (error) {\n      var _error$response9, _error$response9$data;\n      throw new Error(((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.message) || '获取角色权限失败');\n    }\n  },\n  // 批量删除角色\n  batchDeleteRoles: async ids => {\n    try {\n      const response = await apiClient.delete('/roles/batch', {\n        data: {\n          ids\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response0, _error$response0$data;\n      throw new Error(((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : (_error$response0$data = _error$response0.data) === null || _error$response0$data === void 0 ? void 0 : _error$response0$data.message) || '批量删除角色失败');\n    }\n  },\n  // 复制角色\n  copyRole: async (id, newName, newCode) => {\n    try {\n      const response = await apiClient.post(`/roles/${id}/copy`, {\n        name: newName,\n        code: newCode\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response1, _error$response1$data;\n      throw new Error(((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : (_error$response1$data = _error$response1.data) === null || _error$response1$data === void 0 ? void 0 : _error$response1$data.message) || '复制角色失败');\n    }\n  },\n  // 获取角色统计信息\n  getRoleStatistics: async () => {\n    try {\n      const response = await apiClient.get('/roles/statistics');\n      return response.data;\n    } catch (error) {\n      var _error$response10, _error$response10$dat;\n      throw new Error(((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : (_error$response10$dat = _error$response10.data) === null || _error$response10$dat === void 0 ? void 0 : _error$response10$dat.message) || '获取角色统计信息失败');\n    }\n  },\n  // 检查角色编码是否存在\n  checkRoleCodeExists: async (code, excludeId) => {\n    try {\n      const params = excludeId ? {\n        code,\n        excludeId\n      } : {\n        code\n      };\n      const response = await apiClient.get('/roles/check-code', {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response11, _error$response11$dat;\n      throw new Error(((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : (_error$response11$dat = _error$response11.data) === null || _error$response11$dat === void 0 ? void 0 : _error$response11$dat.message) || '检查角色编码失败');\n    }\n  }\n};", "map": {"version": 3, "names": ["apiClient", "roleService", "getRoleList", "params", "response", "get", "success", "data", "error", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errorMessage", "message", "list", "total", "page", "pageSize", "createRole", "roleData", "post", "_error$response3", "_error$response3$data", "Error", "updateRole", "id", "put", "_error$response4", "_error$response4$data", "deleteRole", "delete", "_error$response5", "_error$response5$data", "getRoleDetail", "_error$response6", "_error$response6$data", "getPermissionList", "_error$response7", "_error$response7$data", "updateRolePermissions", "roleId", "permissionIds", "_error$response8", "_error$response8$data", "getRolePermissions", "_error$response9", "_error$response9$data", "batchDeleteRoles", "ids", "_error$response0", "_error$response0$data", "copyRole", "newName", "newCode", "name", "code", "_error$response1", "_error$response1$data", "getRoleStatistics", "_error$response10", "_error$response10$dat", "checkRoleCodeExists", "excludeId", "_error$response11", "_error$response11$dat"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/roleService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\nimport { Role, Permission } from '../pages/Users/<USER>';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data: T;\n  message?: string;\n}\n\nexport interface RoleListResponse {\n  list: Role[];\n  total: number;\n  page: number;\n  pageSize: number;\n}\n\nexport interface RoleQueryParams {\n  name?: string;\n  code?: string;\n  status?: number;\n  page: number;\n  pageSize: number;\n}\n\nexport interface CreateRoleRequest {\n  name: string;\n  code: string;\n  description?: string;\n  status: number;\n}\n\nexport interface UpdateRoleRequest {\n  name: string;\n  description?: string;\n  status: number;\n}\n\nexport const roleService = {\n  // 获取角色列表\n  getRoleList: async (params: RoleQueryParams): Promise<ApiResponse<RoleListResponse>> => {\n    try {\n      const response = await apiClient.get('/roles', { params });\n      return {\n        success: true,\n        data: response.data,\n      };\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.error || error.response?.data?.message || '获取角色列表失败';\n      return {\n        success: false,\n        data: { list: [], total: 0, page: 1, pageSize: 10 },\n        message: errorMessage,\n      };\n    }\n  },\n\n  // 创建角色\n  createRole: async (roleData: CreateRoleRequest): Promise<ApiResponse<Role>> => {\n    try {\n      const response = await apiClient.post('/roles', roleData);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '创建角色失败');\n    }\n  },\n\n  // 更新角色\n  updateRole: async (id: number, roleData: UpdateRoleRequest): Promise<ApiResponse<Role>> => {\n    try {\n      const response = await apiClient.put(`/roles/${id}`, roleData);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '更新角色失败');\n    }\n  },\n\n  // 删除角色\n  deleteRole: async (id: number): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.delete(`/roles/${id}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '删除角色失败');\n    }\n  },\n\n  // 获取角色详情\n  getRoleDetail: async (id: number): Promise<ApiResponse<Role>> => {\n    try {\n      const response = await apiClient.get(`/roles/${id}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '获取角色详情失败');\n    }\n  },\n\n  // 获取权限列表\n  getPermissionList: async (): Promise<ApiResponse<Permission[]>> => {\n    try {\n      const response = await apiClient.get('/permissions');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '获取权限列表失败');\n    }\n  },\n\n  // 更新角色权限\n  updateRolePermissions: async (roleId: number, permissionIds: number[]): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.put(`/roles/${roleId}/permissions`, {\n        permissionIds,\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '更新角色权限失败');\n    }\n  },\n\n  // 获取角色权限\n  getRolePermissions: async (roleId: number): Promise<ApiResponse<number[]>> => {\n    try {\n      const response = await apiClient.get(`/roles/${roleId}/permissions`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '获取角色权限失败');\n    }\n  },\n\n  // 批量删除角色\n  batchDeleteRoles: async (ids: number[]): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.delete('/roles/batch', { data: { ids } });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '批量删除角色失败');\n    }\n  },\n\n  // 复制角色\n  copyRole: async (id: number, newName: string, newCode: string): Promise<ApiResponse<Role>> => {\n    try {\n      const response = await apiClient.post(`/roles/${id}/copy`, {\n        name: newName,\n        code: newCode,\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '复制角色失败');\n    }\n  },\n\n  // 获取角色统计信息\n  getRoleStatistics: async (): Promise<ApiResponse<{\n    total: number;\n    activeRoles: number;\n    totalPermissions: number;\n    roleDistribution: Record<string, number>;\n  }>> => {\n    try {\n      const response = await apiClient.get('/roles/statistics');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '获取角色统计信息失败');\n    }\n  },\n\n  // 检查角色编码是否存在\n  checkRoleCodeExists: async (code: string, excludeId?: number): Promise<ApiResponse<boolean>> => {\n    try {\n      const params = excludeId ? { code, excludeId } : { code };\n      const response = await apiClient.get('/roles/check-code', { params });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '检查角色编码失败');\n    }\n  },\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAqCvC,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,WAAW,EAAE,MAAOC,MAAuB,IAA6C;IACtF,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,QAAQ,EAAE;QAAEF;MAAO,CAAC,CAAC;MAC1D,OAAO;QACLG,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEH,QAAQ,CAACG;MACjB,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAJ,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,OAAAG,gBAAA,GAAIH,KAAK,CAACJ,QAAQ,cAAAO,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAI,UAAU;MAC/F,OAAO;QACLR,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE;UAAEQ,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAG,CAAC;QACnDJ,OAAO,EAAED;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAM,UAAU,EAAE,MAAOC,QAA2B,IAAiC;IAC7E,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMJ,SAAS,CAACqB,IAAI,CAAC,QAAQ,EAAED,QAAQ,CAAC;MACzD,OAAOhB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIC,KAAK,CAAC,EAAAF,gBAAA,GAAAd,KAAK,CAACJ,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBT,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACAW,UAAU,EAAE,MAAAA,CAAOC,EAAU,EAAEN,QAA2B,KAAiC;IACzF,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMJ,SAAS,CAAC2B,GAAG,CAAC,UAAUD,EAAE,EAAE,EAAEN,QAAQ,CAAC;MAC9D,OAAOhB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIL,KAAK,CAAC,EAAAI,gBAAA,GAAApB,KAAK,CAACJ,QAAQ,cAAAwB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBf,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACAgB,UAAU,EAAE,MAAOJ,EAAU,IAA2B;IACtD,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMJ,SAAS,CAAC+B,MAAM,CAAC,UAAUL,EAAE,EAAE,CAAC;MACvD,OAAOtB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIT,KAAK,CAAC,EAAAQ,gBAAA,GAAAxB,KAAK,CAACJ,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACAoB,aAAa,EAAE,MAAOR,EAAU,IAAiC;IAC/D,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,UAAUqB,EAAE,EAAE,CAAC;MACpD,OAAOtB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA2B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIZ,KAAK,CAAC,EAAAW,gBAAA,GAAA3B,KAAK,CAACJ,QAAQ,cAAA+B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBtB,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACAuB,iBAAiB,EAAE,MAAAA,CAAA,KAAgD;IACjE,IAAI;MACF,MAAMjC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,cAAc,CAAC;MACpD,OAAOD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA8B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIf,KAAK,CAAC,EAAAc,gBAAA,GAAA9B,KAAK,CAACJ,QAAQ,cAAAkC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB/B,IAAI,cAAAgC,qBAAA,uBAApBA,qBAAA,CAAsBzB,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACA0B,qBAAqB,EAAE,MAAAA,CAAOC,MAAc,EAAEC,aAAuB,KAA2B;IAC9F,IAAI;MACF,MAAMtC,QAAQ,GAAG,MAAMJ,SAAS,CAAC2B,GAAG,CAAC,UAAUc,MAAM,cAAc,EAAE;QACnEC;MACF,CAAC,CAAC;MACF,OAAOtC,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAmC,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIpB,KAAK,CAAC,EAAAmB,gBAAA,GAAAnC,KAAK,CAACJ,QAAQ,cAAAuC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBpC,IAAI,cAAAqC,qBAAA,uBAApBA,qBAAA,CAAsB9B,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACA+B,kBAAkB,EAAE,MAAOJ,MAAc,IAAqC;IAC5E,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,UAAUoC,MAAM,cAAc,CAAC;MACpE,OAAOrC,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAsC,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIvB,KAAK,CAAC,EAAAsB,gBAAA,GAAAtC,KAAK,CAACJ,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvC,IAAI,cAAAwC,qBAAA,uBAApBA,qBAAA,CAAsBjC,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACAkC,gBAAgB,EAAE,MAAOC,GAAa,IAA2B;IAC/D,IAAI;MACF,MAAM7C,QAAQ,GAAG,MAAMJ,SAAS,CAAC+B,MAAM,CAAC,cAAc,EAAE;QAAExB,IAAI,EAAE;UAAE0C;QAAI;MAAE,CAAC,CAAC;MAC1E,OAAO7C,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA0C,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI3B,KAAK,CAAC,EAAA0B,gBAAA,GAAA1C,KAAK,CAACJ,QAAQ,cAAA8C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBrC,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACAsC,QAAQ,EAAE,MAAAA,CAAO1B,EAAU,EAAE2B,OAAe,EAAEC,OAAe,KAAiC;IAC5F,IAAI;MACF,MAAMlD,QAAQ,GAAG,MAAMJ,SAAS,CAACqB,IAAI,CAAC,UAAUK,EAAE,OAAO,EAAE;QACzD6B,IAAI,EAAEF,OAAO;QACbG,IAAI,EAAEF;MACR,CAAC,CAAC;MACF,OAAOlD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAiD,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIlC,KAAK,CAAC,EAAAiC,gBAAA,GAAAjD,KAAK,CAACJ,QAAQ,cAAAqD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBlD,IAAI,cAAAmD,qBAAA,uBAApBA,qBAAA,CAAsB5C,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACA6C,iBAAiB,EAAE,MAAAA,CAAA,KAKZ;IACL,IAAI;MACF,MAAMvD,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,mBAAmB,CAAC;MACzD,OAAOD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAoD,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIrC,KAAK,CAAC,EAAAoC,iBAAA,GAAApD,KAAK,CAACJ,QAAQ,cAAAwD,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBrD,IAAI,cAAAsD,qBAAA,uBAApBA,qBAAA,CAAsB/C,OAAO,KAAI,YAAY,CAAC;IAChE;EACF,CAAC;EAED;EACAgD,mBAAmB,EAAE,MAAAA,CAAON,IAAY,EAAEO,SAAkB,KAAoC;IAC9F,IAAI;MACF,MAAM5D,MAAM,GAAG4D,SAAS,GAAG;QAAEP,IAAI;QAAEO;MAAU,CAAC,GAAG;QAAEP;MAAK,CAAC;MACzD,MAAMpD,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,mBAAmB,EAAE;QAAEF;MAAO,CAAC,CAAC;MACrE,OAAOC,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAwD,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIzC,KAAK,CAAC,EAAAwC,iBAAA,GAAAxD,KAAK,CAACJ,QAAQ,cAAA4D,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBzD,IAAI,cAAA0D,qBAAA,uBAApBA,qBAAA,CAAsBnD,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}