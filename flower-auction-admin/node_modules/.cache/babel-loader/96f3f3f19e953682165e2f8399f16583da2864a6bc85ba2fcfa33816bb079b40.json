{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Popconfirm, Typography, Row, Col, DatePicker, Switch } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, UserOutlined, ExportOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { userService } from '../../../services/userService';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\n\n// 用户类型枚举\nexport let UserType = /*#__PURE__*/function (UserType) {\n  UserType[UserType[\"AUCTIONEER\"] = 1] = \"AUCTIONEER\";\n  // 拍卖师\n  UserType[UserType[\"BUYER\"] = 2] = \"BUYER\";\n  // 买家\n  UserType[UserType[\"ADMIN\"] = 3] = \"ADMIN\";\n  // 管理员\n  UserType[UserType[\"QUALITY_INSPECTOR\"] = 4] = \"QUALITY_INSPECTOR\"; // 质检员\n  return UserType;\n}({});\n\n// 用户状态枚举\nexport let UserStatus = /*#__PURE__*/function (UserStatus) {\n  UserStatus[UserStatus[\"DISABLED\"] = 0] = \"DISABLED\";\n  // 禁用\n  UserStatus[UserStatus[\"ENABLED\"] = 1] = \"ENABLED\"; // 启用\n  return UserStatus;\n}({});\n\n// 用户数据接口\n\n// 查询参数接口\n\nconst UserList = () => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 用户类型映射\n  const userTypeMap = {\n    [UserType.AUCTIONEER]: {\n      label: '拍卖师',\n      color: 'blue'\n    },\n    [UserType.BUYER]: {\n      label: '买家',\n      color: 'green'\n    },\n    [UserType.ADMIN]: {\n      label: '管理员',\n      color: 'red'\n    },\n    [UserType.QUALITY_INSPECTOR]: {\n      label: '质检员',\n      color: 'orange'\n    }\n  };\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    setLoading(true);\n    try {\n      const response = await userService.getUserList(queryParams);\n      if (response.success) {\n        setUsers(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取用户列表失败');\n        setUsers([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      message.error(error.message || '获取用户列表失败');\n      setUsers([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchUsers();\n  }, [queryParams]);\n\n  // 保存用户\n  const handleSave = async values => {\n    setSaving(true);\n    try {\n      const userData = {\n        ...values,\n        status: values.status ? UserStatus.ENABLED : UserStatus.DISABLED\n      };\n\n      // 删除确认密码字段，后端不需要\n      delete userData.confirmPassword;\n      let response;\n      if (editingUser) {\n        response = await userService.updateUser(editingUser.id, userData);\n      } else {\n        response = await userService.createUser(userData);\n      }\n      if (response.success) {\n        const successMsg = editingUser ? '用户信息更新成功！' : '用户创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3\n        });\n        setIsModalVisible(false);\n        form.resetFields(); // 重置表单\n        fetchUsers(); // 刷新列表\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('username')) {\n            errorMsg = '用户名已存在，请使用其他用户名';\n          } else if (response.message.includes('phone')) {\n            errorMsg = '手机号已被使用，请使用其他手机号';\n          } else if (response.message.includes('email')) {\n            errorMsg = '邮箱已被使用，请使用其他邮箱';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n        message.error({\n          content: errorMsg,\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n\n      // 处理网络错误和其他异常\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const {\n          status,\n          data\n        } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('username')) {\n            errorMsg = '用户名已存在或格式不正确';\n          } else if (data.error && data.error.includes('phone')) {\n            errorMsg = '手机号格式不正确或已被使用';\n          } else if (data.error && data.error.includes('email')) {\n            errorMsg = '邮箱格式不正确或已被使用';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '用户信息冲突，用户名、手机号或邮箱已被使用';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n      message.error({\n        content: errorMsg,\n        duration: 5\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 切换用户状态\n  const handleToggleStatus = async user => {\n    try {\n      const newStatus = user.status === UserStatus.ENABLED ? UserStatus.DISABLED : UserStatus.ENABLED;\n      const response = await userService.updateUserStatus(user.id, newStatus);\n      if (response.success) {\n        message.success('状态更新成功');\n        fetchUsers();\n      } else {\n        message.error(response.message || '状态更新失败');\n      }\n    } catch (error) {\n      message.error(error.message || '状态更新失败');\n    }\n  };\n\n  // 打开新增用户模态框\n  const handleAdd = () => {\n    setEditingUser(null);\n    setIsModalVisible(true);\n    form.resetFields();\n    // 设置默认值\n    form.setFieldsValue({\n      status: true,\n      // 默认启用\n      userType: UserType.BUYER // 默认买家\n    });\n  };\n\n  // 编辑用户\n  const handleEdit = user => {\n    setEditingUser(user);\n    setIsModalVisible(true);\n    form.setFieldsValue({\n      ...user,\n      status: user.status === UserStatus.ENABLED\n    });\n  };\n\n  // 删除用户\n  const handleDelete = async userId => {\n    try {\n      // TODO: 实现删除用户功能\n      message.success('删除成功');\n      fetchUsers();\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 搜索用户\n  const handleSearch = values => {\n    const newParams = {\n      ...queryParams,\n      page: 1,\n      // 重置到第一页\n      ...values\n    };\n    setQueryParams(newParams);\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    const resetParams = {\n      page: 1,\n      pageSize: 10\n    };\n    setQueryParams(resetParams);\n  };\n\n  // 导出用户数据\n  const handleExport = async () => {\n    try {\n      const response = await userService.exportUsers(queryParams);\n      if (response.success) {\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]));\n        const link = document.createElement('a');\n        link.href = url;\n        link.setAttribute('download', `users_${new Date().getTime()}.xlsx`);\n        document.body.appendChild(link);\n        link.click();\n        link.remove();\n        window.URL.revokeObjectURL(url);\n        message.success('导出成功');\n      } else {\n        message.error(response.message || '导出失败');\n      }\n    } catch (error) {\n      message.error(error.message || '导出失败');\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 11\n      }, this), text]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 320,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '真实姓名',\n    dataIndex: 'realName',\n    key: 'realName',\n    width: 100,\n    render: text => text || '-'\n  }, {\n    title: '手机号',\n    dataIndex: 'phone',\n    key: 'phone',\n    width: 120\n  }, {\n    title: '邮箱',\n    dataIndex: 'email',\n    key: 'email',\n    width: 180,\n    render: text => text || '-'\n  }, {\n    title: '用户类型',\n    dataIndex: 'userType',\n    key: 'userType',\n    width: 100,\n    render: userType => {\n      const typeInfo = userTypeMap[userType];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: (typeInfo === null || typeInfo === void 0 ? void 0 : typeInfo.color) || 'default',\n        children: (typeInfo === null || typeInfo === void 0 ? void 0 : typeInfo.label) || '未知'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 354,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: (status, record) => /*#__PURE__*/_jsxDEV(Switch, {\n      checked: status === UserStatus.ENABLED,\n      onChange: () => handleToggleStatus(record),\n      checkedChildren: \"\\u542F\\u7528\",\n      unCheckedChildren: \"\\u7981\\u7528\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 391,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u7528\\u6237\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 402,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 396,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-list-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: searchForm,\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              label: \"\\u7528\\u6237\\u540D\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 431,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 430,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 429,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"phone\",\n              label: \"\\u624B\\u673A\\u53F7\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u624B\\u673A\\u53F7\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 436,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 435,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 434,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"userType\",\n              label: \"\\u7528\\u6237\\u7C7B\\u578B\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u7528\\u6237\\u7C7B\\u578B\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.AUCTIONEER,\n                  children: \"\\u62CD\\u5356\\u5E08\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.BUYER,\n                  children: \"\\u4E70\\u5BB6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 443,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.ADMIN,\n                  children: \"\\u7BA1\\u7406\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.QUALITY_INSPECTOR,\n                  children: \"\\u8D28\\u68C0\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 445,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 441,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 439,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: UserStatus.ENABLED,\n                  children: \"\\u542F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 452,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserStatus.DISABLED,\n                  children: \"\\u7981\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 453,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 451,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 449,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dateRange\",\n              label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n              children: /*#__PURE__*/_jsxDEV(RangePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 459,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 465,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 468,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 464,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 462,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 422,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 421,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 485,\n                columnNumber: 23\n              }, this),\n              onClick: handleAdd,\n              children: \"\\u65B0\\u589E\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 491,\n                columnNumber: 23\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 490,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 482,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 481,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 500,\n              columnNumber: 21\n            }, this),\n            onClick: fetchUsers,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 499,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 498,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 480,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 479,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: users,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 512,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 511,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingUser ? '编辑用户' : '新增用户',\n      open: isModalVisible,\n      onCancel: () => {\n        setIsModalVisible(false);\n        form.resetFields();\n        setEditingUser(null);\n      },\n      footer: null,\n      width: 600,\n      destroyOnClose: true,\n      maskClosable: false,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              label: \"\\u7528\\u6237\\u540D\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 3,\n                max: 20,\n                message: '用户名长度为3-20个字符'\n              }, {\n                pattern: /^[a-zA-Z0-9_]+$/,\n                message: '用户名只能包含字母、数字和下划线'\n              }],\n              extra: !editingUser ? \"用户名创建后不可修改，请谨慎填写\" : undefined,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\\uFF083-20\\u4E2A\\u5B57\\u7B26\\uFF09\",\n                disabled: !!editingUser,\n                showCount: true,\n                maxLength: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 569,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"realName\",\n              label: \"\\u771F\\u5B9E\\u59D3\\u540D\",\n              rules: [{\n                required: true,\n                message: '请输入真实姓名'\n              }, {\n                max: 20,\n                message: '真实姓名不能超过20个字符'\n              }, {\n                pattern: /^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/,\n                message: '真实姓名只能包含中文、英文和空格'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u771F\\u5B9E\\u59D3\\u540D\",\n                showCount: true,\n                maxLength: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 587,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 578,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 577,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"phone\",\n              label: \"\\u624B\\u673A\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入手机号'\n              }, {\n                pattern: /^1[3-9]\\d{9}$/,\n                message: '请输入正确的11位手机号码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u516511\\u4F4D\\u624B\\u673A\\u53F7\\u7801\",\n                maxLength: 11,\n                addonBefore: \"+86\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 606,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"email\",\n              label: \"\\u90AE\\u7BB1\",\n              rules: [{\n                required: true,\n                message: '请输入邮箱'\n              }, {\n                type: 'email',\n                message: '请输入正确的邮箱地址'\n              }, {\n                max: 50,\n                message: '邮箱长度不能超过50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\",\n                type: \"email\",\n                maxLength: 50\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 614,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 613,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 596,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"userType\",\n              label: \"\\u7528\\u6237\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择用户类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u7528\\u6237\\u7C7B\\u578B\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.AUCTIONEER,\n                  children: \"\\u62CD\\u5356\\u5E08\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 640,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.BUYER,\n                  children: \"\\u4E70\\u5BB6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 641,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.ADMIN,\n                  children: \"\\u7BA1\\u7406\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 642,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.QUALITY_INSPECTOR,\n                  children: \"\\u8D28\\u68C0\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 643,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 639,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 634,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 633,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checkedChildren: \"\\u542F\\u7528\",\n                unCheckedChildren: \"\\u7981\\u7528\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 653,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 632,\n          columnNumber: 11\n        }, this), !editingUser && /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              label: \"\\u5BC6\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                max: 32,\n                message: '密码长度为6-32个字符'\n              }, {\n                pattern: /^(?=.*[a-zA-Z])(?=.*\\d)/,\n                message: '密码必须包含字母和数字'\n              }],\n              extra: \"\\u5BC6\\u7801\\u9700\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF0C\\u957F\\u5EA66-32\\u4F4D\",\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\\uFF086-32\\u4F4D\\uFF0C\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF09\",\n                showCount: true,\n                maxLength: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 671,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 661,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 660,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"confirmPassword\",\n              label: \"\\u786E\\u8BA4\\u5BC6\\u7801\",\n              dependencies: ['password'],\n              rules: [{\n                required: true,\n                message: '请确认密码'\n              }, ({\n                getFieldValue\n              }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('password') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致，请重新输入'));\n                }\n              })],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u5BC6\\u7801\\u786E\\u8BA4\",\n                maxLength: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 695,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 679,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 678,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 659,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => {\n                setIsModalVisible(false);\n                form.resetFields();\n                setEditingUser(null);\n              },\n              disabled: saving,\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 706,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: saving,\n              disabled: saving,\n              children: saving ? '保存中...' : editingUser ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 716,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 705,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 704,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 551,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 538,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 417,\n    columnNumber: 5\n  }, this);\n};\n_s(UserList, \"tgCZg6xAGKi5AMgVM3JfYA1BVNI=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = UserList;\nexport default UserList;\nvar _c;\n$RefreshReg$(_c, \"UserList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Popconfirm", "Typography", "Row", "Col", "DatePicker", "Switch", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "UserOutlined", "ExportOutlined", "ReloadOutlined", "userService", "jsxDEV", "_jsxDEV", "Title", "Option", "RangePicker", "UserType", "UserStatus", "UserList", "_s", "users", "setUsers", "loading", "setLoading", "saving", "setSaving", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "editingUser", "setEditingUser", "form", "useForm", "searchForm", "userTypeMap", "AUCTIONEER", "label", "color", "BUYER", "ADMIN", "QUALITY_INSPECTOR", "fetchUsers", "response", "getUserList", "success", "data", "list", "error", "handleSave", "values", "userData", "status", "ENABLED", "DISABLED", "confirmPassword", "updateUser", "id", "createUser", "successMsg", "content", "duration", "resetFields", "errorMsg", "includes", "console", "handleToggleStatus", "user", "newStatus", "updateUserStatus", "handleAdd", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userType", "handleEdit", "handleDelete", "userId", "handleSearch", "newParams", "handleReset", "resetParams", "handleExport", "exportUsers", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "Date", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "columns", "title", "dataIndex", "key", "width", "render", "text", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "typeInfo", "record", "checked", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "toLocaleString", "fixed", "_", "size", "type", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "className", "level", "layout", "onFinish", "autoComplete", "gutter", "style", "xs", "sm", "md", "<PERSON><PERSON>", "name", "placeholder", "allowClear", "value", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "footer", "destroyOnClose", "maskClosable", "span", "rules", "required", "min", "max", "pattern", "extra", "undefined", "disabled", "showCount", "max<PERSON><PERSON><PERSON>", "addonBefore", "valuePropName", "Password", "dependencies", "getFieldValue", "validator", "Promise", "resolve", "reject", "Error", "justifyContent", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  DatePicker,\n  Switch,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  UserOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { userService } from '../../../services/userService';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\n// 用户类型枚举\nexport enum UserType {\n  AUCTIONEER = 1, // 拍卖师\n  BUYER = 2,      // 买家\n  ADMIN = 3,      // 管理员\n  QUALITY_INSPECTOR = 4, // 质检员\n}\n\n// 用户状态枚举\nexport enum UserStatus {\n  DISABLED = 0, // 禁用\n  ENABLED = 1,  // 启用\n}\n\n// 用户数据接口\nexport interface User {\n  id: number;\n  username: string;\n  realName?: string;\n  phone: string;\n  email?: string;\n  userType: UserType;\n  status: UserStatus;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数接口\ninterface UserQueryParams {\n  username?: string;\n  phone?: string;\n  userType?: UserType;\n  status?: UserStatus;\n  dateRange?: [string, string];\n  page: number;\n  pageSize: number;\n}\n\nconst UserList: React.FC = () => {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<UserQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 用户类型映射\n  const userTypeMap = {\n    [UserType.AUCTIONEER]: { label: '拍卖师', color: 'blue' },\n    [UserType.BUYER]: { label: '买家', color: 'green' },\n    [UserType.ADMIN]: { label: '管理员', color: 'red' },\n    [UserType.QUALITY_INSPECTOR]: { label: '质检员', color: 'orange' },\n  };\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    setLoading(true);\n    try {\n      const response = await userService.getUserList(queryParams);\n      if (response.success) {\n        setUsers(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取用户列表失败');\n        setUsers([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      message.error(error.message || '获取用户列表失败');\n      setUsers([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchUsers();\n  }, [queryParams]);\n\n\n\n  // 保存用户\n  const handleSave = async (values: any) => {\n    setSaving(true);\n\n    try {\n      const userData = {\n        ...values,\n        status: values.status ? UserStatus.ENABLED : UserStatus.DISABLED,\n      };\n\n      // 删除确认密码字段，后端不需要\n      delete userData.confirmPassword;\n\n      let response;\n      if (editingUser) {\n        response = await userService.updateUser(editingUser.id, userData);\n      } else {\n        response = await userService.createUser(userData);\n      }\n\n      if (response.success) {\n        const successMsg = editingUser ? '用户信息更新成功！' : '用户创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3,\n        });\n        setIsModalVisible(false);\n        form.resetFields(); // 重置表单\n        fetchUsers(); // 刷新列表\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('username')) {\n            errorMsg = '用户名已存在，请使用其他用户名';\n          } else if (response.message.includes('phone')) {\n            errorMsg = '手机号已被使用，请使用其他手机号';\n          } else if (response.message.includes('email')) {\n            errorMsg = '邮箱已被使用，请使用其他邮箱';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n\n        message.error({\n          content: errorMsg,\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('保存用户失败:', error);\n\n      // 处理网络错误和其他异常\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const { status, data } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('username')) {\n            errorMsg = '用户名已存在或格式不正确';\n          } else if (data.error && data.error.includes('phone')) {\n            errorMsg = '手机号格式不正确或已被使用';\n          } else if (data.error && data.error.includes('email')) {\n            errorMsg = '邮箱格式不正确或已被使用';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '用户信息冲突，用户名、手机号或邮箱已被使用';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n\n      message.error({\n        content: errorMsg,\n        duration: 5,\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 切换用户状态\n  const handleToggleStatus = async (user: User) => {\n    try {\n      const newStatus = user.status === UserStatus.ENABLED ? UserStatus.DISABLED : UserStatus.ENABLED;\n      const response = await userService.updateUserStatus(user.id, newStatus);\n      if (response.success) {\n        message.success('状态更新成功');\n        fetchUsers();\n      } else {\n        message.error(response.message || '状态更新失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '状态更新失败');\n    }\n  };\n\n  // 打开新增用户模态框\n  const handleAdd = () => {\n    setEditingUser(null);\n    setIsModalVisible(true);\n    form.resetFields();\n    // 设置默认值\n    form.setFieldsValue({\n      status: true, // 默认启用\n      userType: UserType.BUYER, // 默认买家\n    });\n  };\n\n  // 编辑用户\n  const handleEdit = (user: User) => {\n    setEditingUser(user);\n    setIsModalVisible(true);\n    form.setFieldsValue({\n      ...user,\n      status: user.status === UserStatus.ENABLED,\n    });\n  };\n\n  // 删除用户\n  const handleDelete = async (userId: number) => {\n    try {\n      // TODO: 实现删除用户功能\n      message.success('删除成功');\n      fetchUsers();\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 搜索用户\n  const handleSearch = (values: any) => {\n    const newParams = {\n      ...queryParams,\n      page: 1, // 重置到第一页\n      ...values,\n    };\n    setQueryParams(newParams);\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    const resetParams = {\n      page: 1,\n      pageSize: 10,\n    };\n    setQueryParams(resetParams);\n  };\n\n  // 导出用户数据\n  const handleExport = async () => {\n    try {\n      const response = await userService.exportUsers(queryParams);\n      if (response.success) {\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]));\n        const link = document.createElement('a');\n        link.href = url;\n        link.setAttribute('download', `users_${new Date().getTime()}.xlsx`);\n        document.body.appendChild(link);\n        link.click();\n        link.remove();\n        window.URL.revokeObjectURL(url);\n        message.success('导出成功');\n      } else {\n        message.error(response.message || '导出失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '导出失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<User> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      width: 120,\n      render: (text: string) => (\n        <Space>\n          <UserOutlined />\n          {text}\n        </Space>\n      ),\n    },\n    {\n      title: '真实姓名',\n      dataIndex: 'realName',\n      key: 'realName',\n      width: 100,\n      render: (text: string) => text || '-',\n    },\n    {\n      title: '手机号',\n      dataIndex: 'phone',\n      key: 'phone',\n      width: 120,\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      width: 180,\n      render: (text: string) => text || '-',\n    },\n    {\n      title: '用户类型',\n      dataIndex: 'userType',\n      key: 'userType',\n      width: 100,\n      render: (userType: UserType) => {\n        const typeInfo = userTypeMap[userType];\n        return (\n          <Tag color={typeInfo?.color || 'default'}>\n            {typeInfo?.label || '未知'}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: UserStatus, record: User) => (\n        <Switch\n          checked={status === UserStatus.ENABLED}\n          onChange={() => handleToggleStatus(record)}\n          checkedChildren=\"启用\"\n          unCheckedChildren=\"禁用\"\n        />\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right',\n      render: (_, record: User) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个用户吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"user-list-container\">\n      <Title level={2}>用户管理</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          form={searchForm}\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"username\" label=\"用户名\">\n                <Input placeholder=\"请输入用户名\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"phone\" label=\"手机号\">\n                <Input placeholder=\"请输入手机号\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"userType\" label=\"用户类型\">\n                <Select placeholder=\"请选择用户类型\" allowClear>\n                  <Option value={UserType.AUCTIONEER}>拍卖师</Option>\n                  <Option value={UserType.BUYER}>买家</Option>\n                  <Option value={UserType.ADMIN}>管理员</Option>\n                  <Option value={UserType.QUALITY_INSPECTOR}>质检员</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"状态\">\n                <Select placeholder=\"请选择状态\" allowClear>\n                  <Option value={UserStatus.ENABLED}>启用</Option>\n                  <Option value={UserStatus.DISABLED}>禁用</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8}>\n              <Form.Item name=\"dateRange\" label=\"创建时间\">\n                <RangePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={4}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={handleAdd}\n              >\n                新增用户\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={handleExport}\n              >\n                导出数据\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchUsers}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 用户列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={users}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 用户编辑模态框 */}\n      <Modal\n        title={editingUser ? '编辑用户' : '新增用户'}\n        open={isModalVisible}\n        onCancel={() => {\n          setIsModalVisible(false);\n          form.resetFields();\n          setEditingUser(null);\n        }}\n        footer={null}\n        width={600}\n        destroyOnClose\n        maskClosable={false}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"username\"\n                label=\"用户名\"\n                rules={[\n                  { required: true, message: '请输入用户名' },\n                  { min: 3, max: 20, message: '用户名长度为3-20个字符' },\n                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },\n                ]}\n                extra={!editingUser ? \"用户名创建后不可修改，请谨慎填写\" : undefined}\n              >\n                <Input\n                  placeholder=\"请输入用户名（3-20个字符）\"\n                  disabled={!!editingUser}\n                  showCount\n                  maxLength={20}\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"realName\"\n                label=\"真实姓名\"\n                rules={[\n                  { required: true, message: '请输入真实姓名' },\n                  { max: 20, message: '真实姓名不能超过20个字符' },\n                  { pattern: /^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/, message: '真实姓名只能包含中文、英文和空格' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入真实姓名\"\n                  showCount\n                  maxLength={20}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"phone\"\n                label=\"手机号\"\n                rules={[\n                  { required: true, message: '请输入手机号' },\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的11位手机号码' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入11位手机号码\"\n                  maxLength={11}\n                  addonBefore=\"+86\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"email\"\n                label=\"邮箱\"\n                rules={[\n                  { required: true, message: '请输入邮箱' },\n                  { type: 'email', message: '请输入正确的邮箱地址' },\n                  { max: 50, message: '邮箱长度不能超过50个字符' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入邮箱地址\"\n                  type=\"email\"\n                  maxLength={50}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"userType\"\n                label=\"用户类型\"\n                rules={[{ required: true, message: '请选择用户类型' }]}\n              >\n                <Select placeholder=\"请选择用户类型\">\n                  <Option value={UserType.AUCTIONEER}>拍卖师</Option>\n                  <Option value={UserType.BUYER}>买家</Option>\n                  <Option value={UserType.ADMIN}>管理员</Option>\n                  <Option value={UserType.QUALITY_INSPECTOR}>质检员</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                valuePropName=\"checked\"\n              >\n                <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          {!editingUser && (\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"password\"\n                  label=\"密码\"\n                  rules={[\n                    { required: true, message: '请输入密码' },\n                    { min: 6, max: 32, message: '密码长度为6-32个字符' },\n                    { pattern: /^(?=.*[a-zA-Z])(?=.*\\d)/, message: '密码必须包含字母和数字' },\n                  ]}\n                  extra=\"密码需包含字母和数字，长度6-32位\"\n                >\n                  <Input.Password\n                    placeholder=\"请输入密码（6-32位，包含字母和数字）\"\n                    showCount\n                    maxLength={32}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"confirmPassword\"\n                  label=\"确认密码\"\n                  dependencies={['password']}\n                  rules={[\n                    { required: true, message: '请确认密码' },\n                    ({ getFieldValue }) => ({\n                      validator(_, value) {\n                        if (!value || getFieldValue('password') === value) {\n                          return Promise.resolve();\n                        }\n                        return Promise.reject(new Error('两次输入的密码不一致，请重新输入'));\n                      },\n                    }),\n                  ]}\n                >\n                  <Input.Password\n                    placeholder=\"请再次输入密码确认\"\n                    maxLength={32}\n                  />\n                </Form.Item>\n              </Col>\n            </Row>\n          )}\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button\n                onClick={() => {\n                  setIsModalVisible(false);\n                  form.resetFields();\n                  setEditingUser(null);\n                }}\n                disabled={saving}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={saving}\n                disabled={saving}\n              >\n                {saving ? '保存中...' : (editingUser ? '更新' : '创建')}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default UserList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGf,UAAU;AAC5B,MAAM;EAAEgB;AAAO,CAAC,GAAGtB,MAAM;AACzB,MAAM;EAAEuB;AAAY,CAAC,GAAGd,UAAU;;AAElC;AACA,WAAYe,QAAQ,0BAARA,QAAQ;EAARA,QAAQ,CAARA,QAAQ;EACF;EADNA,QAAQ,CAARA,QAAQ;EAEF;EAFNA,QAAQ,CAARA,QAAQ;EAGF;EAHNA,QAAQ,CAARA,QAAQ,kDAIK;EAAA,OAJbA,QAAQ;AAAA;;AAOpB;AACA,WAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EACN;EADJA,UAAU,CAAVA,UAAU,8BAEN;EAAA,OAFJA,UAAU;AAAA;;AAKtB;;AAaA;;AAWA,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAkB;IAC9D6C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACmD,IAAI,CAAC,GAAGzC,IAAI,CAAC0C,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAG3C,IAAI,CAAC0C,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAME,WAAW,GAAG;IAClB,CAACvB,QAAQ,CAACwB,UAAU,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAC;IACtD,CAAC1B,QAAQ,CAAC2B,KAAK,GAAG;MAAEF,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACjD,CAAC1B,QAAQ,CAAC4B,KAAK,GAAG;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC;IAChD,CAAC1B,QAAQ,CAAC6B,iBAAiB,GAAG;MAAEJ,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS;EAChE,CAAC;;EAED;EACA,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BvB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMwB,QAAQ,GAAG,MAAMrC,WAAW,CAACsC,WAAW,CAACpB,WAAW,CAAC;MAC3D,IAAImB,QAAQ,CAACE,OAAO,EAAE;QACpB5B,QAAQ,CAAC0B,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;QAC5BxB,QAAQ,CAACoB,QAAQ,CAACG,IAAI,CAACxB,KAAK,CAAC;MAC/B,CAAC,MAAM;QACL9B,OAAO,CAACwD,KAAK,CAACL,QAAQ,CAACnD,OAAO,IAAI,UAAU,CAAC;QAC7CyB,QAAQ,CAAC,EAAE,CAAC;QACZM,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOyB,KAAU,EAAE;MACnBxD,OAAO,CAACwD,KAAK,CAACA,KAAK,CAACxD,OAAO,IAAI,UAAU,CAAC;MAC1CyB,QAAQ,CAAC,EAAE,CAAC;MACZM,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRJ,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACArC,SAAS,CAAC,MAAM;IACd4D,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAAClB,WAAW,CAAC,CAAC;;EAIjB;EACA,MAAMyB,UAAU,GAAG,MAAOC,MAAW,IAAK;IACxC7B,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAM8B,QAAQ,GAAG;QACf,GAAGD,MAAM;QACTE,MAAM,EAAEF,MAAM,CAACE,MAAM,GAAGvC,UAAU,CAACwC,OAAO,GAAGxC,UAAU,CAACyC;MAC1D,CAAC;;MAED;MACA,OAAOH,QAAQ,CAACI,eAAe;MAE/B,IAAIZ,QAAQ;MACZ,IAAIb,WAAW,EAAE;QACfa,QAAQ,GAAG,MAAMrC,WAAW,CAACkD,UAAU,CAAC1B,WAAW,CAAC2B,EAAE,EAAEN,QAAQ,CAAC;MACnE,CAAC,MAAM;QACLR,QAAQ,GAAG,MAAMrC,WAAW,CAACoD,UAAU,CAACP,QAAQ,CAAC;MACnD;MAEA,IAAIR,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMc,UAAU,GAAG7B,WAAW,GAAG,WAAW,GAAG,SAAS;QACxDtC,OAAO,CAACqD,OAAO,CAAC;UACde,OAAO,EAAED,UAAU;UACnBE,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFhC,iBAAiB,CAAC,KAAK,CAAC;QACxBG,IAAI,CAAC8B,WAAW,CAAC,CAAC,CAAC,CAAC;QACpBpB,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACL;QACA,IAAIqB,QAAQ,GAAG,MAAM;QACrB,IAAIpB,QAAQ,CAACnD,OAAO,EAAE;UACpB,IAAImD,QAAQ,CAACnD,OAAO,CAACwE,QAAQ,CAAC,UAAU,CAAC,EAAE;YACzCD,QAAQ,GAAG,iBAAiB;UAC9B,CAAC,MAAM,IAAIpB,QAAQ,CAACnD,OAAO,CAACwE,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC7CD,QAAQ,GAAG,kBAAkB;UAC/B,CAAC,MAAM,IAAIpB,QAAQ,CAACnD,OAAO,CAACwE,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC7CD,QAAQ,GAAG,gBAAgB;UAC7B,CAAC,MAAM,IAAIpB,QAAQ,CAACnD,OAAO,CAACwE,QAAQ,CAAC,YAAY,CAAC,EAAE;YAClDD,QAAQ,GAAG,kBAAkB;UAC/B,CAAC,MAAM;YACLA,QAAQ,GAAGpB,QAAQ,CAACnD,OAAO;UAC7B;QACF;QAEAA,OAAO,CAACwD,KAAK,CAAC;UACZY,OAAO,EAAEG,QAAQ;UACjBF,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOb,KAAU,EAAE;MACnBiB,OAAO,CAACjB,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;;MAE/B;MACA,IAAIe,QAAQ,GAAG,YAAY;MAC3B,IAAIf,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM;UAAES,MAAM;UAAEN;QAAK,CAAC,GAAGE,KAAK,CAACL,QAAQ;QACvC,IAAIS,MAAM,KAAK,GAAG,EAAE;UAClB,IAAIN,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAACgB,QAAQ,CAAC,UAAU,CAAC,EAAE;YACjDD,QAAQ,GAAG,cAAc;UAC3B,CAAC,MAAM,IAAIjB,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAACgB,QAAQ,CAAC,OAAO,CAAC,EAAE;YACrDD,QAAQ,GAAG,eAAe;UAC5B,CAAC,MAAM,IAAIjB,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAACgB,QAAQ,CAAC,OAAO,CAAC,EAAE;YACrDD,QAAQ,GAAG,cAAc;UAC3B,CAAC,MAAM;YACLA,QAAQ,GAAGjB,IAAI,CAACE,KAAK,IAAI,gBAAgB;UAC3C;QACF,CAAC,MAAM,IAAII,MAAM,KAAK,GAAG,EAAE;UACzBW,QAAQ,GAAG,uBAAuB;QACpC,CAAC,MAAM,IAAIX,MAAM,KAAK,GAAG,EAAE;UACzBW,QAAQ,GAAG,gBAAgB;QAC7B,CAAC,MAAM;UACLA,QAAQ,GAAG,SAASX,MAAM,SAAS;QACrC;MACF,CAAC,MAAM,IAAIJ,KAAK,CAACxD,OAAO,EAAE;QACxBuE,QAAQ,GAAGf,KAAK,CAACxD,OAAO;MAC1B;MAEAA,OAAO,CAACwD,KAAK,CAAC;QACZY,OAAO,EAAEG,QAAQ;QACjBF,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRxC,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM6C,kBAAkB,GAAG,MAAOC,IAAU,IAAK;IAC/C,IAAI;MACF,MAAMC,SAAS,GAAGD,IAAI,CAACf,MAAM,KAAKvC,UAAU,CAACwC,OAAO,GAAGxC,UAAU,CAACyC,QAAQ,GAAGzC,UAAU,CAACwC,OAAO;MAC/F,MAAMV,QAAQ,GAAG,MAAMrC,WAAW,CAAC+D,gBAAgB,CAACF,IAAI,CAACV,EAAE,EAAEW,SAAS,CAAC;MACvE,IAAIzB,QAAQ,CAACE,OAAO,EAAE;QACpBrD,OAAO,CAACqD,OAAO,CAAC,QAAQ,CAAC;QACzBH,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLlD,OAAO,CAACwD,KAAK,CAACL,QAAQ,CAACnD,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOwD,KAAU,EAAE;MACnBxD,OAAO,CAACwD,KAAK,CAACA,KAAK,CAACxD,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAM8E,SAAS,GAAGA,CAAA,KAAM;IACtBvC,cAAc,CAAC,IAAI,CAAC;IACpBF,iBAAiB,CAAC,IAAI,CAAC;IACvBG,IAAI,CAAC8B,WAAW,CAAC,CAAC;IAClB;IACA9B,IAAI,CAACuC,cAAc,CAAC;MAClBnB,MAAM,EAAE,IAAI;MAAE;MACdoB,QAAQ,EAAE5D,QAAQ,CAAC2B,KAAK,CAAE;IAC5B,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMkC,UAAU,GAAIN,IAAU,IAAK;IACjCpC,cAAc,CAACoC,IAAI,CAAC;IACpBtC,iBAAiB,CAAC,IAAI,CAAC;IACvBG,IAAI,CAACuC,cAAc,CAAC;MAClB,GAAGJ,IAAI;MACPf,MAAM,EAAEe,IAAI,CAACf,MAAM,KAAKvC,UAAU,CAACwC;IACrC,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqB,YAAY,GAAG,MAAOC,MAAc,IAAK;IAC7C,IAAI;MACF;MACAnF,OAAO,CAACqD,OAAO,CAAC,MAAM,CAAC;MACvBH,UAAU,CAAC,CAAC;IACd,CAAC,CAAC,OAAOM,KAAU,EAAE;MACnBxD,OAAO,CAACwD,KAAK,CAACA,KAAK,CAACxD,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMoF,YAAY,GAAI1B,MAAW,IAAK;IACpC,MAAM2B,SAAS,GAAG;MAChB,GAAGrD,WAAW;MACdE,IAAI,EAAE,CAAC;MAAE;MACT,GAAGwB;IACL,CAAC;IACDzB,cAAc,CAACoD,SAAS,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACxB5C,UAAU,CAAC4B,WAAW,CAAC,CAAC;IACxB,MAAMiB,WAAW,GAAG;MAClBrD,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC;IACDF,cAAc,CAACsD,WAAW,CAAC;EAC7B,CAAC;;EAED;EACA,MAAMC,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAMrC,WAAW,CAAC2E,WAAW,CAACzD,WAAW,CAAC;MAC3D,IAAImB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMqC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAAC3C,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC;QACjE,MAAMyC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;QACfK,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,SAAS,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC;QACnEL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;QAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;QACZT,IAAI,CAACU,MAAM,CAAC,CAAC;QACbd,MAAM,CAACC,GAAG,CAACc,eAAe,CAAChB,GAAG,CAAC;QAC/B1F,OAAO,CAACqD,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACLrD,OAAO,CAACwD,KAAK,CAACL,QAAQ,CAACnD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOwD,KAAU,EAAE;MACnBxD,OAAO,CAACwD,KAAK,CAACA,KAAK,CAACxD,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAM2G,OAA0B,GAAG,CACjC;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnBjG,OAAA,CAACtB,KAAK;MAAAwH,QAAA,gBACJlG,OAAA,CAACL,YAAY;QAAAwG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACfL,IAAI;IAAA;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAKA,IAAI,IAAI;EACpC,CAAC,EACD;IACEL,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAKA,IAAI,IAAI;EACpC,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGhC,QAAkB,IAAK;MAC9B,MAAMuC,QAAQ,GAAG5E,WAAW,CAACqC,QAAQ,CAAC;MACtC,oBACEhE,OAAA,CAACnB,GAAG;QAACiD,KAAK,EAAE,CAAAyE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEzE,KAAK,KAAI,SAAU;QAAAoE,QAAA,EACtC,CAAAK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAE1E,KAAK,KAAI;MAAI;QAAAsE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAEV;EACF,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAACpD,MAAkB,EAAE4D,MAAY,kBACvCxG,OAAA,CAACV,MAAM;MACLmH,OAAO,EAAE7D,MAAM,KAAKvC,UAAU,CAACwC,OAAQ;MACvC6D,QAAQ,EAAEA,CAAA,KAAMhD,kBAAkB,CAAC8C,MAAM,CAAE;MAC3CG,eAAe,EAAC,cAAI;MACpBC,iBAAiB,EAAC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB;EAEL,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAK,IAAIb,IAAI,CAACa,IAAI,CAAC,CAACY,cAAc,CAAC;EAC1D,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVe,KAAK,EAAE,OAAO;IACdd,MAAM,EAAEA,CAACe,CAAC,EAAEP,MAAY,kBACtBxG,OAAA,CAACtB,KAAK;MAACsI,IAAI,EAAC,OAAO;MAAAd,QAAA,gBACjBlG,OAAA,CAACvB,MAAM;QACLwI,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAElH,OAAA,CAACP,YAAY;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBa,OAAO,EAAEA,CAAA,KAAMlD,UAAU,CAACuC,MAAM,CAAE;QAAAN,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTtG,OAAA,CAACf,UAAU;QACT2G,KAAK,EAAC,oEAAa;QACnBwB,SAAS,EAAEA,CAAA,KAAMlD,YAAY,CAACsC,MAAM,CAACvD,EAAE,CAAE;QACzCoE,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAApB,QAAA,eAEflG,OAAA,CAACvB,MAAM;UACLwI,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZO,MAAM;UACNL,IAAI,eAAElH,OAAA,CAACN,cAAc;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEtG,OAAA;IAAKwH,SAAS,EAAC,qBAAqB;IAAAtB,QAAA,gBAClClG,OAAA,CAACC,KAAK;MAACwH,KAAK,EAAE,CAAE;MAAAvB,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7BtG,OAAA,CAACzB,IAAI;MAACiJ,SAAS,EAAC,aAAa;MAACR,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxClG,OAAA,CAACjB,IAAI;QACHyC,IAAI,EAAEE,UAAW;QACjBgG,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEvD,YAAa;QACvBwD,YAAY,EAAC,KAAK;QAAA1B,QAAA,eAElBlG,OAAA,CAACb,GAAG;UAAC0I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACC,KAAK,EAAE;YAAE/B,KAAK,EAAE;UAAO,CAAE;UAAAG,QAAA,gBAC9ClG,OAAA,CAACZ,GAAG;YAAC2I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzBlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cAACC,IAAI,EAAC,UAAU;cAACtG,KAAK,EAAC,oBAAK;cAAAqE,QAAA,eACpClG,OAAA,CAACrB,KAAK;gBAACyJ,WAAW,EAAC,sCAAQ;gBAACC,UAAU;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAAC2I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzBlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cAACC,IAAI,EAAC,OAAO;cAACtG,KAAK,EAAC,oBAAK;cAAAqE,QAAA,eACjClG,OAAA,CAACrB,KAAK;gBAACyJ,WAAW,EAAC,sCAAQ;gBAACC,UAAU;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAAC2I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzBlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cAACC,IAAI,EAAC,UAAU;cAACtG,KAAK,EAAC,0BAAM;cAAAqE,QAAA,eACrClG,OAAA,CAACpB,MAAM;gBAACwJ,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAnC,QAAA,gBACtClG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAElI,QAAQ,CAACwB,UAAW;kBAAAsE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDtG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAElI,QAAQ,CAAC2B,KAAM;kBAAAmE,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CtG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAElI,QAAQ,CAAC4B,KAAM;kBAAAkE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CtG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAElI,QAAQ,CAAC6B,iBAAkB;kBAAAiE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAAC2I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzBlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cAACC,IAAI,EAAC,QAAQ;cAACtG,KAAK,EAAC,cAAI;cAAAqE,QAAA,eACjClG,OAAA,CAACpB,MAAM;gBAACwJ,WAAW,EAAC,gCAAO;gBAACC,UAAU;gBAAAnC,QAAA,gBACpClG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAEjI,UAAU,CAACwC,OAAQ;kBAAAqD,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CtG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAEjI,UAAU,CAACyC,QAAS;kBAAAoD,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAAC2I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzBlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cAACC,IAAI,EAAC,WAAW;cAACtG,KAAK,EAAC,0BAAM;cAAAqE,QAAA,eACtClG,OAAA,CAACG,WAAW;gBAAC2H,KAAK,EAAE;kBAAE/B,KAAK,EAAE;gBAAO;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAAC2I,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzBlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cAAAhC,QAAA,eACRlG,OAAA,CAACtB,KAAK;gBAAAwH,QAAA,gBACJlG,OAAA,CAACvB,MAAM;kBAACwI,IAAI,EAAC,SAAS;kBAACsB,QAAQ,EAAC,QAAQ;kBAACrB,IAAI,eAAElH,OAAA,CAACR,cAAc;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAEnE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTtG,OAAA,CAACvB,MAAM;kBAAC0I,OAAO,EAAE7C,WAAY;kBAAC4C,IAAI,eAAElH,OAAA,CAACH,cAAc;oBAAAsG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAExD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPtG,OAAA,CAACzB,IAAI;MAACiJ,SAAS,EAAC,aAAa;MAACR,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxClG,OAAA,CAACb,GAAG;QAACqJ,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAvC,QAAA,gBACzClG,OAAA,CAACZ,GAAG;UAAA8G,QAAA,eACFlG,OAAA,CAACtB,KAAK;YAAAwH,QAAA,gBACJlG,OAAA,CAACvB,MAAM;cACLwI,IAAI,EAAC,SAAS;cACdC,IAAI,eAAElH,OAAA,CAACT,YAAY;gBAAA4G,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBa,OAAO,EAAErD,SAAU;cAAAoC,QAAA,EACpB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtG,OAAA,CAACvB,MAAM;cACLyI,IAAI,eAAElH,OAAA,CAACJ,cAAc;gBAAAuG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBa,OAAO,EAAE3C,YAAa;cAAA0B,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNtG,OAAA,CAACZ,GAAG;UAAA8G,QAAA,eACFlG,OAAA,CAACvB,MAAM;YACLyI,IAAI,eAAElH,OAAA,CAACH,cAAc;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBa,OAAO,EAAEjF,UAAW;YACpBxB,OAAO,EAAEA,OAAQ;YAAAwF,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPtG,OAAA,CAACzB,IAAI;MAAA2H,QAAA,eACHlG,OAAA,CAACxB,KAAK;QACJmH,OAAO,EAAEA,OAAQ;QACjB+C,UAAU,EAAElI,KAAM;QAClBmI,MAAM,EAAC,IAAI;QACXjI,OAAO,EAAEA,OAAQ;QACjBkI,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAE/H,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZkI,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACpI,KAAK,EAAEqI,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQrI,KAAK,IAAI;UAC5C4F,QAAQ,EAAEA,CAACxF,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAAgF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPtG,OAAA,CAAClB,KAAK;MACJ8G,KAAK,EAAEtE,WAAW,GAAG,MAAM,GAAG,MAAO;MACrC8H,IAAI,EAAEhI,cAAe;MACrBiI,QAAQ,EAAEA,CAAA,KAAM;QACdhI,iBAAiB,CAAC,KAAK,CAAC;QACxBG,IAAI,CAAC8B,WAAW,CAAC,CAAC;QAClB/B,cAAc,CAAC,IAAI,CAAC;MACtB,CAAE;MACF+H,MAAM,EAAE,IAAK;MACbvD,KAAK,EAAE,GAAI;MACXwD,cAAc;MACdC,YAAY,EAAE,KAAM;MAAAtD,QAAA,eAEpBlG,OAAA,CAACjB,IAAI;QACHyC,IAAI,EAAEA,IAAK;QACXkG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAElF,UAAW;QACrBmF,YAAY,EAAC,KAAK;QAAA1B,QAAA,gBAElBlG,OAAA,CAACb,GAAG;UAAC0I,MAAM,EAAE,EAAG;UAAA3B,QAAA,gBACdlG,OAAA,CAACZ,GAAG;YAACqK,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,UAAU;cACftG,KAAK,EAAC,oBAAK;cACX6H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3K,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE4K,GAAG,EAAE,CAAC;gBAAEC,GAAG,EAAE,EAAE;gBAAE7K,OAAO,EAAE;cAAgB,CAAC,EAC7C;gBAAE8K,OAAO,EAAE,iBAAiB;gBAAE9K,OAAO,EAAE;cAAmB,CAAC,CAC3D;cACF+K,KAAK,EAAE,CAACzI,WAAW,GAAG,kBAAkB,GAAG0I,SAAU;cAAA9D,QAAA,eAErDlG,OAAA,CAACrB,KAAK;gBACJyJ,WAAW,EAAC,wEAAiB;gBAC7B6B,QAAQ,EAAE,CAAC,CAAC3I,WAAY;gBACxB4I,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAACqK,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,UAAU;cACftG,KAAK,EAAC,0BAAM;cACZ6H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3K,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAE6K,GAAG,EAAE,EAAE;gBAAE7K,OAAO,EAAE;cAAgB,CAAC,EACrC;gBAAE8K,OAAO,EAAE,4BAA4B;gBAAE9K,OAAO,EAAE;cAAmB,CAAC,CACtE;cAAAkH,QAAA,eAEFlG,OAAA,CAACrB,KAAK;gBACJyJ,WAAW,EAAC,4CAAS;gBACrB8B,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtG,OAAA,CAACb,GAAG;UAAC0I,MAAM,EAAE,EAAG;UAAA3B,QAAA,gBACdlG,OAAA,CAACZ,GAAG;YAACqK,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,OAAO;cACZtG,KAAK,EAAC,oBAAK;cACX6H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3K,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE8K,OAAO,EAAE,eAAe;gBAAE9K,OAAO,EAAE;cAAgB,CAAC,CACtD;cAAAkH,QAAA,eAEFlG,OAAA,CAACrB,KAAK;gBACJyJ,WAAW,EAAC,oDAAY;gBACxB+B,SAAS,EAAE,EAAG;gBACdC,WAAW,EAAC;cAAK;gBAAAjE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAACqK,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,OAAO;cACZtG,KAAK,EAAC,cAAI;cACV6H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3K,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAEiI,IAAI,EAAE,OAAO;gBAAEjI,OAAO,EAAE;cAAa,CAAC,EACxC;gBAAE6K,GAAG,EAAE,EAAE;gBAAE7K,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAAkH,QAAA,eAEFlG,OAAA,CAACrB,KAAK;gBACJyJ,WAAW,EAAC,4CAAS;gBACrBnB,IAAI,EAAC,OAAO;gBACZkD,SAAS,EAAE;cAAG;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENtG,OAAA,CAACb,GAAG;UAAC0I,MAAM,EAAE,EAAG;UAAA3B,QAAA,gBACdlG,OAAA,CAACZ,GAAG;YAACqK,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,UAAU;cACftG,KAAK,EAAC,0BAAM;cACZ6H,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3K,OAAO,EAAE;cAAU,CAAC,CAAE;cAAAkH,QAAA,eAEhDlG,OAAA,CAACpB,MAAM;gBAACwJ,WAAW,EAAC,4CAAS;gBAAAlC,QAAA,gBAC3BlG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAElI,QAAQ,CAACwB,UAAW;kBAAAsE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDtG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAElI,QAAQ,CAAC2B,KAAM;kBAAAmE,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CtG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAElI,QAAQ,CAAC4B,KAAM;kBAAAkE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CtG,OAAA,CAACE,MAAM;kBAACoI,KAAK,EAAElI,QAAQ,CAAC6B,iBAAkB;kBAAAiE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAACqK,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,QAAQ;cACbtG,KAAK,EAAC,cAAI;cACVwI,aAAa,EAAC,SAAS;cAAAnE,QAAA,eAEvBlG,OAAA,CAACV,MAAM;gBAACqH,eAAe,EAAC,cAAI;gBAACC,iBAAiB,EAAC;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL,CAAChF,WAAW,iBACXtB,OAAA,CAACb,GAAG;UAAC0I,MAAM,EAAE,EAAG;UAAA3B,QAAA,gBACdlG,OAAA,CAACZ,GAAG;YAACqK,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,UAAU;cACftG,KAAK,EAAC,cAAI;cACV6H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3K,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAE4K,GAAG,EAAE,CAAC;gBAAEC,GAAG,EAAE,EAAE;gBAAE7K,OAAO,EAAE;cAAe,CAAC,EAC5C;gBAAE8K,OAAO,EAAE,yBAAyB;gBAAE9K,OAAO,EAAE;cAAc,CAAC,CAC9D;cACF+K,KAAK,EAAC,0FAAoB;cAAA7D,QAAA,eAE1BlG,OAAA,CAACrB,KAAK,CAAC2L,QAAQ;gBACblC,WAAW,EAAC,sGAAsB;gBAClC8B,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNtG,OAAA,CAACZ,GAAG;YAACqK,IAAI,EAAE,EAAG;YAAAvD,QAAA,eACZlG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;cACRC,IAAI,EAAC,iBAAiB;cACtBtG,KAAK,EAAC,0BAAM;cACZ0I,YAAY,EAAE,CAAC,UAAU,CAAE;cAC3Bb,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE3K,OAAO,EAAE;cAAQ,CAAC,EACpC,CAAC;gBAAEwL;cAAc,CAAC,MAAM;gBACtBC,SAASA,CAAC1D,CAAC,EAAEuB,KAAK,EAAE;kBAClB,IAAI,CAACA,KAAK,IAAIkC,aAAa,CAAC,UAAU,CAAC,KAAKlC,KAAK,EAAE;oBACjD,OAAOoC,OAAO,CAACC,OAAO,CAAC,CAAC;kBAC1B;kBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,kBAAkB,CAAC,CAAC;gBACtD;cACF,CAAC,CAAC,CACF;cAAA3E,QAAA,eAEFlG,OAAA,CAACrB,KAAK,CAAC2L,QAAQ;gBACblC,WAAW,EAAC,wDAAW;gBACvB+B,SAAS,EAAE;cAAG;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDtG,OAAA,CAACjB,IAAI,CAACmJ,IAAI;UAAAhC,QAAA,eACRlG,OAAA,CAACtB,KAAK;YAACoJ,KAAK,EAAE;cAAE/B,KAAK,EAAE,MAAM;cAAE+E,cAAc,EAAE;YAAW,CAAE;YAAA5E,QAAA,gBAC1DlG,OAAA,CAACvB,MAAM;cACL0I,OAAO,EAAEA,CAAA,KAAM;gBACb9F,iBAAiB,CAAC,KAAK,CAAC;gBACxBG,IAAI,CAAC8B,WAAW,CAAC,CAAC;gBAClB/B,cAAc,CAAC,IAAI,CAAC;cACtB,CAAE;cACF0I,QAAQ,EAAErJ,MAAO;cAAAsF,QAAA,EAClB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtG,OAAA,CAACvB,MAAM;cACLwI,IAAI,EAAC,SAAS;cACdsB,QAAQ,EAAC,QAAQ;cACjB7H,OAAO,EAAEE,MAAO;cAChBqJ,QAAQ,EAAErJ,MAAO;cAAAsF,QAAA,EAEhBtF,MAAM,GAAG,QAAQ,GAAIU,WAAW,GAAG,IAAI,GAAG;YAAK;cAAA6E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC/F,EAAA,CA/oBID,QAAkB;EAAA,QAWPvB,IAAI,CAAC0C,OAAO,EACN1C,IAAI,CAAC0C,OAAO;AAAA;AAAAsJ,EAAA,GAZ7BzK,QAAkB;AAipBxB,eAAeA,QAAQ;AAAC,IAAAyK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}