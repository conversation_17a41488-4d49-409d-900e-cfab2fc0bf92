{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect, useCallback, useRef } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setUser, clearUser, setLoading } from '../store/slices/authSlice';\nimport { authService } from '../services/authService';\nexport const useAuth = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    user,\n    isAuthenticated,\n    loading\n  } = useSelector(state => state.auth);\n  const hasInitialized = useRef(false);\n\n  // 登录\n  const login = async credentials => {\n    try {\n      dispatch(setLoading(true));\n      const response = await authService.login(credentials);\n      if (response.success && response.data) {\n        // 保存token到localStorage\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n\n        // 直接使用登录响应中的用户信息\n        dispatch(setUser(response.data.user));\n        hasInitialized.current = true; // 标记已初始化，避免重复调用checkAuth\n\n        return {\n          success: true\n        };\n      }\n      return {\n        success: false,\n        message: response.message || '登录失败'\n      };\n    } catch (error) {\n      console.error('登录异常:', error);\n      return {\n        success: false,\n        message: error.message || '登录失败'\n      };\n    } finally {\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      await authService.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      // 清除Redux状态\n      dispatch(clearUser());\n      // 重置初始化标记\n      hasInitialized.current = false;\n    }\n  };\n\n  // 检查认证状态\n  const checkAuth = useCallback(async () => {\n    // 防止重复初始化\n    if (hasInitialized.current) {\n      return isAuthenticated;\n    }\n    const token = localStorage.getItem('token');\n    if (!token) {\n      dispatch(clearUser());\n      hasInitialized.current = true;\n      return false;\n    }\n    try {\n      dispatch(setLoading(true));\n      const response = await authService.getUserInfo();\n      if (response.success && response.data) {\n        dispatch(setUser(response.data));\n        hasInitialized.current = true;\n        return true;\n      } else {\n        // token无效，清除认证信息\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        dispatch(clearUser());\n        hasInitialized.current = true;\n        return false;\n      }\n    } catch (error) {\n      console.error('Check auth error:', error);\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      dispatch(clearUser());\n      hasInitialized.current = true;\n      return false;\n    } finally {\n      dispatch(setLoading(false));\n    }\n  }, [dispatch]); // 移除isAuthenticated依赖，避免无限循环\n\n  // 刷新token\n  const refreshToken = async () => {\n    const refreshTokenValue = localStorage.getItem('refreshToken');\n    if (!refreshTokenValue) {\n      return false;\n    }\n    try {\n      const response = await authService.refreshToken(refreshTokenValue);\n      if (response.success && response.data) {\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Refresh token error:', error);\n      return false;\n    }\n  };\n\n  // 初始化时检查认证状态（只执行一次）\n  useEffect(() => {\n    if (!hasInitialized.current) {\n      checkAuth();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // 空依赖数组，确保只在组件挂载时执行一次\n\n  return {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    checkAuth,\n    refreshToken\n  };\n};\n_s(useAuth, \"PbiPqJjCXKeL0hD4XoY6LNMoAbU=\", false, function () {\n  return [useDispatch, useSelector];\n});", "map": {"version": 3, "names": ["useEffect", "useCallback", "useRef", "useDispatch", "useSelector", "setUser", "clearUser", "setLoading", "authService", "useAuth", "_s", "dispatch", "user", "isAuthenticated", "loading", "state", "auth", "hasInitialized", "login", "credentials", "response", "success", "data", "localStorage", "setItem", "token", "refreshToken", "current", "message", "error", "console", "logout", "removeItem", "checkAuth", "getItem", "getUserInfo", "refreshTokenValue"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/hooks/useAuth.ts"], "sourcesContent": ["import { useEffect, useCallback, useRef } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../store';\nimport { setUser, clearUser, setLoading } from '../store/slices/authSlice';\nimport { authService } from '../services/authService';\n\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  role: string;\n  avatar?: string;\n  realName?: string;\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport const useAuth = () => {\n  const dispatch = useDispatch();\n  const { user, isAuthenticated, loading } = useSelector((state: RootState) => state.auth);\n  const hasInitialized = useRef(false);\n\n  // 登录\n  const login = async (credentials: LoginCredentials) => {\n    try {\n      dispatch(setLoading(true));\n      const response = await authService.login(credentials);\n\n      if (response.success && response.data) {\n        // 保存token到localStorage\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n\n        // 直接使用登录响应中的用户信息\n        dispatch(setUser(response.data.user));\n        hasInitialized.current = true; // 标记已初始化，避免重复调用checkAuth\n\n        return { success: true };\n      }\n\n      return { success: false, message: response.message || '登录失败' };\n    } catch (error: any) {\n      console.error('登录异常:', error);\n      return { success: false, message: error.message || '登录失败' };\n    } finally {\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      await authService.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      // 清除Redux状态\n      dispatch(clearUser());\n      // 重置初始化标记\n      hasInitialized.current = false;\n    }\n  };\n\n  // 检查认证状态\n  const checkAuth = useCallback(async () => {\n    // 防止重复初始化\n    if (hasInitialized.current) {\n      return isAuthenticated;\n    }\n\n    const token = localStorage.getItem('token');\n    if (!token) {\n      dispatch(clearUser());\n      hasInitialized.current = true;\n      return false;\n    }\n\n    try {\n      dispatch(setLoading(true));\n      const response = await authService.getUserInfo();\n      if (response.success && response.data) {\n        dispatch(setUser(response.data));\n        hasInitialized.current = true;\n        return true;\n      } else {\n        // token无效，清除认证信息\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        dispatch(clearUser());\n        hasInitialized.current = true;\n        return false;\n      }\n    } catch (error) {\n      console.error('Check auth error:', error);\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      dispatch(clearUser());\n      hasInitialized.current = true;\n      return false;\n    } finally {\n      dispatch(setLoading(false));\n    }\n  }, [dispatch]); // 移除isAuthenticated依赖，避免无限循环\n\n  // 刷新token\n  const refreshToken = async () => {\n    const refreshTokenValue = localStorage.getItem('refreshToken');\n    if (!refreshTokenValue) {\n      return false;\n    }\n\n    try {\n      const response = await authService.refreshToken(refreshTokenValue);\n      if (response.success && response.data) {\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Refresh token error:', error);\n      return false;\n    }\n  };\n\n  // 初始化时检查认证状态（只执行一次）\n  useEffect(() => {\n    if (!hasInitialized.current) {\n      checkAuth();\n    }\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []); // 空依赖数组，确保只在组件挂载时执行一次\n\n  return {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    checkAuth,\n    refreshToken,\n  };\n};\n"], "mappings": ";AAAA,SAASA,SAAS,EAAEC,WAAW,EAAEC,MAAM,QAAQ,OAAO;AACtD,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,OAAO,EAAEC,SAAS,EAAEC,UAAU,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,QAAQ,yBAAyB;AAgBrD,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,IAAI;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGV,WAAW,CAAEW,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EACxF,MAAMC,cAAc,GAAGf,MAAM,CAAC,KAAK,CAAC;;EAEpC;EACA,MAAMgB,KAAK,GAAG,MAAOC,WAA6B,IAAK;IACrD,IAAI;MACFR,QAAQ,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;MAC1B,MAAMa,QAAQ,GAAG,MAAMZ,WAAW,CAACU,KAAK,CAACC,WAAW,CAAC;MAErD,IAAIC,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrC;QACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;QAClDF,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACE,IAAI,CAACI,YAAY,CAAC;;QAEhE;QACAf,QAAQ,CAACN,OAAO,CAACe,QAAQ,CAACE,IAAI,CAACV,IAAI,CAAC,CAAC;QACrCK,cAAc,CAACU,OAAO,GAAG,IAAI,CAAC,CAAC;;QAE/B,OAAO;UAAEN,OAAO,EAAE;QAAK,CAAC;MAC1B;MAEA,OAAO;QAAEA,OAAO,EAAE,KAAK;QAAEO,OAAO,EAAER,QAAQ,CAACQ,OAAO,IAAI;MAAO,CAAC;IAChE,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B,OAAO;QAAER,OAAO,EAAE,KAAK;QAAEO,OAAO,EAAEC,KAAK,CAACD,OAAO,IAAI;MAAO,CAAC;IAC7D,CAAC,SAAS;MACRjB,QAAQ,CAACJ,UAAU,CAAC,KAAK,CAAC,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMwB,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMvB,WAAW,CAACuB,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOF,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACAN,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;MAChCT,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;MACvC;MACArB,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrB;MACAW,cAAc,CAACU,OAAO,GAAG,KAAK;IAChC;EACF,CAAC;;EAED;EACA,MAAMM,SAAS,GAAGhC,WAAW,CAAC,YAAY;IACxC;IACA,IAAIgB,cAAc,CAACU,OAAO,EAAE;MAC1B,OAAOd,eAAe;IACxB;IAEA,MAAMY,KAAK,GAAGF,YAAY,CAACW,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACT,KAAK,EAAE;MACVd,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrBW,cAAc,CAACU,OAAO,GAAG,IAAI;MAC7B,OAAO,KAAK;IACd;IAEA,IAAI;MACFhB,QAAQ,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;MAC1B,MAAMa,QAAQ,GAAG,MAAMZ,WAAW,CAAC2B,WAAW,CAAC,CAAC;MAChD,IAAIf,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrCX,QAAQ,CAACN,OAAO,CAACe,QAAQ,CAACE,IAAI,CAAC,CAAC;QAChCL,cAAc,CAACU,OAAO,GAAG,IAAI;QAC7B,OAAO,IAAI;MACb,CAAC,MAAM;QACL;QACAJ,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;QAChCT,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;QACvCrB,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;QACrBW,cAAc,CAACU,OAAO,GAAG,IAAI;QAC7B,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCN,YAAY,CAACS,UAAU,CAAC,OAAO,CAAC;MAChCT,YAAY,CAACS,UAAU,CAAC,cAAc,CAAC;MACvCrB,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrBW,cAAc,CAACU,OAAO,GAAG,IAAI;MAC7B,OAAO,KAAK;IACd,CAAC,SAAS;MACRhB,QAAQ,CAACJ,UAAU,CAAC,KAAK,CAAC,CAAC;IAC7B;EACF,CAAC,EAAE,CAACI,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAEhB;EACA,MAAMe,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMU,iBAAiB,GAAGb,YAAY,CAACW,OAAO,CAAC,cAAc,CAAC;IAC9D,IAAI,CAACE,iBAAiB,EAAE;MACtB,OAAO,KAAK;IACd;IAEA,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMZ,WAAW,CAACkB,YAAY,CAACU,iBAAiB,CAAC;MAClE,IAAIhB,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrCC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;QAClDF,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACE,IAAI,CAACI,YAAY,CAAC;QAChE,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOG,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA7B,SAAS,CAAC,MAAM;IACd,IAAI,CAACiB,cAAc,CAACU,OAAO,EAAE;MAC3BM,SAAS,CAAC,CAAC;IACb;IACA;EACF,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;;EAER,OAAO;IACLrB,IAAI;IACJC,eAAe;IACfC,OAAO;IACPI,KAAK;IACLa,MAAM;IACNE,SAAS;IACTP;EACF,CAAC;AACH,CAAC;AAAChB,EAAA,CAhIWD,OAAO;EAAA,QACDN,WAAW,EACeC,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}