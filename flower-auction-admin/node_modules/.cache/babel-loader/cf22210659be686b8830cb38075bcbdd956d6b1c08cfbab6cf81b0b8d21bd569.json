{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Row, Col, Statistic, Typography, DatePicker, Select, Button, Space, Table, Progress, Tag, Divider, message } from 'antd';\nimport { DollarOutlined, RiseOutlined, PieChartOutlined, BarChartOutlined, DownloadOutlined, ReloadOutlined } from '@ant-design/icons';\nimport dayjs from 'dayjs';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  RangePicker\n} = DatePicker;\nconst {\n  Option\n} = Select;\n\n// 财务数据接口\n\n// 收入分析数据\n\n// 佣金记录\n\nconst FinanceReports = () => {\n  _s();\n  const [loading, setLoading] = useState(false);\n  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'day'), dayjs()]);\n  const [reportType, setReportType] = useState('daily');\n  const [financeData, setFinanceData] = useState({\n    totalRevenue: 0,\n    totalCommission: 0,\n    totalProfit: 0,\n    orderCount: 0,\n    avgOrderValue: 0,\n    growthRate: 0\n  });\n  const [revenueAnalysis, setRevenueAnalysis] = useState([]);\n  const [commissionRecords, setCommissionRecords] = useState([]);\n\n  // 模拟数据\n  const mockFinanceData = {\n    totalRevenue: 156780.50,\n    totalCommission: 7839.03,\n    totalProfit: 148941.47,\n    orderCount: 234,\n    avgOrderValue: 670.26,\n    growthRate: 15.6\n  };\n  const mockRevenueAnalysis = [{\n    date: '2024-01-01',\n    revenue: 5200,\n    commission: 260,\n    orders: 8\n  }, {\n    date: '2024-01-02',\n    revenue: 6800,\n    commission: 340,\n    orders: 12\n  }, {\n    date: '2024-01-03',\n    revenue: 4500,\n    commission: 225,\n    orders: 6\n  }, {\n    date: '2024-01-04',\n    revenue: 7200,\n    commission: 360,\n    orders: 15\n  }, {\n    date: '2024-01-05',\n    revenue: 5900,\n    commission: 295,\n    orders: 9\n  }];\n  const mockCommissionRecords = [{\n    id: 1,\n    orderNo: 'ORD-20240115-001',\n    sellerName: '花卉供应商A',\n    orderAmount: 1200.00,\n    commissionRate: 5.0,\n    commissionAmount: 60.00,\n    status: 'settled',\n    createdAt: '2024-01-15 10:30:00',\n    settledAt: '2024-01-16 09:00:00'\n  }, {\n    id: 2,\n    orderNo: 'ORD-20240115-002',\n    sellerName: '花卉供应商B',\n    orderAmount: 850.00,\n    commissionRate: 4.5,\n    commissionAmount: 38.25,\n    status: 'pending',\n    createdAt: '2024-01-15 14:20:00'\n  }, {\n    id: 3,\n    orderNo: 'ORD-20240114-003',\n    sellerName: '花卉供应商C',\n    orderAmount: 2100.00,\n    commissionRate: 6.0,\n    commissionAmount: 126.00,\n    status: 'settled',\n    createdAt: '2024-01-14 16:45:00',\n    settledAt: '2024-01-15 10:00:00'\n  }];\n\n  // 获取财务数据\n  const fetchFinanceData = async () => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API获取财务数据\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setFinanceData(mockFinanceData);\n      setRevenueAnalysis(mockRevenueAnalysis);\n      setCommissionRecords(mockCommissionRecords);\n    } catch (error) {\n      message.error(error.message || '获取财务数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchFinanceData();\n  }, [dateRange, reportType]);\n\n  // 导出报表\n  const handleExport = () => {\n    message.success('导出功能开发中...');\n  };\n\n  // 佣金状态映射\n  const commissionStatusMap = {\n    pending: {\n      label: '待结算',\n      color: 'orange'\n    },\n    settled: {\n      label: '已结算',\n      color: 'green'\n    },\n    cancelled: {\n      label: '已取消',\n      color: 'red'\n    }\n  };\n\n  // 佣金记录表格列\n  const commissionColumns = [{\n    title: '订单号',\n    dataIndex: 'orderNo',\n    key: 'orderNo',\n    width: 150\n  }, {\n    title: '卖家',\n    dataIndex: 'sellerName',\n    key: 'sellerName',\n    width: 120\n  }, {\n    title: '订单金额',\n    dataIndex: 'orderAmount',\n    key: 'orderAmount',\n    width: 120,\n    render: amount => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      children: [\"\\xA5\", amount.toFixed(2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 189,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '佣金率',\n    dataIndex: 'commissionRate',\n    key: 'commissionRate',\n    width: 100,\n    render: rate => `${rate}%`\n  }, {\n    title: '佣金金额',\n    dataIndex: 'commissionAmount',\n    key: 'commissionAmount',\n    width: 120,\n    render: amount => /*#__PURE__*/_jsxDEV(Text, {\n      strong: true,\n      style: {\n        color: '#f50'\n      },\n      children: [\"\\xA5\", amount.toFixed(2)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 205,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => {\n      const statusInfo = commissionStatusMap[status];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || 'default',\n        children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.label) || '未知'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 216,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => dayjs(text).format('YYYY-MM-DD HH:mm')\n  }, {\n    title: '结算时间',\n    dataIndex: 'settledAt',\n    key: 'settledAt',\n    width: 160,\n    render: text => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u8D22\\u52A1\\u62A5\\u8868\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 240,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u65F6\\u95F4\\u8303\\u56F4:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(RangePicker, {\n              value: dateRange,\n              onChange: dates => {\n                if (dates) {\n                  setDateRange([dates[0], dates[1]]);\n                }\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 248,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 245,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Text, {\n              children: \"\\u62A5\\u8868\\u7C7B\\u578B:\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 260,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Select, {\n              value: reportType,\n              onChange: setReportType,\n              style: {\n                width: 120\n              },\n              children: [/*#__PURE__*/_jsxDEV(Option, {\n                value: \"daily\",\n                children: \"\\u65E5\\u62A5\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 266,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Option, {\n                value: \"monthly\",\n                children: \"\\u6708\\u62A5\\u8868\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 261,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 258,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 275,\n                columnNumber: 23\n              }, this),\n              onClick: fetchFinanceData,\n              loading: loading,\n              children: \"\\u5237\\u65B0\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 273,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(DownloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 282,\n                columnNumber: 23\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\\u62A5\\u8868\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 281,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 272,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 271,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 243,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u6536\\u5165\",\n            value: financeData.totalRevenue,\n            precision: 2,\n            prefix: /*#__PURE__*/_jsxDEV(DollarOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 300,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u5143\",\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: [/*#__PURE__*/_jsxDEV(RiseOutlined, {\n                style: {\n                  color: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 17\n              }, this), ' ', \"\\u589E\\u957F\\u7387: \", financeData.growthRate, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 294,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u4F63\\u91D1\",\n            value: financeData.totalCommission,\n            precision: 2,\n            prefix: /*#__PURE__*/_jsxDEV(PieChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 318,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u5143\",\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 314,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: [/*#__PURE__*/_jsxDEV(Progress, {\n              percent: Math.round(financeData.totalCommission / financeData.totalRevenue * 100),\n              size: \"small\",\n              showInfo: false\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 323,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: [\"\\u5360\\u603B\\u6536\\u5165 \", (financeData.totalCommission / financeData.totalRevenue * 100).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 328,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 322,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 313,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 312,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u51C0\\u5229\\u6DA6\",\n            value: financeData.totalProfit,\n            precision: 2,\n            prefix: /*#__PURE__*/_jsxDEV(BarChartOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 340,\n              columnNumber: 23\n            }, this),\n            suffix: \"\\u5143\",\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: [\"\\u5229\\u6DA6\\u7387: \", (financeData.totalProfit / financeData.totalRevenue * 100).toFixed(1), \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 344,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 334,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: [/*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u8BA2\\u5355\\u6570\\u91CF\",\n            value: financeData.orderCount,\n            suffix: \"\\u7B14\",\n            valueStyle: {\n              color: '#f50'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 353,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginTop: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: [\"\\u5E73\\u5747\\u8BA2\\u5355\\u4EF7\\u503C: \\xA5\", financeData.avgOrderValue.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 359,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 352,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 293,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u6536\\u5165\\u8D8B\\u52BF\\u5206\\u6790\",\n      style: {\n        marginBottom: 24\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        gutter: 16,\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 16,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              height: 300,\n              background: '#f5f5f5',\n              display: 'flex',\n              alignItems: 'center',\n              justifyContent: 'center',\n              borderRadius: 8\n            },\n            children: /*#__PURE__*/_jsxDEV(Text, {\n              type: \"secondary\",\n              children: \"\\u6536\\u5165\\u8D8B\\u52BF\\u56FE\\u8868\\uFF08\\u9700\\u8981\\u96C6\\u6210\\u56FE\\u8868\\u5E93\\uFF09\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 383,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 373,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          xs: 24,\n          lg: 8,\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              padding: 16\n            },\n            children: [/*#__PURE__*/_jsxDEV(Title, {\n              level: 4,\n              children: \"\\u6570\\u636E\\u6982\\u89C8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Space, {\n              direction: \"vertical\",\n              style: {\n                width: '100%'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u6700\\u9AD8\\u5355\\u65E5\\u6536\\u5165\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 392,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontSize: 18,\n                    color: '#f50'\n                  },\n                  children: [\"\\xA5\", Math.max(...revenueAnalysis.map(item => item.revenue)).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 390,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 397,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u5E73\\u5747\\u65E5\\u6536\\u5165\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 399,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 400,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontSize: 18,\n                    color: '#1890ff'\n                  },\n                  children: [\"\\xA5\", (revenueAnalysis.reduce((sum, item) => sum + item.revenue, 0) / revenueAnalysis.length).toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 401,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 398,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  strong: true,\n                  children: \"\\u603B\\u8BA2\\u5355\\u6570\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 407,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 408,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  style: {\n                    fontSize: 18,\n                    color: '#52c41a'\n                  },\n                  children: [revenueAnalysis.reduce((sum, item) => sum + item.orders, 0), \" \\u7B14\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 409,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 406,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 389,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 370,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 369,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      title: \"\\u4F63\\u91D1\\u7BA1\\u7406\",\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: commissionColumns,\n        dataSource: commissionRecords,\n        rowKey: \"id\",\n        loading: loading,\n        pagination: {\n          pageSize: 10,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`\n        },\n        summary: pageData => {\n          const totalCommission = pageData.reduce((sum, record) => sum + record.commissionAmount, 0);\n          const settledCommission = pageData.filter(record => record.status === 'settled').reduce((sum, record) => sum + record.commissionAmount, 0);\n          const pendingCommission = pageData.filter(record => record.status === 'pending').reduce((sum, record) => sum + record.commissionAmount, 0);\n          return /*#__PURE__*/_jsxDEV(Table.Summary.Row, {\n            children: [/*#__PURE__*/_jsxDEV(Table.Summary.Cell, {\n              index: 0,\n              colSpan: 4,\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                children: \"\\u5408\\u8BA1\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 445,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table.Summary.Cell, {\n              index: 4,\n              children: /*#__PURE__*/_jsxDEV(Text, {\n                strong: true,\n                style: {\n                  color: '#f50'\n                },\n                children: [\"\\xA5\", totalCommission.toFixed(2)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Table.Summary.Cell, {\n              index: 5,\n              colSpan: 3,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: [\"\\u5DF2\\u7ED3\\u7B97: \\xA5\", settledCommission.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: [\"\\u5F85\\u7ED3\\u7B97: \\xA5\", pendingCommission.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 15\n          }, this);\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 239,\n    columnNumber: 5\n  }, this);\n};\n_s(FinanceReports, \"cQHbfQ+HN/gbg+mSakHLHI49t9c=\");\n_c = FinanceReports;\nexport default FinanceReports;\nvar _c;\n$RefreshReg$(_c, \"FinanceReports\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Row", "Col", "Statistic", "Typography", "DatePicker", "Select", "<PERSON><PERSON>", "Space", "Table", "Progress", "Tag", "Divider", "message", "DollarOutlined", "RiseOutlined", "PieChartOutlined", "BarChartOutlined", "DownloadOutlined", "ReloadOutlined", "dayjs", "jsxDEV", "_jsxDEV", "Title", "Text", "RangePicker", "Option", "FinanceReports", "_s", "loading", "setLoading", "date<PERSON><PERSON><PERSON>", "setDateRange", "subtract", "reportType", "setReportType", "financeData", "setFinanceData", "totalRevenue", "totalCommission", "totalProfit", "orderCount", "avgOrderValue", "growthRate", "revenueAnalysis", "setRevenueAnalysis", "commissionRecords", "setCommissionRecords", "mockFinanceData", "mockRevenueAnalysis", "date", "revenue", "commission", "orders", "mockCommissionRecords", "id", "orderNo", "sellerName", "orderAmount", "commissionRate", "commissionAmount", "status", "createdAt", "settledAt", "fetchFinanceData", "Promise", "resolve", "setTimeout", "error", "handleExport", "success", "commissionStatusMap", "pending", "label", "color", "settled", "cancelled", "commissionColumns", "title", "dataIndex", "key", "width", "render", "amount", "strong", "children", "toFixed", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "rate", "style", "statusInfo", "text", "format", "padding", "level", "size", "marginBottom", "gutter", "align", "value", "onChange", "dates", "type", "icon", "onClick", "xs", "sm", "md", "precision", "prefix", "suffix", "valueStyle", "marginTop", "percent", "Math", "round", "showInfo", "lg", "height", "background", "display", "alignItems", "justifyContent", "borderRadius", "direction", "fontSize", "max", "map", "item", "reduce", "sum", "length", "columns", "dataSource", "<PERSON><PERSON><PERSON>", "pagination", "pageSize", "showSizeChanger", "showQuickJumper", "showTotal", "total", "range", "summary", "pageData", "record", "settledCommission", "filter", "pendingCommission", "Summary", "Cell", "index", "colSpan", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Statistic,\n  Typography,\n  DatePicker,\n  Select,\n  Button,\n  Space,\n  Table,\n  Progress,\n  Tag,\n  Divider,\n  message,\n} from 'antd';\nimport {\n  DollarOutlined,\n  RiseOutlined,\n  FallOutlined,\n  PieChartOutlined,\n  Bar<PERSON><PERSON>Outlined,\n  DownloadOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport dayjs from 'dayjs';\n\nconst { Title, Text } = Typography;\nconst { RangePicker } = DatePicker;\nconst { Option } = Select;\n\n// 财务数据接口\ninterface FinanceData {\n  totalRevenue: number;\n  totalCommission: number;\n  totalProfit: number;\n  orderCount: number;\n  avgOrderValue: number;\n  growthRate: number;\n}\n\n// 收入分析数据\ninterface RevenueAnalysis {\n  date: string;\n  revenue: number;\n  commission: number;\n  orders: number;\n}\n\n// 佣金记录\ninterface CommissionRecord {\n  id: number;\n  orderNo: string;\n  sellerName: string;\n  orderAmount: number;\n  commissionRate: number;\n  commissionAmount: number;\n  status: 'pending' | 'settled' | 'cancelled';\n  createdAt: string;\n  settledAt?: string;\n}\n\nconst FinanceReports: React.FC = () => {\n  const [loading, setLoading] = useState(false);\n  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([\n    dayjs().subtract(30, 'day'),\n    dayjs(),\n  ]);\n  const [reportType, setReportType] = useState<'daily' | 'monthly'>('daily');\n  const [financeData, setFinanceData] = useState<FinanceData>({\n    totalRevenue: 0,\n    totalCommission: 0,\n    totalProfit: 0,\n    orderCount: 0,\n    avgOrderValue: 0,\n    growthRate: 0,\n  });\n  const [revenueAnalysis, setRevenueAnalysis] = useState<RevenueAnalysis[]>([]);\n  const [commissionRecords, setCommissionRecords] = useState<CommissionRecord[]>([]);\n\n  // 模拟数据\n  const mockFinanceData: FinanceData = {\n    totalRevenue: 156780.50,\n    totalCommission: 7839.03,\n    totalProfit: 148941.47,\n    orderCount: 234,\n    avgOrderValue: 670.26,\n    growthRate: 15.6,\n  };\n\n  const mockRevenueAnalysis: RevenueAnalysis[] = [\n    { date: '2024-01-01', revenue: 5200, commission: 260, orders: 8 },\n    { date: '2024-01-02', revenue: 6800, commission: 340, orders: 12 },\n    { date: '2024-01-03', revenue: 4500, commission: 225, orders: 6 },\n    { date: '2024-01-04', revenue: 7200, commission: 360, orders: 15 },\n    { date: '2024-01-05', revenue: 5900, commission: 295, orders: 9 },\n  ];\n\n  const mockCommissionRecords: CommissionRecord[] = [\n    {\n      id: 1,\n      orderNo: 'ORD-20240115-001',\n      sellerName: '花卉供应商A',\n      orderAmount: 1200.00,\n      commissionRate: 5.0,\n      commissionAmount: 60.00,\n      status: 'settled',\n      createdAt: '2024-01-15 10:30:00',\n      settledAt: '2024-01-16 09:00:00',\n    },\n    {\n      id: 2,\n      orderNo: 'ORD-20240115-002',\n      sellerName: '花卉供应商B',\n      orderAmount: 850.00,\n      commissionRate: 4.5,\n      commissionAmount: 38.25,\n      status: 'pending',\n      createdAt: '2024-01-15 14:20:00',\n    },\n    {\n      id: 3,\n      orderNo: 'ORD-20240114-003',\n      sellerName: '花卉供应商C',\n      orderAmount: 2100.00,\n      commissionRate: 6.0,\n      commissionAmount: 126.00,\n      status: 'settled',\n      createdAt: '2024-01-14 16:45:00',\n      settledAt: '2024-01-15 10:00:00',\n    },\n  ];\n\n  // 获取财务数据\n  const fetchFinanceData = async () => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API获取财务数据\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      setFinanceData(mockFinanceData);\n      setRevenueAnalysis(mockRevenueAnalysis);\n      setCommissionRecords(mockCommissionRecords);\n    } catch (error: any) {\n      message.error(error.message || '获取财务数据失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchFinanceData();\n  }, [dateRange, reportType]);\n\n  // 导出报表\n  const handleExport = () => {\n    message.success('导出功能开发中...');\n  };\n\n  // 佣金状态映射\n  const commissionStatusMap = {\n    pending: { label: '待结算', color: 'orange' },\n    settled: { label: '已结算', color: 'green' },\n    cancelled: { label: '已取消', color: 'red' },\n  };\n\n  // 佣金记录表格列\n  const commissionColumns: ColumnsType<CommissionRecord> = [\n    {\n      title: '订单号',\n      dataIndex: 'orderNo',\n      key: 'orderNo',\n      width: 150,\n    },\n    {\n      title: '卖家',\n      dataIndex: 'sellerName',\n      key: 'sellerName',\n      width: 120,\n    },\n    {\n      title: '订单金额',\n      dataIndex: 'orderAmount',\n      key: 'orderAmount',\n      width: 120,\n      render: (amount: number) => (\n        <Text strong>¥{amount.toFixed(2)}</Text>\n      ),\n    },\n    {\n      title: '佣金率',\n      dataIndex: 'commissionRate',\n      key: 'commissionRate',\n      width: 100,\n      render: (rate: number) => `${rate}%`,\n    },\n    {\n      title: '佣金金额',\n      dataIndex: 'commissionAmount',\n      key: 'commissionAmount',\n      width: 120,\n      render: (amount: number) => (\n        <Text strong style={{ color: '#f50' }}>¥{amount.toFixed(2)}</Text>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: keyof typeof commissionStatusMap) => {\n        const statusInfo = commissionStatusMap[status];\n        return (\n          <Tag color={statusInfo?.color || 'default'}>\n            {statusInfo?.label || '未知'}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),\n    },\n    {\n      title: '结算时间',\n      dataIndex: 'settledAt',\n      key: 'settledAt',\n      width: 160,\n      render: (text?: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-',\n    },\n  ];\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>财务报表</Title>\n\n      {/* 筛选条件 */}\n      <Card size=\"small\" style={{ marginBottom: 16 }}>\n        <Row gutter={16} align=\"middle\">\n          <Col>\n            <Space>\n              <Text>时间范围:</Text>\n              <RangePicker\n                value={dateRange}\n                onChange={(dates) => {\n                  if (dates) {\n                    setDateRange([dates[0]!, dates[1]!]);\n                  }\n                }}\n              />\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <Text>报表类型:</Text>\n              <Select\n                value={reportType}\n                onChange={setReportType}\n                style={{ width: 120 }}\n              >\n                <Option value=\"daily\">日报表</Option>\n                <Option value=\"monthly\">月报表</Option>\n              </Select>\n            </Space>\n          </Col>\n          <Col>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<ReloadOutlined />}\n                onClick={fetchFinanceData}\n                loading={loading}\n              >\n                刷新\n              </Button>\n              <Button\n                icon={<DownloadOutlined />}\n                onClick={handleExport}\n              >\n                导出报表\n              </Button>\n            </Space>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 核心指标统计 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总收入\"\n              value={financeData.totalRevenue}\n              precision={2}\n              prefix={<DollarOutlined />}\n              suffix=\"元\"\n              valueStyle={{ color: '#3f8600' }}\n            />\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                <RiseOutlined style={{ color: '#52c41a' }} />\n                {' '}增长率: {financeData.growthRate}%\n              </Text>\n            </div>\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总佣金\"\n              value={financeData.totalCommission}\n              precision={2}\n              prefix={<PieChartOutlined />}\n              suffix=\"元\"\n              valueStyle={{ color: '#1890ff' }}\n            />\n            <div style={{ marginTop: 8 }}>\n              <Progress\n                percent={Math.round((financeData.totalCommission / financeData.totalRevenue) * 100)}\n                size=\"small\"\n                showInfo={false}\n              />\n              <Text type=\"secondary\">\n                占总收入 {((financeData.totalCommission / financeData.totalRevenue) * 100).toFixed(1)}%\n              </Text>\n            </div>\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"净利润\"\n              value={financeData.totalProfit}\n              precision={2}\n              prefix={<BarChartOutlined />}\n              suffix=\"元\"\n              valueStyle={{ color: '#722ed1' }}\n            />\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                利润率: {((financeData.totalProfit / financeData.totalRevenue) * 100).toFixed(1)}%\n              </Text>\n            </div>\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"订单数量\"\n              value={financeData.orderCount}\n              suffix=\"笔\"\n              valueStyle={{ color: '#f50' }}\n            />\n            <div style={{ marginTop: 8 }}>\n              <Text type=\"secondary\">\n                平均订单价值: ¥{financeData.avgOrderValue.toFixed(2)}\n              </Text>\n            </div>\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 收入趋势分析 */}\n      <Card title=\"收入趋势分析\" style={{ marginBottom: 24 }}>\n        <Row gutter={16}>\n          <Col xs={24} lg={16}>\n            {/* 这里应该放置图表组件，如 ECharts 或 Chart.js */}\n            <div\n              style={{\n                height: 300,\n                background: '#f5f5f5',\n                display: 'flex',\n                alignItems: 'center',\n                justifyContent: 'center',\n                borderRadius: 8,\n              }}\n            >\n              <Text type=\"secondary\">收入趋势图表（需要集成图表库）</Text>\n            </div>\n          </Col>\n          <Col xs={24} lg={8}>\n            <div style={{ padding: 16 }}>\n              <Title level={4}>数据概览</Title>\n              <Space direction=\"vertical\" style={{ width: '100%' }}>\n                <div>\n                  <Text strong>最高单日收入</Text>\n                  <br />\n                  <Text style={{ fontSize: 18, color: '#f50' }}>\n                    ¥{Math.max(...revenueAnalysis.map(item => item.revenue)).toFixed(2)}\n                  </Text>\n                </div>\n                <Divider />\n                <div>\n                  <Text strong>平均日收入</Text>\n                  <br />\n                  <Text style={{ fontSize: 18, color: '#1890ff' }}>\n                    ¥{(revenueAnalysis.reduce((sum, item) => sum + item.revenue, 0) / revenueAnalysis.length).toFixed(2)}\n                  </Text>\n                </div>\n                <Divider />\n                <div>\n                  <Text strong>总订单数</Text>\n                  <br />\n                  <Text style={{ fontSize: 18, color: '#52c41a' }}>\n                    {revenueAnalysis.reduce((sum, item) => sum + item.orders, 0)} 笔\n                  </Text>\n                </div>\n              </Space>\n            </div>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 佣金管理 */}\n      <Card title=\"佣金管理\">\n        <Table\n          columns={commissionColumns}\n          dataSource={commissionRecords}\n          rowKey=\"id\"\n          loading={loading}\n          pagination={{\n            pageSize: 10,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          }}\n          summary={(pageData) => {\n            const totalCommission = pageData.reduce((sum, record) => sum + record.commissionAmount, 0);\n            const settledCommission = pageData\n              .filter(record => record.status === 'settled')\n              .reduce((sum, record) => sum + record.commissionAmount, 0);\n            const pendingCommission = pageData\n              .filter(record => record.status === 'pending')\n              .reduce((sum, record) => sum + record.commissionAmount, 0);\n\n            return (\n              <Table.Summary.Row>\n                <Table.Summary.Cell index={0} colSpan={4}>\n                  <Text strong>合计</Text>\n                </Table.Summary.Cell>\n                <Table.Summary.Cell index={4}>\n                  <Text strong style={{ color: '#f50' }}>\n                    ¥{totalCommission.toFixed(2)}\n                  </Text>\n                </Table.Summary.Cell>\n                <Table.Summary.Cell index={5} colSpan={3}>\n                  <Space>\n                    <Text>已结算: ¥{settledCommission.toFixed(2)}</Text>\n                    <Text>待结算: ¥{pendingCommission.toFixed(2)}</Text>\n                  </Space>\n                </Table.Summary.Cell>\n              </Table.Summary.Row>\n            );\n          }}\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default FinanceReports;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,SAAS,EACTC,UAAU,EACVC,UAAU,EACVC,MAAM,EACNC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,QAAQ,EACRC,GAAG,EACHC,OAAO,EACPC,OAAO,QACF,MAAM;AACb,SACEC,cAAc,EACdC,YAAY,EAEZC,gBAAgB,EAChBC,gBAAgB,EAChBC,gBAAgB,EAChBC,cAAc,QACT,mBAAmB;AAE1B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAY,CAAC,GAAGpB,UAAU;AAClC,MAAM;EAAEqB;AAAO,CAAC,GAAGpB,MAAM;;AAEzB;;AAUA;;AAQA;;AAaA,MAAMqB,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGhC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiC,SAAS,EAAEC,YAAY,CAAC,GAAGlC,QAAQ,CAA6B,CACrEsB,KAAK,CAAC,CAAC,CAACa,QAAQ,CAAC,EAAE,EAAE,KAAK,CAAC,EAC3Bb,KAAK,CAAC,CAAC,CACR,CAAC;EACF,MAAM,CAACc,UAAU,EAAEC,aAAa,CAAC,GAAGrC,QAAQ,CAAsB,OAAO,CAAC;EAC1E,MAAM,CAACsC,WAAW,EAAEC,cAAc,CAAC,GAAGvC,QAAQ,CAAc;IAC1DwC,YAAY,EAAE,CAAC;IACfC,eAAe,EAAE,CAAC;IAClBC,WAAW,EAAE,CAAC;IACdC,UAAU,EAAE,CAAC;IACbC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAG/C,QAAQ,CAAoB,EAAE,CAAC;EAC7E,MAAM,CAACgD,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGjD,QAAQ,CAAqB,EAAE,CAAC;;EAElF;EACA,MAAMkD,eAA4B,GAAG;IACnCV,YAAY,EAAE,SAAS;IACvBC,eAAe,EAAE,OAAO;IACxBC,WAAW,EAAE,SAAS;IACtBC,UAAU,EAAE,GAAG;IACfC,aAAa,EAAE,MAAM;IACrBC,UAAU,EAAE;EACd,CAAC;EAED,MAAMM,mBAAsC,GAAG,CAC7C;IAAEC,IAAI,EAAE,YAAY;IAAEC,OAAO,EAAE,IAAI;IAAEC,UAAU,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAE,CAAC,EACjE;IAAEH,IAAI,EAAE,YAAY;IAAEC,OAAO,EAAE,IAAI;IAAEC,UAAU,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,EAClE;IAAEH,IAAI,EAAE,YAAY;IAAEC,OAAO,EAAE,IAAI;IAAEC,UAAU,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAE,CAAC,EACjE;IAAEH,IAAI,EAAE,YAAY;IAAEC,OAAO,EAAE,IAAI;IAAEC,UAAU,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAG,CAAC,EAClE;IAAEH,IAAI,EAAE,YAAY;IAAEC,OAAO,EAAE,IAAI;IAAEC,UAAU,EAAE,GAAG;IAAEC,MAAM,EAAE;EAAE,CAAC,CAClE;EAED,MAAMC,qBAAyC,GAAG,CAChD;IACEC,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,kBAAkB;IAC3BC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,OAAO;IACpBC,cAAc,EAAE,GAAG;IACnBC,gBAAgB,EAAE,KAAK;IACvBC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,qBAAqB;IAChCC,SAAS,EAAE;EACb,CAAC,EACD;IACER,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,kBAAkB;IAC3BC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,MAAM;IACnBC,cAAc,EAAE,GAAG;IACnBC,gBAAgB,EAAE,KAAK;IACvBC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE;EACb,CAAC,EACD;IACEP,EAAE,EAAE,CAAC;IACLC,OAAO,EAAE,kBAAkB;IAC3BC,UAAU,EAAE,QAAQ;IACpBC,WAAW,EAAE,OAAO;IACpBC,cAAc,EAAE,GAAG;IACnBC,gBAAgB,EAAE,MAAM;IACxBC,MAAM,EAAE,SAAS;IACjBC,SAAS,EAAE,qBAAqB;IAChCC,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnClC,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,IAAImC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;MACvD7B,cAAc,CAACW,eAAe,CAAC;MAC/BH,kBAAkB,CAACI,mBAAmB,CAAC;MACvCF,oBAAoB,CAACO,qBAAqB,CAAC;IAC7C,CAAC,CAAC,OAAOc,KAAU,EAAE;MACnBvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACRiB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA/B,SAAS,CAAC,MAAM;IACdiE,gBAAgB,CAAC,CAAC;EACpB,CAAC,EAAE,CAACjC,SAAS,EAAEG,UAAU,CAAC,CAAC;;EAE3B;EACA,MAAMmC,YAAY,GAAGA,CAAA,KAAM;IACzBxD,OAAO,CAACyD,OAAO,CAAC,YAAY,CAAC;EAC/B,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAG;IAC1BC,OAAO,EAAE;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC1CC,OAAO,EAAE;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACzCE,SAAS,EAAE;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM;EAC1C,CAAC;;EAED;EACA,MAAMG,iBAAgD,GAAG,CACvD;IACEC,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,SAAS;IACpBC,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,YAAY;IACvBC,GAAG,EAAE,YAAY;IACjBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,aAAa;IACxBC,GAAG,EAAE,aAAa;IAClBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,MAAc,iBACrB7D,OAAA,CAACE,IAAI;MAAC4D,MAAM;MAAAC,QAAA,GAAC,MAAC,EAACF,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAE3C,CAAC,EACD;IACEZ,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,gBAAgB;IAC3BC,GAAG,EAAE,gBAAgB;IACrBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGS,IAAY,IAAK,GAAGA,IAAI;EACnC,CAAC,EACD;IACEb,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,kBAAkB;IAC7BC,GAAG,EAAE,kBAAkB;IACvBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,MAAc,iBACrB7D,OAAA,CAACE,IAAI;MAAC4D,MAAM;MAACQ,KAAK,EAAE;QAAElB,KAAK,EAAE;MAAO,CAAE;MAAAW,QAAA,GAAC,MAAC,EAACF,MAAM,CAACG,OAAO,CAAC,CAAC,CAAC;IAAA;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO;EAErE,CAAC,EACD;IACEZ,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGrB,MAAwC,IAAK;MACpD,MAAMgC,UAAU,GAAGtB,mBAAmB,CAACV,MAAM,CAAC;MAC9C,oBACEvC,OAAA,CAACX,GAAG;QAAC+D,KAAK,EAAE,CAAAmB,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEnB,KAAK,KAAI,SAAU;QAAAW,QAAA,EACxC,CAAAQ,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAEpB,KAAK,KAAI;MAAI;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB,CAAC;IAEV;EACF,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGY,IAAY,IAAK1E,KAAK,CAAC0E,IAAI,CAAC,CAACC,MAAM,CAAC,kBAAkB;EACjE,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGY,IAAa,IAAKA,IAAI,GAAG1E,KAAK,CAAC0E,IAAI,CAAC,CAACC,MAAM,CAAC,kBAAkB,CAAC,GAAG;EAC7E,CAAC,CACF;EAED,oBACEzE,OAAA;IAAKsE,KAAK,EAAE;MAAEI,OAAO,EAAE;IAAG,CAAE;IAAAX,QAAA,gBAC1B/D,OAAA,CAACC,KAAK;MAAC0E,KAAK,EAAE,CAAE;MAAAZ,QAAA,EAAC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7BpE,OAAA,CAACtB,IAAI;MAACkG,IAAI,EAAC,OAAO;MAACN,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAG,CAAE;MAAAd,QAAA,eAC7C/D,OAAA,CAACrB,GAAG;QAACmG,MAAM,EAAE,EAAG;QAACC,KAAK,EAAC,QAAQ;QAAAhB,QAAA,gBAC7B/D,OAAA,CAACpB,GAAG;UAAAmF,QAAA,eACF/D,OAAA,CAACd,KAAK;YAAA6E,QAAA,gBACJ/D,OAAA,CAACE,IAAI;cAAA6D,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBpE,OAAA,CAACG,WAAW;cACV6E,KAAK,EAAEvE,SAAU;cACjBwE,QAAQ,EAAGC,KAAK,IAAK;gBACnB,IAAIA,KAAK,EAAE;kBACTxE,YAAY,CAAC,CAACwE,KAAK,CAAC,CAAC,CAAC,EAAGA,KAAK,CAAC,CAAC,CAAC,CAAE,CAAC;gBACtC;cACF;YAAE;cAAAjB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpE,OAAA,CAACpB,GAAG;UAAAmF,QAAA,eACF/D,OAAA,CAACd,KAAK;YAAA6E,QAAA,gBACJ/D,OAAA,CAACE,IAAI;cAAA6D,QAAA,EAAC;YAAK;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClBpE,OAAA,CAAChB,MAAM;cACLgG,KAAK,EAAEpE,UAAW;cAClBqE,QAAQ,EAAEpE,aAAc;cACxByD,KAAK,EAAE;gBAAEX,KAAK,EAAE;cAAI,CAAE;cAAAI,QAAA,gBAEtB/D,OAAA,CAACI,MAAM;gBAAC4E,KAAK,EAAC,OAAO;gBAAAjB,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC,eAClCpE,OAAA,CAACI,MAAM;gBAAC4E,KAAK,EAAC,SAAS;gBAAAjB,QAAA,EAAC;cAAG;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNpE,OAAA,CAACpB,GAAG;UAAAmF,QAAA,eACF/D,OAAA,CAACd,KAAK;YAAA6E,QAAA,gBACJ/D,OAAA,CAACf,MAAM;cACLkG,IAAI,EAAC,SAAS;cACdC,IAAI,eAAEpF,OAAA,CAACH,cAAc;gBAAAoE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBiB,OAAO,EAAE3C,gBAAiB;cAC1BnC,OAAO,EAAEA,OAAQ;cAAAwD,QAAA,EAClB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTpE,OAAA,CAACf,MAAM;cACLmG,IAAI,eAAEpF,OAAA,CAACJ,gBAAgB;gBAAAqE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cAC3BiB,OAAO,EAAEtC,YAAa;cAAAgB,QAAA,EACvB;YAED;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPpE,OAAA,CAACrB,GAAG;MAACmG,MAAM,EAAE,EAAG;MAACR,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAG,CAAE;MAAAd,QAAA,gBAC3C/D,OAAA,CAACpB,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACzB/D,OAAA,CAACtB,IAAI;UAAAqF,QAAA,gBACH/D,OAAA,CAACnB,SAAS;YACR2E,KAAK,EAAC,oBAAK;YACXwB,KAAK,EAAElE,WAAW,CAACE,YAAa;YAChCyE,SAAS,EAAE,CAAE;YACbC,MAAM,eAAE1F,OAAA,CAACR,cAAc;cAAAyE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC3BuB,MAAM,EAAC,QAAG;YACVC,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFpE,OAAA;YAAKsE,KAAK,EAAE;cAAEuB,SAAS,EAAE;YAAE,CAAE;YAAA9B,QAAA,eAC3B/D,OAAA,CAACE,IAAI;cAACiF,IAAI,EAAC,WAAW;cAAApB,QAAA,gBACpB/D,OAAA,CAACP,YAAY;gBAAC6E,KAAK,EAAE;kBAAElB,KAAK,EAAE;gBAAU;cAAE;gBAAAa,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,EAC5C,GAAG,EAAC,sBAAK,EAACtD,WAAW,CAACO,UAAU,EAAC,GACpC;YAAA;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpE,OAAA,CAACpB,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACzB/D,OAAA,CAACtB,IAAI;UAAAqF,QAAA,gBACH/D,OAAA,CAACnB,SAAS;YACR2E,KAAK,EAAC,oBAAK;YACXwB,KAAK,EAAElE,WAAW,CAACG,eAAgB;YACnCwE,SAAS,EAAE,CAAE;YACbC,MAAM,eAAE1F,OAAA,CAACN,gBAAgB;cAAAuE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BuB,MAAM,EAAC,QAAG;YACVC,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFpE,OAAA;YAAKsE,KAAK,EAAE;cAAEuB,SAAS,EAAE;YAAE,CAAE;YAAA9B,QAAA,gBAC3B/D,OAAA,CAACZ,QAAQ;cACP0G,OAAO,EAAEC,IAAI,CAACC,KAAK,CAAElF,WAAW,CAACG,eAAe,GAAGH,WAAW,CAACE,YAAY,GAAI,GAAG,CAAE;cACpF4D,IAAI,EAAC,OAAO;cACZqB,QAAQ,EAAE;YAAM;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjB,CAAC,eACFpE,OAAA,CAACE,IAAI;cAACiF,IAAI,EAAC,WAAW;cAAApB,QAAA,GAAC,2BAChB,EAAC,CAAEjD,WAAW,CAACG,eAAe,GAAGH,WAAW,CAACE,YAAY,GAAI,GAAG,EAAEgD,OAAO,CAAC,CAAC,CAAC,EAAC,GACpF;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpE,OAAA,CAACpB,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACzB/D,OAAA,CAACtB,IAAI;UAAAqF,QAAA,gBACH/D,OAAA,CAACnB,SAAS;YACR2E,KAAK,EAAC,oBAAK;YACXwB,KAAK,EAAElE,WAAW,CAACI,WAAY;YAC/BuE,SAAS,EAAE,CAAE;YACbC,MAAM,eAAE1F,OAAA,CAACL,gBAAgB;cAAAsE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC7BuB,MAAM,EAAC,QAAG;YACVC,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAU;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC,CAAC,eACFpE,OAAA;YAAKsE,KAAK,EAAE;cAAEuB,SAAS,EAAE;YAAE,CAAE;YAAA9B,QAAA,eAC3B/D,OAAA,CAACE,IAAI;cAACiF,IAAI,EAAC,WAAW;cAAApB,QAAA,GAAC,sBAChB,EAAC,CAAEjD,WAAW,CAACI,WAAW,GAAGJ,WAAW,CAACE,YAAY,GAAI,GAAG,EAAEgD,OAAO,CAAC,CAAC,CAAC,EAAC,GAChF;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACNpE,OAAA,CAACpB,GAAG;QAAC0G,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAzB,QAAA,eACzB/D,OAAA,CAACtB,IAAI;UAAAqF,QAAA,gBACH/D,OAAA,CAACnB,SAAS;YACR2E,KAAK,EAAC,0BAAM;YACZwB,KAAK,EAAElE,WAAW,CAACK,UAAW;YAC9BwE,MAAM,EAAC,QAAG;YACVC,UAAU,EAAE;cAAExC,KAAK,EAAE;YAAO;UAAE;YAAAa,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/B,CAAC,eACFpE,OAAA;YAAKsE,KAAK,EAAE;cAAEuB,SAAS,EAAE;YAAE,CAAE;YAAA9B,QAAA,eAC3B/D,OAAA,CAACE,IAAI;cAACiF,IAAI,EAAC,WAAW;cAAApB,QAAA,GAAC,4CACZ,EAACjD,WAAW,CAACM,aAAa,CAAC4C,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNpE,OAAA,CAACtB,IAAI;MAAC8E,KAAK,EAAC,sCAAQ;MAACc,KAAK,EAAE;QAAEO,YAAY,EAAE;MAAG,CAAE;MAAAd,QAAA,eAC/C/D,OAAA,CAACrB,GAAG;QAACmG,MAAM,EAAE,EAAG;QAAAf,QAAA,gBACd/D,OAAA,CAACpB,GAAG;UAAC0G,EAAE,EAAE,EAAG;UAACY,EAAE,EAAE,EAAG;UAAAnC,QAAA,eAElB/D,OAAA;YACEsE,KAAK,EAAE;cACL6B,MAAM,EAAE,GAAG;cACXC,UAAU,EAAE,SAAS;cACrBC,OAAO,EAAE,MAAM;cACfC,UAAU,EAAE,QAAQ;cACpBC,cAAc,EAAE,QAAQ;cACxBC,YAAY,EAAE;YAChB,CAAE;YAAAzC,QAAA,eAEF/D,OAAA,CAACE,IAAI;cAACiF,IAAI,EAAC,WAAW;cAAApB,QAAA,EAAC;YAAe;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNpE,OAAA,CAACpB,GAAG;UAAC0G,EAAE,EAAE,EAAG;UAACY,EAAE,EAAE,CAAE;UAAAnC,QAAA,eACjB/D,OAAA;YAAKsE,KAAK,EAAE;cAAEI,OAAO,EAAE;YAAG,CAAE;YAAAX,QAAA,gBAC1B/D,OAAA,CAACC,KAAK;cAAC0E,KAAK,EAAE,CAAE;cAAAZ,QAAA,EAAC;YAAI;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC7BpE,OAAA,CAACd,KAAK;cAACuH,SAAS,EAAC,UAAU;cAACnC,KAAK,EAAE;gBAAEX,KAAK,EAAE;cAAO,CAAE;cAAAI,QAAA,gBACnD/D,OAAA;gBAAA+D,QAAA,gBACE/D,OAAA,CAACE,IAAI;kBAAC4D,MAAM;kBAAAC,QAAA,EAAC;gBAAM;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eAC1BpE,OAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpE,OAAA,CAACE,IAAI;kBAACoE,KAAK,EAAE;oBAAEoC,QAAQ,EAAE,EAAE;oBAAEtD,KAAK,EAAE;kBAAO,CAAE;kBAAAW,QAAA,GAAC,MAC3C,EAACgC,IAAI,CAACY,GAAG,CAAC,GAAGrF,eAAe,CAACsF,GAAG,CAACC,IAAI,IAAIA,IAAI,CAAChF,OAAO,CAAC,CAAC,CAACmC,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/D,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpE,OAAA,CAACV,OAAO;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXpE,OAAA;gBAAA+D,QAAA,gBACE/D,OAAA,CAACE,IAAI;kBAAC4D,MAAM;kBAAAC,QAAA,EAAC;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACzBpE,OAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpE,OAAA,CAACE,IAAI;kBAACoE,KAAK,EAAE;oBAAEoC,QAAQ,EAAE,EAAE;oBAAEtD,KAAK,EAAE;kBAAU,CAAE;kBAAAW,QAAA,GAAC,MAC9C,EAAC,CAACzC,eAAe,CAACwF,MAAM,CAAC,CAACC,GAAG,EAAEF,IAAI,KAAKE,GAAG,GAAGF,IAAI,CAAChF,OAAO,EAAE,CAAC,CAAC,GAAGP,eAAe,CAAC0F,MAAM,EAAEhD,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC,eACNpE,OAAA,CAACV,OAAO;gBAAA2E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAC,eACXpE,OAAA;gBAAA+D,QAAA,gBACE/D,OAAA,CAACE,IAAI;kBAAC4D,MAAM;kBAAAC,QAAA,EAAC;gBAAI;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC,eACxBpE,OAAA;kBAAAiE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,eACNpE,OAAA,CAACE,IAAI;kBAACoE,KAAK,EAAE;oBAAEoC,QAAQ,EAAE,EAAE;oBAAEtD,KAAK,EAAE;kBAAU,CAAE;kBAAAW,QAAA,GAC7CzC,eAAe,CAACwF,MAAM,CAAC,CAACC,GAAG,EAAEF,IAAI,KAAKE,GAAG,GAAGF,IAAI,CAAC9E,MAAM,EAAE,CAAC,CAAC,EAAC,SAC/D;gBAAA;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPpE,OAAA,CAACtB,IAAI;MAAC8E,KAAK,EAAC,0BAAM;MAAAO,QAAA,eAChB/D,OAAA,CAACb,KAAK;QACJ8H,OAAO,EAAE1D,iBAAkB;QAC3B2D,UAAU,EAAE1F,iBAAkB;QAC9B2F,MAAM,EAAC,IAAI;QACX5G,OAAO,EAAEA,OAAQ;QACjB6G,UAAU,EAAE;UACVC,QAAQ,EAAE,EAAE;UACZC,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACC,KAAK,EAAEC,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQD,KAAK;QAC1C,CAAE;QACFE,OAAO,EAAGC,QAAQ,IAAK;UACrB,MAAM3G,eAAe,GAAG2G,QAAQ,CAACd,MAAM,CAAC,CAACC,GAAG,EAAEc,MAAM,KAAKd,GAAG,GAAGc,MAAM,CAACvF,gBAAgB,EAAE,CAAC,CAAC;UAC1F,MAAMwF,iBAAiB,GAAGF,QAAQ,CAC/BG,MAAM,CAACF,MAAM,IAAIA,MAAM,CAACtF,MAAM,KAAK,SAAS,CAAC,CAC7CuE,MAAM,CAAC,CAACC,GAAG,EAAEc,MAAM,KAAKd,GAAG,GAAGc,MAAM,CAACvF,gBAAgB,EAAE,CAAC,CAAC;UAC5D,MAAM0F,iBAAiB,GAAGJ,QAAQ,CAC/BG,MAAM,CAACF,MAAM,IAAIA,MAAM,CAACtF,MAAM,KAAK,SAAS,CAAC,CAC7CuE,MAAM,CAAC,CAACC,GAAG,EAAEc,MAAM,KAAKd,GAAG,GAAGc,MAAM,CAACvF,gBAAgB,EAAE,CAAC,CAAC;UAE5D,oBACEtC,OAAA,CAACb,KAAK,CAAC8I,OAAO,CAACtJ,GAAG;YAAAoF,QAAA,gBAChB/D,OAAA,CAACb,KAAK,CAAC8I,OAAO,CAACC,IAAI;cAACC,KAAK,EAAE,CAAE;cAACC,OAAO,EAAE,CAAE;cAAArE,QAAA,eACvC/D,OAAA,CAACE,IAAI;gBAAC4D,MAAM;gBAAAC,QAAA,EAAC;cAAE;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACrBpE,OAAA,CAACb,KAAK,CAAC8I,OAAO,CAACC,IAAI;cAACC,KAAK,EAAE,CAAE;cAAApE,QAAA,eAC3B/D,OAAA,CAACE,IAAI;gBAAC4D,MAAM;gBAACQ,KAAK,EAAE;kBAAElB,KAAK,EAAE;gBAAO,CAAE;gBAAAW,QAAA,GAAC,MACpC,EAAC9C,eAAe,CAAC+C,OAAO,CAAC,CAAC,CAAC;cAAA;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACW,CAAC,eACrBpE,OAAA,CAACb,KAAK,CAAC8I,OAAO,CAACC,IAAI;cAACC,KAAK,EAAE,CAAE;cAACC,OAAO,EAAE,CAAE;cAAArE,QAAA,eACvC/D,OAAA,CAACd,KAAK;gBAAA6E,QAAA,gBACJ/D,OAAA,CAACE,IAAI;kBAAA6D,QAAA,GAAC,0BAAM,EAAC+D,iBAAiB,CAAC9D,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eACjDpE,OAAA,CAACE,IAAI;kBAAA6D,QAAA,GAAC,0BAAM,EAACiE,iBAAiB,CAAChE,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACU,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAExB;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC9D,EAAA,CAhZID,cAAwB;AAAAgI,EAAA,GAAxBhI,cAAwB;AAkZ9B,eAAeA,cAAc;AAAC,IAAAgI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}