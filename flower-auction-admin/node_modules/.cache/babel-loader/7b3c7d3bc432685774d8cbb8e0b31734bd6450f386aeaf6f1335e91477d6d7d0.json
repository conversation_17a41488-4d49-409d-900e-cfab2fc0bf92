{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/App.tsx\";\nimport React from 'react';\nimport { RouterProvider } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { store } from './store';\nimport { router } from './routes';\nimport AuthProvider from './components/AuthProvider';\nimport './App.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(ConfigProvider, {\n      locale: zhCN,\n      children: /*#__PURE__*/_jsxDEV(AuthProvider, {\n        children: /*#__PURE__*/_jsxDEV(RouterProvider, {\n          router: router\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 16,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 15,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 13,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "RouterProvider", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "zhCN", "store", "router", "<PERSON>th<PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "App", "children", "locale", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { RouterProvider } from 'react-router-dom';\nimport { Provider } from 'react-redux';\nimport { ConfigProvider } from 'antd';\nimport zhCN from 'antd/locale/zh_CN';\nimport { store } from './store';\nimport { router } from './routes';\nimport AuthProvider from './components/AuthProvider';\nimport './App.css';\n\nfunction App() {\n  return (\n    <Provider store={store}>\n      <ConfigProvider locale={zhCN}>\n        <AuthProvider>\n          <RouterProvider router={router} />\n        </AuthProvider>\n      </ConfigProvider>\n    </Provider>\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,cAAc,QAAQ,MAAM;AACrC,OAAOC,IAAI,MAAM,mBAAmB;AACpC,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,MAAM,QAAQ,UAAU;AACjC,OAAOC,YAAY,MAAM,2BAA2B;AACpD,OAAO,WAAW;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnB,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACP,QAAQ;IAACG,KAAK,EAAEA,KAAM;IAAAM,QAAA,eACrBF,OAAA,CAACN,cAAc;MAACS,MAAM,EAAER,IAAK;MAAAO,QAAA,eAC3BF,OAAA,CAACF,YAAY;QAAAI,QAAA,eACXF,OAAA,CAACR,cAAc;UAACK,MAAM,EAAEA;QAAO;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACT,CAAC;AAEf;AAACC,EAAA,GAVQP,GAAG;AAYZ,eAAeA,GAAG;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}