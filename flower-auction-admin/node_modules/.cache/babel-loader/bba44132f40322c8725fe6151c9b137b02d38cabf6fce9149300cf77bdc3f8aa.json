{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { setUser, clearUser, setLoading } from '../store/slices/authSlice';\nimport { authService } from '../services/authService';\nexport const useAuth = () => {\n  _s();\n  const dispatch = useDispatch();\n  const {\n    user,\n    isAuthenticated,\n    loading\n  } = useSelector(state => state.auth);\n\n  // 登录\n  const login = async credentials => {\n    try {\n      console.log('useAuth: 开始登录流程');\n      dispatch(setLoading(true));\n      const response = await authService.login(credentials);\n      console.log('useAuth: 登录服务响应', response);\n      if (response.success && response.data) {\n        // 保存token到localStorage\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        console.log('useAuth: Token已保存到localStorage');\n\n        // 直接使用登录响应中的用户信息\n        dispatch(setUser(response.data.user));\n        console.log('useAuth: 用户信息已设置到Redux', response.data.user);\n        return {\n          success: true\n        };\n      }\n      console.log('useAuth: 登录失败', response.message);\n      return {\n        success: false,\n        message: response.message || '登录失败'\n      };\n    } catch (error) {\n      console.error('useAuth: 登录异常', error);\n      return {\n        success: false,\n        message: error.message || '登录失败'\n      };\n    } finally {\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      await authService.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      // 清除Redux状态\n      dispatch(clearUser());\n    }\n  };\n\n  // 检查认证状态\n  const checkAuth = async () => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      dispatch(clearUser());\n      return false;\n    }\n    try {\n      dispatch(setLoading(true));\n      const response = await authService.getUserInfo();\n      if (response.success && response.data) {\n        dispatch(setUser(response.data));\n        return true;\n      } else {\n        // token无效，清除认证信息\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        dispatch(clearUser());\n        return false;\n      }\n    } catch (error) {\n      console.error('Check auth error:', error);\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      dispatch(clearUser());\n      return false;\n    } finally {\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 刷新token\n  const refreshToken = async () => {\n    const refreshTokenValue = localStorage.getItem('refreshToken');\n    if (!refreshTokenValue) {\n      return false;\n    }\n    try {\n      const response = await authService.refreshToken(refreshTokenValue);\n      if (response.success && response.data) {\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Refresh token error:', error);\n      return false;\n    }\n  };\n\n  // 初始化时检查认证状态\n  useEffect(() => {\n    checkAuth();\n  }, []);\n  return {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    checkAuth,\n    refreshToken\n  };\n};\n_s(useAuth, \"7cXZFt8FozMiF0hOcm5Ciy0qFqc=\", false, function () {\n  return [useDispatch, useSelector];\n});", "map": {"version": 3, "names": ["useEffect", "useDispatch", "useSelector", "setUser", "clearUser", "setLoading", "authService", "useAuth", "_s", "dispatch", "user", "isAuthenticated", "loading", "state", "auth", "login", "credentials", "console", "log", "response", "success", "data", "localStorage", "setItem", "token", "refreshToken", "message", "error", "logout", "removeItem", "checkAuth", "getItem", "getUserInfo", "refreshTokenValue"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/hooks/useAuth.ts"], "sourcesContent": ["import { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { RootState } from '../store';\nimport { setUser, clearUser, setLoading } from '../store/slices/authSlice';\nimport { authService } from '../services/authService';\n\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  role: string;\n  avatar?: string;\n  realName?: string;\n}\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport const useAuth = () => {\n  const dispatch = useDispatch();\n  const { user, isAuthenticated, loading } = useSelector((state: RootState) => state.auth);\n\n  // 登录\n  const login = async (credentials: LoginCredentials) => {\n    try {\n      console.log('useAuth: 开始登录流程');\n      dispatch(setLoading(true));\n      const response = await authService.login(credentials);\n      console.log('useAuth: 登录服务响应', response);\n\n      if (response.success && response.data) {\n        // 保存token到localStorage\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        console.log('useAuth: Token已保存到localStorage');\n\n        // 直接使用登录响应中的用户信息\n        dispatch(setUser(response.data.user));\n        console.log('useAuth: 用户信息已设置到Redux', response.data.user);\n\n        return { success: true };\n      }\n\n      console.log('useAuth: 登录失败', response.message);\n      return { success: false, message: response.message || '登录失败' };\n    } catch (error: any) {\n      console.error('useAuth: 登录异常', error);\n      return { success: false, message: error.message || '登录失败' };\n    } finally {\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 登出\n  const logout = async () => {\n    try {\n      await authService.logout();\n    } catch (error) {\n      console.error('Logout error:', error);\n    } finally {\n      // 清除本地存储\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      // 清除Redux状态\n      dispatch(clearUser());\n    }\n  };\n\n  // 检查认证状态\n  const checkAuth = async () => {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      dispatch(clearUser());\n      return false;\n    }\n\n    try {\n      dispatch(setLoading(true));\n      const response = await authService.getUserInfo();\n      if (response.success && response.data) {\n        dispatch(setUser(response.data));\n        return true;\n      } else {\n        // token无效，清除认证信息\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        dispatch(clearUser());\n        return false;\n      }\n    } catch (error) {\n      console.error('Check auth error:', error);\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      dispatch(clearUser());\n      return false;\n    } finally {\n      dispatch(setLoading(false));\n    }\n  };\n\n  // 刷新token\n  const refreshToken = async () => {\n    const refreshTokenValue = localStorage.getItem('refreshToken');\n    if (!refreshTokenValue) {\n      return false;\n    }\n\n    try {\n      const response = await authService.refreshToken(refreshTokenValue);\n      if (response.success && response.data) {\n        localStorage.setItem('token', response.data.token);\n        localStorage.setItem('refreshToken', response.data.refreshToken);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error('Refresh token error:', error);\n      return false;\n    }\n  };\n\n  // 初始化时检查认证状态\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  return {\n    user,\n    isAuthenticated,\n    loading,\n    login,\n    logout,\n    checkAuth,\n    refreshToken,\n  };\n};\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,WAAW,EAAEC,WAAW,QAAQ,aAAa;AAEtD,SAASC,OAAO,EAAEC,SAAS,EAAEC,UAAU,QAAQ,2BAA2B;AAC1E,SAASC,WAAW,QAAQ,yBAAyB;AAgBrD,OAAO,MAAMC,OAAO,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAMC,QAAQ,GAAGR,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAES,IAAI;IAAEC,eAAe;IAAEC;EAAQ,CAAC,GAAGV,WAAW,CAAEW,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;;EAExF;EACA,MAAMC,KAAK,GAAG,MAAOC,WAA6B,IAAK;IACrD,IAAI;MACFC,OAAO,CAACC,GAAG,CAAC,iBAAiB,CAAC;MAC9BT,QAAQ,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;MAC1B,MAAMc,QAAQ,GAAG,MAAMb,WAAW,CAACS,KAAK,CAACC,WAAW,CAAC;MACrDC,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAEC,QAAQ,CAAC;MAExC,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrC;QACAC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;QAClDF,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACE,IAAI,CAACI,YAAY,CAAC;QAChER,OAAO,CAACC,GAAG,CAAC,gCAAgC,CAAC;;QAE7C;QACAT,QAAQ,CAACN,OAAO,CAACgB,QAAQ,CAACE,IAAI,CAACX,IAAI,CAAC,CAAC;QACrCO,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEC,QAAQ,CAACE,IAAI,CAACX,IAAI,CAAC;QAEzD,OAAO;UAAEU,OAAO,EAAE;QAAK,CAAC;MAC1B;MAEAH,OAAO,CAACC,GAAG,CAAC,eAAe,EAAEC,QAAQ,CAACO,OAAO,CAAC;MAC9C,OAAO;QAAEN,OAAO,EAAE,KAAK;QAAEM,OAAO,EAAEP,QAAQ,CAACO,OAAO,IAAI;MAAO,CAAC;IAChE,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBV,OAAO,CAACU,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrC,OAAO;QAAEP,OAAO,EAAE,KAAK;QAAEM,OAAO,EAAEC,KAAK,CAACD,OAAO,IAAI;MAAO,CAAC;IAC7D,CAAC,SAAS;MACRjB,QAAQ,CAACJ,UAAU,CAAC,KAAK,CAAC,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMuB,MAAM,GAAG,MAAAA,CAAA,KAAY;IACzB,IAAI;MACF,MAAMtB,WAAW,CAACsB,MAAM,CAAC,CAAC;IAC5B,CAAC,CAAC,OAAOD,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;IACvC,CAAC,SAAS;MACR;MACAL,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;MAChCP,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;MACvC;MACApB,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;IACvB;EACF,CAAC;;EAED;EACA,MAAM0B,SAAS,GAAG,MAAAA,CAAA,KAAY;IAC5B,MAAMN,KAAK,GAAGF,YAAY,CAACS,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACP,KAAK,EAAE;MACVf,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrB,OAAO,KAAK;IACd;IAEA,IAAI;MACFK,QAAQ,CAACJ,UAAU,CAAC,IAAI,CAAC,CAAC;MAC1B,MAAMc,QAAQ,GAAG,MAAMb,WAAW,CAAC0B,WAAW,CAAC,CAAC;MAChD,IAAIb,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrCZ,QAAQ,CAACN,OAAO,CAACgB,QAAQ,CAACE,IAAI,CAAC,CAAC;QAChC,OAAO,IAAI;MACb,CAAC,MAAM;QACL;QACAC,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;QAChCP,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;QACvCpB,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;QACrB,OAAO,KAAK;MACd;IACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,mBAAmB,EAAEA,KAAK,CAAC;MACzCL,YAAY,CAACO,UAAU,CAAC,OAAO,CAAC;MAChCP,YAAY,CAACO,UAAU,CAAC,cAAc,CAAC;MACvCpB,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC;MACrB,OAAO,KAAK;IACd,CAAC,SAAS;MACRK,QAAQ,CAACJ,UAAU,CAAC,KAAK,CAAC,CAAC;IAC7B;EACF,CAAC;;EAED;EACA,MAAMoB,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,MAAMQ,iBAAiB,GAAGX,YAAY,CAACS,OAAO,CAAC,cAAc,CAAC;IAC9D,IAAI,CAACE,iBAAiB,EAAE;MACtB,OAAO,KAAK;IACd;IAEA,IAAI;MACF,MAAMd,QAAQ,GAAG,MAAMb,WAAW,CAACmB,YAAY,CAACQ,iBAAiB,CAAC;MAClE,IAAId,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,IAAI,EAAE;QACrCC,YAAY,CAACC,OAAO,CAAC,OAAO,EAAEJ,QAAQ,CAACE,IAAI,CAACG,KAAK,CAAC;QAClDF,YAAY,CAACC,OAAO,CAAC,cAAc,EAAEJ,QAAQ,CAACE,IAAI,CAACI,YAAY,CAAC;QAChE,OAAO,IAAI;MACb;MACA,OAAO,KAAK;IACd,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdV,OAAO,CAACU,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAC5C,OAAO,KAAK;IACd;EACF,CAAC;;EAED;EACA3B,SAAS,CAAC,MAAM;IACd8B,SAAS,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACLpB,IAAI;IACJC,eAAe;IACfC,OAAO;IACPG,KAAK;IACLa,MAAM;IACNE,SAAS;IACTL;EACF,CAAC;AACH,CAAC;AAACjB,EAAA,CArHWD,OAAO;EAAA,QACDN,WAAW,EACeC,WAAW;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}