{"ast": null, "code": "import { apiClient } from './apiClient';\nexport const authService = {\n  // 用户登录\n  login: async credentials => {\n    try {\n      const response = await apiClient.post('/auth/login', credentials);\n\n      // 后端返回的数据结构：{ success: true, data: { user: {...}, token: \"...\", refreshToken: \"...\" } }\n\n      if (response.data && response.data.success && response.data.data) {\n        const {\n          data\n        } = response.data;\n        return {\n          success: true,\n          data: {\n            token: data.token,\n            refreshToken: data.refreshToken,\n            user: {\n              id: data.user.id,\n              username: data.user.username,\n              email: data.user.email || '',\n              role: data.user.userType === 3 ? 'admin' : 'user',\n              realName: data.user.realName\n            }\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '登录响应数据格式错误'\n        };\n      }\n    } catch (error) {\n      console.error('Login error:', error);\n\n      // 处理不同类型的网络错误\n      let errorMessage = '登录失败';\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        errorMessage = '无法连接到服务器，请检查服务器是否启动';\n      } else if (error.code === 'TIMEOUT' || error.code === 'ECONNABORTED') {\n        errorMessage = '请求超时，请检查网络连接';\n      } else if (error.response) {\n        // 服务器响应了错误状态码\n        const status = error.response.status;\n        if (status >= 500) {\n          errorMessage = '服务器内部错误，请稍后重试';\n        } else if (status === 404) {\n          errorMessage = '登录接口不存在，请联系系统管理员';\n        } else if (status === 401) {\n          errorMessage = '用户名或密码错误';\n        } else {\n          var _error$response$data, _error$response$data2;\n          errorMessage = ((_error$response$data = error.response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || ((_error$response$data2 = error.response.data) === null || _error$response$data2 === void 0 ? void 0 : _error$response$data2.message) || `服务器错误 (${status})`;\n        }\n      } else if (error.request) {\n        // 请求已发出但没有收到响应\n        errorMessage = '服务器无响应，请检查网络连接或服务器状态';\n      } else {\n        // 其他错误\n        errorMessage = error.message || '未知错误';\n      }\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n  // 用户登出\n  logout: async () => {\n    try {\n      const response = await apiClient.post('/auth/logout');\n      return response.data;\n    } catch (error) {\n      var _error$response, _error$response$data3;\n      throw new Error(((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data3 = _error$response.data) === null || _error$response$data3 === void 0 ? void 0 : _error$response$data3.message) || '登出失败');\n    }\n  },\n  // 获取用户信息\n  getUserInfo: async () => {\n    try {\n      const response = await apiClient.get('/auth/me');\n\n      // 后端返回的数据结构需要转换\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          data: {\n            id: response.data.data.id,\n            username: response.data.data.username,\n            email: response.data.data.email || '',\n            role: response.data.data.role || 'user',\n            realName: response.data.data.realName\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '获取用户信息失败'\n        };\n      }\n    } catch (error) {\n      var _error$response2, _error$response2$data;\n      return {\n        success: false,\n        message: ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.error) || error.message || '获取用户信息失败'\n      };\n    }\n  },\n  // 刷新token\n  refreshToken: async refreshToken => {\n    try {\n      const response = await apiClient.post('/auth/refresh', {\n        refreshToken\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '刷新token失败');\n    }\n  },\n  // 修改密码\n  changePassword: async (oldPassword, newPassword) => {\n    try {\n      const response = await apiClient.post('/auth/change-password', {\n        oldPassword,\n        newPassword\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '修改密码失败');\n    }\n  }\n};", "map": {"version": 3, "names": ["apiClient", "authService", "login", "credentials", "response", "post", "data", "success", "token", "refreshToken", "user", "id", "username", "email", "role", "userType", "realName", "message", "error", "console", "errorMessage", "code", "status", "_error$response$data", "_error$response$data2", "request", "logout", "_error$response", "_error$response$data3", "Error", "getUserInfo", "get", "_error$response2", "_error$response2$data", "_error$response3", "_error$response3$data", "changePassword", "oldPassword", "newPassword", "_error$response4", "_error$response4$data"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/authService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\nimport { LoginCredentials, User } from '../hooks/useAuth';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n}\n\nexport interface LoginResponse {\n  token: string;\n  refreshToken: string;\n  user: User;\n}\n\nexport const authService = {\n  // 用户登录\n  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {\n    try {\n      const response = await apiClient.post('/auth/login', credentials);\n\n      // 后端返回的数据结构：{ success: true, data: { user: {...}, token: \"...\", refreshToken: \"...\" } }\n\n      if (response.data && response.data.success && response.data.data) {\n        const { data } = response.data;\n        return {\n          success: true,\n          data: {\n            token: data.token,\n            refreshToken: data.refreshToken,\n            user: {\n              id: data.user.id,\n              username: data.user.username,\n              email: data.user.email || '',\n              role: data.user.userType === 3 ? 'admin' : 'user',\n              realName: data.user.realName,\n            }\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '登录响应数据格式错误'\n        };\n      }\n    } catch (error: any) {\n      console.error('Login error:', error);\n\n      // 处理不同类型的网络错误\n      let errorMessage = '登录失败';\n\n      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {\n        errorMessage = '无法连接到服务器，请检查服务器是否启动';\n      } else if (error.code === 'TIMEOUT' || error.code === 'ECONNABORTED') {\n        errorMessage = '请求超时，请检查网络连接';\n      } else if (error.response) {\n        // 服务器响应了错误状态码\n        const status = error.response.status;\n        if (status >= 500) {\n          errorMessage = '服务器内部错误，请稍后重试';\n        } else if (status === 404) {\n          errorMessage = '登录接口不存在，请联系系统管理员';\n        } else if (status === 401) {\n          errorMessage = '用户名或密码错误';\n        } else {\n          errorMessage = error.response.data?.error || error.response.data?.message || `服务器错误 (${status})`;\n        }\n      } else if (error.request) {\n        // 请求已发出但没有收到响应\n        errorMessage = '服务器无响应，请检查网络连接或服务器状态';\n      } else {\n        // 其他错误\n        errorMessage = error.message || '未知错误';\n      }\n\n      return {\n        success: false,\n        message: errorMessage\n      };\n    }\n  },\n\n  // 用户登出\n  logout: async (): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.post('/auth/logout');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '登出失败');\n    }\n  },\n\n  // 获取用户信息\n  getUserInfo: async (): Promise<ApiResponse<User>> => {\n    try {\n      const response = await apiClient.get('/auth/me');\n\n      // 后端返回的数据结构需要转换\n      if (response.data && response.data.success) {\n        return {\n          success: true,\n          data: {\n            id: response.data.data.id,\n            username: response.data.data.username,\n            email: response.data.data.email || '',\n            role: response.data.data.role || 'user',\n            realName: response.data.data.realName,\n          }\n        };\n      } else {\n        return {\n          success: false,\n          message: '获取用户信息失败'\n        };\n      }\n    } catch (error: any) {\n      return {\n        success: false,\n        message: error.response?.data?.error || error.message || '获取用户信息失败'\n      };\n    }\n  },\n\n  // 刷新token\n  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {\n    try {\n      const response = await apiClient.post('/auth/refresh', { refreshToken });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '刷新token失败');\n    }\n  },\n\n  // 修改密码\n  changePassword: async (oldPassword: string, newPassword: string): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.post('/auth/change-password', {\n        oldPassword,\n        newPassword,\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '修改密码失败');\n    }\n  },\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAevC,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,KAAK,EAAE,MAAOC,WAA6B,IAA0C;IACnF,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,aAAa,EAAEF,WAAW,CAAC;;MAEjE;;MAEA,IAAIC,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,IAAIH,QAAQ,CAACE,IAAI,CAACA,IAAI,EAAE;QAChE,MAAM;UAAEA;QAAK,CAAC,GAAGF,QAAQ,CAACE,IAAI;QAC9B,OAAO;UACLC,OAAO,EAAE,IAAI;UACbD,IAAI,EAAE;YACJE,KAAK,EAAEF,IAAI,CAACE,KAAK;YACjBC,YAAY,EAAEH,IAAI,CAACG,YAAY;YAC/BC,IAAI,EAAE;cACJC,EAAE,EAAEL,IAAI,CAACI,IAAI,CAACC,EAAE;cAChBC,QAAQ,EAAEN,IAAI,CAACI,IAAI,CAACE,QAAQ;cAC5BC,KAAK,EAAEP,IAAI,CAACI,IAAI,CAACG,KAAK,IAAI,EAAE;cAC5BC,IAAI,EAAER,IAAI,CAACI,IAAI,CAACK,QAAQ,KAAK,CAAC,GAAG,OAAO,GAAG,MAAM;cACjDC,QAAQ,EAAEV,IAAI,CAACI,IAAI,CAACM;YACtB;UACF;QACF,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLT,OAAO,EAAE,KAAK;UACdU,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;;MAEpC;MACA,IAAIE,YAAY,GAAG,MAAM;MAEzB,IAAIF,KAAK,CAACG,IAAI,KAAK,cAAc,IAAIH,KAAK,CAACG,IAAI,KAAK,aAAa,EAAE;QACjED,YAAY,GAAG,qBAAqB;MACtC,CAAC,MAAM,IAAIF,KAAK,CAACG,IAAI,KAAK,SAAS,IAAIH,KAAK,CAACG,IAAI,KAAK,cAAc,EAAE;QACpED,YAAY,GAAG,cAAc;MAC/B,CAAC,MAAM,IAAIF,KAAK,CAACd,QAAQ,EAAE;QACzB;QACA,MAAMkB,MAAM,GAAGJ,KAAK,CAACd,QAAQ,CAACkB,MAAM;QACpC,IAAIA,MAAM,IAAI,GAAG,EAAE;UACjBF,YAAY,GAAG,eAAe;QAChC,CAAC,MAAM,IAAIE,MAAM,KAAK,GAAG,EAAE;UACzBF,YAAY,GAAG,kBAAkB;QACnC,CAAC,MAAM,IAAIE,MAAM,KAAK,GAAG,EAAE;UACzBF,YAAY,GAAG,UAAU;QAC3B,CAAC,MAAM;UAAA,IAAAG,oBAAA,EAAAC,qBAAA;UACLJ,YAAY,GAAG,EAAAG,oBAAA,GAAAL,KAAK,CAACd,QAAQ,CAACE,IAAI,cAAAiB,oBAAA,uBAAnBA,oBAAA,CAAqBL,KAAK,OAAAM,qBAAA,GAAIN,KAAK,CAACd,QAAQ,CAACE,IAAI,cAAAkB,qBAAA,uBAAnBA,qBAAA,CAAqBP,OAAO,KAAI,UAAUK,MAAM,GAAG;QAClG;MACF,CAAC,MAAM,IAAIJ,KAAK,CAACO,OAAO,EAAE;QACxB;QACAL,YAAY,GAAG,sBAAsB;MACvC,CAAC,MAAM;QACL;QACAA,YAAY,GAAGF,KAAK,CAACD,OAAO,IAAI,MAAM;MACxC;MAEA,OAAO;QACLV,OAAO,EAAE,KAAK;QACdU,OAAO,EAAEG;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAM,MAAM,EAAE,MAAAA,CAAA,KAAkC;IACxC,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,cAAc,CAAC;MACrD,OAAOD,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOY,KAAU,EAAE;MAAA,IAAAS,eAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIC,KAAK,CAAC,EAAAF,eAAA,GAAAT,KAAK,CAACd,QAAQ,cAAAuB,eAAA,wBAAAC,qBAAA,GAAdD,eAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBX,OAAO,KAAI,MAAM,CAAC;IAC1D;EACF,CAAC;EAED;EACAa,WAAW,EAAE,MAAAA,CAAA,KAAwC;IACnD,IAAI;MACF,MAAM1B,QAAQ,GAAG,MAAMJ,SAAS,CAAC+B,GAAG,CAAC,UAAU,CAAC;;MAEhD;MACA,IAAI3B,QAAQ,CAACE,IAAI,IAAIF,QAAQ,CAACE,IAAI,CAACC,OAAO,EAAE;QAC1C,OAAO;UACLA,OAAO,EAAE,IAAI;UACbD,IAAI,EAAE;YACJK,EAAE,EAAEP,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACK,EAAE;YACzBC,QAAQ,EAAER,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACM,QAAQ;YACrCC,KAAK,EAAET,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACO,KAAK,IAAI,EAAE;YACrCC,IAAI,EAAEV,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACQ,IAAI,IAAI,MAAM;YACvCE,QAAQ,EAAEZ,QAAQ,CAACE,IAAI,CAACA,IAAI,CAACU;UAC/B;QACF,CAAC;MACH,CAAC,MAAM;QACL,OAAO;UACLT,OAAO,EAAE,KAAK;UACdU,OAAO,EAAE;QACX,CAAC;MACH;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACnB,OAAO;QACL1B,OAAO,EAAE,KAAK;QACdU,OAAO,EAAE,EAAAe,gBAAA,GAAAd,KAAK,CAACd,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB1B,IAAI,cAAA2B,qBAAA,uBAApBA,qBAAA,CAAsBf,KAAK,KAAIA,KAAK,CAACD,OAAO,IAAI;MAC3D,CAAC;IACH;EACF,CAAC;EAED;EACAR,YAAY,EAAE,MAAOA,YAAoB,IAAoE;IAC3G,IAAI;MACF,MAAML,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,eAAe,EAAE;QAAEI;MAAa,CAAC,CAAC;MACxE,OAAOL,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOY,KAAU,EAAE;MAAA,IAAAgB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIN,KAAK,CAAC,EAAAK,gBAAA,GAAAhB,KAAK,CAACd,QAAQ,cAAA8B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB5B,IAAI,cAAA6B,qBAAA,uBAApBA,qBAAA,CAAsBlB,OAAO,KAAI,WAAW,CAAC;IAC/D;EACF,CAAC;EAED;EACAmB,cAAc,EAAE,MAAAA,CAAOC,WAAmB,EAAEC,WAAmB,KAA2B;IACxF,IAAI;MACF,MAAMlC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,IAAI,CAAC,uBAAuB,EAAE;QAC7DgC,WAAW;QACXC;MACF,CAAC,CAAC;MACF,OAAOlC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOY,KAAU,EAAE;MAAA,IAAAqB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIX,KAAK,CAAC,EAAAU,gBAAA,GAAArB,KAAK,CAACd,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBjC,IAAI,cAAAkC,qBAAA,uBAApBA,qBAAA,CAAsBvB,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}