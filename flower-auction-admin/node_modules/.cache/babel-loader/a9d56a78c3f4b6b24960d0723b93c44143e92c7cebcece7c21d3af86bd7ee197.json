{"ast": null, "code": "import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar any = function any(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n  }\n  callback(errors);\n};\nexport default any;", "map": {"version": 3, "names": ["rules", "isEmptyValue", "any", "rule", "value", "callback", "source", "options", "errors", "validate", "required", "hasOwnProperty", "field"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@rc-component/async-validator/es/validator/any.js"], "sourcesContent": ["import rules from \"../rule\";\nimport { isEmptyValue } from \"../util\";\nvar any = function any(rule, value, callback, source, options) {\n  var errors = [];\n  var validate = rule.required || !rule.required && source.hasOwnProperty(rule.field);\n  if (validate) {\n    if (isEmptyValue(value) && !rule.required) {\n      return callback();\n    }\n    rules.required(rule, value, source, errors, options);\n  }\n  callback(errors);\n};\nexport default any;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,SAASC,YAAY,QAAQ,SAAS;AACtC,IAAIC,GAAG,GAAG,SAASA,GAAGA,CAACC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC7D,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIC,QAAQ,GAAGN,IAAI,CAACO,QAAQ,IAAI,CAACP,IAAI,CAACO,QAAQ,IAAIJ,MAAM,CAACK,cAAc,CAACR,IAAI,CAACS,KAAK,CAAC;EACnF,IAAIH,QAAQ,EAAE;IACZ,IAAIR,YAAY,CAACG,KAAK,CAAC,IAAI,CAACD,IAAI,CAACO,QAAQ,EAAE;MACzC,OAAOL,QAAQ,CAAC,CAAC;IACnB;IACAL,KAAK,CAACU,QAAQ,CAACP,IAAI,EAAEC,KAAK,EAAEE,MAAM,EAAEE,MAAM,EAAED,OAAO,CAAC;EACtD;EACAF,QAAQ,CAACG,MAAM,CAAC;AAClB,CAAC;AACD,eAAeN,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}