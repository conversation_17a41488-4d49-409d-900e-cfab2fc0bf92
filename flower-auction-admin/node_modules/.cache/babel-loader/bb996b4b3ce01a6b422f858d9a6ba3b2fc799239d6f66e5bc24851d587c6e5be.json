{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport raf from \"rc-util/es/raf\";\nimport React, { useEffect, useRef, useState } from 'react';\nvar useIndicator = function useIndicator(options) {\n  var activeTabOffset = options.activeTabOffset,\n    horizontal = options.horizontal,\n    rtl = options.rtl,\n    _options$indicator = options.indicator,\n    indicator = _options$indicator === void 0 ? {} : _options$indicator;\n  var size = indicator.size,\n    _indicator$align = indicator.align,\n    align = _indicator$align === void 0 ? 'center' : _indicator$align;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    inkStyle = _useState2[0],\n    setInkStyle = _useState2[1];\n  var inkBarRafRef = useRef();\n  var getLength = React.useCallback(function (origin) {\n    if (typeof size === 'function') {\n      return size(origin);\n    }\n    if (typeof size === 'number') {\n      return size;\n    }\n    return origin;\n  }, [size]);\n\n  // Delay set ink style to avoid remove tab blink\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n  useEffect(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (horizontal) {\n        newInkStyle.width = getLength(activeTabOffset.width);\n        var key = rtl ? 'right' : 'left';\n        if (align === 'start') {\n          newInkStyle[key] = activeTabOffset[key];\n        }\n        if (align === 'center') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width / 2;\n          newInkStyle.transform = rtl ? 'translateX(50%)' : 'translateX(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width;\n          newInkStyle.transform = 'translateX(-100%)';\n        }\n      } else {\n        newInkStyle.height = getLength(activeTabOffset.height);\n        if (align === 'start') {\n          newInkStyle.top = activeTabOffset.top;\n        }\n        if (align === 'center') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height / 2;\n          newInkStyle.transform = 'translateY(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height;\n          newInkStyle.transform = 'translateY(-100%)';\n        }\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      // Avoid jitter caused by tiny numerical differences\n      // fix https://github.com/ant-design/ant-design/issues/53378\n      var isEqual = inkStyle && newInkStyle && Object.keys(newInkStyle).every(function (key) {\n        var newValue = newInkStyle[key];\n        var oldValue = inkStyle[key];\n        return typeof newValue === 'number' && typeof oldValue === 'number' ? Math.round(newValue) === Math.round(oldValue) : newValue === oldValue;\n      });\n      if (!isEqual) {\n        setInkStyle(newInkStyle);\n      }\n    });\n    return cleanInkBarRaf;\n  }, [JSON.stringify(activeTabOffset), horizontal, rtl, align, getLength]);\n  return {\n    style: inkStyle\n  };\n};\nexport default useIndicator;", "map": {"version": 3, "names": ["_slicedToArray", "raf", "React", "useEffect", "useRef", "useState", "useIndicator", "options", "activeTabOffset", "horizontal", "rtl", "_options$indicator", "indicator", "size", "_indicator$align", "align", "_useState", "_useState2", "inkStyle", "setInkStyle", "inkBarRafRef", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "origin", "cleanInkBarRaf", "cancel", "current", "newInkStyle", "width", "key", "transform", "height", "top", "isEqual", "Object", "keys", "every", "newValue", "oldValue", "Math", "round", "JSON", "stringify", "style"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/rc-tabs/es/hooks/useIndicator.js"], "sourcesContent": ["import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport raf from \"rc-util/es/raf\";\nimport React, { useEffect, useRef, useState } from 'react';\nvar useIndicator = function useIndicator(options) {\n  var activeTabOffset = options.activeTabOffset,\n    horizontal = options.horizontal,\n    rtl = options.rtl,\n    _options$indicator = options.indicator,\n    indicator = _options$indicator === void 0 ? {} : _options$indicator;\n  var size = indicator.size,\n    _indicator$align = indicator.align,\n    align = _indicator$align === void 0 ? 'center' : _indicator$align;\n  var _useState = useState(),\n    _useState2 = _slicedToArray(_useState, 2),\n    inkStyle = _useState2[0],\n    setInkStyle = _useState2[1];\n  var inkBarRafRef = useRef();\n  var getLength = React.useCallback(function (origin) {\n    if (typeof size === 'function') {\n      return size(origin);\n    }\n    if (typeof size === 'number') {\n      return size;\n    }\n    return origin;\n  }, [size]);\n\n  // Delay set ink style to avoid remove tab blink\n  function cleanInkBarRaf() {\n    raf.cancel(inkBarRafRef.current);\n  }\n  useEffect(function () {\n    var newInkStyle = {};\n    if (activeTabOffset) {\n      if (horizontal) {\n        newInkStyle.width = getLength(activeTabOffset.width);\n        var key = rtl ? 'right' : 'left';\n        if (align === 'start') {\n          newInkStyle[key] = activeTabOffset[key];\n        }\n        if (align === 'center') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width / 2;\n          newInkStyle.transform = rtl ? 'translateX(50%)' : 'translateX(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle[key] = activeTabOffset[key] + activeTabOffset.width;\n          newInkStyle.transform = 'translateX(-100%)';\n        }\n      } else {\n        newInkStyle.height = getLength(activeTabOffset.height);\n        if (align === 'start') {\n          newInkStyle.top = activeTabOffset.top;\n        }\n        if (align === 'center') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height / 2;\n          newInkStyle.transform = 'translateY(-50%)';\n        }\n        if (align === 'end') {\n          newInkStyle.top = activeTabOffset.top + activeTabOffset.height;\n          newInkStyle.transform = 'translateY(-100%)';\n        }\n      }\n    }\n    cleanInkBarRaf();\n    inkBarRafRef.current = raf(function () {\n      // Avoid jitter caused by tiny numerical differences\n      // fix https://github.com/ant-design/ant-design/issues/53378\n      var isEqual = inkStyle && newInkStyle && Object.keys(newInkStyle).every(function (key) {\n        var newValue = newInkStyle[key];\n        var oldValue = inkStyle[key];\n        return typeof newValue === 'number' && typeof oldValue === 'number' ? Math.round(newValue) === Math.round(oldValue) : newValue === oldValue;\n      });\n      if (!isEqual) {\n        setInkStyle(newInkStyle);\n      }\n    });\n    return cleanInkBarRaf;\n  }, [JSON.stringify(activeTabOffset), horizontal, rtl, align, getLength]);\n  return {\n    style: inkStyle\n  };\n};\nexport default useIndicator;"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,GAAG,MAAM,gBAAgB;AAChC,OAAOC,KAAK,IAAIC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,IAAIC,YAAY,GAAG,SAASA,YAAYA,CAACC,OAAO,EAAE;EAChD,IAAIC,eAAe,GAAGD,OAAO,CAACC,eAAe;IAC3CC,UAAU,GAAGF,OAAO,CAACE,UAAU;IAC/BC,GAAG,GAAGH,OAAO,CAACG,GAAG;IACjBC,kBAAkB,GAAGJ,OAAO,CAACK,SAAS;IACtCA,SAAS,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,kBAAkB;EACrE,IAAIE,IAAI,GAAGD,SAAS,CAACC,IAAI;IACvBC,gBAAgB,GAAGF,SAAS,CAACG,KAAK;IAClCA,KAAK,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,gBAAgB;EACnE,IAAIE,SAAS,GAAGX,QAAQ,CAAC,CAAC;IACxBY,UAAU,GAAGjB,cAAc,CAACgB,SAAS,EAAE,CAAC,CAAC;IACzCE,QAAQ,GAAGD,UAAU,CAAC,CAAC,CAAC;IACxBE,WAAW,GAAGF,UAAU,CAAC,CAAC,CAAC;EAC7B,IAAIG,YAAY,GAAGhB,MAAM,CAAC,CAAC;EAC3B,IAAIiB,SAAS,GAAGnB,KAAK,CAACoB,WAAW,CAAC,UAAUC,MAAM,EAAE;IAClD,IAAI,OAAOV,IAAI,KAAK,UAAU,EAAE;MAC9B,OAAOA,IAAI,CAACU,MAAM,CAAC;IACrB;IACA,IAAI,OAAOV,IAAI,KAAK,QAAQ,EAAE;MAC5B,OAAOA,IAAI;IACb;IACA,OAAOU,MAAM;EACf,CAAC,EAAE,CAACV,IAAI,CAAC,CAAC;;EAEV;EACA,SAASW,cAAcA,CAAA,EAAG;IACxBvB,GAAG,CAACwB,MAAM,CAACL,YAAY,CAACM,OAAO,CAAC;EAClC;EACAvB,SAAS,CAAC,YAAY;IACpB,IAAIwB,WAAW,GAAG,CAAC,CAAC;IACpB,IAAInB,eAAe,EAAE;MACnB,IAAIC,UAAU,EAAE;QACdkB,WAAW,CAACC,KAAK,GAAGP,SAAS,CAACb,eAAe,CAACoB,KAAK,CAAC;QACpD,IAAIC,GAAG,GAAGnB,GAAG,GAAG,OAAO,GAAG,MAAM;QAChC,IAAIK,KAAK,KAAK,OAAO,EAAE;UACrBY,WAAW,CAACE,GAAG,CAAC,GAAGrB,eAAe,CAACqB,GAAG,CAAC;QACzC;QACA,IAAId,KAAK,KAAK,QAAQ,EAAE;UACtBY,WAAW,CAACE,GAAG,CAAC,GAAGrB,eAAe,CAACqB,GAAG,CAAC,GAAGrB,eAAe,CAACoB,KAAK,GAAG,CAAC;UACnED,WAAW,CAACG,SAAS,GAAGpB,GAAG,GAAG,iBAAiB,GAAG,kBAAkB;QACtE;QACA,IAAIK,KAAK,KAAK,KAAK,EAAE;UACnBY,WAAW,CAACE,GAAG,CAAC,GAAGrB,eAAe,CAACqB,GAAG,CAAC,GAAGrB,eAAe,CAACoB,KAAK;UAC/DD,WAAW,CAACG,SAAS,GAAG,mBAAmB;QAC7C;MACF,CAAC,MAAM;QACLH,WAAW,CAACI,MAAM,GAAGV,SAAS,CAACb,eAAe,CAACuB,MAAM,CAAC;QACtD,IAAIhB,KAAK,KAAK,OAAO,EAAE;UACrBY,WAAW,CAACK,GAAG,GAAGxB,eAAe,CAACwB,GAAG;QACvC;QACA,IAAIjB,KAAK,KAAK,QAAQ,EAAE;UACtBY,WAAW,CAACK,GAAG,GAAGxB,eAAe,CAACwB,GAAG,GAAGxB,eAAe,CAACuB,MAAM,GAAG,CAAC;UAClEJ,WAAW,CAACG,SAAS,GAAG,kBAAkB;QAC5C;QACA,IAAIf,KAAK,KAAK,KAAK,EAAE;UACnBY,WAAW,CAACK,GAAG,GAAGxB,eAAe,CAACwB,GAAG,GAAGxB,eAAe,CAACuB,MAAM;UAC9DJ,WAAW,CAACG,SAAS,GAAG,mBAAmB;QAC7C;MACF;IACF;IACAN,cAAc,CAAC,CAAC;IAChBJ,YAAY,CAACM,OAAO,GAAGzB,GAAG,CAAC,YAAY;MACrC;MACA;MACA,IAAIgC,OAAO,GAAGf,QAAQ,IAAIS,WAAW,IAAIO,MAAM,CAACC,IAAI,CAACR,WAAW,CAAC,CAACS,KAAK,CAAC,UAAUP,GAAG,EAAE;QACrF,IAAIQ,QAAQ,GAAGV,WAAW,CAACE,GAAG,CAAC;QAC/B,IAAIS,QAAQ,GAAGpB,QAAQ,CAACW,GAAG,CAAC;QAC5B,OAAO,OAAOQ,QAAQ,KAAK,QAAQ,IAAI,OAAOC,QAAQ,KAAK,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC,KAAKE,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC,GAAGD,QAAQ,KAAKC,QAAQ;MAC7I,CAAC,CAAC;MACF,IAAI,CAACL,OAAO,EAAE;QACZd,WAAW,CAACQ,WAAW,CAAC;MAC1B;IACF,CAAC,CAAC;IACF,OAAOH,cAAc;EACvB,CAAC,EAAE,CAACiB,IAAI,CAACC,SAAS,CAAClC,eAAe,CAAC,EAAEC,UAAU,EAAEC,GAAG,EAAEK,KAAK,EAAEM,SAAS,CAAC,CAAC;EACxE,OAAO;IACLsB,KAAK,EAAEzB;EACT,CAAC;AACH,CAAC;AACD,eAAeZ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}