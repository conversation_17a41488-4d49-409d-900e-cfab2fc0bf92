{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/layouts/MainLayout/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Layout, ConfigProvider, Spin } from 'antd';\nimport { Outlet, useNavigate } from 'react-router-dom';\nimport SideMenu from '../../components/SideMenu';\nimport HeaderComponent from '../../components/Header';\nimport BreadcrumbComponent from '../../components/Breadcrumb';\nimport { useAuth } from '../../hooks/useAuth';\nimport zhCN from 'antd/lib/locale/zh_CN';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Content\n} = Layout;\nconst MainLayout = () => {\n  _s();\n  const [collapsed, setCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\n  const {\n    isAuthenticated,\n    user,\n    loading\n  } = useAuth();\n  const navigate = useNavigate();\n\n  // 检查认证状态\n  useEffect(() => {\n    // 如果不在加载中且未认证，跳转到登录页\n    if (!loading && !isAuthenticated) {\n      navigate('/login', {\n        replace: true\n      });\n    }\n  }, [isAuthenticated, loading, navigate]);\n\n  // 监听窗口大小变化\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n      if (window.innerWidth < 768) {\n        setCollapsed(true);\n      }\n    };\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n  const toggleCollapsed = () => {\n    setCollapsed(!collapsed);\n  };\n\n  // 如果正在加载认证状态，显示加载页面\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        flexDirection: 'column',\n        gap: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Spin, {\n        size: \"large\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          color: '#666'\n        },\n        children: \"\\u6B63\\u5728\\u9A8C\\u8BC1\\u767B\\u5F55\\u72B6\\u6001...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 47,\n      columnNumber: 7\n    }, this);\n  }\n\n  // 如果未认证，不渲染主布局（会被useEffect重定向到登录页）\n  if (!isAuthenticated) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(ConfigProvider, {\n    locale: zhCN,\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      className: \"main-layout\",\n      children: [/*#__PURE__*/_jsxDEV(SideMenu, {\n        collapsed: collapsed,\n        onCollapse: setCollapsed\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        className: `site-layout ${collapsed ? 'collapsed' : ''}`,\n        children: [/*#__PURE__*/_jsxDEV(HeaderComponent, {\n          collapsed: collapsed,\n          toggle: toggleCollapsed\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Content, {\n          className: \"site-layout-content\",\n          children: [/*#__PURE__*/_jsxDEV(BreadcrumbComponent, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 73,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"content-wrapper\",\n            children: /*#__PURE__*/_jsxDEV(Outlet, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 72,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 68,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 5\n  }, this);\n};\n_s(MainLayout, \"mhRQPXS1k918xe81JeXTDaD6rBQ=\", false, function () {\n  return [useAuth, useNavigate];\n});\n_c = MainLayout;\nexport default MainLayout;\nvar _c;\n$RefreshReg$(_c, \"MainLayout\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Layout", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Spin", "Outlet", "useNavigate", "SideMenu", "HeaderComponent", "BreadcrumbComponent", "useAuth", "zhCN", "jsxDEV", "_jsxDEV", "Content", "MainLayout", "_s", "collapsed", "setCollapsed", "isMobile", "setIsMobile", "window", "innerWidth", "isAuthenticated", "user", "loading", "navigate", "replace", "handleResize", "addEventListener", "removeEventListener", "toggleCollapsed", "style", "display", "justifyContent", "alignItems", "height", "flexDirection", "gap", "children", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "locale", "className", "onCollapse", "toggle", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/layouts/MainLayout/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Layout, ConfigProvider, Spin } from 'antd';\nimport { Outlet, useNavigate } from 'react-router-dom';\nimport SideMenu from '../../components/SideMenu';\nimport HeaderComponent from '../../components/Header';\nimport BreadcrumbComponent from '../../components/Breadcrumb';\nimport { useAuth } from '../../hooks/useAuth';\nimport zhCN from 'antd/lib/locale/zh_CN';\nimport './index.css';\n\nconst { Content } = Layout;\n\nconst MainLayout: React.FC = () => {\n  const [collapsed, setCollapsed] = useState(false);\n  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);\n  const { isAuthenticated, user, loading } = useAuth();\n  const navigate = useNavigate();\n\n  // 检查认证状态\n  useEffect(() => {\n    // 如果不在加载中且未认证，跳转到登录页\n    if (!loading && !isAuthenticated) {\n      navigate('/login', { replace: true });\n    }\n  }, [isAuthenticated, loading, navigate]);\n\n  // 监听窗口大小变化\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth < 768);\n      if (window.innerWidth < 768) {\n        setCollapsed(true);\n      }\n    };\n\n    window.addEventListener('resize', handleResize);\n    return () => window.removeEventListener('resize', handleResize);\n  }, []);\n\n  const toggleCollapsed = () => {\n    setCollapsed(!collapsed);\n  };\n\n  // 如果正在加载认证状态，显示加载页面\n  if (loading) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        flexDirection: 'column',\n        gap: '16px'\n      }}>\n        <Spin size=\"large\" />\n        <div style={{ color: '#666' }}>正在验证登录状态...</div>\n      </div>\n    );\n  }\n\n  // 如果未认证，不渲染主布局（会被useEffect重定向到登录页）\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <ConfigProvider locale={zhCN}>\n      <Layout className=\"main-layout\">\n        <SideMenu collapsed={collapsed} onCollapse={setCollapsed} />\n        <Layout className={`site-layout ${collapsed ? 'collapsed' : ''}`}>\n          <HeaderComponent collapsed={collapsed} toggle={toggleCollapsed} />\n          <Content className=\"site-layout-content\">\n            <BreadcrumbComponent />\n            <div className=\"content-wrapper\">\n              <Outlet />\n            </div>\n          </Content>\n        </Layout>\n      </Layout>\n    </ConfigProvider>\n  );\n};\n\nexport default MainLayout;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,MAAM,EAAEC,cAAc,EAAEC,IAAI,QAAQ,MAAM;AACnD,SAASC,MAAM,EAAEC,WAAW,QAAQ,kBAAkB;AACtD,OAAOC,QAAQ,MAAM,2BAA2B;AAChD,OAAOC,eAAe,MAAM,yBAAyB;AACrD,OAAOC,mBAAmB,MAAM,6BAA6B;AAC7D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAOC,IAAI,MAAM,uBAAuB;AACxC,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAQ,CAAC,GAAGZ,MAAM;AAE1B,MAAMa,UAAoB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACjC,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACmB,QAAQ,EAAEC,WAAW,CAAC,GAAGpB,QAAQ,CAACqB,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;EACjE,MAAM;IAAEC,eAAe;IAAEC,IAAI;IAAEC;EAAQ,CAAC,GAAGf,OAAO,CAAC,CAAC;EACpD,MAAMgB,QAAQ,GAAGpB,WAAW,CAAC,CAAC;;EAE9B;EACAL,SAAS,CAAC,MAAM;IACd;IACA,IAAI,CAACwB,OAAO,IAAI,CAACF,eAAe,EAAE;MAChCG,QAAQ,CAAC,QAAQ,EAAE;QAAEC,OAAO,EAAE;MAAK,CAAC,CAAC;IACvC;EACF,CAAC,EAAE,CAACJ,eAAe,EAAEE,OAAO,EAAEC,QAAQ,CAAC,CAAC;;EAExC;EACAzB,SAAS,CAAC,MAAM;IACd,MAAM2B,YAAY,GAAGA,CAAA,KAAM;MACzBR,WAAW,CAACC,MAAM,CAACC,UAAU,GAAG,GAAG,CAAC;MACpC,IAAID,MAAM,CAACC,UAAU,GAAG,GAAG,EAAE;QAC3BJ,YAAY,CAAC,IAAI,CAAC;MACpB;IACF,CAAC;IAEDG,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAC/C,OAAO,MAAMP,MAAM,CAACS,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;EACjE,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,eAAe,GAAGA,CAAA,KAAM;IAC5Bb,YAAY,CAAC,CAACD,SAAS,CAAC;EAC1B,CAAC;;EAED;EACA,IAAIQ,OAAO,EAAE;IACX,oBACEZ,OAAA;MAAKmB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE;MACP,CAAE;MAAAC,QAAA,gBACA1B,OAAA,CAACT,IAAI;QAACoC,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACrB/B,OAAA;QAAKmB,KAAK,EAAE;UAAEa,KAAK,EAAE;QAAO,CAAE;QAAAN,QAAA,EAAC;MAAW;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC7C,CAAC;EAEV;;EAEA;EACA,IAAI,CAACrB,eAAe,EAAE;IACpB,OAAO,IAAI;EACb;EAEA,oBACEV,OAAA,CAACV,cAAc;IAAC2C,MAAM,EAAEnC,IAAK;IAAA4B,QAAA,eAC3B1B,OAAA,CAACX,MAAM;MAAC6C,SAAS,EAAC,aAAa;MAAAR,QAAA,gBAC7B1B,OAAA,CAACN,QAAQ;QAACU,SAAS,EAAEA,SAAU;QAAC+B,UAAU,EAAE9B;MAAa;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5D/B,OAAA,CAACX,MAAM;QAAC6C,SAAS,EAAE,eAAe9B,SAAS,GAAG,WAAW,GAAG,EAAE,EAAG;QAAAsB,QAAA,gBAC/D1B,OAAA,CAACL,eAAe;UAACS,SAAS,EAAEA,SAAU;UAACgC,MAAM,EAAElB;QAAgB;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAClE/B,OAAA,CAACC,OAAO;UAACiC,SAAS,EAAC,qBAAqB;UAAAR,QAAA,gBACtC1B,OAAA,CAACJ,mBAAmB;YAAAgC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eACvB/B,OAAA;YAAKkC,SAAS,EAAC,iBAAiB;YAAAR,QAAA,eAC9B1B,OAAA,CAACR,MAAM;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAErB,CAAC;AAAC5B,EAAA,CArEID,UAAoB;EAAA,QAGmBL,OAAO,EACjCJ,WAAW;AAAA;AAAA4C,EAAA,GAJxBnC,UAAoB;AAuE1B,eAAeA,UAAU;AAAC,IAAAmC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}