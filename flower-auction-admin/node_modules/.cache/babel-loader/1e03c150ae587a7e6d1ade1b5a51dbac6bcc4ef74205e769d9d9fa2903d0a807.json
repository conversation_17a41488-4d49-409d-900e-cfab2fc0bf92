{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Card, Row, Col, Button, Input, message, Typography, Space, Tag, Avatar, List, Statistic, Progress, Badge, Modal, Form, InputNumber, Alert } from 'antd';\nimport { PlayCircleOutlined, PauseCircleOutlined, StopOutlined, AuditOutlined, UserOutlined, ClockCircleOutlined, TrophyOutlined } from '@ant-design/icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text\n} = Typography;\nconst {\n  TextArea\n} = Input;\n\n// 竞价记录接口\n\n// 拍卖商品接口\n\nconst LiveBidding = () => {\n  _s();\n  const [currentItem, setCurrentItem] = useState(null);\n  const [bidRecords, setBidRecords] = useState([]);\n  const [isAuctionActive, setIsAuctionActive] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState(0);\n  const [bidAmount, setBidAmount] = useState(0);\n  const [isBidModalVisible, setIsBidModalVisible] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState(0);\n  const [form] = Form.useForm();\n  const timerRef = useRef(null);\n  const wsRef = useRef(null);\n\n  // 模拟拍卖商品数据\n  const mockAuctionItem = {\n    id: 1,\n    productName: '荷兰郁金香 - 红色经典',\n    productCode: 'TLP-001',\n    images: ['https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=400', 'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400'],\n    startingPrice: 100.00,\n    currentPrice: 350.00,\n    reservePrice: 500.00,\n    bidIncrement: 10.00,\n    bidCount: 15,\n    timeRemaining: 300,\n    // 5分钟\n    status: 'ongoing',\n    highestBidder: 'user123',\n    description: '来自荷兰的优质郁金香，颜色鲜艳，品质上乘。适合园艺爱好者和花卉收藏家。'\n  };\n\n  // 模拟竞价记录\n  const mockBidRecords = [{\n    id: 1,\n    userId: 1,\n    username: 'user123',\n    bidAmount: 350.00,\n    bidTime: new Date().toISOString(),\n    isWinning: true\n  }, {\n    id: 2,\n    userId: 2,\n    username: 'flower_lover',\n    bidAmount: 340.00,\n    bidTime: new Date(Date.now() - 30000).toISOString(),\n    isWinning: false\n  }, {\n    id: 3,\n    userId: 3,\n    username: 'garden_master',\n    bidAmount: 330.00,\n    bidTime: new Date(Date.now() - 60000).toISOString(),\n    isWinning: false\n  }];\n\n  // 初始化数据\n  useEffect(() => {\n    setCurrentItem(mockAuctionItem);\n    setBidRecords(mockBidRecords);\n    setTimeRemaining(mockAuctionItem.timeRemaining);\n    setOnlineUsers(Math.floor(Math.random() * 50) + 10);\n\n    // 设置下一次出价金额\n    setBidAmount(mockAuctionItem.currentPrice + mockAuctionItem.bidIncrement);\n  }, []);\n\n  // 倒计时器\n  useEffect(() => {\n    if (isAuctionActive && timeRemaining > 0) {\n      timerRef.current = setInterval(() => {\n        setTimeRemaining(prev => {\n          if (prev <= 1) {\n            setIsAuctionActive(false);\n            message.info('拍卖时间结束');\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n    } else {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    }\n    return () => {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    };\n  }, [isAuctionActive, timeRemaining]);\n\n  // WebSocket连接（模拟）\n  useEffect(() => {\n    // 这里应该建立WebSocket连接来接收实时竞价信息\n    // wsRef.current = new WebSocket('ws://localhost:8081/ws/auction');\n\n    return () => {\n      if (wsRef.current) {\n        wsRef.current.close();\n      }\n    };\n  }, []);\n\n  // 格式化时间\n  const formatTime = seconds => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 开始拍卖\n  const handleStartAuction = () => {\n    setIsAuctionActive(true);\n    message.success('拍卖已开始');\n  };\n\n  // 暂停拍卖\n  const handlePauseAuction = () => {\n    setIsAuctionActive(false);\n    message.info('拍卖已暂停');\n  };\n\n  // 结束拍卖\n  const handleEndAuction = () => {\n    setIsAuctionActive(false);\n    setTimeRemaining(0);\n    message.success('拍卖已结束');\n  };\n\n  // 出价\n  const handleBid = () => {\n    setIsBidModalVisible(true);\n    form.setFieldsValue({\n      bidAmount\n    });\n  };\n\n  // 确认出价\n  const handleConfirmBid = async values => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API提交出价\n      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用\n\n      const newBid = {\n        id: Date.now(),\n        userId: 999,\n        username: 'admin',\n        bidAmount: values.bidAmount,\n        bidTime: new Date().toISOString(),\n        isWinning: true\n      };\n\n      // 更新竞价记录\n      setBidRecords(prev => {\n        const updated = prev.map(record => ({\n          ...record,\n          isWinning: false\n        }));\n        return [newBid, ...updated];\n      });\n\n      // 更新当前价格\n      if (currentItem) {\n        setCurrentItem(prev => prev ? {\n          ...prev,\n          currentPrice: values.bidAmount,\n          bidCount: prev.bidCount + 1,\n          highestBidder: 'admin'\n        } : null);\n      }\n\n      // 设置下一次出价金额\n      setBidAmount(values.bidAmount + ((currentItem === null || currentItem === void 0 ? void 0 : currentItem.bidIncrement) || 10));\n      message.success('出价成功');\n      setIsBidModalVisible(false);\n    } catch (error) {\n      message.error(error.message || '出价失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  if (!currentItem) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: 24,\n        textAlign: 'center'\n      },\n      children: /*#__PURE__*/_jsxDEV(Title, {\n        level: 3,\n        children: \"\\u6682\\u65E0\\u8FDB\\u884C\\u4E2D\\u7684\\u62CD\\u5356\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 256,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 255,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u5B9E\\u65F6\\u7ADE\\u4EF7\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 263,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 24,\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 16,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              md: 8,\n              children: /*#__PURE__*/_jsxDEV(\"img\", {\n                src: currentItem.images[0],\n                alt: currentItem.productName,\n                style: {\n                  width: '100%',\n                  height: 200,\n                  objectFit: 'cover',\n                  borderRadius: 8\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 272,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 271,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              md: 16,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                size: \"small\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Title, {\n                  level: 3,\n                  style: {\n                    margin: 0\n                  },\n                  children: currentItem.productName\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u5546\\u54C1\\u7F16\\u53F7: \", currentItem.productCode]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  children: currentItem.description\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 289,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Row, {\n                  gutter: 16,\n                  children: [/*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Statistic, {\n                      title: \"\\u8D77\\u62CD\\u4EF7\",\n                      value: currentItem.startingPrice,\n                      precision: 2,\n                      prefix: \"\\xA5\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 293,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 292,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Statistic, {\n                      title: \"\\u5F53\\u524D\\u4EF7\\u683C\",\n                      value: currentItem.currentPrice,\n                      precision: 2,\n                      prefix: \"\\xA5\",\n                      valueStyle: {\n                        color: '#f50',\n                        fontSize: 24,\n                        fontWeight: 'bold'\n                      }\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 301,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 300,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(Col, {\n                    span: 8,\n                    children: /*#__PURE__*/_jsxDEV(Statistic, {\n                      title: \"\\u51FA\\u4EF7\\u6B21\\u6570\",\n                      value: currentItem.bidCount,\n                      suffix: \"\\u6B21\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 310,\n                      columnNumber: 23\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 309,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 291,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 284,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 283,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 270,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u62CD\\u5356\\u63A7\\u5236\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            align: \"middle\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: /*#__PURE__*/_jsxDEV(Badge, {\n                  status: isAuctionActive ? 'processing' : 'default',\n                  text: /*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    children: [\"\\u72B6\\u6001: \", isAuctionActive ? '进行中' : '已暂停']\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 326,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: [/*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5269\\u4F59\\u65F6\\u95F4\",\n                value: formatTime(timeRemaining),\n                prefix: /*#__PURE__*/_jsxDEV(ClockCircleOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 341,\n                  columnNumber: 27\n                }, this),\n                valueStyle: {\n                  color: timeRemaining < 60 ? '#f50' : '#1890ff',\n                  fontSize: 20,\n                  fontWeight: 'bold'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 338,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Progress, {\n                percent: Math.max(0, timeRemaining / 300 * 100),\n                showInfo: false,\n                strokeColor: timeRemaining < 60 ? '#f50' : '#1890ff'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [!isAuctionActive ? /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  icon: /*#__PURE__*/_jsxDEV(PlayCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 359,\n                    columnNumber: 29\n                  }, this),\n                  onClick: handleStartAuction,\n                  disabled: timeRemaining === 0,\n                  children: \"\\u5F00\\u59CB\\u62CD\\u5356\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 357,\n                  columnNumber: 21\n                }, this) : /*#__PURE__*/_jsxDEV(Button, {\n                  icon: /*#__PURE__*/_jsxDEV(PauseCircleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 367,\n                    columnNumber: 29\n                  }, this),\n                  onClick: handlePauseAuction,\n                  children: \"\\u6682\\u505C\\u62CD\\u5356\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 366,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  danger: true,\n                  icon: /*#__PURE__*/_jsxDEV(StopOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 375,\n                    columnNumber: 27\n                  }, this),\n                  onClick: handleEndAuction,\n                  children: \"\\u7ED3\\u675F\\u62CD\\u5356\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 373,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 355,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 354,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 324,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 323,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u5FEB\\u901F\\u51FA\\u4EF7\",\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            align: \"middle\",\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                direction: \"vertical\",\n                style: {\n                  width: '100%'\n                },\n                children: [/*#__PURE__*/_jsxDEV(Text, {\n                  children: [\"\\u5EFA\\u8BAE\\u51FA\\u4EF7: \\xA5\", bidAmount.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 390,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"secondary\",\n                  children: [\"\\u52A0\\u4EF7\\u5E45\\u5EA6: \\xA5\", currentItem.bidIncrement.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 391,\n                  columnNumber: 19\n                }, this), currentItem.reservePrice && /*#__PURE__*/_jsxDEV(Text, {\n                  type: \"warning\",\n                  children: [\"\\u4FDD\\u7559\\u4EF7: \\xA5\", currentItem.reservePrice.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 389,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 388,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 12,\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  size: \"large\",\n                  icon: /*#__PURE__*/_jsxDEV(AuditOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 406,\n                    columnNumber: 27\n                  }, this),\n                  onClick: handleBid,\n                  disabled: !isAuctionActive || timeRemaining === 0,\n                  children: [\"\\u51FA\\u4EF7 \\xA5\", bidAmount.toFixed(2)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 403,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 402,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 401,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 387,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 386,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 267,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        lg: 8,\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          size: \"small\",\n          style: {\n            marginBottom: 16\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: 16,\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u5728\\u7EBF\\u7528\\u6237\",\n                value: onlineUsers,\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 427,\n                  columnNumber: 27\n                }, this),\n                valueStyle: {\n                  color: '#52c41a'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 424,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 423,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              span: 12,\n              children: /*#__PURE__*/_jsxDEV(Statistic, {\n                title: \"\\u6700\\u9AD8\\u51FA\\u4EF7\\u4EBA\",\n                value: currentItem.highestBidder || '暂无',\n                prefix: /*#__PURE__*/_jsxDEV(TrophyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 435,\n                  columnNumber: 27\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 432,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 431,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 422,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 421,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u7ADE\\u4EF7\\u8BB0\\u5F55\",\n          size: \"small\",\n          children: /*#__PURE__*/_jsxDEV(List, {\n            dataSource: bidRecords,\n            renderItem: record => /*#__PURE__*/_jsxDEV(List.Item, {\n              style: {\n                padding: '8px 0',\n                backgroundColor: record.isWinning ? '#f6ffed' : 'transparent',\n                borderRadius: record.isWinning ? 4 : 0,\n                paddingLeft: record.isWinning ? 8 : 0\n              },\n              children: /*#__PURE__*/_jsxDEV(List.Item.Meta, {\n                avatar: /*#__PURE__*/_jsxDEV(Avatar, {\n                  src: record.avatar,\n                  icon: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 458,\n                    columnNumber: 31\n                  }, this),\n                  style: {\n                    backgroundColor: record.isWinning ? '#52c41a' : '#1890ff'\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 23\n                }, this),\n                title: /*#__PURE__*/_jsxDEV(Space, {\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: record.isWinning,\n                    children: record.username\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 466,\n                    columnNumber: 25\n                  }, this), record.isWinning && /*#__PURE__*/_jsxDEV(Tag, {\n                    color: \"green\",\n                    children: \"\\u6700\\u9AD8\\u4EF7\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 470,\n                    columnNumber: 27\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 465,\n                  columnNumber: 23\n                }, this),\n                description: /*#__PURE__*/_jsxDEV(Space, {\n                  direction: \"vertical\",\n                  size: 0,\n                  children: [/*#__PURE__*/_jsxDEV(Text, {\n                    strong: true,\n                    style: {\n                      color: '#f50'\n                    },\n                    children: [\"\\xA5\", record.bidAmount.toFixed(2)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 478,\n                    columnNumber: 25\n                  }, this), /*#__PURE__*/_jsxDEV(Text, {\n                    type: \"secondary\",\n                    style: {\n                      fontSize: 12\n                    },\n                    children: new Date(record.bidTime).toLocaleTimeString()\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 481,\n                    columnNumber: 25\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 477,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 454,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 446,\n              columnNumber: 17\n            }, this),\n            style: {\n              maxHeight: 400,\n              overflowY: 'auto'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 442,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 419,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 265,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: \"\\u786E\\u8BA4\\u51FA\\u4EF7\",\n      open: isBidModalVisible,\n      onCancel: () => setIsBidModalVisible(false),\n      footer: null,\n      width: 400,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleConfirmBid,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Alert, {\n          message: \"\\u51FA\\u4EF7\\u63D0\\u9192\",\n          description: `当前最高价: ¥${currentItem.currentPrice.toFixed(2)}，最小加价幅度: ¥${currentItem.bidIncrement.toFixed(2)}`,\n          type: \"info\",\n          showIcon: true,\n          style: {\n            marginBottom: 16\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"bidAmount\",\n          label: \"\\u51FA\\u4EF7\\u91D1\\u989D\",\n          rules: [{\n            required: true,\n            message: '请输入出价金额'\n          }, {\n            type: 'number',\n            min: currentItem.currentPrice + currentItem.bidIncrement,\n            message: `出价必须高于当前价格 ¥${(currentItem.currentPrice + currentItem.bidIncrement).toFixed(2)}`\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            style: {\n              width: '100%'\n            },\n            precision: 2,\n            min: currentItem.currentPrice + currentItem.bidIncrement,\n            step: currentItem.bidIncrement,\n            addonBefore: \"\\xA5\",\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u51FA\\u4EF7\\u91D1\\u989D\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 529,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsBidModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 541,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: loading,\n              children: \"\\u786E\\u8BA4\\u51FA\\u4EF7\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 544,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 540,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 539,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 496,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 262,\n    columnNumber: 5\n  }, this);\n};\n_s(LiveBidding, \"0oubRgTOsamiUR01CZqRswCWAIU=\", false, function () {\n  return [Form.useForm];\n});\n_c = LiveBidding;\nexport default LiveBidding;\nvar _c;\n$RefreshReg$(_c, \"LiveBidding\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Card", "Row", "Col", "<PERSON><PERSON>", "Input", "message", "Typography", "Space", "Tag", "Avatar", "List", "Statistic", "Progress", "Badge", "Modal", "Form", "InputNumber", "<PERSON><PERSON>", "PlayCircleOutlined", "PauseCircleOutlined", "StopOutlined", "AuditOutlined", "UserOutlined", "ClockCircleOutlined", "TrophyOutlined", "jsxDEV", "_jsxDEV", "Title", "Text", "TextArea", "LiveBidding", "_s", "currentItem", "setCurrentItem", "bidRecords", "setBidRecords", "isAuctionActive", "setIsAuctionActive", "timeRemaining", "setTimeRemaining", "bidAmount", "setBidAmount", "isBidModalVisible", "setIsBidModalVisible", "loading", "setLoading", "onlineUsers", "setOnlineUsers", "form", "useForm", "timerRef", "wsRef", "mockAuctionItem", "id", "productName", "productCode", "images", "startingPrice", "currentPrice", "reservePrice", "bidIncrement", "bidCount", "status", "highestBidder", "description", "mockBidRecords", "userId", "username", "bidTime", "Date", "toISOString", "isWinning", "now", "Math", "floor", "random", "current", "setInterval", "prev", "info", "clearInterval", "close", "formatTime", "seconds", "mins", "secs", "toString", "padStart", "handleStartAuction", "success", "handlePauseAuction", "handleEndAuction", "handleBid", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleConfirmBid", "values", "Promise", "resolve", "setTimeout", "newBid", "updated", "map", "record", "error", "style", "padding", "textAlign", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "xs", "lg", "marginBottom", "md", "src", "alt", "width", "height", "objectFit", "borderRadius", "direction", "size", "margin", "type", "span", "title", "value", "precision", "prefix", "valueStyle", "color", "fontSize", "fontWeight", "suffix", "align", "sm", "text", "strong", "percent", "max", "showInfo", "strokeColor", "icon", "onClick", "disabled", "danger", "toFixed", "dataSource", "renderItem", "<PERSON><PERSON>", "backgroundColor", "paddingLeft", "Meta", "avatar", "toLocaleTimeString", "maxHeight", "overflowY", "open", "onCancel", "footer", "layout", "onFinish", "autoComplete", "showIcon", "name", "label", "rules", "required", "min", "step", "addonBefore", "placeholder", "justifyContent", "htmlType", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\nimport {\n  Card,\n  Row,\n  Col,\n  Button,\n  Input,\n  message,\n  Typography,\n  Space,\n  Tag,\n  Avatar,\n  List,\n  Statistic,\n  Progress,\n  Badge,\n  Tooltip,\n  Modal,\n  Form,\n  InputNumber,\n  Alert,\n  Divider,\n} from 'antd';\nimport {\n  PlayCircleOutlined,\n  PauseCircleOutlined,\n  StopOutlined,\n  AuditOutlined,\n  UserOutlined,\n  ClockCircleOutlined,\n  DollarOutlined,\n  TrophyOutlined,\n  WarningOutlined,\n} from '@ant-design/icons';\n\nconst { Title, Text } = Typography;\nconst { TextArea } = Input;\n\n// 竞价记录接口\ninterface BidRecord {\n  id: number;\n  userId: number;\n  username: string;\n  avatar?: string;\n  bidAmount: number;\n  bidTime: string;\n  isWinning: boolean;\n}\n\n// 拍卖商品接口\ninterface LiveAuctionItem {\n  id: number;\n  productName: string;\n  productCode: string;\n  images: string[];\n  startingPrice: number;\n  currentPrice: number;\n  reservePrice?: number;\n  bidIncrement: number;\n  bidCount: number;\n  timeRemaining: number; // 剩余时间（秒）\n  status: 'pending' | 'ongoing' | 'ended';\n  highestBidder?: string;\n  description: string;\n}\n\nconst LiveBidding: React.FC = () => {\n  const [currentItem, setCurrentItem] = useState<LiveAuctionItem | null>(null);\n  const [bidRecords, setBidRecords] = useState<BidRecord[]>([]);\n  const [isAuctionActive, setIsAuctionActive] = useState(false);\n  const [timeRemaining, setTimeRemaining] = useState(0);\n  const [bidAmount, setBidAmount] = useState<number>(0);\n  const [isBidModalVisible, setIsBidModalVisible] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const [onlineUsers, setOnlineUsers] = useState(0);\n  const [form] = Form.useForm();\n  const timerRef = useRef<NodeJS.Timeout | null>(null);\n  const wsRef = useRef<WebSocket | null>(null);\n\n  // 模拟拍卖商品数据\n  const mockAuctionItem: LiveAuctionItem = {\n    id: 1,\n    productName: '荷兰郁金香 - 红色经典',\n    productCode: 'TLP-001',\n    images: [\n      'https://images.unsplash.com/photo-1520637836862-4d197d17c90a?w=400',\n      'https://images.unsplash.com/photo-1518709268805-4e9042af2176?w=400',\n    ],\n    startingPrice: 100.00,\n    currentPrice: 350.00,\n    reservePrice: 500.00,\n    bidIncrement: 10.00,\n    bidCount: 15,\n    timeRemaining: 300, // 5分钟\n    status: 'ongoing',\n    highestBidder: 'user123',\n    description: '来自荷兰的优质郁金香，颜色鲜艳，品质上乘。适合园艺爱好者和花卉收藏家。',\n  };\n\n  // 模拟竞价记录\n  const mockBidRecords: BidRecord[] = [\n    {\n      id: 1,\n      userId: 1,\n      username: 'user123',\n      bidAmount: 350.00,\n      bidTime: new Date().toISOString(),\n      isWinning: true,\n    },\n    {\n      id: 2,\n      userId: 2,\n      username: 'flower_lover',\n      bidAmount: 340.00,\n      bidTime: new Date(Date.now() - 30000).toISOString(),\n      isWinning: false,\n    },\n    {\n      id: 3,\n      userId: 3,\n      username: 'garden_master',\n      bidAmount: 330.00,\n      bidTime: new Date(Date.now() - 60000).toISOString(),\n      isWinning: false,\n    },\n  ];\n\n  // 初始化数据\n  useEffect(() => {\n    setCurrentItem(mockAuctionItem);\n    setBidRecords(mockBidRecords);\n    setTimeRemaining(mockAuctionItem.timeRemaining);\n    setOnlineUsers(Math.floor(Math.random() * 50) + 10);\n\n    // 设置下一次出价金额\n    setBidAmount(mockAuctionItem.currentPrice + mockAuctionItem.bidIncrement);\n  }, []);\n\n  // 倒计时器\n  useEffect(() => {\n    if (isAuctionActive && timeRemaining > 0) {\n      timerRef.current = setInterval(() => {\n        setTimeRemaining(prev => {\n          if (prev <= 1) {\n            setIsAuctionActive(false);\n            message.info('拍卖时间结束');\n            return 0;\n          }\n          return prev - 1;\n        });\n      }, 1000);\n    } else {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    }\n\n    return () => {\n      if (timerRef.current) {\n        clearInterval(timerRef.current);\n      }\n    };\n  }, [isAuctionActive, timeRemaining]);\n\n  // WebSocket连接（模拟）\n  useEffect(() => {\n    // 这里应该建立WebSocket连接来接收实时竞价信息\n    // wsRef.current = new WebSocket('ws://localhost:8081/ws/auction');\n\n    return () => {\n      if (wsRef.current) {\n        wsRef.current.close();\n      }\n    };\n  }, []);\n\n  // 格式化时间\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  // 开始拍卖\n  const handleStartAuction = () => {\n    setIsAuctionActive(true);\n    message.success('拍卖已开始');\n  };\n\n  // 暂停拍卖\n  const handlePauseAuction = () => {\n    setIsAuctionActive(false);\n    message.info('拍卖已暂停');\n  };\n\n  // 结束拍卖\n  const handleEndAuction = () => {\n    setIsAuctionActive(false);\n    setTimeRemaining(0);\n    message.success('拍卖已结束');\n  };\n\n  // 出价\n  const handleBid = () => {\n    setIsBidModalVisible(true);\n    form.setFieldsValue({ bidAmount });\n  };\n\n  // 确认出价\n  const handleConfirmBid = async (values: any) => {\n    setLoading(true);\n    try {\n      // 这里应该调用后端API提交出价\n      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用\n\n      const newBid: BidRecord = {\n        id: Date.now(),\n        userId: 999,\n        username: 'admin',\n        bidAmount: values.bidAmount,\n        bidTime: new Date().toISOString(),\n        isWinning: true,\n      };\n\n      // 更新竞价记录\n      setBidRecords(prev => {\n        const updated = prev.map(record => ({ ...record, isWinning: false }));\n        return [newBid, ...updated];\n      });\n\n      // 更新当前价格\n      if (currentItem) {\n        setCurrentItem(prev => prev ? {\n          ...prev,\n          currentPrice: values.bidAmount,\n          bidCount: prev.bidCount + 1,\n          highestBidder: 'admin',\n        } : null);\n      }\n\n      // 设置下一次出价金额\n      setBidAmount(values.bidAmount + (currentItem?.bidIncrement || 10));\n\n      message.success('出价成功');\n      setIsBidModalVisible(false);\n    } catch (error: any) {\n      message.error(error.message || '出价失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (!currentItem) {\n    return (\n      <div style={{ padding: 24, textAlign: 'center' }}>\n        <Title level={3}>暂无进行中的拍卖</Title>\n      </div>\n    );\n  }\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>实时竞价</Title>\n\n      <Row gutter={24}>\n        {/* 左侧：商品信息和控制面板 */}\n        <Col xs={24} lg={16}>\n          {/* 商品信息卡片 */}\n          <Card style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col xs={24} md={8}>\n                <img\n                  src={currentItem.images[0]}\n                  alt={currentItem.productName}\n                  style={{\n                    width: '100%',\n                    height: 200,\n                    objectFit: 'cover',\n                    borderRadius: 8,\n                  }}\n                />\n              </Col>\n              <Col xs={24} md={16}>\n                <Space direction=\"vertical\" size=\"small\" style={{ width: '100%' }}>\n                  <Title level={3} style={{ margin: 0 }}>\n                    {currentItem.productName}\n                  </Title>\n                  <Text type=\"secondary\">商品编号: {currentItem.productCode}</Text>\n                  <Text>{currentItem.description}</Text>\n\n                  <Row gutter={16}>\n                    <Col span={8}>\n                      <Statistic\n                        title=\"起拍价\"\n                        value={currentItem.startingPrice}\n                        precision={2}\n                        prefix=\"¥\"\n                      />\n                    </Col>\n                    <Col span={8}>\n                      <Statistic\n                        title=\"当前价格\"\n                        value={currentItem.currentPrice}\n                        precision={2}\n                        prefix=\"¥\"\n                        valueStyle={{ color: '#f50', fontSize: 24, fontWeight: 'bold' }}\n                      />\n                    </Col>\n                    <Col span={8}>\n                      <Statistic\n                        title=\"出价次数\"\n                        value={currentItem.bidCount}\n                        suffix=\"次\"\n                      />\n                    </Col>\n                  </Row>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 拍卖控制面板 */}\n          <Card title=\"拍卖控制\" style={{ marginBottom: 16 }}>\n            <Row gutter={16} align=\"middle\">\n              <Col xs={24} sm={8}>\n                <Space>\n                  <Badge\n                    status={isAuctionActive ? 'processing' : 'default'}\n                    text={\n                      <Text strong>\n                        状态: {isAuctionActive ? '进行中' : '已暂停'}\n                      </Text>\n                    }\n                  />\n                </Space>\n              </Col>\n              <Col xs={24} sm={8}>\n                <Statistic\n                  title=\"剩余时间\"\n                  value={formatTime(timeRemaining)}\n                  prefix={<ClockCircleOutlined />}\n                  valueStyle={{\n                    color: timeRemaining < 60 ? '#f50' : '#1890ff',\n                    fontSize: 20,\n                    fontWeight: 'bold',\n                  }}\n                />\n                <Progress\n                  percent={Math.max(0, (timeRemaining / 300) * 100)}\n                  showInfo={false}\n                  strokeColor={timeRemaining < 60 ? '#f50' : '#1890ff'}\n                />\n              </Col>\n              <Col xs={24} sm={8}>\n                <Space>\n                  {!isAuctionActive ? (\n                    <Button\n                      type=\"primary\"\n                      icon={<PlayCircleOutlined />}\n                      onClick={handleStartAuction}\n                      disabled={timeRemaining === 0}\n                    >\n                      开始拍卖\n                    </Button>\n                  ) : (\n                    <Button\n                      icon={<PauseCircleOutlined />}\n                      onClick={handlePauseAuction}\n                    >\n                      暂停拍卖\n                    </Button>\n                  )}\n                  <Button\n                    danger\n                    icon={<StopOutlined />}\n                    onClick={handleEndAuction}\n                  >\n                    结束拍卖\n                  </Button>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 出价面板 */}\n          <Card title=\"快速出价\">\n            <Row gutter={16} align=\"middle\">\n              <Col xs={24} sm={12}>\n                <Space direction=\"vertical\" style={{ width: '100%' }}>\n                  <Text>建议出价: ¥{bidAmount.toFixed(2)}</Text>\n                  <Text type=\"secondary\">\n                    加价幅度: ¥{currentItem.bidIncrement.toFixed(2)}\n                  </Text>\n                  {currentItem.reservePrice && (\n                    <Text type=\"warning\">\n                      保留价: ¥{currentItem.reservePrice.toFixed(2)}\n                    </Text>\n                  )}\n                </Space>\n              </Col>\n              <Col xs={24} sm={12}>\n                <Space>\n                  <Button\n                    type=\"primary\"\n                    size=\"large\"\n                    icon={<AuditOutlined />}\n                    onClick={handleBid}\n                    disabled={!isAuctionActive || timeRemaining === 0}\n                  >\n                    出价 ¥{bidAmount.toFixed(2)}\n                  </Button>\n                </Space>\n              </Col>\n            </Row>\n          </Card>\n        </Col>\n\n        {/* 右侧：竞价记录和在线用户 */}\n        <Col xs={24} lg={8}>\n          {/* 在线统计 */}\n          <Card size=\"small\" style={{ marginBottom: 16 }}>\n            <Row gutter={16}>\n              <Col span={12}>\n                <Statistic\n                  title=\"在线用户\"\n                  value={onlineUsers}\n                  prefix={<UserOutlined />}\n                  valueStyle={{ color: '#52c41a' }}\n                />\n              </Col>\n              <Col span={12}>\n                <Statistic\n                  title=\"最高出价人\"\n                  value={currentItem.highestBidder || '暂无'}\n                  prefix={<TrophyOutlined />}\n                />\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 竞价记录 */}\n          <Card title=\"竞价记录\" size=\"small\">\n            <List\n              dataSource={bidRecords}\n              renderItem={(record) => (\n                <List.Item\n                  style={{\n                    padding: '8px 0',\n                    backgroundColor: record.isWinning ? '#f6ffed' : 'transparent',\n                    borderRadius: record.isWinning ? 4 : 0,\n                    paddingLeft: record.isWinning ? 8 : 0,\n                  }}\n                >\n                  <List.Item.Meta\n                    avatar={\n                      <Avatar\n                        src={record.avatar}\n                        icon={<UserOutlined />}\n                        style={{\n                          backgroundColor: record.isWinning ? '#52c41a' : '#1890ff',\n                        }}\n                      />\n                    }\n                    title={\n                      <Space>\n                        <Text strong={record.isWinning}>\n                          {record.username}\n                        </Text>\n                        {record.isWinning && (\n                          <Tag color=\"green\">\n                            最高价\n                          </Tag>\n                        )}\n                      </Space>\n                    }\n                    description={\n                      <Space direction=\"vertical\" size={0}>\n                        <Text strong style={{ color: '#f50' }}>\n                          ¥{record.bidAmount.toFixed(2)}\n                        </Text>\n                        <Text type=\"secondary\" style={{ fontSize: 12 }}>\n                          {new Date(record.bidTime).toLocaleTimeString()}\n                        </Text>\n                      </Space>\n                    }\n                  />\n                </List.Item>\n              )}\n              style={{ maxHeight: 400, overflowY: 'auto' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 出价确认模态框 */}\n      <Modal\n        title=\"确认出价\"\n        open={isBidModalVisible}\n        onCancel={() => setIsBidModalVisible(false)}\n        footer={null}\n        width={400}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleConfirmBid}\n          autoComplete=\"off\"\n        >\n          <Alert\n            message=\"出价提醒\"\n            description={`当前最高价: ¥${currentItem.currentPrice.toFixed(2)}，最小加价幅度: ¥${currentItem.bidIncrement.toFixed(2)}`}\n            type=\"info\"\n            showIcon\n            style={{ marginBottom: 16 }}\n          />\n\n          <Form.Item\n            name=\"bidAmount\"\n            label=\"出价金额\"\n            rules={[\n              { required: true, message: '请输入出价金额' },\n              {\n                type: 'number',\n                min: currentItem.currentPrice + currentItem.bidIncrement,\n                message: `出价必须高于当前价格 ¥${(currentItem.currentPrice + currentItem.bidIncrement).toFixed(2)}`,\n              },\n            ]}\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              precision={2}\n              min={currentItem.currentPrice + currentItem.bidIncrement}\n              step={currentItem.bidIncrement}\n              addonBefore=\"¥\"\n              placeholder=\"请输入出价金额\"\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setIsBidModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\" loading={loading}>\n                确认出价\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default LiveBidding;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,IAAI,EACJC,GAAG,EACHC,GAAG,EACHC,MAAM,EACNC,KAAK,EACLC,OAAO,EACPC,UAAU,EACVC,KAAK,EACLC,GAAG,EACHC,MAAM,EACNC,IAAI,EACJC,SAAS,EACTC,QAAQ,EACRC,KAAK,EAELC,KAAK,EACLC,IAAI,EACJC,WAAW,EACXC,KAAK,QAEA,MAAM;AACb,SACEC,kBAAkB,EAClBC,mBAAmB,EACnBC,YAAY,EACZC,aAAa,EACbC,YAAY,EACZC,mBAAmB,EAEnBC,cAAc,QAET,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAM;EAAEC,KAAK;EAAEC;AAAK,CAAC,GAAGtB,UAAU;AAClC,MAAM;EAAEuB;AAAS,CAAC,GAAGzB,KAAK;;AAE1B;;AAWA;;AAiBA,MAAM0B,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGpC,QAAQ,CAAyB,IAAI,CAAC;EAC5E,MAAM,CAACqC,UAAU,EAAEC,aAAa,CAAC,GAAGtC,QAAQ,CAAc,EAAE,CAAC;EAC7D,MAAM,CAACuC,eAAe,EAAEC,kBAAkB,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACyC,aAAa,EAAEC,gBAAgB,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACrD,MAAM,CAAC2C,SAAS,EAAEC,YAAY,CAAC,GAAG5C,QAAQ,CAAS,CAAC,CAAC;EACrD,MAAM,CAAC6C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAAC+C,OAAO,EAAEC,UAAU,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAC,CAAC,CAAC;EACjD,MAAM,CAACmD,IAAI,CAAC,GAAGjC,IAAI,CAACkC,OAAO,CAAC,CAAC;EAC7B,MAAMC,QAAQ,GAAGnD,MAAM,CAAwB,IAAI,CAAC;EACpD,MAAMoD,KAAK,GAAGpD,MAAM,CAAmB,IAAI,CAAC;;EAE5C;EACA,MAAMqD,eAAgC,GAAG;IACvCC,EAAE,EAAE,CAAC;IACLC,WAAW,EAAE,cAAc;IAC3BC,WAAW,EAAE,SAAS;IACtBC,MAAM,EAAE,CACN,oEAAoE,EACpE,oEAAoE,CACrE;IACDC,aAAa,EAAE,MAAM;IACrBC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,MAAM;IACpBC,YAAY,EAAE,KAAK;IACnBC,QAAQ,EAAE,EAAE;IACZvB,aAAa,EAAE,GAAG;IAAE;IACpBwB,MAAM,EAAE,SAAS;IACjBC,aAAa,EAAE,SAAS;IACxBC,WAAW,EAAE;EACf,CAAC;;EAED;EACA,MAAMC,cAA2B,GAAG,CAClC;IACEZ,EAAE,EAAE,CAAC;IACLa,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,SAAS;IACnB3B,SAAS,EAAE,MAAM;IACjB4B,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IACjCC,SAAS,EAAE;EACb,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLa,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,cAAc;IACxB3B,SAAS,EAAE,MAAM;IACjB4B,OAAO,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAACF,WAAW,CAAC,CAAC;IACnDC,SAAS,EAAE;EACb,CAAC,EACD;IACElB,EAAE,EAAE,CAAC;IACLa,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,eAAe;IACzB3B,SAAS,EAAE,MAAM;IACjB4B,OAAO,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACG,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC,CAACF,WAAW,CAAC,CAAC;IACnDC,SAAS,EAAE;EACb,CAAC,CACF;;EAED;EACAzE,SAAS,CAAC,MAAM;IACdmC,cAAc,CAACmB,eAAe,CAAC;IAC/BjB,aAAa,CAAC8B,cAAc,CAAC;IAC7B1B,gBAAgB,CAACa,eAAe,CAACd,aAAa,CAAC;IAC/CS,cAAc,CAAC0B,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,EAAE,CAAC;;IAEnD;IACAlC,YAAY,CAACW,eAAe,CAACM,YAAY,GAAGN,eAAe,CAACQ,YAAY,CAAC;EAC3E,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA9D,SAAS,CAAC,MAAM;IACd,IAAIsC,eAAe,IAAIE,aAAa,GAAG,CAAC,EAAE;MACxCY,QAAQ,CAAC0B,OAAO,GAAGC,WAAW,CAAC,MAAM;QACnCtC,gBAAgB,CAACuC,IAAI,IAAI;UACvB,IAAIA,IAAI,IAAI,CAAC,EAAE;YACbzC,kBAAkB,CAAC,KAAK,CAAC;YACzBhC,OAAO,CAAC0E,IAAI,CAAC,QAAQ,CAAC;YACtB,OAAO,CAAC;UACV;UACA,OAAOD,IAAI,GAAG,CAAC;QACjB,CAAC,CAAC;MACJ,CAAC,EAAE,IAAI,CAAC;IACV,CAAC,MAAM;MACL,IAAI5B,QAAQ,CAAC0B,OAAO,EAAE;QACpBI,aAAa,CAAC9B,QAAQ,CAAC0B,OAAO,CAAC;MACjC;IACF;IAEA,OAAO,MAAM;MACX,IAAI1B,QAAQ,CAAC0B,OAAO,EAAE;QACpBI,aAAa,CAAC9B,QAAQ,CAAC0B,OAAO,CAAC;MACjC;IACF,CAAC;EACH,CAAC,EAAE,CAACxC,eAAe,EAAEE,aAAa,CAAC,CAAC;;EAEpC;EACAxC,SAAS,CAAC,MAAM;IACd;IACA;;IAEA,OAAO,MAAM;MACX,IAAIqD,KAAK,CAACyB,OAAO,EAAE;QACjBzB,KAAK,CAACyB,OAAO,CAACK,KAAK,CAAC,CAAC;MACvB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;;EAEN;EACA,MAAMC,UAAU,GAAIC,OAAe,IAAK;IACtC,MAAMC,IAAI,GAAGX,IAAI,CAACC,KAAK,CAACS,OAAO,GAAG,EAAE,CAAC;IACrC,MAAME,IAAI,GAAGF,OAAO,GAAG,EAAE;IACzB,OAAO,GAAGC,IAAI,CAACE,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EAClF,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BnD,kBAAkB,CAAC,IAAI,CAAC;IACxBhC,OAAO,CAACoF,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BrD,kBAAkB,CAAC,KAAK,CAAC;IACzBhC,OAAO,CAAC0E,IAAI,CAAC,OAAO,CAAC;EACvB,CAAC;;EAED;EACA,MAAMY,gBAAgB,GAAGA,CAAA,KAAM;IAC7BtD,kBAAkB,CAAC,KAAK,CAAC;IACzBE,gBAAgB,CAAC,CAAC,CAAC;IACnBlC,OAAO,CAACoF,OAAO,CAAC,OAAO,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMG,SAAS,GAAGA,CAAA,KAAM;IACtBjD,oBAAoB,CAAC,IAAI,CAAC;IAC1BK,IAAI,CAAC6C,cAAc,CAAC;MAAErD;IAAU,CAAC,CAAC;EACpC,CAAC;;EAED;EACA,MAAMsD,gBAAgB,GAAG,MAAOC,MAAW,IAAK;IAC9ClD,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA,MAAM,IAAImD,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC,CAAC,CAAC;;MAEzD,MAAME,MAAiB,GAAG;QACxB9C,EAAE,EAAEgB,IAAI,CAACG,GAAG,CAAC,CAAC;QACdN,MAAM,EAAE,GAAG;QACXC,QAAQ,EAAE,OAAO;QACjB3B,SAAS,EAAEuD,MAAM,CAACvD,SAAS;QAC3B4B,OAAO,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACjCC,SAAS,EAAE;MACb,CAAC;;MAED;MACApC,aAAa,CAAC2C,IAAI,IAAI;QACpB,MAAMsB,OAAO,GAAGtB,IAAI,CAACuB,GAAG,CAACC,MAAM,KAAK;UAAE,GAAGA,MAAM;UAAE/B,SAAS,EAAE;QAAM,CAAC,CAAC,CAAC;QACrE,OAAO,CAAC4B,MAAM,EAAE,GAAGC,OAAO,CAAC;MAC7B,CAAC,CAAC;;MAEF;MACA,IAAIpE,WAAW,EAAE;QACfC,cAAc,CAAC6C,IAAI,IAAIA,IAAI,GAAG;UAC5B,GAAGA,IAAI;UACPpB,YAAY,EAAEqC,MAAM,CAACvD,SAAS;UAC9BqB,QAAQ,EAAEiB,IAAI,CAACjB,QAAQ,GAAG,CAAC;UAC3BE,aAAa,EAAE;QACjB,CAAC,GAAG,IAAI,CAAC;MACX;;MAEA;MACAtB,YAAY,CAACsD,MAAM,CAACvD,SAAS,IAAI,CAAAR,WAAW,aAAXA,WAAW,uBAAXA,WAAW,CAAE4B,YAAY,KAAI,EAAE,CAAC,CAAC;MAElEvD,OAAO,CAACoF,OAAO,CAAC,MAAM,CAAC;MACvB9C,oBAAoB,CAAC,KAAK,CAAC;IAC7B,CAAC,CAAC,OAAO4D,KAAU,EAAE;MACnBlG,OAAO,CAACkG,KAAK,CAACA,KAAK,CAAClG,OAAO,IAAI,MAAM,CAAC;IACxC,CAAC,SAAS;MACRwC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,IAAI,CAACb,WAAW,EAAE;IAChB,oBACEN,OAAA;MAAK8E,KAAK,EAAE;QAAEC,OAAO,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAS,CAAE;MAAAC,QAAA,eAC/CjF,OAAA,CAACC,KAAK;QAACiF,KAAK,EAAE,CAAE;QAAAD,QAAA,EAAC;MAAQ;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC;EAEV;EAEA,oBACEtF,OAAA;IAAK8E,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAE,QAAA,gBAC1BjF,OAAA,CAACC,KAAK;MAACiF,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAE7BtF,OAAA,CAACzB,GAAG;MAACgH,MAAM,EAAE,EAAG;MAAAN,QAAA,gBAEdjF,OAAA,CAACxB,GAAG;QAACgH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAAAR,QAAA,gBAElBjF,OAAA,CAAC1B,IAAI;UAACwG,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,eAChCjF,OAAA,CAACzB,GAAG;YAACgH,MAAM,EAAE,EAAG;YAAAN,QAAA,gBACdjF,OAAA,CAACxB,GAAG;cAACgH,EAAE,EAAE,EAAG;cAACG,EAAE,EAAE,CAAE;cAAAV,QAAA,eACjBjF,OAAA;gBACE4F,GAAG,EAAEtF,WAAW,CAACwB,MAAM,CAAC,CAAC,CAAE;gBAC3B+D,GAAG,EAAEvF,WAAW,CAACsB,WAAY;gBAC7BkD,KAAK,EAAE;kBACLgB,KAAK,EAAE,MAAM;kBACbC,MAAM,EAAE,GAAG;kBACXC,SAAS,EAAE,OAAO;kBAClBC,YAAY,EAAE;gBAChB;cAAE;gBAAAd,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtF,OAAA,CAACxB,GAAG;cAACgH,EAAE,EAAE,EAAG;cAACG,EAAE,EAAE,EAAG;cAAAV,QAAA,eAClBjF,OAAA,CAACnB,KAAK;gBAACqH,SAAS,EAAC,UAAU;gBAACC,IAAI,EAAC,OAAO;gBAACrB,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBAAAb,QAAA,gBAChEjF,OAAA,CAACC,KAAK;kBAACiF,KAAK,EAAE,CAAE;kBAACJ,KAAK,EAAE;oBAAEsB,MAAM,EAAE;kBAAE,CAAE;kBAAAnB,QAAA,EACnC3E,WAAW,CAACsB;gBAAW;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACRtF,OAAA,CAACE,IAAI;kBAACmG,IAAI,EAAC,WAAW;kBAAApB,QAAA,GAAC,4BAAM,EAAC3E,WAAW,CAACuB,WAAW;gBAAA;kBAAAsD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC7DtF,OAAA,CAACE,IAAI;kBAAA+E,QAAA,EAAE3E,WAAW,CAACgC;gBAAW;kBAAA6C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAEtCtF,OAAA,CAACzB,GAAG;kBAACgH,MAAM,EAAE,EAAG;kBAAAN,QAAA,gBACdjF,OAAA,CAACxB,GAAG;oBAAC8H,IAAI,EAAE,CAAE;oBAAArB,QAAA,eACXjF,OAAA,CAACf,SAAS;sBACRsH,KAAK,EAAC,oBAAK;sBACXC,KAAK,EAAElG,WAAW,CAACyB,aAAc;sBACjC0E,SAAS,EAAE,CAAE;sBACbC,MAAM,EAAC;oBAAG;sBAAAvB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNtF,OAAA,CAACxB,GAAG;oBAAC8H,IAAI,EAAE,CAAE;oBAAArB,QAAA,eACXjF,OAAA,CAACf,SAAS;sBACRsH,KAAK,EAAC,0BAAM;sBACZC,KAAK,EAAElG,WAAW,CAAC0B,YAAa;sBAChCyE,SAAS,EAAE,CAAE;sBACbC,MAAM,EAAC,MAAG;sBACVC,UAAU,EAAE;wBAAEC,KAAK,EAAE,MAAM;wBAAEC,QAAQ,EAAE,EAAE;wBAAEC,UAAU,EAAE;sBAAO;oBAAE;sBAAA3B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC,eACNtF,OAAA,CAACxB,GAAG;oBAAC8H,IAAI,EAAE,CAAE;oBAAArB,QAAA,eACXjF,OAAA,CAACf,SAAS;sBACRsH,KAAK,EAAC,0BAAM;sBACZC,KAAK,EAAElG,WAAW,CAAC6B,QAAS;sBAC5B4E,MAAM,EAAC;oBAAG;sBAAA5B,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACX;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPtF,OAAA,CAAC1B,IAAI;UAACiI,KAAK,EAAC,0BAAM;UAACzB,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,eAC7CjF,OAAA,CAACzB,GAAG;YAACgH,MAAM,EAAE,EAAG;YAACyB,KAAK,EAAC,QAAQ;YAAA/B,QAAA,gBAC7BjF,OAAA,CAACxB,GAAG;cAACgH,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAAhC,QAAA,eACjBjF,OAAA,CAACnB,KAAK;gBAAAoG,QAAA,eACJjF,OAAA,CAACb,KAAK;kBACJiD,MAAM,EAAE1B,eAAe,GAAG,YAAY,GAAG,SAAU;kBACnDwG,IAAI,eACFlH,OAAA,CAACE,IAAI;oBAACiH,MAAM;oBAAAlC,QAAA,GAAC,gBACP,EAACvE,eAAe,GAAG,KAAK,GAAG,KAAK;kBAAA;oBAAAyE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAChC;gBACP;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNtF,OAAA,CAACxB,GAAG;cAACgH,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAAhC,QAAA,gBACjBjF,OAAA,CAACf,SAAS;gBACRsH,KAAK,EAAC,0BAAM;gBACZC,KAAK,EAAEhD,UAAU,CAAC5C,aAAa,CAAE;gBACjC8F,MAAM,eAAE1G,OAAA,CAACH,mBAAmB;kBAAAsF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBAChCqB,UAAU,EAAE;kBACVC,KAAK,EAAEhG,aAAa,GAAG,EAAE,GAAG,MAAM,GAAG,SAAS;kBAC9CiG,QAAQ,EAAE,EAAE;kBACZC,UAAU,EAAE;gBACd;cAAE;gBAAA3B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACFtF,OAAA,CAACd,QAAQ;gBACPkI,OAAO,EAAErE,IAAI,CAACsE,GAAG,CAAC,CAAC,EAAGzG,aAAa,GAAG,GAAG,GAAI,GAAG,CAAE;gBAClD0G,QAAQ,EAAE,KAAM;gBAChBC,WAAW,EAAE3G,aAAa,GAAG,EAAE,GAAG,MAAM,GAAG;cAAU;gBAAAuE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtF,OAAA,CAACxB,GAAG;cAACgH,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,CAAE;cAAAhC,QAAA,eACjBjF,OAAA,CAACnB,KAAK;gBAAAoG,QAAA,GACH,CAACvE,eAAe,gBACfV,OAAA,CAACvB,MAAM;kBACL4H,IAAI,EAAC,SAAS;kBACdmB,IAAI,eAAExH,OAAA,CAACR,kBAAkB;oBAAA2F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC7BmC,OAAO,EAAE3D,kBAAmB;kBAC5B4D,QAAQ,EAAE9G,aAAa,KAAK,CAAE;kBAAAqE,QAAA,EAC/B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,gBAETtF,OAAA,CAACvB,MAAM;kBACL+I,IAAI,eAAExH,OAAA,CAACP,mBAAmB;oBAAA0F,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAC9BmC,OAAO,EAAEzD,kBAAmB;kBAAAiB,QAAA,EAC7B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CACT,eACDtF,OAAA,CAACvB,MAAM;kBACLkJ,MAAM;kBACNH,IAAI,eAAExH,OAAA,CAACN,YAAY;oBAAAyF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBmC,OAAO,EAAExD,gBAAiB;kBAAAgB,QAAA,EAC3B;gBAED;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPtF,OAAA,CAAC1B,IAAI;UAACiI,KAAK,EAAC,0BAAM;UAAAtB,QAAA,eAChBjF,OAAA,CAACzB,GAAG;YAACgH,MAAM,EAAE,EAAG;YAACyB,KAAK,EAAC,QAAQ;YAAA/B,QAAA,gBAC7BjF,OAAA,CAACxB,GAAG;cAACgH,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,EAAG;cAAAhC,QAAA,eAClBjF,OAAA,CAACnB,KAAK;gBAACqH,SAAS,EAAC,UAAU;gBAACpB,KAAK,EAAE;kBAAEgB,KAAK,EAAE;gBAAO,CAAE;gBAAAb,QAAA,gBACnDjF,OAAA,CAACE,IAAI;kBAAA+E,QAAA,GAAC,gCAAO,EAACnE,SAAS,CAAC8G,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAO,CAAC,eAC1CtF,OAAA,CAACE,IAAI;kBAACmG,IAAI,EAAC,WAAW;kBAAApB,QAAA,GAAC,gCACd,EAAC3E,WAAW,CAAC4B,YAAY,CAAC0F,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvC,CAAC,EACNhF,WAAW,CAAC2B,YAAY,iBACvBjC,OAAA,CAACE,IAAI;kBAACmG,IAAI,EAAC,SAAS;kBAAApB,QAAA,GAAC,0BACb,EAAC3E,WAAW,CAAC2B,YAAY,CAAC2F,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtC,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACNtF,OAAA,CAACxB,GAAG;cAACgH,EAAE,EAAE,EAAG;cAACyB,EAAE,EAAE,EAAG;cAAAhC,QAAA,eAClBjF,OAAA,CAACnB,KAAK;gBAAAoG,QAAA,eACJjF,OAAA,CAACvB,MAAM;kBACL4H,IAAI,EAAC,SAAS;kBACdF,IAAI,EAAC,OAAO;kBACZqB,IAAI,eAAExH,OAAA,CAACL,aAAa;oBAAAwF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACxBmC,OAAO,EAAEvD,SAAU;kBACnBwD,QAAQ,EAAE,CAAChH,eAAe,IAAIE,aAAa,KAAK,CAAE;kBAAAqE,QAAA,GACnD,mBACK,EAACnE,SAAS,CAAC8G,OAAO,CAAC,CAAC,CAAC;gBAAA;kBAAAzC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNtF,OAAA,CAACxB,GAAG;QAACgH,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAR,QAAA,gBAEjBjF,OAAA,CAAC1B,IAAI;UAAC6H,IAAI,EAAC,OAAO;UAACrB,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAG,CAAE;UAAAT,QAAA,eAC7CjF,OAAA,CAACzB,GAAG;YAACgH,MAAM,EAAE,EAAG;YAAAN,QAAA,gBACdjF,OAAA,CAACxB,GAAG;cAAC8H,IAAI,EAAE,EAAG;cAAArB,QAAA,eACZjF,OAAA,CAACf,SAAS;gBACRsH,KAAK,EAAC,0BAAM;gBACZC,KAAK,EAAEpF,WAAY;gBACnBsF,MAAM,eAAE1G,OAAA,CAACJ,YAAY;kBAAAuF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBqB,UAAU,EAAE;kBAAEC,KAAK,EAAE;gBAAU;cAAE;gBAAAzB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC,eACNtF,OAAA,CAACxB,GAAG;cAAC8H,IAAI,EAAE,EAAG;cAAArB,QAAA,eACZjF,OAAA,CAACf,SAAS;gBACRsH,KAAK,EAAC,gCAAO;gBACbC,KAAK,EAAElG,WAAW,CAAC+B,aAAa,IAAI,IAAK;gBACzCqE,MAAM,eAAE1G,OAAA,CAACF,cAAc;kBAAAqF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAE;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPtF,OAAA,CAAC1B,IAAI;UAACiI,KAAK,EAAC,0BAAM;UAACJ,IAAI,EAAC,OAAO;UAAAlB,QAAA,eAC7BjF,OAAA,CAAChB,IAAI;YACH6I,UAAU,EAAErH,UAAW;YACvBsH,UAAU,EAAGlD,MAAM,iBACjB5E,OAAA,CAAChB,IAAI,CAAC+I,IAAI;cACRjD,KAAK,EAAE;gBACLC,OAAO,EAAE,OAAO;gBAChBiD,eAAe,EAAEpD,MAAM,CAAC/B,SAAS,GAAG,SAAS,GAAG,aAAa;gBAC7DoD,YAAY,EAAErB,MAAM,CAAC/B,SAAS,GAAG,CAAC,GAAG,CAAC;gBACtCoF,WAAW,EAAErD,MAAM,CAAC/B,SAAS,GAAG,CAAC,GAAG;cACtC,CAAE;cAAAoC,QAAA,eAEFjF,OAAA,CAAChB,IAAI,CAAC+I,IAAI,CAACG,IAAI;gBACbC,MAAM,eACJnI,OAAA,CAACjB,MAAM;kBACL6G,GAAG,EAAEhB,MAAM,CAACuD,MAAO;kBACnBX,IAAI,eAAExH,OAAA,CAACJ,YAAY;oBAAAuF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACvBR,KAAK,EAAE;oBACLkD,eAAe,EAAEpD,MAAM,CAAC/B,SAAS,GAAG,SAAS,GAAG;kBAClD;gBAAE;kBAAAsC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CACF;gBACDiB,KAAK,eACHvG,OAAA,CAACnB,KAAK;kBAAAoG,QAAA,gBACJjF,OAAA,CAACE,IAAI;oBAACiH,MAAM,EAAEvC,MAAM,CAAC/B,SAAU;oBAAAoC,QAAA,EAC5BL,MAAM,CAACnC;kBAAQ;oBAAA0C,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACZ,CAAC,EACNV,MAAM,CAAC/B,SAAS,iBACf7C,OAAA,CAAClB,GAAG;oBAAC8H,KAAK,EAAC,OAAO;oBAAA3B,QAAA,EAAC;kBAEnB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAK,CACN;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACI,CACR;gBACDhD,WAAW,eACTtC,OAAA,CAACnB,KAAK;kBAACqH,SAAS,EAAC,UAAU;kBAACC,IAAI,EAAE,CAAE;kBAAAlB,QAAA,gBAClCjF,OAAA,CAACE,IAAI;oBAACiH,MAAM;oBAACrC,KAAK,EAAE;sBAAE8B,KAAK,EAAE;oBAAO,CAAE;oBAAA3B,QAAA,GAAC,MACpC,EAACL,MAAM,CAAC9D,SAAS,CAAC8G,OAAO,CAAC,CAAC,CAAC;kBAAA;oBAAAzC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACzB,CAAC,eACPtF,OAAA,CAACE,IAAI;oBAACmG,IAAI,EAAC,WAAW;oBAACvB,KAAK,EAAE;sBAAE+B,QAAQ,EAAE;oBAAG,CAAE;oBAAA5B,QAAA,EAC5C,IAAItC,IAAI,CAACiC,MAAM,CAAClC,OAAO,CAAC,CAAC0F,kBAAkB,CAAC;kBAAC;oBAAAjD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC1C,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACF;cACR;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACF;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CACX;YACFR,KAAK,EAAE;cAAEuD,SAAS,EAAE,GAAG;cAAEC,SAAS,EAAE;YAAO;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNtF,OAAA,CAACZ,KAAK;MACJmH,KAAK,EAAC,0BAAM;MACZgC,IAAI,EAAEvH,iBAAkB;MACxBwH,QAAQ,EAAEA,CAAA,KAAMvH,oBAAoB,CAAC,KAAK,CAAE;MAC5CwH,MAAM,EAAE,IAAK;MACb3C,KAAK,EAAE,GAAI;MAAAb,QAAA,eAEXjF,OAAA,CAACX,IAAI;QACHiC,IAAI,EAAEA,IAAK;QACXoH,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEvE,gBAAiB;QAC3BwE,YAAY,EAAC,KAAK;QAAA3D,QAAA,gBAElBjF,OAAA,CAACT,KAAK;UACJZ,OAAO,EAAC,0BAAM;UACd2D,WAAW,EAAE,WAAWhC,WAAW,CAAC0B,YAAY,CAAC4F,OAAO,CAAC,CAAC,CAAC,aAAatH,WAAW,CAAC4B,YAAY,CAAC0F,OAAO,CAAC,CAAC,CAAC,EAAG;UAC9GvB,IAAI,EAAC,MAAM;UACXwC,QAAQ;UACR/D,KAAK,EAAE;YAAEY,YAAY,EAAE;UAAG;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7B,CAAC,eAEFtF,OAAA,CAACX,IAAI,CAAC0I,IAAI;UACRe,IAAI,EAAC,WAAW;UAChBC,KAAK,EAAC,0BAAM;UACZC,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEtK,OAAO,EAAE;UAAU,CAAC,EACtC;YACE0H,IAAI,EAAE,QAAQ;YACd6C,GAAG,EAAE5I,WAAW,CAAC0B,YAAY,GAAG1B,WAAW,CAAC4B,YAAY;YACxDvD,OAAO,EAAE,eAAe,CAAC2B,WAAW,CAAC0B,YAAY,GAAG1B,WAAW,CAAC4B,YAAY,EAAE0F,OAAO,CAAC,CAAC,CAAC;UAC1F,CAAC,CACD;UAAA3C,QAAA,eAEFjF,OAAA,CAACV,WAAW;YACVwF,KAAK,EAAE;cAAEgB,KAAK,EAAE;YAAO,CAAE;YACzBW,SAAS,EAAE,CAAE;YACbyC,GAAG,EAAE5I,WAAW,CAAC0B,YAAY,GAAG1B,WAAW,CAAC4B,YAAa;YACzDiH,IAAI,EAAE7I,WAAW,CAAC4B,YAAa;YAC/BkH,WAAW,EAAC,MAAG;YACfC,WAAW,EAAC;UAAS;YAAAlE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZtF,OAAA,CAACX,IAAI,CAAC0I,IAAI;UAAA9C,QAAA,eACRjF,OAAA,CAACnB,KAAK;YAACiG,KAAK,EAAE;cAAEgB,KAAK,EAAE,MAAM;cAAEwD,cAAc,EAAE;YAAW,CAAE;YAAArE,QAAA,gBAC1DjF,OAAA,CAACvB,MAAM;cAACgJ,OAAO,EAAEA,CAAA,KAAMxG,oBAAoB,CAAC,KAAK,CAAE;cAAAgE,QAAA,EAAC;YAEpD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTtF,OAAA,CAACvB,MAAM;cAAC4H,IAAI,EAAC,SAAS;cAACkD,QAAQ,EAAC,QAAQ;cAACrI,OAAO,EAAEA,OAAQ;cAAA+D,QAAA,EAAC;YAE3D;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACjF,EAAA,CAteID,WAAqB;EAAA,QASVf,IAAI,CAACkC,OAAO;AAAA;AAAAiI,EAAA,GATvBpJ,WAAqB;AAwe3B,eAAeA,WAAW;AAAC,IAAAoJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}