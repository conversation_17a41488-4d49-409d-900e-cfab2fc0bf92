{"ast": null, "code": "import _wrapAsyncGenerator from \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@babel/runtime/helpers/esm/wrapAsyncGenerator.js\";\nimport _awaitAsyncGenerator from \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@babel/runtime/helpers/esm/awaitAsyncGenerator.js\";\nimport _asyncGeneratorDelegate from \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@babel/runtime/helpers/esm/asyncGeneratorDelegate.js\";\nimport _asyncIterator from \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@babel/runtime/helpers/esm/asyncIterator.js\";\nexport const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n  let pos = 0;\n  let end;\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n};\nexport const readBytes = /*#__PURE__*/function () {\n  var _ref = _wrapAsyncGenerator(function* (iterable, chunkSize) {\n    var _iteratorAbruptCompletion = false;\n    var _didIteratorError = false;\n    var _iteratorError;\n    try {\n      for (var _iterator = _asyncIterator(readStream(iterable)), _step; _iteratorAbruptCompletion = !(_step = yield _awaitAsyncGenerator(_iterator.next())).done; _iteratorAbruptCompletion = false) {\n        const chunk = _step.value;\n        {\n          yield* _asyncGeneratorDelegate(_asyncIterator(streamChunk(chunk, chunkSize)), _awaitAsyncGenerator);\n        }\n      }\n    } catch (err) {\n      _didIteratorError = true;\n      _iteratorError = err;\n    } finally {\n      try {\n        if (_iteratorAbruptCompletion && _iterator.return != null) {\n          yield _awaitAsyncGenerator(_iterator.return());\n        }\n      } finally {\n        if (_didIteratorError) {\n          throw _iteratorError;\n        }\n      }\n    }\n  });\n  return function readBytes(_x, _x2) {\n    return _ref.apply(this, arguments);\n  };\n}();\nconst readStream = /*#__PURE__*/function () {\n  var _ref2 = _wrapAsyncGenerator(function* (stream) {\n    if (stream[Symbol.asyncIterator]) {\n      yield* _asyncGeneratorDelegate(_asyncIterator(stream), _awaitAsyncGenerator);\n      return;\n    }\n    const reader = stream.getReader();\n    try {\n      for (;;) {\n        const {\n          done,\n          value\n        } = yield _awaitAsyncGenerator(reader.read());\n        if (done) {\n          break;\n        }\n        yield value;\n      }\n    } finally {\n      yield _awaitAsyncGenerator(reader.cancel());\n    }\n  });\n  return function readStream(_x3) {\n    return _ref2.apply(this, arguments);\n  };\n}();\nexport const trackStream = (stream, chunkSize, onProgress, onFinish) => {\n  const iterator = readBytes(stream, chunkSize);\n  let bytes = 0;\n  let done;\n  let _onFinish = e => {\n    if (!done) {\n      done = true;\n      onFinish && onFinish(e);\n    }\n  };\n  return new ReadableStream({\n    async pull(controller) {\n      try {\n        const {\n          done,\n          value\n        } = await iterator.next();\n        if (done) {\n          _onFinish();\n          controller.close();\n          return;\n        }\n        let len = value.byteLength;\n        if (onProgress) {\n          let loadedBytes = bytes += len;\n          onProgress(loadedBytes);\n        }\n        controller.enqueue(new Uint8Array(value));\n      } catch (err) {\n        _onFinish(err);\n        throw err;\n      }\n    },\n    cancel(reason) {\n      _onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  });\n};", "map": {"version": 3, "names": ["streamChunk", "chunk", "chunkSize", "len", "byteLength", "pos", "end", "slice", "readBytes", "_ref", "_wrapAsyncGenerator", "iterable", "_iteratorAbruptCompletion", "_didIteratorError", "_iteratorError", "_iterator", "_asyncIterator", "readStream", "_step", "_awaitAsyncGenerator", "next", "done", "value", "_asyncGeneratorDelegate", "err", "return", "_x", "_x2", "apply", "arguments", "_ref2", "stream", "Symbol", "asyncIterator", "reader", "<PERSON><PERSON><PERSON><PERSON>", "read", "cancel", "_x3", "trackStream", "onProgress", "onFinish", "iterator", "bytes", "_onFinish", "e", "ReadableStream", "pull", "controller", "close", "loadedBytes", "enqueue", "Uint8Array", "reason", "highWaterMark"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/axios/lib/helpers/trackStream.js"], "sourcesContent": ["\nexport const streamChunk = function* (chunk, chunkSize) {\n  let len = chunk.byteLength;\n\n  if (!chunkSize || len < chunkSize) {\n    yield chunk;\n    return;\n  }\n\n  let pos = 0;\n  let end;\n\n  while (pos < len) {\n    end = pos + chunkSize;\n    yield chunk.slice(pos, end);\n    pos = end;\n  }\n}\n\nexport const readBytes = async function* (iterable, chunkSize) {\n  for await (const chunk of readStream(iterable)) {\n    yield* streamChunk(chunk, chunkSize);\n  }\n}\n\nconst readStream = async function* (stream) {\n  if (stream[Symbol.asyncIterator]) {\n    yield* stream;\n    return;\n  }\n\n  const reader = stream.getReader();\n  try {\n    for (;;) {\n      const {done, value} = await reader.read();\n      if (done) {\n        break;\n      }\n      yield value;\n    }\n  } finally {\n    await reader.cancel();\n  }\n}\n\nexport const trackStream = (stream, chunkSize, onProgress, onFinish) => {\n  const iterator = readBytes(stream, chunkSize);\n\n  let bytes = 0;\n  let done;\n  let _onFinish = (e) => {\n    if (!done) {\n      done = true;\n      onFinish && onFinish(e);\n    }\n  }\n\n  return new ReadableStream({\n    async pull(controller) {\n      try {\n        const {done, value} = await iterator.next();\n\n        if (done) {\n         _onFinish();\n          controller.close();\n          return;\n        }\n\n        let len = value.byteLength;\n        if (onProgress) {\n          let loadedBytes = bytes += len;\n          onProgress(loadedBytes);\n        }\n        controller.enqueue(new Uint8Array(value));\n      } catch (err) {\n        _onFinish(err);\n        throw err;\n      }\n    },\n    cancel(reason) {\n      _onFinish(reason);\n      return iterator.return();\n    }\n  }, {\n    highWaterMark: 2\n  })\n}\n"], "mappings": ";;;;AACA,OAAO,MAAMA,WAAW,GAAG,UAAAA,CAAWC,KAAK,EAAEC,SAAS,EAAE;EACtD,IAAIC,GAAG,GAAGF,KAAK,CAACG,UAAU;EAE1B,IAAI,CAACF,SAAS,IAAIC,GAAG,GAAGD,SAAS,EAAE;IACjC,MAAMD,KAAK;IACX;EACF;EAEA,IAAII,GAAG,GAAG,CAAC;EACX,IAAIC,GAAG;EAEP,OAAOD,GAAG,GAAGF,GAAG,EAAE;IAChBG,GAAG,GAAGD,GAAG,GAAGH,SAAS;IACrB,MAAMD,KAAK,CAACM,KAAK,CAACF,GAAG,EAAEC,GAAG,CAAC;IAC3BD,GAAG,GAAGC,GAAG;EACX;AACF,CAAC;AAED,OAAO,MAAME,SAAS;EAAA,IAAAC,IAAA,GAAAC,mBAAA,CAAG,WAAiBC,QAAQ,EAAET,SAAS,EAAE;IAAA,IAAAU,yBAAA;IAAA,IAAAC,iBAAA;IAAA,IAAAC,cAAA;IAAA;MAC7D,SAAAC,SAAA,GAAAC,cAAA,CAA0BC,UAAU,CAACN,QAAQ,CAAC,GAAAO,KAAA,EAAAN,yBAAA,KAAAM,KAAA,SAAAC,oBAAA,CAAAJ,SAAA,CAAAK,IAAA,KAAAC,IAAA,EAAAT,yBAAA,UAAE;QAAA,MAA/BX,KAAK,GAAAiB,KAAA,CAAAI,KAAA;QAAA;UACpB,OAAAC,uBAAA,CAAAP,cAAA,CAAOhB,WAAW,CAACC,KAAK,EAAEC,SAAS,CAAC,GAAAiB,oBAAA;QAAC;MACvC;IAAC,SAAAK,GAAA;MAAAX,iBAAA;MAAAC,cAAA,GAAAU,GAAA;IAAA;MAAA;QAAA,IAAAZ,yBAAA,IAAAG,SAAA,CAAAU,MAAA;UAAA,MAAAN,oBAAA,CAAAJ,SAAA,CAAAU,MAAA;QAAA;MAAA;QAAA,IAAAZ,iBAAA;UAAA,MAAAC,cAAA;QAAA;MAAA;IAAA;EACH,CAAC;EAAA,gBAJYN,SAASA,CAAAkB,EAAA,EAAAC,GAAA;IAAA,OAAAlB,IAAA,CAAAmB,KAAA,OAAAC,SAAA;EAAA;AAAA,GAIrB;AAED,MAAMZ,UAAU;EAAA,IAAAa,KAAA,GAAApB,mBAAA,CAAG,WAAiBqB,MAAM,EAAE;IAC1C,IAAIA,MAAM,CAACC,MAAM,CAACC,aAAa,CAAC,EAAE;MAChC,OAAAV,uBAAA,CAAAP,cAAA,CAAOe,MAAM,GAAAZ,oBAAA;MACb;IACF;IAEA,MAAMe,MAAM,GAAGH,MAAM,CAACI,SAAS,CAAC,CAAC;IACjC,IAAI;MACF,SAAS;QACP,MAAM;UAACd,IAAI;UAAEC;QAAK,CAAC,SAAAH,oBAAA,CAASe,MAAM,CAACE,IAAI,CAAC,CAAC;QACzC,IAAIf,IAAI,EAAE;UACR;QACF;QACA,MAAMC,KAAK;MACb;IACF,CAAC,SAAS;MACR,MAAAH,oBAAA,CAAMe,MAAM,CAACG,MAAM,CAAC,CAAC;IACvB;EACF,CAAC;EAAA,gBAlBKpB,UAAUA,CAAAqB,GAAA;IAAA,OAAAR,KAAA,CAAAF,KAAA,OAAAC,SAAA;EAAA;AAAA,GAkBf;AAED,OAAO,MAAMU,WAAW,GAAGA,CAACR,MAAM,EAAE7B,SAAS,EAAEsC,UAAU,EAAEC,QAAQ,KAAK;EACtE,MAAMC,QAAQ,GAAGlC,SAAS,CAACuB,MAAM,EAAE7B,SAAS,CAAC;EAE7C,IAAIyC,KAAK,GAAG,CAAC;EACb,IAAItB,IAAI;EACR,IAAIuB,SAAS,GAAIC,CAAC,IAAK;IACrB,IAAI,CAACxB,IAAI,EAAE;MACTA,IAAI,GAAG,IAAI;MACXoB,QAAQ,IAAIA,QAAQ,CAACI,CAAC,CAAC;IACzB;EACF,CAAC;EAED,OAAO,IAAIC,cAAc,CAAC;IACxB,MAAMC,IAAIA,CAACC,UAAU,EAAE;MACrB,IAAI;QACF,MAAM;UAAC3B,IAAI;UAAEC;QAAK,CAAC,GAAG,MAAMoB,QAAQ,CAACtB,IAAI,CAAC,CAAC;QAE3C,IAAIC,IAAI,EAAE;UACTuB,SAAS,CAAC,CAAC;UACVI,UAAU,CAACC,KAAK,CAAC,CAAC;UAClB;QACF;QAEA,IAAI9C,GAAG,GAAGmB,KAAK,CAAClB,UAAU;QAC1B,IAAIoC,UAAU,EAAE;UACd,IAAIU,WAAW,GAAGP,KAAK,IAAIxC,GAAG;UAC9BqC,UAAU,CAACU,WAAW,CAAC;QACzB;QACAF,UAAU,CAACG,OAAO,CAAC,IAAIC,UAAU,CAAC9B,KAAK,CAAC,CAAC;MAC3C,CAAC,CAAC,OAAOE,GAAG,EAAE;QACZoB,SAAS,CAACpB,GAAG,CAAC;QACd,MAAMA,GAAG;MACX;IACF,CAAC;IACDa,MAAMA,CAACgB,MAAM,EAAE;MACbT,SAAS,CAACS,MAAM,CAAC;MACjB,OAAOX,QAAQ,CAACjB,MAAM,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE;IACD6B,aAAa,EAAE;EACjB,CAAC,CAAC;AACJ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}