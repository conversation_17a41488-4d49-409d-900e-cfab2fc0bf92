{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Modal, Form, message, Popconfirm, Typography, Row, Col, Tree, Switch, Tag, Descriptions, Select } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, SettingOutlined, ReloadOutlined, UserOutlined } from '@ant-design/icons';\nimport { roleService } from '../../../services/roleService';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\n\n// 角色数据接口\n\n// 权限数据接口\n\n// 查询参数接口\n\nconst RoleManagement = () => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  const [permissions, setPermissions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);\n  const [editingRole, setEditingRole] = useState(null);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [checkedPermissions, setCheckedPermissions] = useState([]);\n  const [expandedKeys, setExpandedKeys] = useState([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const response = await roleService.getRoleList(queryParams);\n      if (response.success) {\n        // 处理后端返回的数据，添加缺失的字段\n        const processedRoles = response.data.list.map(role => ({\n          ...role,\n          userCount: role.userCount || 0,\n          permissions: role.permissions || []\n        }));\n        setRoles(processedRoles);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取角色列表失败');\n      }\n    } catch (error) {\n      message.error(error.message || '获取角色列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取权限列表\n  const fetchPermissions = async () => {\n    try {\n      const response = await roleService.getPermissionList();\n      if (response.success) {\n        // 处理后端返回的权限数据，转换为前端期望的格式\n        const rawData = response.data;\n        const permissionList = rawData.list || rawData || [];\n        const processedPermissions = permissionList.map(permission => ({\n          id: permission.id,\n          name: permission.name,\n          code: permission.code,\n          type: permission.type || 'menu',\n          parentId: permission.parent_id,\n          path: permission.path,\n          children: []\n        }));\n\n        // 构建树形结构\n        const permissionTree = buildPermissionTree(processedPermissions);\n        setPermissions(permissionTree);\n      }\n    } catch (error) {\n      console.error('获取权限列表失败:', error);\n    }\n  };\n\n  // 构建权限树形结构\n  const buildPermissionTree = permissions => {\n    const map = new Map();\n    const roots = [];\n\n    // 创建映射\n    permissions.forEach(permission => {\n      map.set(permission.id, {\n        ...permission,\n        children: []\n      });\n    });\n\n    // 构建树形结构\n    permissions.forEach(permission => {\n      const node = map.get(permission.id);\n      if (permission.parentId && map.has(permission.parentId)) {\n        map.get(permission.parentId).children.push(node);\n      } else {\n        roots.push(node);\n      }\n    });\n    return roots;\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增角色\n  const handleAdd = () => {\n    setEditingRole(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑角色\n  const handleEdit = role => {\n    setEditingRole(role);\n    form.setFieldsValue({\n      ...role,\n      status: role.status === 1\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除角色\n  const handleDelete = async id => {\n    try {\n      const response = await roleService.deleteRole(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchRoles();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存角色\n  const handleSave = async values => {\n    try {\n      const roleData = {\n        ...values,\n        status: values.status ? 1 : 0\n      };\n      let response;\n      if (editingRole) {\n        response = await roleService.updateRole(editingRole.id, roleData);\n      } else {\n        response = await roleService.createRole(roleData);\n      }\n      if (response.success) {\n        message.success(editingRole ? '更新成功' : '创建成功');\n        setIsModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '保存失败');\n      }\n    } catch (error) {\n      message.error(error.message || '保存失败');\n    }\n  };\n\n  // 配置权限\n  const handleConfigPermissions = role => {\n    setSelectedRole(role);\n    setCheckedPermissions(role.permissions || []);\n    setIsPermissionModalVisible(true);\n  };\n\n  // 保存权限配置\n  const handleSavePermissions = async () => {\n    if (!selectedRole) return;\n    try {\n      const response = await roleService.updateRolePermissions(selectedRole.id, checkedPermissions);\n      if (response.success) {\n        message.success('权限配置成功');\n        setIsPermissionModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '权限配置失败');\n      }\n    } catch (error) {\n      message.error(error.message || '权限配置失败');\n    }\n  };\n\n  // 转换权限数据为树形结构\n  const convertPermissionsToTreeData = permissions => {\n    return permissions.map(permission => ({\n      title: permission.name,\n      key: permission.id,\n      children: permission.children ? convertPermissionsToTreeData(permission.children) : undefined\n    }));\n  };\n\n  // 权限树选择处理\n  const handlePermissionCheck = checkedKeys => {\n    if (Array.isArray(checkedKeys)) {\n      setCheckedPermissions(checkedKeys);\n    } else {\n      setCheckedPermissions(checkedKeys.checked);\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '角色名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 120\n  }, {\n    title: '角色编码',\n    dataIndex: 'code',\n    key: 'code',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 313,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    width: 200,\n    render: text => text || '-'\n  }, {\n    title: '用户数量',\n    dataIndex: 'userCount',\n    key: 'userCount',\n    width: 100,\n    render: count => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }, this), count]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 328,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 1 ? 'green' : 'red',\n      children: status === 1 ? '启用' : '禁用'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 362,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleConfigPermissions(record),\n        children: \"\\u914D\\u7F6E\\u6743\\u9650\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 359,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 370,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 367,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u89D2\\u8272\\u5417\\uFF1F\",\n        description: \"\\u5220\\u9664\\u540E\\u8BE5\\u89D2\\u8272\\u4E0B\\u7684\\u7528\\u6237\\u5C06\\u5931\\u53BB\\u76F8\\u5E94\\u6743\\u9650\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 386,\n            columnNumber: 21\n          }, this),\n          disabled: record.userCount > 0,\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 382,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 375,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 358,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"role-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u89D2\\u8272\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u540D\\u79F0\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 411,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 410,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 409,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u7F16\\u7801\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 416,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 415,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: 1,\n                  children: \"\\u542F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 422,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 0,\n                  children: \"\\u7981\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 423,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 421,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 420,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 419,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 430,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 430,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 433,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 429,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 428,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 427,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 408,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 402,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u65B0\\u589E\\u89D2\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 447,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 446,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 457,\n              columnNumber: 21\n            }, this),\n            onClick: fetchRoles,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 456,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 455,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 445,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 444,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: roles,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1000\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingRole ? '编辑角色' : '新增角色',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入角色名称'\n              }, {\n                max: 50,\n                message: '角色名称不能超过50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 519,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 511,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入角色编码'\n              }, {\n                pattern: /^[A-Z_]+$/,\n                message: '角色编码只能包含大写字母和下划线'\n              }, {\n                max: 50,\n                message: '角色编码不能超过50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u7F16\\u7801\",\n                disabled: !!editingRole\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 532,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 509,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u89D2\\u8272\\u63CF\\u8FF0\",\n          rules: [{\n            max: 200,\n            message: '角色描述不能超过200个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u63CF\\u8FF0\",\n            rows: 4,\n            showCount: true,\n            maxLength: 200\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 537,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Switch, {\n            checkedChildren: \"\\u542F\\u7528\",\n            unCheckedChildren: \"\\u7981\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 557,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 552,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingRole ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 565,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 503,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 495,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `配置权限 - ${selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name}`,\n      open: isPermissionModalVisible,\n      onCancel: () => setIsPermissionModalVisible(false),\n      onOk: handleSavePermissions,\n      width: 800,\n      destroyOnClose: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Descriptions, {\n          size: \"small\",\n          column: 2,\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.code\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 585,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7528\\u6237\\u6570\\u91CF\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.userCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 586,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5F53\\u524D\\u6743\\u9650\\u6570\\u91CF\",\n            children: checkedPermissions.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 583,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 582,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: '1px solid #d9d9d9',\n          borderRadius: 6,\n          padding: 16,\n          maxHeight: 400,\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tree, {\n          checkable: true,\n          checkedKeys: checkedPermissions,\n          expandedKeys: expandedKeys,\n          onCheck: handlePermissionCheck,\n          onExpand: setExpandedKeys,\n          treeData: convertPermissionsToTreeData(permissions),\n          height: 350\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 592,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 591,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 574,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 398,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleManagement, \"pWX0hyl+c2/X5FvYRQEgQ/DVS6A=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = RoleManagement;\nexport default RoleManagement;\nvar _c;\n$RefreshReg$(_c, \"RoleManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Modal", "Form", "message", "Popconfirm", "Typography", "Row", "Col", "Tree", "Switch", "Tag", "Descriptions", "Select", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "SettingOutlined", "ReloadOutlined", "UserOutlined", "roleService", "jsxDEV", "_jsxDEV", "Title", "Option", "TextArea", "RoleManagement", "_s", "roles", "setRoles", "permissions", "setPermissions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "isPermissionModalVisible", "setIsPermissionModalVisible", "editingRole", "setEditingRole", "selectedR<PERSON>", "setSelectedRole", "checkedPermissions", "setCheckedPermissions", "expandedKeys", "setExpandedKeys", "saving", "setSaving", "form", "useForm", "searchForm", "fetchRoles", "response", "getRoleList", "success", "processedRoles", "data", "list", "map", "role", "userCount", "error", "fetchPermissions", "getPermissionList", "rawData", "permissionList", "processedPermissions", "permission", "id", "name", "code", "type", "parentId", "parent_id", "path", "children", "permissionTree", "buildPermissionTree", "console", "Map", "roots", "for<PERSON>ach", "set", "node", "get", "has", "push", "handleSearch", "values", "handleReset", "handleAdd", "resetFields", "handleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "status", "handleDelete", "deleteRole", "handleSave", "roleData", "updateRole", "createRole", "handleConfigPermissions", "handleSavePermissions", "updateRolePermissions", "convertPermissionsToTreeData", "title", "key", "undefined", "handlePermissionCheck", "checked<PERSON>eys", "Array", "isArray", "checked", "columns", "dataIndex", "width", "render", "text", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "count", "Date", "toLocaleString", "fixed", "_", "record", "size", "icon", "onClick", "description", "onConfirm", "okText", "cancelText", "danger", "disabled", "className", "level", "layout", "onFinish", "autoComplete", "gutter", "style", "xs", "sm", "md", "<PERSON><PERSON>", "label", "placeholder", "allowClear", "value", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "open", "onCancel", "footer", "destroyOnClose", "span", "rules", "required", "max", "pattern", "rows", "showCount", "max<PERSON><PERSON><PERSON>", "valuePropName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "justifyContent", "onOk", "marginBottom", "column", "length", "border", "borderRadius", "padding", "maxHeight", "overflow", "checkable", "onCheck", "onExpand", "treeData", "height", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  Tree,\n  Switch,\n  Tag,\n  Descriptions,\n  Select,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SettingOutlined,\n  ReloadOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { DataNode } from 'antd/es/tree';\nimport { roleService } from '../../../services/roleService';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// 角色数据接口\nexport interface Role {\n  id: number;\n  name: string;\n  code: string;\n  description?: string;\n  status: number;\n  userCount: number;\n  permissions: number[];\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 权限数据接口\nexport interface Permission {\n  id: number;\n  name: string;\n  code: string;\n  type: 'menu' | 'button' | 'api';\n  parentId?: number;\n  path?: string;\n  children?: Permission[];\n}\n\n// 查询参数接口\ninterface RoleQueryParams {\n  name?: string;\n  code?: string;\n  status?: number;\n  page: number;\n  pageSize: number;\n}\n\nconst RoleManagement: React.FC = () => {\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<RoleQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null);\n  const [checkedPermissions, setCheckedPermissions] = useState<number[]>([]);\n  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const response = await roleService.getRoleList(queryParams);\n      if (response.success) {\n        // 处理后端返回的数据，添加缺失的字段\n        const processedRoles = response.data.list.map((role: any) => ({\n          ...role,\n          userCount: role.userCount || 0,\n          permissions: role.permissions || [],\n        }));\n        setRoles(processedRoles);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取角色列表失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '获取角色列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取权限列表\n  const fetchPermissions = async () => {\n    try {\n      const response = await roleService.getPermissionList();\n      if (response.success) {\n        // 处理后端返回的权限数据，转换为前端期望的格式\n        const rawData = response.data as any;\n        const permissionList = rawData.list || rawData || [];\n        const processedPermissions = permissionList.map((permission: any) => ({\n          id: permission.id,\n          name: permission.name,\n          code: permission.code,\n          type: permission.type || 'menu',\n          parentId: permission.parent_id,\n          path: permission.path,\n          children: [],\n        }));\n\n        // 构建树形结构\n        const permissionTree = buildPermissionTree(processedPermissions);\n        setPermissions(permissionTree);\n      }\n    } catch (error: any) {\n      console.error('获取权限列表失败:', error);\n    }\n  };\n\n  // 构建权限树形结构\n  const buildPermissionTree = (permissions: any[]): Permission[] => {\n    const map = new Map();\n    const roots: Permission[] = [];\n\n    // 创建映射\n    permissions.forEach(permission => {\n      map.set(permission.id, { ...permission, children: [] });\n    });\n\n    // 构建树形结构\n    permissions.forEach(permission => {\n      const node = map.get(permission.id);\n      if (permission.parentId && map.has(permission.parentId)) {\n        map.get(permission.parentId).children.push(node);\n      } else {\n        roots.push(node);\n      }\n    });\n\n    return roots;\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增角色\n  const handleAdd = () => {\n    setEditingRole(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑角色\n  const handleEdit = (role: Role) => {\n    setEditingRole(role);\n    form.setFieldsValue({\n      ...role,\n      status: role.status === 1,\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除角色\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await roleService.deleteRole(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchRoles();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存角色\n  const handleSave = async (values: any) => {\n    try {\n      const roleData = {\n        ...values,\n        status: values.status ? 1 : 0,\n      };\n\n      let response;\n      if (editingRole) {\n        response = await roleService.updateRole(editingRole.id, roleData);\n      } else {\n        response = await roleService.createRole(roleData);\n      }\n\n      if (response.success) {\n        message.success(editingRole ? '更新成功' : '创建成功');\n        setIsModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '保存失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '保存失败');\n    }\n  };\n\n  // 配置权限\n  const handleConfigPermissions = (role: Role) => {\n    setSelectedRole(role);\n    setCheckedPermissions(role.permissions || []);\n    setIsPermissionModalVisible(true);\n  };\n\n  // 保存权限配置\n  const handleSavePermissions = async () => {\n    if (!selectedRole) return;\n\n    try {\n      const response = await roleService.updateRolePermissions(\n        selectedRole.id,\n        checkedPermissions\n      );\n      if (response.success) {\n        message.success('权限配置成功');\n        setIsPermissionModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '权限配置失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '权限配置失败');\n    }\n  };\n\n  // 转换权限数据为树形结构\n  const convertPermissionsToTreeData = (permissions: Permission[]): DataNode[] => {\n    return permissions.map(permission => ({\n      title: permission.name,\n      key: permission.id,\n      children: permission.children ? convertPermissionsToTreeData(permission.children) : undefined,\n    }));\n  };\n\n  // 权限树选择处理\n  const handlePermissionCheck = (checkedKeys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {\n    if (Array.isArray(checkedKeys)) {\n      setCheckedPermissions(checkedKeys as number[]);\n    } else {\n      setCheckedPermissions(checkedKeys.checked as number[]);\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Role> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '角色名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 120,\n    },\n    {\n      title: '角色编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 120,\n      render: (text: string) => <Tag color=\"blue\">{text}</Tag>,\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      width: 200,\n      render: (text: string) => text || '-',\n    },\n    {\n      title: '用户数量',\n      dataIndex: 'userCount',\n      key: 'userCount',\n      width: 100,\n      render: (count: number) => (\n        <Space>\n          <UserOutlined />\n          {count}\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: number) => (\n        <Tag color={status === 1 ? 'green' : 'red'}>\n          {status === 1 ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right',\n      render: (_, record: Role) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<SettingOutlined />}\n            onClick={() => handleConfigPermissions(record)}\n          >\n            配置权限\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个角色吗？\"\n            description=\"删除后该角色下的用户将失去相应权限\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              disabled={record.userCount > 0}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"role-management-container\">\n      <Title level={2}>角色管理</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"name\" label=\"角色名称\">\n                <Input placeholder=\"请输入角色名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"code\" label=\"角色编码\">\n                <Input placeholder=\"请输入角色编码\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"状态\">\n                <Select placeholder=\"请选择状态\" allowClear>\n                  <Option value={1}>启用</Option>\n                  <Option value={0}>禁用</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              新增角色\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchRoles}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 角色列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={roles}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1000 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 角色编辑模态框 */}\n      <Modal\n        title={editingRole ? '编辑角色' : '新增角色'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"角色名称\"\n                rules={[\n                  { required: true, message: '请输入角色名称' },\n                  { max: 50, message: '角色名称不能超过50个字符' },\n                ]}\n              >\n                <Input placeholder=\"请输入角色名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"code\"\n                label=\"角色编码\"\n                rules={[\n                  { required: true, message: '请输入角色编码' },\n                  { pattern: /^[A-Z_]+$/, message: '角色编码只能包含大写字母和下划线' },\n                  { max: 50, message: '角色编码不能超过50个字符' },\n                ]}\n              >\n                <Input placeholder=\"请输入角色编码\" disabled={!!editingRole} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"角色描述\"\n            rules={[\n              { max: 200, message: '角色描述不能超过200个字符' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入角色描述\"\n              rows={4}\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            valuePropName=\"checked\"\n          >\n            <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n          </Form.Item>\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setIsModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingRole ? '更新' : '创建'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 权限配置模态框 */}\n      <Modal\n        title={`配置权限 - ${selectedRole?.name}`}\n        open={isPermissionModalVisible}\n        onCancel={() => setIsPermissionModalVisible(false)}\n        onOk={handleSavePermissions}\n        width={800}\n        destroyOnClose\n      >\n        <div style={{ marginBottom: 16 }}>\n          <Descriptions size=\"small\" column={2}>\n            <Descriptions.Item label=\"角色名称\">{selectedRole?.name}</Descriptions.Item>\n            <Descriptions.Item label=\"角色编码\">{selectedRole?.code}</Descriptions.Item>\n            <Descriptions.Item label=\"用户数量\">{selectedRole?.userCount}</Descriptions.Item>\n            <Descriptions.Item label=\"当前权限数量\">{checkedPermissions.length}</Descriptions.Item>\n          </Descriptions>\n        </div>\n\n        <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16, maxHeight: 400, overflow: 'auto' }}>\n          <Tree\n            checkable\n            checkedKeys={checkedPermissions}\n            expandedKeys={expandedKeys}\n            onCheck={handlePermissionCheck}\n            onExpand={setExpandedKeys}\n            treeData={convertPermissionsToTreeData(permissions)}\n            height={350}\n          />\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default RoleManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,YAAY,EACZC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,YAAY,QACP,mBAAmB;AAG1B,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGlB,UAAU;AAC5B,MAAM;EAAEmB;AAAO,CAAC,GAAGZ,MAAM;AACzB,MAAM;EAAEa;AAAS,CAAC,GAAGzB,KAAK;;AAE1B;;AAaA;;AAWA;;AASA,MAAM0B,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAkB;IAC9D4C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACsD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvD,QAAQ,CAAW,EAAE,CAAC;EAC1E,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAc,EAAE,CAAC;EACjE,MAAM,CAAC0D,MAAM,EAAEC,SAAS,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC4D,IAAI,CAAC,GAAGpD,IAAI,CAACqD,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAGtD,IAAI,CAACqD,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMyB,QAAQ,GAAG,MAAMtC,WAAW,CAACuC,WAAW,CAACvB,WAAW,CAAC;MAC3D,IAAIsB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAEC,IAAS,KAAM;UAC5D,GAAGA,IAAI;UACPC,SAAS,EAAED,IAAI,CAACC,SAAS,IAAI,CAAC;UAC9BpC,WAAW,EAAEmC,IAAI,CAACnC,WAAW,IAAI;QACnC,CAAC,CAAC,CAAC;QACHD,QAAQ,CAACgC,cAAc,CAAC;QACxB1B,QAAQ,CAACuB,QAAQ,CAACI,IAAI,CAAC5B,KAAK,CAAC;MAC/B,CAAC,MAAM;QACL/B,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,UAAU,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACR8B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMmC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMV,QAAQ,GAAG,MAAMtC,WAAW,CAACiD,iBAAiB,CAAC,CAAC;MACtD,IAAIX,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMU,OAAO,GAAGZ,QAAQ,CAACI,IAAW;QACpC,MAAMS,cAAc,GAAGD,OAAO,CAACP,IAAI,IAAIO,OAAO,IAAI,EAAE;QACpD,MAAME,oBAAoB,GAAGD,cAAc,CAACP,GAAG,CAAES,UAAe,KAAM;UACpEC,EAAE,EAAED,UAAU,CAACC,EAAE;UACjBC,IAAI,EAAEF,UAAU,CAACE,IAAI;UACrBC,IAAI,EAAEH,UAAU,CAACG,IAAI;UACrBC,IAAI,EAAEJ,UAAU,CAACI,IAAI,IAAI,MAAM;UAC/BC,QAAQ,EAAEL,UAAU,CAACM,SAAS;UAC9BC,IAAI,EAAEP,UAAU,CAACO,IAAI;UACrBC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,cAAc,GAAGC,mBAAmB,CAACX,oBAAoB,CAAC;QAChEzC,cAAc,CAACmD,cAAc,CAAC;MAChC;IACF,CAAC,CAAC,OAAOf,KAAU,EAAE;MACnBiB,OAAO,CAACjB,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMgB,mBAAmB,GAAIrD,WAAkB,IAAmB;IAChE,MAAMkC,GAAG,GAAG,IAAIqB,GAAG,CAAC,CAAC;IACrB,MAAMC,KAAmB,GAAG,EAAE;;IAE9B;IACAxD,WAAW,CAACyD,OAAO,CAACd,UAAU,IAAI;MAChCT,GAAG,CAACwB,GAAG,CAACf,UAAU,CAACC,EAAE,EAAE;QAAE,GAAGD,UAAU;QAAEQ,QAAQ,EAAE;MAAG,CAAC,CAAC;IACzD,CAAC,CAAC;;IAEF;IACAnD,WAAW,CAACyD,OAAO,CAACd,UAAU,IAAI;MAChC,MAAMgB,IAAI,GAAGzB,GAAG,CAAC0B,GAAG,CAACjB,UAAU,CAACC,EAAE,CAAC;MACnC,IAAID,UAAU,CAACK,QAAQ,IAAId,GAAG,CAAC2B,GAAG,CAAClB,UAAU,CAACK,QAAQ,CAAC,EAAE;QACvDd,GAAG,CAAC0B,GAAG,CAACjB,UAAU,CAACK,QAAQ,CAAC,CAACG,QAAQ,CAACW,IAAI,CAACH,IAAI,CAAC;MAClD,CAAC,MAAM;QACLH,KAAK,CAACM,IAAI,CAACH,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,OAAOH,KAAK;EACd,CAAC;;EAED;EACA3F,SAAS,CAAC,MAAM;IACd8D,UAAU,CAAC,CAAC;IACZW,gBAAgB,CAAC,CAAC;IACpB;EACA,CAAC,EAAE,CAAChC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMyD,YAAY,GAAIC,MAAW,IAAK;IACpCzD,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAG0D,MAAM;MACTxD,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyD,WAAW,GAAGA,CAAA,KAAM;IACxB1D,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMyD,SAAS,GAAGA,CAAA,KAAM;IACtBnD,cAAc,CAAC,IAAI,CAAC;IACpBS,IAAI,CAAC2C,WAAW,CAAC,CAAC;IAClBxD,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMyD,UAAU,GAAIjC,IAAU,IAAK;IACjCpB,cAAc,CAACoB,IAAI,CAAC;IACpBX,IAAI,CAAC6C,cAAc,CAAC;MAClB,GAAGlC,IAAI;MACPmC,MAAM,EAAEnC,IAAI,CAACmC,MAAM,KAAK;IAC1B,CAAC,CAAC;IACF3D,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM4D,YAAY,GAAG,MAAO3B,EAAU,IAAK;IACzC,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMtC,WAAW,CAACkF,UAAU,CAAC5B,EAAE,CAAC;MACjD,IAAIhB,QAAQ,CAACE,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAC,MAAM,CAAC;QACvBH,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLtD,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMoG,UAAU,GAAG,MAAOT,MAAW,IAAK;IACxC,IAAI;MACF,MAAMU,QAAQ,GAAG;QACf,GAAGV,MAAM;QACTM,MAAM,EAAEN,MAAM,CAACM,MAAM,GAAG,CAAC,GAAG;MAC9B,CAAC;MAED,IAAI1C,QAAQ;MACZ,IAAId,WAAW,EAAE;QACfc,QAAQ,GAAG,MAAMtC,WAAW,CAACqF,UAAU,CAAC7D,WAAW,CAAC8B,EAAE,EAAE8B,QAAQ,CAAC;MACnE,CAAC,MAAM;QACL9C,QAAQ,GAAG,MAAMtC,WAAW,CAACsF,UAAU,CAACF,QAAQ,CAAC;MACnD;MAEA,IAAI9C,QAAQ,CAACE,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAChB,WAAW,GAAG,MAAM,GAAG,MAAM,CAAC;QAC9CH,iBAAiB,CAAC,KAAK,CAAC;QACxBgB,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLtD,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMwG,uBAAuB,GAAI1C,IAAU,IAAK;IAC9ClB,eAAe,CAACkB,IAAI,CAAC;IACrBhB,qBAAqB,CAACgB,IAAI,CAACnC,WAAW,IAAI,EAAE,CAAC;IAC7Ca,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;;EAED;EACA,MAAMiE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAAC9D,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMtC,WAAW,CAACyF,qBAAqB,CACtD/D,YAAY,CAAC4B,EAAE,EACf1B,kBACF,CAAC;MACD,IAAIU,QAAQ,CAACE,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAC,QAAQ,CAAC;QACzBjB,2BAA2B,CAAC,KAAK,CAAC;QAClCc,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLtD,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAM2G,4BAA4B,GAAIhF,WAAyB,IAAiB;IAC9E,OAAOA,WAAW,CAACkC,GAAG,CAACS,UAAU,KAAK;MACpCsC,KAAK,EAAEtC,UAAU,CAACE,IAAI;MACtBqC,GAAG,EAAEvC,UAAU,CAACC,EAAE;MAClBO,QAAQ,EAAER,UAAU,CAACQ,QAAQ,GAAG6B,4BAA4B,CAACrC,UAAU,CAACQ,QAAQ,CAAC,GAAGgC;IACtF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,WAA6E,IAAK;IAC/G,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;MAC9BlE,qBAAqB,CAACkE,WAAuB,CAAC;IAChD,CAAC,MAAM;MACLlE,qBAAqB,CAACkE,WAAW,CAACG,OAAmB,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMC,OAA0B,GAAG,CACjC;IACER,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,IAAI;IACfR,GAAG,EAAE,IAAI;IACTS,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,MAAM;IACjBR,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,MAAM;IACjBR,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBAAKrG,OAAA,CAACZ,GAAG;MAACkH,KAAK,EAAC,MAAM;MAAA3C,QAAA,EAAE0C;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACzD,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,aAAa;IACxBR,GAAG,EAAE,aAAa;IAClBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAKA,IAAI,IAAI;EACpC,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,WAAW;IACtBR,GAAG,EAAE,WAAW;IAChBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGO,KAAa,iBACpB3G,OAAA,CAACvB,KAAK;MAAAkF,QAAA,gBACJ3D,OAAA,CAACH,YAAY;QAAA0G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACfC,KAAK;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,QAAQ;IACnBR,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGtB,MAAc,iBACrB9E,OAAA,CAACZ,GAAG;MAACkH,KAAK,EAAExB,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,KAAM;MAAAnB,QAAA,EACxCmB,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG;IAAI;MAAAyB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,WAAW;IACtBR,GAAG,EAAE,WAAW;IAChBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAK,IAAIO,IAAI,CAACP,IAAI,CAAC,CAACQ,cAAc,CAAC;EAC1D,CAAC,EACD;IACEpB,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,GAAG;IACVW,KAAK,EAAE,OAAO;IACdV,MAAM,EAAEA,CAACW,CAAC,EAAEC,MAAY,kBACtBhH,OAAA,CAACvB,KAAK;MAACwI,IAAI,EAAC,OAAO;MAAAtD,QAAA,gBACjB3D,OAAA,CAACxB,MAAM;QACL+E,IAAI,EAAC,MAAM;QACX0D,IAAI,EAAC,OAAO;QACZC,IAAI,eAAElH,OAAA,CAACL,eAAe;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1BS,OAAO,EAAEA,CAAA,KAAM9B,uBAAuB,CAAC2B,MAAM,CAAE;QAAArD,QAAA,EAChD;MAED;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1G,OAAA,CAACxB,MAAM;QACL+E,IAAI,EAAC,MAAM;QACX0D,IAAI,EAAC,OAAO;QACZC,IAAI,eAAElH,OAAA,CAACP,YAAY;UAAA8G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBS,OAAO,EAAEA,CAAA,KAAMvC,UAAU,CAACoC,MAAM,CAAE;QAAArD,QAAA,EACnC;MAED;QAAA4C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT1G,OAAA,CAAClB,UAAU;QACT2G,KAAK,EAAC,oEAAa;QACnB2B,WAAW,EAAC,wGAAmB;QAC/BC,SAAS,EAAEA,CAAA,KAAMtC,YAAY,CAACiC,MAAM,CAAC5D,EAAE,CAAE;QACzCkE,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAA5D,QAAA,eAEf3D,OAAA,CAACxB,MAAM;UACL+E,IAAI,EAAC,MAAM;UACX0D,IAAI,EAAC,OAAO;UACZO,MAAM;UACNN,IAAI,eAAElH,OAAA,CAACN,cAAc;YAAA6G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBe,QAAQ,EAAET,MAAM,CAACpE,SAAS,GAAG,CAAE;UAAAe,QAAA,EAChC;QAED;UAAA4C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE1G,OAAA;IAAK0H,SAAS,EAAC,2BAA2B;IAAA/D,QAAA,gBACxC3D,OAAA,CAACC,KAAK;MAAC0H,KAAK,EAAE,CAAE;MAAAhE,QAAA,EAAC;IAAI;MAAA4C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7B1G,OAAA,CAAC1B,IAAI;MAACoJ,SAAS,EAAC,aAAa;MAACT,IAAI,EAAC,OAAO;MAAAtD,QAAA,eACxC3D,OAAA,CAACpB,IAAI;QACHgJ,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEtD,YAAa;QACvBuD,YAAY,EAAC,KAAK;QAAAnE,QAAA,eAElB3D,OAAA,CAAChB,GAAG;UAAC+I,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACC,KAAK,EAAE;YAAE7B,KAAK,EAAE;UAAO,CAAE;UAAAxC,QAAA,gBAC9C3D,OAAA,CAACf,GAAG;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxE,QAAA,eACzB3D,OAAA,CAACpB,IAAI,CAACwJ,IAAI;cAAC/E,IAAI,EAAC,MAAM;cAACgF,KAAK,EAAC,0BAAM;cAAA1E,QAAA,eACjC3D,OAAA,CAACtB,KAAK;gBAAC4J,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1G,OAAA,CAACf,GAAG;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxE,QAAA,eACzB3D,OAAA,CAACpB,IAAI,CAACwJ,IAAI;cAAC/E,IAAI,EAAC,MAAM;cAACgF,KAAK,EAAC,0BAAM;cAAA1E,QAAA,eACjC3D,OAAA,CAACtB,KAAK;gBAAC4J,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1G,OAAA,CAACf,GAAG;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxE,QAAA,eACzB3D,OAAA,CAACpB,IAAI,CAACwJ,IAAI;cAAC/E,IAAI,EAAC,QAAQ;cAACgF,KAAK,EAAC,cAAI;cAAA1E,QAAA,eACjC3D,OAAA,CAACV,MAAM;gBAACgJ,WAAW,EAAC,gCAAO;gBAACC,UAAU;gBAAA5E,QAAA,gBACpC3D,OAAA,CAACE,MAAM;kBAACsI,KAAK,EAAE,CAAE;kBAAA7E,QAAA,EAAC;gBAAE;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7B1G,OAAA,CAACE,MAAM;kBAACsI,KAAK,EAAE,CAAE;kBAAA7E,QAAA,EAAC;gBAAE;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1G,OAAA,CAACf,GAAG;YAACgJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAxE,QAAA,eACzB3D,OAAA,CAACpB,IAAI,CAACwJ,IAAI;cAAAzE,QAAA,eACR3D,OAAA,CAACvB,KAAK;gBAAAkF,QAAA,gBACJ3D,OAAA,CAACxB,MAAM;kBAAC+E,IAAI,EAAC,SAAS;kBAACkF,QAAQ,EAAC,QAAQ;kBAACvB,IAAI,eAAElH,OAAA,CAACR,cAAc;oBAAA+G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAA/C,QAAA,EAAC;gBAEnE;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT1G,OAAA,CAACxB,MAAM;kBAAC2I,OAAO,EAAE1C,WAAY;kBAACyC,IAAI,eAAElH,OAAA,CAACJ,cAAc;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAA/C,QAAA,EAAC;gBAExD;kBAAA4C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP1G,OAAA,CAAC1B,IAAI;MAACoJ,SAAS,EAAC,aAAa;MAACT,IAAI,EAAC,OAAO;MAAAtD,QAAA,eACxC3D,OAAA,CAAChB,GAAG;QAAC0J,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAhF,QAAA,gBACzC3D,OAAA,CAACf,GAAG;UAAA0E,QAAA,eACF3D,OAAA,CAACxB,MAAM;YACL+E,IAAI,EAAC,SAAS;YACd2D,IAAI,eAAElH,OAAA,CAACT,YAAY;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAEzC,SAAU;YAAAf,QAAA,EACpB;UAED;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN1G,OAAA,CAACf,GAAG;UAAA0E,QAAA,eACF3D,OAAA,CAACxB,MAAM;YACL0I,IAAI,eAAElH,OAAA,CAACJ,cAAc;cAAA2G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBS,OAAO,EAAEhF,UAAW;YACpBzB,OAAO,EAAEA,OAAQ;YAAAiD,QAAA,EAClB;UAED;YAAA4C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP1G,OAAA,CAAC1B,IAAI;MAAAqF,QAAA,eACH3D,OAAA,CAACzB,KAAK;QACJ0H,OAAO,EAAEA,OAAQ;QACjB2C,UAAU,EAAEtI,KAAM;QAClBuI,MAAM,EAAC,IAAI;QACXnI,OAAO,EAAEA,OAAQ;QACjBoI,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAEnI,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZsI,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACxI,KAAK,EAAEyI,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQzI,KAAK,IAAI;UAC5C0I,QAAQ,EAAEA,CAACtI,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAAsF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP1G,OAAA,CAACrB,KAAK;MACJ8G,KAAK,EAAEnE,WAAW,GAAG,MAAM,GAAG,MAAO;MACrCiI,IAAI,EAAErI,cAAe;MACrBsI,QAAQ,EAAEA,CAAA,KAAMrI,iBAAiB,CAAC,KAAK,CAAE;MACzCsI,MAAM,EAAE,IAAK;MACbtD,KAAK,EAAE,GAAI;MACXuD,cAAc;MAAA/F,QAAA,eAEd3D,OAAA,CAACpB,IAAI;QACHoD,IAAI,EAAEA,IAAK;QACX4F,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE5C,UAAW;QACrB6C,YAAY,EAAC,KAAK;QAAAnE,QAAA,gBAElB3D,OAAA,CAAChB,GAAG;UAAC+I,MAAM,EAAE,EAAG;UAAApE,QAAA,gBACd3D,OAAA,CAACf,GAAG;YAAC0K,IAAI,EAAE,EAAG;YAAAhG,QAAA,eACZ3D,OAAA,CAACpB,IAAI,CAACwJ,IAAI;cACR/E,IAAI,EAAC,MAAM;cACXgF,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhL,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEiL,GAAG,EAAE,EAAE;gBAAEjL,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAA8E,QAAA,eAEF3D,OAAA,CAACtB,KAAK;gBAAC4J,WAAW,EAAC;cAAS;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN1G,OAAA,CAACf,GAAG;YAAC0K,IAAI,EAAE,EAAG;YAAAhG,QAAA,eACZ3D,OAAA,CAACpB,IAAI,CAACwJ,IAAI;cACR/E,IAAI,EAAC,MAAM;cACXgF,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEhL,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEkL,OAAO,EAAE,WAAW;gBAAElL,OAAO,EAAE;cAAmB,CAAC,EACrD;gBAAEiL,GAAG,EAAE,EAAE;gBAAEjL,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAA8E,QAAA,eAEF3D,OAAA,CAACtB,KAAK;gBAAC4J,WAAW,EAAC,4CAAS;gBAACb,QAAQ,EAAE,CAAC,CAACnG;cAAY;gBAAAiF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN1G,OAAA,CAACpB,IAAI,CAACwJ,IAAI;UACR/E,IAAI,EAAC,aAAa;UAClBgF,KAAK,EAAC,0BAAM;UACZuB,KAAK,EAAE,CACL;YAAEE,GAAG,EAAE,GAAG;YAAEjL,OAAO,EAAE;UAAiB,CAAC,CACvC;UAAA8E,QAAA,eAEF3D,OAAA,CAACG,QAAQ;YACPmI,WAAW,EAAC,4CAAS;YACrB0B,IAAI,EAAE,CAAE;YACRC,SAAS;YACTC,SAAS,EAAE;UAAI;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ1G,OAAA,CAACpB,IAAI,CAACwJ,IAAI;UACR/E,IAAI,EAAC,QAAQ;UACbgF,KAAK,EAAC,cAAI;UACV8B,aAAa,EAAC,SAAS;UAAAxG,QAAA,eAEvB3D,OAAA,CAACb,MAAM;YAACiL,eAAe,EAAC,cAAI;YAACC,iBAAiB,EAAC;UAAI;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEZ1G,OAAA,CAACpB,IAAI,CAACwJ,IAAI;UAAAzE,QAAA,eACR3D,OAAA,CAACvB,KAAK;YAACuJ,KAAK,EAAE;cAAE7B,KAAK,EAAE,MAAM;cAAEmE,cAAc,EAAE;YAAW,CAAE;YAAA3G,QAAA,gBAC1D3D,OAAA,CAACxB,MAAM;cAAC2I,OAAO,EAAEA,CAAA,KAAMhG,iBAAiB,CAAC,KAAK,CAAE;cAAAwC,QAAA,EAAC;YAEjD;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT1G,OAAA,CAACxB,MAAM;cAAC+E,IAAI,EAAC,SAAS;cAACkF,QAAQ,EAAC,QAAQ;cAAA9E,QAAA,EACrCrC,WAAW,GAAG,IAAI,GAAG;YAAI;cAAAiF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR1G,OAAA,CAACrB,KAAK;MACJ8G,KAAK,EAAE,UAAUjE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6B,IAAI,EAAG;MACtCkG,IAAI,EAAEnI,wBAAyB;MAC/BoI,QAAQ,EAAEA,CAAA,KAAMnI,2BAA2B,CAAC,KAAK,CAAE;MACnDkJ,IAAI,EAAEjF,qBAAsB;MAC5Ba,KAAK,EAAE,GAAI;MACXuD,cAAc;MAAA/F,QAAA,gBAEd3D,OAAA;QAAKgI,KAAK,EAAE;UAAEwC,YAAY,EAAE;QAAG,CAAE;QAAA7G,QAAA,eAC/B3D,OAAA,CAACX,YAAY;UAAC4H,IAAI,EAAC,OAAO;UAACwD,MAAM,EAAE,CAAE;UAAA9G,QAAA,gBACnC3D,OAAA,CAACX,YAAY,CAAC+I,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA1E,QAAA,EAAEnC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE6B;UAAI;YAAAkD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACxE1G,OAAA,CAACX,YAAY,CAAC+I,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA1E,QAAA,EAAEnC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE8B;UAAI;YAAAiD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACxE1G,OAAA,CAACX,YAAY,CAAC+I,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA1E,QAAA,EAAEnC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoB;UAAS;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC7E1G,OAAA,CAACX,YAAY,CAAC+I,IAAI;YAACC,KAAK,EAAC,sCAAQ;YAAA1E,QAAA,EAAEjC,kBAAkB,CAACgJ;UAAM;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEN1G,OAAA;QAAKgI,KAAK,EAAE;UAAE2C,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE,CAAC;UAAEC,OAAO,EAAE,EAAE;UAAEC,SAAS,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAApH,QAAA,eAC1G3D,OAAA,CAACd,IAAI;UACH8L,SAAS;UACTnF,WAAW,EAAEnE,kBAAmB;UAChCE,YAAY,EAAEA,YAAa;UAC3BqJ,OAAO,EAAErF,qBAAsB;UAC/BsF,QAAQ,EAAErJ,eAAgB;UAC1BsJ,QAAQ,EAAE3F,4BAA4B,CAAChF,WAAW,CAAE;UACpD4K,MAAM,EAAE;QAAI;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAACrG,EAAA,CArhBID,cAAwB;EAAA,QAgBbxB,IAAI,CAACqD,OAAO,EACNrD,IAAI,CAACqD,OAAO;AAAA;AAAAoJ,EAAA,GAjB7BjL,cAAwB;AAuhB9B,eAAeA,cAAc;AAAC,IAAAiL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}