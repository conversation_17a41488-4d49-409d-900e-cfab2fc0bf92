{"ast": null, "code": "import { apiClient } from './apiClient';\nexport const productService = {\n  // 获取商品列表\n  getProductList: async params => {\n    try {\n      const response = await apiClient.get('/products', {\n        params\n      });\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response, _error$response$data, _error$response2, _error$response2$data;\n      const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.error) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || '获取商品列表失败';\n      return {\n        success: false,\n        data: {\n          list: [],\n          total: 0,\n          page: 1,\n          pageSize: 10\n        },\n        message: errorMessage\n      };\n    }\n  },\n  // 创建商品\n  createProduct: async productData => {\n    try {\n      const response = await apiClient.post('/products', productData);\n      return response.data;\n    } catch (error) {\n      var _error$response3, _error$response3$data;\n      throw new Error(((_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : (_error$response3$data = _error$response3.data) === null || _error$response3$data === void 0 ? void 0 : _error$response3$data.message) || '创建商品失败');\n    }\n  },\n  // 更新商品\n  updateProduct: async (id, productData) => {\n    try {\n      const response = await apiClient.put(`/products/${id}`, productData);\n      return response.data;\n    } catch (error) {\n      var _error$response4, _error$response4$data;\n      throw new Error(((_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : (_error$response4$data = _error$response4.data) === null || _error$response4$data === void 0 ? void 0 : _error$response4$data.message) || '更新商品失败');\n    }\n  },\n  // 删除商品\n  deleteProduct: async id => {\n    try {\n      const response = await apiClient.delete(`/products/${id}`);\n      return response.data;\n    } catch (error) {\n      var _error$response5, _error$response5$data;\n      throw new Error(((_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : (_error$response5$data = _error$response5.data) === null || _error$response5$data === void 0 ? void 0 : _error$response5$data.message) || '删除商品失败');\n    }\n  },\n  // 更新商品状态\n  updateProductStatus: async (id, status) => {\n    try {\n      const response = await apiClient.put(`/products/status/${id}`, {\n        status\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response6, _error$response6$data;\n      throw new Error(((_error$response6 = error.response) === null || _error$response6 === void 0 ? void 0 : (_error$response6$data = _error$response6.data) === null || _error$response6$data === void 0 ? void 0 : _error$response6$data.message) || '更新商品状态失败');\n    }\n  },\n  // 获取商品详情\n  getProductDetail: async id => {\n    try {\n      const response = await apiClient.get(`/products/${id}`);\n      return response.data;\n    } catch (error) {\n      var _error$response7, _error$response7$data;\n      throw new Error(((_error$response7 = error.response) === null || _error$response7 === void 0 ? void 0 : (_error$response7$data = _error$response7.data) === null || _error$response7$data === void 0 ? void 0 : _error$response7$data.message) || '获取商品详情失败');\n    }\n  },\n  // 获取商品分类列表\n  getCategoryList: async () => {\n    try {\n      const response = await apiClient.get('/categories/tree');\n      return response.data;\n    } catch (error) {\n      var _error$response8, _error$response8$data;\n      throw new Error(((_error$response8 = error.response) === null || _error$response8 === void 0 ? void 0 : (_error$response8$data = _error$response8.data) === null || _error$response8$data === void 0 ? void 0 : _error$response8$data.message) || '获取商品分类失败');\n    }\n  },\n  // 批量删除商品\n  batchDeleteProducts: async ids => {\n    try {\n      const response = await apiClient.delete('/products/batch', {\n        data: {\n          ids\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response9, _error$response9$data;\n      throw new Error(((_error$response9 = error.response) === null || _error$response9 === void 0 ? void 0 : (_error$response9$data = _error$response9.data) === null || _error$response9$data === void 0 ? void 0 : _error$response9$data.message) || '批量删除商品失败');\n    }\n  },\n  // 导出商品数据\n  exportProducts: async params => {\n    try {\n      const response = await apiClient.get('/products/export', {\n        params,\n        responseType: 'blob'\n      });\n      return {\n        success: true,\n        data: response.data\n      };\n    } catch (error) {\n      var _error$response0, _error$response0$data;\n      throw new Error(((_error$response0 = error.response) === null || _error$response0 === void 0 ? void 0 : (_error$response0$data = _error$response0.data) === null || _error$response0$data === void 0 ? void 0 : _error$response0$data.message) || '导出商品数据失败');\n    }\n  },\n  // 批量导入商品\n  importProducts: async file => {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      const response = await apiClient.post('/products/import', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response1, _error$response1$data;\n      throw new Error(((_error$response1 = error.response) === null || _error$response1 === void 0 ? void 0 : (_error$response1$data = _error$response1.data) === null || _error$response1$data === void 0 ? void 0 : _error$response1$data.message) || '导入商品数据失败');\n    }\n  },\n  // 上传商品图片\n  uploadProductImage: async file => {\n    try {\n      const formData = new FormData();\n      formData.append('image', file);\n      const response = await apiClient.post('/products/upload-image', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response10, _error$response10$dat;\n      throw new Error(((_error$response10 = error.response) === null || _error$response10 === void 0 ? void 0 : (_error$response10$dat = _error$response10.data) === null || _error$response10$dat === void 0 ? void 0 : _error$response10$dat.message) || '上传图片失败');\n    }\n  },\n  // 获取商品统计信息\n  getProductStatistics: async () => {\n    try {\n      const response = await apiClient.get('/products/statistics');\n      return response.data;\n    } catch (error) {\n      var _error$response11, _error$response11$dat;\n      throw new Error(((_error$response11 = error.response) === null || _error$response11 === void 0 ? void 0 : (_error$response11$dat = _error$response11.data) === null || _error$response11$dat === void 0 ? void 0 : _error$response11$dat.message) || '获取商品统计信息失败');\n    }\n  },\n  // 商品审核\n  auditProduct: async (id, status, reason) => {\n    try {\n      const response = await apiClient.post(`/products/${id}/audit`, {\n        status,\n        reason\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response12, _error$response12$dat;\n      throw new Error(((_error$response12 = error.response) === null || _error$response12 === void 0 ? void 0 : (_error$response12$dat = _error$response12.data) === null || _error$response12$dat === void 0 ? void 0 : _error$response12$dat.message) || '商品审核失败');\n    }\n  },\n  // 获取待审核商品列表\n  getPendingAuditProducts: async params => {\n    try {\n      const response = await apiClient.get('/products/pending-audit', {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      var _error$response13, _error$response13$dat;\n      throw new Error(((_error$response13 = error.response) === null || _error$response13 === void 0 ? void 0 : (_error$response13$dat = _error$response13.data) === null || _error$response13$dat === void 0 ? void 0 : _error$response13$dat.message) || '获取待审核商品列表失败');\n    }\n  }\n};", "map": {"version": 3, "names": ["apiClient", "productService", "getProductList", "params", "response", "get", "success", "data", "error", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "errorMessage", "message", "list", "total", "page", "pageSize", "createProduct", "productData", "post", "_error$response3", "_error$response3$data", "Error", "updateProduct", "id", "put", "_error$response4", "_error$response4$data", "deleteProduct", "delete", "_error$response5", "_error$response5$data", "updateProductStatus", "status", "_error$response6", "_error$response6$data", "getProductDetail", "_error$response7", "_error$response7$data", "getCategoryList", "_error$response8", "_error$response8$data", "batchDeleteProducts", "ids", "_error$response9", "_error$response9$data", "exportProducts", "responseType", "_error$response0", "_error$response0$data", "importProducts", "file", "formData", "FormData", "append", "headers", "_error$response1", "_error$response1$data", "uploadProductImage", "_error$response10", "_error$response10$dat", "getProductStatistics", "_error$response11", "_error$response11$dat", "auditProduct", "reason", "_error$response12", "_error$response12$dat", "getPendingAuditProducts", "_error$response13", "_error$response13$dat"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/productService.ts"], "sourcesContent": ["import { apiClient } from './apiClient';\nimport { Product, Category, QualityLevel, ProductStatus } from '../pages/Products/ProductList';\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data: T;\n  message?: string;\n}\n\nexport interface ProductListResponse {\n  list: Product[];\n  total: number;\n  page: number;\n  pageSize: number;\n}\n\nexport interface ProductQueryParams {\n  name?: string;\n  categoryId?: number;\n  qualityLevel?: QualityLevel;\n  status?: ProductStatus;\n  origin?: string;\n  page: number;\n  pageSize: number;\n}\n\nexport interface CreateProductRequest {\n  name: string;\n  categoryId: number;\n  description?: string;\n  qualityLevel: QualityLevel;\n  origin: string;\n  supplierId: number;\n  status: ProductStatus;\n  images?: string[];\n}\n\nexport interface UpdateProductRequest {\n  name: string;\n  categoryId: number;\n  description?: string;\n  qualityLevel: QualityLevel;\n  origin: string;\n  supplierId: number;\n  status: ProductStatus;\n  images?: string[];\n}\n\nexport const productService = {\n  // 获取商品列表\n  getProductList: async (params: ProductQueryParams): Promise<ApiResponse<ProductListResponse>> => {\n    try {\n      const response = await apiClient.get('/products', { params });\n      return {\n        success: true,\n        data: response.data,\n      };\n    } catch (error: any) {\n      const errorMessage = error.response?.data?.error || error.response?.data?.message || '获取商品列表失败';\n      return {\n        success: false,\n        data: { list: [], total: 0, page: 1, pageSize: 10 },\n        message: errorMessage,\n      };\n    }\n  },\n\n  // 创建商品\n  createProduct: async (productData: CreateProductRequest): Promise<ApiResponse<Product>> => {\n    try {\n      const response = await apiClient.post('/products', productData);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '创建商品失败');\n    }\n  },\n\n  // 更新商品\n  updateProduct: async (id: number, productData: UpdateProductRequest): Promise<ApiResponse<Product>> => {\n    try {\n      const response = await apiClient.put(`/products/${id}`, productData);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '更新商品失败');\n    }\n  },\n\n  // 删除商品\n  deleteProduct: async (id: number): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.delete(`/products/${id}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '删除商品失败');\n    }\n  },\n\n  // 更新商品状态\n  updateProductStatus: async (id: number, status: ProductStatus): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.put(`/products/status/${id}`, { status });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '更新商品状态失败');\n    }\n  },\n\n  // 获取商品详情\n  getProductDetail: async (id: number): Promise<ApiResponse<Product>> => {\n    try {\n      const response = await apiClient.get(`/products/${id}`);\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '获取商品详情失败');\n    }\n  },\n\n  // 获取商品分类列表\n  getCategoryList: async (): Promise<ApiResponse<Category[]>> => {\n    try {\n      const response = await apiClient.get('/categories/tree');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '获取商品分类失败');\n    }\n  },\n\n  // 批量删除商品\n  batchDeleteProducts: async (ids: number[]): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.delete('/products/batch', { data: { ids } });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '批量删除商品失败');\n    }\n  },\n\n  // 导出商品数据\n  exportProducts: async (params: ProductQueryParams): Promise<ApiResponse<Blob>> => {\n    try {\n      const response = await apiClient.get('/products/export', {\n        params,\n        responseType: 'blob',\n      });\n      return {\n        success: true,\n        data: response.data,\n      };\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '导出商品数据失败');\n    }\n  },\n\n  // 批量导入商品\n  importProducts: async (file: File): Promise<ApiResponse> => {\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      const response = await apiClient.post('/products/import', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '导入商品数据失败');\n    }\n  },\n\n  // 上传商品图片\n  uploadProductImage: async (file: File): Promise<ApiResponse<{ url: string }>> => {\n    try {\n      const formData = new FormData();\n      formData.append('image', file);\n      const response = await apiClient.post('/products/upload-image', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '上传图片失败');\n    }\n  },\n\n  // 获取商品统计信息\n  getProductStatistics: async (): Promise<ApiResponse<{\n    total: number;\n    onlineProducts: number;\n    newProductsToday: number;\n    categoryDistribution: Record<string, number>;\n    qualityDistribution: Record<QualityLevel, number>;\n  }>> => {\n    try {\n      const response = await apiClient.get('/products/statistics');\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '获取商品统计信息失败');\n    }\n  },\n\n  // 商品审核\n  auditProduct: async (id: number, status: 'approved' | 'rejected', reason?: string): Promise<ApiResponse> => {\n    try {\n      const response = await apiClient.post(`/products/${id}/audit`, { status, reason });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '商品审核失败');\n    }\n  },\n\n  // 获取待审核商品列表\n  getPendingAuditProducts: async (params: { page: number; pageSize: number }): Promise<ApiResponse<ProductListResponse>> => {\n    try {\n      const response = await apiClient.get('/products/pending-audit', { params });\n      return response.data;\n    } catch (error: any) {\n      throw new Error(error.response?.data?.message || '获取待审核商品列表失败');\n    }\n  },\n};\n"], "mappings": "AAAA,SAASA,SAAS,QAAQ,aAAa;AAgDvC,OAAO,MAAMC,cAAc,GAAG;EAC5B;EACAC,cAAc,EAAE,MAAOC,MAA0B,IAAgD;IAC/F,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,WAAW,EAAE;QAAEF;MAAO,CAAC,CAAC;MAC7D,OAAO;QACLG,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEH,QAAQ,CAACG;MACjB,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAC,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA;MACnB,MAAMC,YAAY,GAAG,EAAAJ,eAAA,GAAAD,KAAK,CAACJ,QAAQ,cAAAK,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBF,IAAI,cAAAG,oBAAA,uBAApBA,oBAAA,CAAsBF,KAAK,OAAAG,gBAAA,GAAIH,KAAK,CAACJ,QAAQ,cAAAO,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBJ,IAAI,cAAAK,qBAAA,uBAApBA,qBAAA,CAAsBE,OAAO,KAAI,UAAU;MAC/F,OAAO;QACLR,OAAO,EAAE,KAAK;QACdC,IAAI,EAAE;UAAEQ,IAAI,EAAE,EAAE;UAAEC,KAAK,EAAE,CAAC;UAAEC,IAAI,EAAE,CAAC;UAAEC,QAAQ,EAAE;QAAG,CAAC;QACnDJ,OAAO,EAAED;MACX,CAAC;IACH;EACF,CAAC;EAED;EACAM,aAAa,EAAE,MAAOC,WAAiC,IAAoC;IACzF,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMJ,SAAS,CAACqB,IAAI,CAAC,WAAW,EAAED,WAAW,CAAC;MAC/D,OAAOhB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAc,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIC,KAAK,CAAC,EAAAF,gBAAA,GAAAd,KAAK,CAACJ,QAAQ,cAAAkB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBf,IAAI,cAAAgB,qBAAA,uBAApBA,qBAAA,CAAsBT,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACAW,aAAa,EAAE,MAAAA,CAAOC,EAAU,EAAEN,WAAiC,KAAoC;IACrG,IAAI;MACF,MAAMhB,QAAQ,GAAG,MAAMJ,SAAS,CAAC2B,GAAG,CAAC,aAAaD,EAAE,EAAE,EAAEN,WAAW,CAAC;MACpE,OAAOhB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAoB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIL,KAAK,CAAC,EAAAI,gBAAA,GAAApB,KAAK,CAACJ,QAAQ,cAAAwB,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBrB,IAAI,cAAAsB,qBAAA,uBAApBA,qBAAA,CAAsBf,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACAgB,aAAa,EAAE,MAAOJ,EAAU,IAA2B;IACzD,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMJ,SAAS,CAAC+B,MAAM,CAAC,aAAaL,EAAE,EAAE,CAAC;MAC1D,OAAOtB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAwB,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIT,KAAK,CAAC,EAAAQ,gBAAA,GAAAxB,KAAK,CAACJ,QAAQ,cAAA4B,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBzB,IAAI,cAAA0B,qBAAA,uBAApBA,qBAAA,CAAsBnB,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACAoB,mBAAmB,EAAE,MAAAA,CAAOR,EAAU,EAAES,MAAqB,KAA2B;IACtF,IAAI;MACF,MAAM/B,QAAQ,GAAG,MAAMJ,SAAS,CAAC2B,GAAG,CAAC,oBAAoBD,EAAE,EAAE,EAAE;QAAES;MAAO,CAAC,CAAC;MAC1E,OAAO/B,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA4B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIb,KAAK,CAAC,EAAAY,gBAAA,GAAA5B,KAAK,CAACJ,QAAQ,cAAAgC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB7B,IAAI,cAAA8B,qBAAA,uBAApBA,qBAAA,CAAsBvB,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACAwB,gBAAgB,EAAE,MAAOZ,EAAU,IAAoC;IACrE,IAAI;MACF,MAAMtB,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,aAAaqB,EAAE,EAAE,CAAC;MACvD,OAAOtB,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA+B,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIhB,KAAK,CAAC,EAAAe,gBAAA,GAAA/B,KAAK,CAACJ,QAAQ,cAAAmC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBhC,IAAI,cAAAiC,qBAAA,uBAApBA,qBAAA,CAAsB1B,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACA2B,eAAe,EAAE,MAAAA,CAAA,KAA8C;IAC7D,IAAI;MACF,MAAMrC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,kBAAkB,CAAC;MACxD,OAAOD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAkC,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAInB,KAAK,CAAC,EAAAkB,gBAAA,GAAAlC,KAAK,CAACJ,QAAQ,cAAAsC,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnC,IAAI,cAAAoC,qBAAA,uBAApBA,qBAAA,CAAsB7B,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACA8B,mBAAmB,EAAE,MAAOC,GAAa,IAA2B;IAClE,IAAI;MACF,MAAMzC,QAAQ,GAAG,MAAMJ,SAAS,CAAC+B,MAAM,CAAC,iBAAiB,EAAE;QAAExB,IAAI,EAAE;UAAEsC;QAAI;MAAE,CAAC,CAAC;MAC7E,OAAOzC,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAsC,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIvB,KAAK,CAAC,EAAAsB,gBAAA,GAAAtC,KAAK,CAACJ,QAAQ,cAAA0C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBvC,IAAI,cAAAwC,qBAAA,uBAApBA,qBAAA,CAAsBjC,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACAkC,cAAc,EAAE,MAAO7C,MAA0B,IAAiC;IAChF,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,kBAAkB,EAAE;QACvDF,MAAM;QACN8C,YAAY,EAAE;MAChB,CAAC,CAAC;MACF,OAAO;QACL3C,OAAO,EAAE,IAAI;QACbC,IAAI,EAAEH,QAAQ,CAACG;MACjB,CAAC;IACH,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA0C,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI3B,KAAK,CAAC,EAAA0B,gBAAA,GAAA1C,KAAK,CAACJ,QAAQ,cAAA8C,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgB3C,IAAI,cAAA4C,qBAAA,uBAApBA,qBAAA,CAAsBrC,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACAsC,cAAc,EAAE,MAAOC,IAAU,IAA2B;IAC1D,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,IAAI,CAAC;MAC7B,MAAMjD,QAAQ,GAAG,MAAMJ,SAAS,CAACqB,IAAI,CAAC,kBAAkB,EAAEiC,QAAQ,EAAE;QAClEG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOrD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAkD,gBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAInC,KAAK,CAAC,EAAAkC,gBAAA,GAAAlD,KAAK,CAACJ,QAAQ,cAAAsD,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBnD,IAAI,cAAAoD,qBAAA,uBAApBA,qBAAA,CAAsB7C,OAAO,KAAI,UAAU,CAAC;IAC9D;EACF,CAAC;EAED;EACA8C,kBAAkB,EAAE,MAAOP,IAAU,IAA4C;IAC/E,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,OAAO,EAAEH,IAAI,CAAC;MAC9B,MAAMjD,QAAQ,GAAG,MAAMJ,SAAS,CAACqB,IAAI,CAAC,wBAAwB,EAAEiC,QAAQ,EAAE;QACxEG,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MACF,OAAOrD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAqD,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAItC,KAAK,CAAC,EAAAqC,iBAAA,GAAArD,KAAK,CAACJ,QAAQ,cAAAyD,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBtD,IAAI,cAAAuD,qBAAA,uBAApBA,qBAAA,CAAsBhD,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACAiD,oBAAoB,EAAE,MAAAA,CAAA,KAMf;IACL,IAAI;MACF,MAAM3D,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,sBAAsB,CAAC;MAC5D,OAAOD,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAAwD,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIzC,KAAK,CAAC,EAAAwC,iBAAA,GAAAxD,KAAK,CAACJ,QAAQ,cAAA4D,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBzD,IAAI,cAAA0D,qBAAA,uBAApBA,qBAAA,CAAsBnD,OAAO,KAAI,YAAY,CAAC;IAChE;EACF,CAAC;EAED;EACAoD,YAAY,EAAE,MAAAA,CAAOxC,EAAU,EAAES,MAA+B,EAAEgC,MAAe,KAA2B;IAC1G,IAAI;MACF,MAAM/D,QAAQ,GAAG,MAAMJ,SAAS,CAACqB,IAAI,CAAC,aAAaK,EAAE,QAAQ,EAAE;QAAES,MAAM;QAAEgC;MAAO,CAAC,CAAC;MAClF,OAAO/D,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA4D,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAI7C,KAAK,CAAC,EAAA4C,iBAAA,GAAA5D,KAAK,CAACJ,QAAQ,cAAAgE,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgB7D,IAAI,cAAA8D,qBAAA,uBAApBA,qBAAA,CAAsBvD,OAAO,KAAI,QAAQ,CAAC;IAC5D;EACF,CAAC;EAED;EACAwD,uBAAuB,EAAE,MAAOnE,MAA0C,IAAgD;IACxH,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,yBAAyB,EAAE;QAAEF;MAAO,CAAC,CAAC;MAC3E,OAAOC,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAU,EAAE;MAAA,IAAA+D,iBAAA,EAAAC,qBAAA;MACnB,MAAM,IAAIhD,KAAK,CAAC,EAAA+C,iBAAA,GAAA/D,KAAK,CAACJ,QAAQ,cAAAmE,iBAAA,wBAAAC,qBAAA,GAAdD,iBAAA,CAAgBhE,IAAI,cAAAiE,qBAAA,uBAApBA,qBAAA,CAAsB1D,OAAO,KAAI,aAAa,CAAC;IACjE;EACF;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}