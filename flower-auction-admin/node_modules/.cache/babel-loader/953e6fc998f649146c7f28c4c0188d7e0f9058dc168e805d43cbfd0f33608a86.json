{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Form, message, Popconfirm, Typography, Row, Col, Image, Switch } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { productService } from '../../../services/productService';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\n\n// 商品质量等级枚举\nexport let QualityLevel = /*#__PURE__*/function (QualityLevel) {\n  QualityLevel[QualityLevel[\"EXCELLENT\"] = 1] = \"EXCELLENT\";\n  // 优\n  QualityLevel[QualityLevel[\"GOOD\"] = 2] = \"GOOD\";\n  // 良\n  QualityLevel[QualityLevel[\"MEDIUM\"] = 3] = \"MEDIUM\"; // 中\n  return QualityLevel;\n}({});\n\n// 商品状态枚举\nexport let ProductStatus = /*#__PURE__*/function (ProductStatus) {\n  ProductStatus[ProductStatus[\"OFFLINE\"] = 0] = \"OFFLINE\";\n  // 下架\n  ProductStatus[ProductStatus[\"ONLINE\"] = 1] = \"ONLINE\"; // 上架\n  return ProductStatus;\n}({});\n\n// 商品数据接口\n\n// 商品类别接口\n\n// 查询参数接口\n\nconst ProductList = () => {\n  _s();\n  const [products, setProducts] = useState([]);\n  const [categories, setCategories] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isDetailVisible, setIsDetailVisible] = useState(false);\n  const [editingProduct, setEditingProduct] = useState(null);\n  const [viewingProduct, setViewingProduct] = useState(null);\n  const [fileList, setFileList] = useState([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 质量等级映射\n  const qualityLevelMap = {\n    [QualityLevel.EXCELLENT]: {\n      label: '优',\n      color: 'green'\n    },\n    [QualityLevel.GOOD]: {\n      label: '良',\n      color: 'blue'\n    },\n    [QualityLevel.MEDIUM]: {\n      label: '中',\n      color: 'orange'\n    }\n  };\n\n  // 获取商品列表\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await productService.getProductList(queryParams);\n      if (response.success) {\n        setProducts(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取商品列表失败');\n        setProducts([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n      let errorMsg = '获取商品列表失败';\n      if (error.response) {\n        const {\n          status\n        } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问商品列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setProducts([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品类别\n  const fetchCategories = async () => {\n    try {\n      const response = await productService.getCategoryList();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error) {\n      console.error('获取商品类别失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增商品\n  const handleAdd = () => {\n    setEditingProduct(null);\n    form.resetFields();\n    setFileList([]);\n    setIsModalVisible(true);\n  };\n\n  // 编辑商品\n  const handleEdit = product => {\n    setEditingProduct(product);\n    form.setFieldsValue({\n      ...product,\n      status: product.status === ProductStatus.ONLINE\n    });\n\n    // 设置图片列表\n    if (product.images && product.images.length > 0) {\n      const imageFiles = product.images.map((url, index) => ({\n        uid: `${index}`,\n        name: `image-${index}`,\n        status: 'done',\n        url: url\n      }));\n      setFileList(imageFiles);\n    } else {\n      setFileList([]);\n    }\n    setIsModalVisible(true);\n  };\n\n  // 查看商品详情\n  const handleView = product => {\n    setViewingProduct(product);\n    setIsDetailVisible(true);\n  };\n\n  // 删除商品\n  const handleDelete = async id => {\n    try {\n      const response = await productService.deleteProduct(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchProducts();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存商品\n  const handleSave = async values => {\n    try {\n      const productData = {\n        ...values,\n        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,\n        images: fileList.map(file => {\n          var _file$response;\n          return file.url || ((_file$response = file.response) === null || _file$response === void 0 ? void 0 : _file$response.url);\n        }).filter(Boolean)\n      };\n      let response;\n      if (editingProduct) {\n        response = await productService.updateProduct(editingProduct.id, productData);\n      } else {\n        response = await productService.createProduct(productData);\n      }\n      if (response.success) {\n        message.success(editingProduct ? '更新成功' : '创建成功');\n        setIsModalVisible(false);\n        fetchProducts();\n      } else {\n        message.error(response.message || '保存失败');\n      }\n    } catch (error) {\n      message.error(error.message || '保存失败');\n    }\n  };\n\n  // 切换商品状态\n  const handleToggleStatus = async product => {\n    try {\n      const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;\n      const response = await productService.updateProductStatus(product.id, newStatus);\n      if (response.success) {\n        message.success('状态更新成功');\n        fetchProducts();\n      } else {\n        message.error(response.message || '状态更新失败');\n      }\n    } catch (error) {\n      message.error(error.message || '状态更新失败');\n    }\n  };\n\n  // 图片上传处理\n  const handleUploadChange = ({\n    fileList: newFileList\n  }) => {\n    setFileList(newFileList);\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '商品图片',\n    dataIndex: 'images',\n    key: 'images',\n    width: 100,\n    render: images => images && images.length > 0 ? /*#__PURE__*/_jsxDEV(Image, {\n      width: 60,\n      height: 60,\n      src: images[0],\n      style: {\n        objectFit: 'cover',\n        borderRadius: 4\n      },\n      fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 11\n    }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        width: 60,\n        height: 60,\n        background: '#f5f5f5',\n        borderRadius: 4,\n        display: 'flex',\n        alignItems: 'center',\n        justifyContent: 'center'\n      },\n      children: \"\\u65E0\\u56FE\\u7247\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 311,\n      columnNumber: 11\n    }, this)\n  }, {\n    title: '商品名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 150,\n    render: text => /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        fontWeight: 500\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '分类',\n    dataIndex: 'categoryName',\n    key: 'categoryName',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 331,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '质量等级',\n    dataIndex: 'qualityLevel',\n    key: 'qualityLevel',\n    width: 100,\n    render: level => {\n      const levelInfo = qualityLevelMap[level];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: (levelInfo === null || levelInfo === void 0 ? void 0 : levelInfo.color) || 'default',\n        children: (levelInfo === null || levelInfo === void 0 ? void 0 : levelInfo.label) || '未知'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 341,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '产地',\n    dataIndex: 'origin',\n    key: 'origin',\n    width: 120\n  }, {\n    title: '供应商',\n    dataIndex: 'supplierName',\n    key: 'supplierName',\n    width: 120\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: (status, record) => /*#__PURE__*/_jsxDEV(Switch, {\n      checked: status === ProductStatus.ONLINE,\n      onChange: () => handleToggleStatus(record),\n      checkedChildren: \"\\u4E0A\\u67B6\",\n      unCheckedChildren: \"\\u4E0B\\u67B6\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 180,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 390,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleView(record),\n        children: \"\\u67E5\\u770B\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 387,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 398,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 395,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u5546\\u54C1\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 413,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 409,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 403,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"product-list-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u5546\\u54C1\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 425,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 435,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"categoryId\",\n              label: \"\\u5546\\u54C1\\u5206\\u7C7B\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\\u5206\\u7C7B\",\n                allowClear: true,\n                children: categories.map(category => /*#__PURE__*/_jsxDEV(Option, {\n                  value: category.id,\n                  children: category.name\n                }, category.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 444,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 442,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 441,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 440,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"qualityLevel\",\n              label: \"\\u8D28\\u91CF\\u7B49\\u7EA7\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u8D28\\u91CF\\u7B49\\u7EA7\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.EXCELLENT,\n                  children: \"\\u4F18\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.GOOD,\n                  children: \"\\u826F\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 455,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: QualityLevel.MEDIUM,\n                  children: \"\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 456,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u5546\\u54C1\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: ProductStatus.ONLINE,\n                  children: \"\\u4E0A\\u67B6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: ProductStatus.OFFLINE,\n                  children: \"\\u4E0B\\u67B6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 464,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 462,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 460,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"origin\",\n              label: \"\\u4EA7\\u5730\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4EA7\\u5730\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 476,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 476,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 479,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 479,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 475,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 474,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 473,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 434,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 429,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 428,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 495,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u65B0\\u589E\\u5546\\u54C1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 21\n            }, this),\n            onClick: fetchProducts,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 501,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 491,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 490,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: products,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1400\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 424,\n    columnNumber: 5\n  }, this);\n};\n_s(ProductList, \"/UVt+8d1LaaRiDcxHYyYDJb/iPc=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = ProductList;\nexport default ProductList;\nvar _c;\n$RefreshReg$(_c, \"ProductList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Form", "message", "Popconfirm", "Typography", "Row", "Col", "Image", "Switch", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ReloadOutlined", "productService", "jsxDEV", "_jsxDEV", "Title", "Option", "TextArea", "QualityLevel", "ProductStatus", "ProductList", "_s", "products", "setProducts", "categories", "setCategories", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "isDetailVisible", "setIsDetailVisible", "editingProduct", "setEditingProduct", "viewingProduct", "setViewingProduct", "fileList", "setFileList", "saving", "setSaving", "form", "useForm", "searchForm", "qualityLevelMap", "EXCELLENT", "label", "color", "GOOD", "MEDIUM", "fetchProducts", "response", "getProductList", "success", "data", "list", "error", "console", "errorMsg", "status", "fetchCategories", "getCategoryList", "handleSearch", "values", "handleReset", "resetFields", "handleAdd", "handleEdit", "product", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ONLINE", "images", "length", "imageFiles", "map", "url", "index", "uid", "name", "handleView", "handleDelete", "id", "deleteProduct", "handleSave", "productData", "OFFLINE", "file", "_file$response", "filter", "Boolean", "updateProduct", "createProduct", "handleToggleStatus", "newStatus", "updateProductStatus", "handleUploadChange", "newFileList", "columns", "title", "dataIndex", "key", "width", "render", "height", "src", "style", "objectFit", "borderRadius", "fallback", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "background", "display", "alignItems", "justifyContent", "children", "text", "fontWeight", "level", "levelInfo", "record", "checked", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "Date", "toLocaleString", "fixed", "_", "size", "type", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "className", "layout", "onFinish", "autoComplete", "gutter", "xs", "sm", "md", "<PERSON><PERSON>", "placeholder", "allowClear", "category", "value", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  Upload,\n  Image,\n  InputNumber,\n  Descriptions,\n  Switch,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n  UploadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { UploadFile } from 'antd/es/upload/interface';\nimport { productService } from '../../../services/productService';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// 商品质量等级枚举\nexport enum QualityLevel {\n  EXCELLENT = 1, // 优\n  GOOD = 2,      // 良\n  MEDIUM = 3,    // 中\n}\n\n// 商品状态枚举\nexport enum ProductStatus {\n  OFFLINE = 0, // 下架\n  ONLINE = 1,  // 上架\n}\n\n// 商品数据接口\nexport interface Product {\n  id: number;\n  name: string;\n  categoryId: number;\n  categoryName: string;\n  description?: string;\n  qualityLevel: QualityLevel;\n  origin: string;\n  supplierId: number;\n  supplierName: string;\n  status: ProductStatus;\n  images?: string[];\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 商品类别接口\nexport interface Category {\n  id: number;\n  name: string;\n  parentId?: number;\n  level: number;\n}\n\n// 查询参数接口\ninterface ProductQueryParams {\n  name?: string;\n  categoryId?: number;\n  qualityLevel?: QualityLevel;\n  status?: ProductStatus;\n  origin?: string;\n  page: number;\n  pageSize: number;\n}\n\nconst ProductList: React.FC = () => {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [categories, setCategories] = useState<Category[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<ProductQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isDetailVisible, setIsDetailVisible] = useState(false);\n  const [editingProduct, setEditingProduct] = useState<Product | null>(null);\n  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);\n  const [fileList, setFileList] = useState<UploadFile[]>([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 质量等级映射\n  const qualityLevelMap = {\n    [QualityLevel.EXCELLENT]: { label: '优', color: 'green' },\n    [QualityLevel.GOOD]: { label: '良', color: 'blue' },\n    [QualityLevel.MEDIUM]: { label: '中', color: 'orange' },\n  };\n\n  // 获取商品列表\n  const fetchProducts = async () => {\n    setLoading(true);\n    try {\n      const response = await productService.getProductList(queryParams);\n      if (response.success) {\n        setProducts(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取商品列表失败');\n        setProducts([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取商品列表失败:', error);\n      let errorMsg = '获取商品列表失败';\n      if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问商品列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setProducts([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品类别\n  const fetchCategories = async () => {\n    try {\n      const response = await productService.getCategoryList();\n      if (response.success) {\n        setCategories(response.data);\n      }\n    } catch (error: any) {\n      console.error('获取商品类别失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchProducts();\n    fetchCategories();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增商品\n  const handleAdd = () => {\n    setEditingProduct(null);\n    form.resetFields();\n    setFileList([]);\n    setIsModalVisible(true);\n  };\n\n  // 编辑商品\n  const handleEdit = (product: Product) => {\n    setEditingProduct(product);\n    form.setFieldsValue({\n      ...product,\n      status: product.status === ProductStatus.ONLINE,\n    });\n\n    // 设置图片列表\n    if (product.images && product.images.length > 0) {\n      const imageFiles = product.images.map((url, index) => ({\n        uid: `${index}`,\n        name: `image-${index}`,\n        status: 'done' as const,\n        url: url,\n      }));\n      setFileList(imageFiles);\n    } else {\n      setFileList([]);\n    }\n\n    setIsModalVisible(true);\n  };\n\n  // 查看商品详情\n  const handleView = (product: Product) => {\n    setViewingProduct(product);\n    setIsDetailVisible(true);\n  };\n\n  // 删除商品\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await productService.deleteProduct(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchProducts();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存商品\n  const handleSave = async (values: any) => {\n    try {\n      const productData = {\n        ...values,\n        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,\n        images: fileList.map(file => file.url || file.response?.url).filter(Boolean),\n      };\n\n      let response;\n      if (editingProduct) {\n        response = await productService.updateProduct(editingProduct.id, productData);\n      } else {\n        response = await productService.createProduct(productData);\n      }\n\n      if (response.success) {\n        message.success(editingProduct ? '更新成功' : '创建成功');\n        setIsModalVisible(false);\n        fetchProducts();\n      } else {\n        message.error(response.message || '保存失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '保存失败');\n    }\n  };\n\n  // 切换商品状态\n  const handleToggleStatus = async (product: Product) => {\n    try {\n      const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;\n      const response = await productService.updateProductStatus(product.id, newStatus);\n      if (response.success) {\n        message.success('状态更新成功');\n        fetchProducts();\n      } else {\n        message.error(response.message || '状态更新失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '状态更新失败');\n    }\n  };\n\n  // 图片上传处理\n  const handleUploadChange = ({ fileList: newFileList }: any) => {\n    setFileList(newFileList);\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Product> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '商品图片',\n      dataIndex: 'images',\n      key: 'images',\n      width: 100,\n      render: (images: string[]) => (\n        images && images.length > 0 ? (\n          <Image\n            width={60}\n            height={60}\n            src={images[0]}\n            style={{ objectFit: 'cover', borderRadius: 4 }}\n            fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n          />\n        ) : (\n          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>\n            无图片\n          </div>\n        )\n      ),\n    },\n    {\n      title: '商品名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 150,\n      render: (text: string) => (\n        <div style={{ fontWeight: 500 }}>{text}</div>\n      ),\n    },\n    {\n      title: '分类',\n      dataIndex: 'categoryName',\n      key: 'categoryName',\n      width: 120,\n      render: (text: string) => <Tag color=\"blue\">{text}</Tag>,\n    },\n    {\n      title: '质量等级',\n      dataIndex: 'qualityLevel',\n      key: 'qualityLevel',\n      width: 100,\n      render: (level: QualityLevel) => {\n        const levelInfo = qualityLevelMap[level];\n        return (\n          <Tag color={levelInfo?.color || 'default'}>\n            {levelInfo?.label || '未知'}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '产地',\n      dataIndex: 'origin',\n      key: 'origin',\n      width: 120,\n    },\n    {\n      title: '供应商',\n      dataIndex: 'supplierName',\n      key: 'supplierName',\n      width: 120,\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: ProductStatus, record: Product) => (\n        <Switch\n          checked={status === ProductStatus.ONLINE}\n          onChange={() => handleToggleStatus(record)}\n          checkedChildren=\"上架\"\n          unCheckedChildren=\"下架\"\n        />\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 180,\n      fixed: 'right',\n      render: (_, record: Product) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EyeOutlined />}\n            onClick={() => handleView(record)}\n          >\n            查看\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个商品吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"product-list-container\">\n      <Title level={2}>商品管理</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"name\" label=\"商品名称\">\n                <Input placeholder=\"请输入商品名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"categoryId\" label=\"商品分类\">\n                <Select placeholder=\"请选择商品分类\" allowClear>\n                  {categories.map(category => (\n                    <Option key={category.id} value={category.id}>\n                      {category.name}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"qualityLevel\" label=\"质量等级\">\n                <Select placeholder=\"请选择质量等级\" allowClear>\n                  <Option value={QualityLevel.EXCELLENT}>优</Option>\n                  <Option value={QualityLevel.GOOD}>良</Option>\n                  <Option value={QualityLevel.MEDIUM}>中</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"商品状态\">\n                <Select placeholder=\"请选择商品状态\" allowClear>\n                  <Option value={ProductStatus.ONLINE}>上架</Option>\n                  <Option value={ProductStatus.OFFLINE}>下架</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"origin\" label=\"产地\">\n                <Input placeholder=\"请输入产地\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              新增商品\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchProducts}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 商品列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={products}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1400 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n    </div>\n  );\n};\n\nexport default ProductList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EAEHC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EAEHC,KAAK,EAGLC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EAEXC,cAAc,QAET,mBAAmB;AAG1B,SAASC,cAAc,QAAQ,kCAAkC;AACjE,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGd,UAAU;AAC5B,MAAM;EAAEe;AAAO,CAAC,GAAGpB,MAAM;AACzB,MAAM;EAAEqB;AAAS,CAAC,GAAGtB,KAAK;;AAE1B;AACA,WAAYuB,YAAY,0BAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EACP;EADLA,YAAY,CAAZA,YAAY;EAEP;EAFLA,YAAY,CAAZA,YAAY,4BAGP;EAAA,OAHLA,YAAY;AAAA;;AAMxB;AACA,WAAYC,aAAa,0BAAbA,aAAa;EAAbA,aAAa,CAAbA,aAAa;EACV;EADHA,aAAa,CAAbA,aAAa,4BAEV;EAAA,OAFHA,aAAa;AAAA;;AAKzB;;AAiBA;;AAQA;;AAWA,MAAMC,WAAqB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGlC,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACmC,UAAU,EAAEC,aAAa,CAAC,GAAGpC,QAAQ,CAAa,EAAE,CAAC;EAC5D,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,KAAK,EAAEC,QAAQ,CAAC,GAAGxC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAACyC,WAAW,EAAEC,cAAc,CAAC,GAAG1C,QAAQ,CAAqB;IACjE2C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG9C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAAC+C,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACiD,cAAc,EAAEC,iBAAiB,CAAC,GAAGlD,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACmD,cAAc,EAAEC,iBAAiB,CAAC,GAAGpD,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAACqD,QAAQ,EAAEC,WAAW,CAAC,GAAGtD,QAAQ,CAAe,EAAE,CAAC;EAC1D,MAAM,CAACuD,MAAM,EAAEC,SAAS,CAAC,GAAGxD,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyD,IAAI,CAAC,GAAGhD,IAAI,CAACiD,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAGlD,IAAI,CAACiD,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAME,eAAe,GAAG;IACtB,CAAC/B,YAAY,CAACgC,SAAS,GAAG;MAAEC,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACxD,CAAClC,YAAY,CAACmC,IAAI,GAAG;MAAEF,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAO,CAAC;IAClD,CAAClC,YAAY,CAACoC,MAAM,GAAG;MAAEH,KAAK,EAAE,GAAG;MAAEC,KAAK,EAAE;IAAS;EACvD,CAAC;;EAED;EACA,MAAMG,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC5B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAM6B,QAAQ,GAAG,MAAM5C,cAAc,CAAC6C,cAAc,CAAC3B,WAAW,CAAC;MACjE,IAAI0B,QAAQ,CAACE,OAAO,EAAE;QACpBnC,WAAW,CAACiC,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;QAC/B/B,QAAQ,CAAC2B,QAAQ,CAACG,IAAI,CAAC/B,KAAK,CAAC;MAC/B,CAAC,MAAM;QACL7B,OAAO,CAAC8D,KAAK,CAACL,QAAQ,CAACzD,OAAO,IAAI,UAAU,CAAC;QAC7CwB,WAAW,CAAC,EAAE,CAAC;QACfM,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIE,QAAQ,GAAG,UAAU;MACzB,IAAIF,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM;UAAEQ;QAAO,CAAC,GAAGH,KAAK,CAACL,QAAQ;QACjC,IAAIQ,MAAM,KAAK,GAAG,EAAE;UAClBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,YAAY;QACzB,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,eAAe;QAC5B;MACF;MACAhE,OAAO,CAAC8D,KAAK,CAACE,QAAQ,CAAC;MACvBxC,WAAW,CAAC,EAAE,CAAC;MACfM,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF,MAAMT,QAAQ,GAAG,MAAM5C,cAAc,CAACsD,eAAe,CAAC,CAAC;MACvD,IAAIV,QAAQ,CAACE,OAAO,EAAE;QACpBjC,aAAa,CAAC+B,QAAQ,CAACG,IAAI,CAAC;MAC9B;IACF,CAAC,CAAC,OAAOE,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACAvE,SAAS,CAAC,MAAM;IACdiE,aAAa,CAAC,CAAC;IACfU,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACnC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMqC,YAAY,GAAIC,MAAW,IAAK;IACpCrC,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAGsC,MAAM;MACTpC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMqC,WAAW,GAAGA,CAAA,KAAM;IACxBrB,UAAU,CAACsB,WAAW,CAAC,CAAC;IACxBvC,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMsC,SAAS,GAAGA,CAAA,KAAM;IACtBhC,iBAAiB,CAAC,IAAI,CAAC;IACvBO,IAAI,CAACwB,WAAW,CAAC,CAAC;IAClB3B,WAAW,CAAC,EAAE,CAAC;IACfR,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMqC,UAAU,GAAIC,OAAgB,IAAK;IACvClC,iBAAiB,CAACkC,OAAO,CAAC;IAC1B3B,IAAI,CAAC4B,cAAc,CAAC;MAClB,GAAGD,OAAO;MACVT,MAAM,EAAES,OAAO,CAACT,MAAM,KAAK7C,aAAa,CAACwD;IAC3C,CAAC,CAAC;;IAEF;IACA,IAAIF,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAMC,UAAU,GAAGL,OAAO,CAACG,MAAM,CAACG,GAAG,CAAC,CAACC,GAAG,EAAEC,KAAK,MAAM;QACrDC,GAAG,EAAE,GAAGD,KAAK,EAAE;QACfE,IAAI,EAAE,SAASF,KAAK,EAAE;QACtBjB,MAAM,EAAE,MAAe;QACvBgB,GAAG,EAAEA;MACP,CAAC,CAAC,CAAC;MACHrC,WAAW,CAACmC,UAAU,CAAC;IACzB,CAAC,MAAM;MACLnC,WAAW,CAAC,EAAE,CAAC;IACjB;IAEAR,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMiD,UAAU,GAAIX,OAAgB,IAAK;IACvChC,iBAAiB,CAACgC,OAAO,CAAC;IAC1BpC,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMgD,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAM9B,QAAQ,GAAG,MAAM5C,cAAc,CAAC2E,aAAa,CAACD,EAAE,CAAC;MACvD,IAAI9B,QAAQ,CAACE,OAAO,EAAE;QACpB3D,OAAO,CAAC2D,OAAO,CAAC,MAAM,CAAC;QACvBH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLxD,OAAO,CAAC8D,KAAK,CAACL,QAAQ,CAACzD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAO8D,KAAU,EAAE;MACnB9D,OAAO,CAAC8D,KAAK,CAACA,KAAK,CAAC9D,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMyF,UAAU,GAAG,MAAOpB,MAAW,IAAK;IACxC,IAAI;MACF,MAAMqB,WAAW,GAAG;QAClB,GAAGrB,MAAM;QACTJ,MAAM,EAAEI,MAAM,CAACJ,MAAM,GAAG7C,aAAa,CAACwD,MAAM,GAAGxD,aAAa,CAACuE,OAAO;QACpEd,MAAM,EAAElC,QAAQ,CAACqC,GAAG,CAACY,IAAI;UAAA,IAAAC,cAAA;UAAA,OAAID,IAAI,CAACX,GAAG,MAAAY,cAAA,GAAID,IAAI,CAACnC,QAAQ,cAAAoC,cAAA,uBAAbA,cAAA,CAAeZ,GAAG;QAAA,EAAC,CAACa,MAAM,CAACC,OAAO;MAC7E,CAAC;MAED,IAAItC,QAAQ;MACZ,IAAIlB,cAAc,EAAE;QAClBkB,QAAQ,GAAG,MAAM5C,cAAc,CAACmF,aAAa,CAACzD,cAAc,CAACgD,EAAE,EAAEG,WAAW,CAAC;MAC/E,CAAC,MAAM;QACLjC,QAAQ,GAAG,MAAM5C,cAAc,CAACoF,aAAa,CAACP,WAAW,CAAC;MAC5D;MAEA,IAAIjC,QAAQ,CAACE,OAAO,EAAE;QACpB3D,OAAO,CAAC2D,OAAO,CAACpB,cAAc,GAAG,MAAM,GAAG,MAAM,CAAC;QACjDH,iBAAiB,CAAC,KAAK,CAAC;QACxBoB,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLxD,OAAO,CAAC8D,KAAK,CAACL,QAAQ,CAACzD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAO8D,KAAU,EAAE;MACnB9D,OAAO,CAAC8D,KAAK,CAACA,KAAK,CAAC9D,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMkG,kBAAkB,GAAG,MAAOxB,OAAgB,IAAK;IACrD,IAAI;MACF,MAAMyB,SAAS,GAAGzB,OAAO,CAACT,MAAM,KAAK7C,aAAa,CAACwD,MAAM,GAAGxD,aAAa,CAACuE,OAAO,GAAGvE,aAAa,CAACwD,MAAM;MACxG,MAAMnB,QAAQ,GAAG,MAAM5C,cAAc,CAACuF,mBAAmB,CAAC1B,OAAO,CAACa,EAAE,EAAEY,SAAS,CAAC;MAChF,IAAI1C,QAAQ,CAACE,OAAO,EAAE;QACpB3D,OAAO,CAAC2D,OAAO,CAAC,QAAQ,CAAC;QACzBH,aAAa,CAAC,CAAC;MACjB,CAAC,MAAM;QACLxD,OAAO,CAAC8D,KAAK,CAACL,QAAQ,CAACzD,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAO8D,KAAU,EAAE;MACnB9D,OAAO,CAAC8D,KAAK,CAACA,KAAK,CAAC9D,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMqG,kBAAkB,GAAGA,CAAC;IAAE1D,QAAQ,EAAE2D;EAAiB,CAAC,KAAK;IAC7D1D,WAAW,CAAC0D,WAAW,CAAC;EAC1B,CAAC;;EAED;EACA,MAAMC,OAA6B,GAAG,CACpC;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG/B,MAAgB,IACvBA,MAAM,IAAIA,MAAM,CAACC,MAAM,GAAG,CAAC,gBACzB/D,OAAA,CAACV,KAAK;MACJsG,KAAK,EAAE,EAAG;MACVE,MAAM,EAAE,EAAG;MACXC,GAAG,EAAEjC,MAAM,CAAC,CAAC,CAAE;MACfkC,KAAK,EAAE;QAAEC,SAAS,EAAE,OAAO;QAAEC,YAAY,EAAE;MAAE,CAAE;MAC/CC,QAAQ,EAAC;IAAgoB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1oB,CAAC,gBAEFvG,OAAA;MAAKgG,KAAK,EAAE;QAAEJ,KAAK,EAAE,EAAE;QAAEE,MAAM,EAAE,EAAE;QAAEU,UAAU,EAAE,SAAS;QAAEN,YAAY,EAAE,CAAC;QAAEO,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,cAAc,EAAE;MAAS,CAAE;MAAAC,QAAA,EAAC;IAEhJ;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK;EAGX,CAAC,EACD;IACEd,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,MAAM;IACjBC,GAAG,EAAE,MAAM;IACXC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,iBACnB7G,OAAA;MAAKgG,KAAK,EAAE;QAAEc,UAAU,EAAE;MAAI,CAAE;MAAAF,QAAA,EAAEC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EAEhD,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,iBAAK7G,OAAA,CAACjB,GAAG;MAACuD,KAAK,EAAC,MAAM;MAAAsE,QAAA,EAAEC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACzD,CAAC,EACD;IACEd,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGkB,KAAmB,IAAK;MAC/B,MAAMC,SAAS,GAAG7E,eAAe,CAAC4E,KAAK,CAAC;MACxC,oBACE/G,OAAA,CAACjB,GAAG;QAACuD,KAAK,EAAE,CAAA0E,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE1E,KAAK,KAAI,SAAU;QAAAsE,QAAA,EACvC,CAAAI,SAAS,aAATA,SAAS,uBAATA,SAAS,CAAE3E,KAAK,KAAI;MAAI;QAAA+D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACtB,CAAC;IAEV;EACF,CAAC,EACD;IACEd,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,cAAc;IACzBC,GAAG,EAAE,cAAc;IACnBC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC3C,MAAqB,EAAE+D,MAAe,kBAC7CjH,OAAA,CAACT,MAAM;MACL2H,OAAO,EAAEhE,MAAM,KAAK7C,aAAa,CAACwD,MAAO;MACzCsD,QAAQ,EAAEA,CAAA,KAAMhC,kBAAkB,CAAC8B,MAAM,CAAE;MAC3CG,eAAe,EAAC,cAAI;MACpBC,iBAAiB,EAAC;IAAI;MAAAjB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB;EAEL,CAAC,EACD;IACEd,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGgB,IAAY,IAAK,IAAIS,IAAI,CAACT,IAAI,CAAC,CAACU,cAAc,CAAC;EAC1D,CAAC,EACD;IACE9B,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACV4B,KAAK,EAAE,OAAO;IACd3B,MAAM,EAAEA,CAAC4B,CAAC,EAAER,MAAe,kBACzBjH,OAAA,CAACpB,KAAK;MAAC8I,IAAI,EAAC,OAAO;MAAAd,QAAA,gBACjB5G,OAAA,CAACrB,MAAM;QACLgJ,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE5H,OAAA,CAACJ,WAAW;UAAAwG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACtBsB,OAAO,EAAEA,CAAA,KAAMvD,UAAU,CAAC2C,MAAM,CAAE;QAAAL,QAAA,EACnC;MAED;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvG,OAAA,CAACrB,MAAM;QACLgJ,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE5H,OAAA,CAACN,YAAY;UAAA0G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBsB,OAAO,EAAEA,CAAA,KAAMnE,UAAU,CAACuD,MAAM,CAAE;QAAAL,QAAA,EACnC;MAED;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTvG,OAAA,CAACd,UAAU;QACTuG,KAAK,EAAC,oEAAa;QACnBqC,SAAS,EAAEA,CAAA,KAAMvD,YAAY,CAAC0C,MAAM,CAACzC,EAAE,CAAE;QACzCuD,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAApB,QAAA,eAEf5G,OAAA,CAACrB,MAAM;UACLgJ,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZO,MAAM;UACNL,IAAI,eAAE5H,OAAA,CAACL,cAAc;YAAAyG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAK,QAAA,EAC1B;QAED;UAAAR,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEvG,OAAA;IAAKkI,SAAS,EAAC,wBAAwB;IAAAtB,QAAA,gBACrC5G,OAAA,CAACC,KAAK;MAAC8G,KAAK,EAAE,CAAE;MAAAH,QAAA,EAAC;IAAI;MAAAR,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7BvG,OAAA,CAACvB,IAAI;MAACyJ,SAAS,EAAC,aAAa;MAACR,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxC5G,OAAA,CAAChB,IAAI;QACHmJ,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAE/E,YAAa;QACvBgF,YAAY,EAAC,KAAK;QAAAzB,QAAA,eAElB5G,OAAA,CAACZ,GAAG;UAACkJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACtC,KAAK,EAAE;YAAEJ,KAAK,EAAE;UAAO,CAAE;UAAAgB,QAAA,gBAC9C5G,OAAA,CAACX,GAAG;YAACkJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACzB5G,OAAA,CAAChB,IAAI,CAAC0J,IAAI;cAACrE,IAAI,EAAC,MAAM;cAAChC,KAAK,EAAC,0BAAM;cAAAuE,QAAA,eACjC5G,OAAA,CAACnB,KAAK;gBAAC8J,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNvG,OAAA,CAACX,GAAG;YAACkJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACzB5G,OAAA,CAAChB,IAAI,CAAC0J,IAAI;cAACrE,IAAI,EAAC,YAAY;cAAChC,KAAK,EAAC,0BAAM;cAAAuE,QAAA,eACvC5G,OAAA,CAAClB,MAAM;gBAAC6J,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAhC,QAAA,EACrClG,UAAU,CAACuD,GAAG,CAAC4E,QAAQ,iBACtB7I,OAAA,CAACE,MAAM;kBAAmB4I,KAAK,EAAED,QAAQ,CAACrE,EAAG;kBAAAoC,QAAA,EAC1CiC,QAAQ,CAACxE;gBAAI,GADHwE,QAAQ,CAACrE,EAAE;kBAAA4B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEhB,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNvG,OAAA,CAACX,GAAG;YAACkJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACzB5G,OAAA,CAAChB,IAAI,CAAC0J,IAAI;cAACrE,IAAI,EAAC,cAAc;cAAChC,KAAK,EAAC,0BAAM;cAAAuE,QAAA,eACzC5G,OAAA,CAAClB,MAAM;gBAAC6J,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAhC,QAAA,gBACtC5G,OAAA,CAACE,MAAM;kBAAC4I,KAAK,EAAE1I,YAAY,CAACgC,SAAU;kBAAAwE,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACjDvG,OAAA,CAACE,MAAM;kBAAC4I,KAAK,EAAE1I,YAAY,CAACmC,IAAK;kBAAAqE,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC5CvG,OAAA,CAACE,MAAM;kBAAC4I,KAAK,EAAE1I,YAAY,CAACoC,MAAO;kBAAAoE,QAAA,EAAC;gBAAC;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNvG,OAAA,CAACX,GAAG;YAACkJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACzB5G,OAAA,CAAChB,IAAI,CAAC0J,IAAI;cAACrE,IAAI,EAAC,QAAQ;cAAChC,KAAK,EAAC,0BAAM;cAAAuE,QAAA,eACnC5G,OAAA,CAAClB,MAAM;gBAAC6J,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAhC,QAAA,gBACtC5G,OAAA,CAACE,MAAM;kBAAC4I,KAAK,EAAEzI,aAAa,CAACwD,MAAO;kBAAA+C,QAAA,EAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDvG,OAAA,CAACE,MAAM;kBAAC4I,KAAK,EAAEzI,aAAa,CAACuE,OAAQ;kBAAAgC,QAAA,EAAC;gBAAE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3C;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNvG,OAAA,CAACX,GAAG;YAACkJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACzB5G,OAAA,CAAChB,IAAI,CAAC0J,IAAI;cAACrE,IAAI,EAAC,QAAQ;cAAChC,KAAK,EAAC,cAAI;cAAAuE,QAAA,eACjC5G,OAAA,CAACnB,KAAK;gBAAC8J,WAAW,EAAC,gCAAO;gBAACC,UAAU;cAAA;gBAAAxC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNvG,OAAA,CAACX,GAAG;YAACkJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA7B,QAAA,eACzB5G,OAAA,CAAChB,IAAI,CAAC0J,IAAI;cAAA9B,QAAA,eACR5G,OAAA,CAACpB,KAAK;gBAAAgI,QAAA,gBACJ5G,OAAA,CAACrB,MAAM;kBAACgJ,IAAI,EAAC,SAAS;kBAACoB,QAAQ,EAAC,QAAQ;kBAACnB,IAAI,eAAE5H,OAAA,CAACP,cAAc;oBAAA2G,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAK,QAAA,EAAC;gBAEnE;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTvG,OAAA,CAACrB,MAAM;kBAACkJ,OAAO,EAAEtE,WAAY;kBAACqE,IAAI,eAAE5H,OAAA,CAACH,cAAc;oBAAAuG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAK,QAAA,EAAC;gBAExD;kBAAAR,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPvG,OAAA,CAACvB,IAAI;MAACyJ,SAAS,EAAC,aAAa;MAACR,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxC5G,OAAA,CAACZ,GAAG;QAAC4J,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAArC,QAAA,gBACzC5G,OAAA,CAACX,GAAG;UAAAuH,QAAA,eACF5G,OAAA,CAACrB,MAAM;YACLgJ,IAAI,EAAC,SAAS;YACdC,IAAI,eAAE5H,OAAA,CAACR,YAAY;cAAA4G,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBsB,OAAO,EAAEpE,SAAU;YAAAmD,QAAA,EACpB;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNvG,OAAA,CAACX,GAAG;UAAAuH,QAAA,eACF5G,OAAA,CAACrB,MAAM;YACLiJ,IAAI,eAAE5H,OAAA,CAACH,cAAc;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBsB,OAAO,EAAEpF,aAAc;YACvB7B,OAAO,EAAEA,OAAQ;YAAAgG,QAAA,EAClB;UAED;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPvG,OAAA,CAACvB,IAAI;MAAAmI,QAAA,eACH5G,OAAA,CAACtB,KAAK;QACJ8G,OAAO,EAAEA,OAAQ;QACjB0D,UAAU,EAAE1I,QAAS;QACrB2I,MAAM,EAAC,IAAI;QACXvI,OAAO,EAAEA,OAAQ;QACjBwI,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAEvI,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZ0I,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC5I,KAAK,EAAE6I,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ7I,KAAK,IAAI;UAC5CqG,QAAQ,EAAEA,CAACjG,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAAiF,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAChG,EAAA,CAlcID,WAAqB;EAAA,QAeVtB,IAAI,CAACiD,OAAO,EACNjD,IAAI,CAACiD,OAAO;AAAA;AAAA2H,EAAA,GAhB7BtJ,WAAqB;AAoc3B,eAAeA,WAAW;AAAC,IAAAsJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}