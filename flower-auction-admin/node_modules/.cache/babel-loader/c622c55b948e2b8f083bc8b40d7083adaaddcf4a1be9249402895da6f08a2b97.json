{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Typography, Row, Col, InputNumber, Image, Popconfirm, Statistic, Badge, Tooltip } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, EyeOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { auctionService } from '../../../services/auctionService';\nimport { productService } from '../../../services/productService';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\n\n// 拍卖商品状态枚举\nexport let AuctionItemStatus = /*#__PURE__*/function (AuctionItemStatus) {\n  AuctionItemStatus[\"PENDING\"] = \"pending\";\n  // 待拍卖\n  AuctionItemStatus[\"ONGOING\"] = \"ongoing\";\n  // 拍卖中\n  AuctionItemStatus[\"SOLD\"] = \"sold\";\n  // 已成交\n  AuctionItemStatus[\"UNSOLD\"] = \"unsold\";\n  // 流拍\n  AuctionItemStatus[\"WITHDRAWN\"] = \"withdrawn\"; // 撤回\n  return AuctionItemStatus;\n}({});\n\n// 拍卖商品接口\n\n// 查询参数接口\n\nconst AuctionItems = () => {\n  _s();\n  const [auctionItems, setAuctionItems] = useState([]);\n  const [products, setProducts] = useState([]);\n  const [auctions, setAuctions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingItem, setEditingItem] = useState(null);\n  const [statistics, setStatistics] = useState({\n    totalItems: 0,\n    pendingItems: 0,\n    ongoingItems: 0,\n    soldItems: 0,\n    totalValue: 0\n  });\n  const [form] = Form.useForm();\n\n  // 拍卖商品状态映射\n  const itemStatusMap = {\n    [AuctionItemStatus.PENDING]: {\n      label: '待拍卖',\n      color: 'default'\n    },\n    [AuctionItemStatus.ONGOING]: {\n      label: '拍卖中',\n      color: 'blue'\n    },\n    [AuctionItemStatus.SOLD]: {\n      label: '已成交',\n      color: 'green'\n    },\n    [AuctionItemStatus.UNSOLD]: {\n      label: '流拍',\n      color: 'orange'\n    },\n    [AuctionItemStatus.WITHDRAWN]: {\n      label: '撤回',\n      color: 'red'\n    }\n  };\n\n  // 获取拍卖商品列表\n  const fetchAuctionItems = async () => {\n    setLoading(true);\n    try {\n      // 这里需要调用后端API获取拍卖商品列表\n      // 暂时使用模拟数据\n      const mockData = {\n        success: true,\n        data: {\n          list: [],\n          total: 0,\n          page: queryParams.page,\n          pageSize: queryParams.pageSize\n        }\n      };\n      setAuctionItems(mockData.data.list);\n      setTotal(mockData.data.total);\n    } catch (error) {\n      message.error(error.message || '获取拍卖商品列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品列表（用于添加拍卖商品）\n  const fetchProducts = async () => {\n    try {\n      const response = await productService.getProductList({\n        page: 1,\n        pageSize: 100,\n        status: 1 // 只获取已审核的商品\n      });\n      if (response.success) {\n        setProducts(response.data.list);\n      }\n    } catch (error) {\n      console.error('获取商品列表失败:', error);\n    }\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    try {\n      const response = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 100\n      });\n      if (response.success) {\n        setAuctions(response.data.list);\n      }\n    } catch (error) {\n      console.error('获取拍卖会列表失败:', error);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      // 这里需要调用后端API获取统计信息\n      // 暂时使用模拟数据\n      setStatistics({\n        totalItems: 0,\n        pendingItems: 0,\n        ongoingItems: 0,\n        soldItems: 0,\n        totalValue: 0\n      });\n    } catch (error) {\n      console.error('获取统计信息失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchAuctionItems();\n    fetchProducts();\n    fetchAuctions();\n    fetchStatistics();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增拍卖商品\n  const handleAdd = () => {\n    setEditingItem(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑拍卖商品\n  const handleEdit = item => {\n    setEditingItem(item);\n    form.setFieldsValue(item);\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖商品\n  const handleDelete = async id => {\n    try {\n      // 调用删除API\n      message.success('删除成功');\n      fetchAuctionItems();\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖商品\n  const handleSave = async values => {\n    try {\n      if (editingItem) {\n        // 更新拍卖商品\n        message.success('更新成功');\n      } else {\n        // 添加拍卖商品\n        await auctionService.addAuctionItem(values.auctionId, {\n          productId: values.productId,\n          startingPrice: values.startingPrice,\n          reservePrice: values.reservePrice,\n          bidIncrement: values.bidIncrement\n        });\n        message.success('添加成功');\n      }\n      setIsModalVisible(false);\n      fetchAuctionItems();\n    } catch (error) {\n      message.error(error.message || '保存失败');\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: 24\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u62CD\\u5356\\u5546\\u54C1\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 259,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Row, {\n      gutter: 16,\n      style: {\n        marginBottom: 24\n      },\n      children: [/*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u603B\\u5546\\u54C1\\u6570\",\n            value: statistics.totalItems,\n            prefix: /*#__PURE__*/_jsxDEV(GavelOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 268,\n              columnNumber: 23\n            }, this),\n            valueStyle: {\n              color: '#3f8600'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 264,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 263,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5F85\\u62CD\\u5356\",\n            value: statistics.pendingItems,\n            valueStyle: {\n              color: '#1890ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 274,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 273,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u62CD\\u5356\\u4E2D\",\n            value: statistics.ongoingItems,\n            valueStyle: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 284,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 283,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 282,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Col, {\n        xs: 24,\n        sm: 12,\n        md: 6,\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          children: /*#__PURE__*/_jsxDEV(Statistic, {\n            title: \"\\u5DF2\\u6210\\u4EA4\",\n            value: statistics.soldItems,\n            valueStyle: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 293,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 291,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 262,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"productName\",\n              label: \"\\u5546\\u54C1\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5546\\u54C1\\u540D\\u79F0\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u62CD\\u5356\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.PENDING,\n                  children: \"\\u5F85\\u62CD\\u5356\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.ONGOING,\n                  children: \"\\u62CD\\u5356\\u4E2D\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.SOLD,\n                  children: \"\\u5DF2\\u6210\\u4EA4\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.UNSOLD,\n                  children: \"\\u6D41\\u62CD\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: AuctionItemStatus.WITHDRAWN,\n                  children: \"\\u64A4\\u56DE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 322,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 316,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 315,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"auctionId\",\n              label: \"\\u62CD\\u5356\\u4F1A\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u4F1A\",\n                allowClear: true,\n                children: auctions.map(auction => /*#__PURE__*/_jsxDEV(Option, {\n                  value: auction.id,\n                  children: auction.title\n                }, auction.id, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 330,\n                  columnNumber: 21\n                }, this))\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 328,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 340,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 340,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 343,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 343,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 338,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 337,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 303,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      style: {\n        marginBottom: 16\n      },\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 359,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u6DFB\\u52A0\\u62CD\\u5356\\u5546\\u54C1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 357,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 356,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 367,\n              columnNumber: 21\n            }, this),\n            onClick: fetchAuctionItems,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 366,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 365,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 355,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: [{\n          title: 'ID',\n          dataIndex: 'id',\n          key: 'id',\n          width: 80\n        }, {\n          title: '商品信息',\n          key: 'product',\n          width: 250,\n          render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [record.images && record.images.length > 0 && /*#__PURE__*/_jsxDEV(Image, {\n              width: 60,\n              height: 60,\n              src: record.images[0],\n              style: {\n                marginRight: 12,\n                borderRadius: 4\n              },\n              fallback: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 394,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontWeight: 500\n                },\n                children: record.productName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 403,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 12,\n                  color: '#999'\n                },\n                children: [\"\\u7F16\\u53F7: \", record.productCode]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 404,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                style: {\n                  fontSize: 12,\n                  color: '#999'\n                },\n                children: [record.quantity, \" \", record.unit]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 407,\n                columnNumber: 21\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 402,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 392,\n            columnNumber: 17\n          }, this)\n        }, {\n          title: '拍卖状态',\n          dataIndex: 'status',\n          key: 'status',\n          width: 100,\n          render: status => {\n            const statusInfo = itemStatusMap[status];\n            return /*#__PURE__*/_jsxDEV(Badge, {\n              status: status === AuctionItemStatus.ONGOING ? 'processing' : status === AuctionItemStatus.SOLD ? 'success' : status === AuctionItemStatus.UNSOLD ? 'warning' : status === AuctionItemStatus.WITHDRAWN ? 'error' : 'default',\n              text: /*#__PURE__*/_jsxDEV(Tag, {\n                color: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.color) || 'default',\n                children: (statusInfo === null || statusInfo === void 0 ? void 0 : statusInfo.label) || '未知'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 23\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 19\n            }, this);\n          }\n        }, {\n          title: '价格信息',\n          key: 'price',\n          width: 150,\n          render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u8D77\\u62CD: \\xA5\", record.startingPrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 444,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                color: '#f50',\n                fontWeight: 500\n              },\n              children: [\"\\u5F53\\u524D: \\xA5\", record.currentPrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 445,\n              columnNumber: 19\n            }, this), record.reservePrice && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: 12,\n                color: '#999'\n              },\n              children: [\"\\u4FDD\\u7559: \\xA5\", record.reservePrice.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 443,\n            columnNumber: 17\n          }, this)\n        }, {\n          title: '竞价信息',\n          key: 'bid',\n          width: 120,\n          render: (_, record) => /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u51FA\\u4EF7\\u6B21\\u6570: \", record.bidCount]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [\"\\u52A0\\u4EF7\\u5E45\\u5EA6: \\xA5\", record.bidIncrement.toFixed(2)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 463,\n              columnNumber: 19\n            }, this), record.highestBidder && /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: 12,\n                color: '#999'\n              },\n              children: [\"\\u6700\\u9AD8\\u51FA\\u4EF7\\u4EBA: \", record.highestBidder]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 465,\n              columnNumber: 21\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 461,\n            columnNumber: 17\n          }, this)\n        }, {\n          title: '创建时间',\n          dataIndex: 'createdAt',\n          key: 'createdAt',\n          width: 160,\n          render: text => new Date(text).toLocaleString()\n        }, {\n          title: '操作',\n          key: 'action',\n          width: 200,\n          fixed: 'right',\n          render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u67E5\\u770B\\u8BE6\\u60C5\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(EyeOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 490,\n                  columnNumber: 29\n                }, this),\n                onClick: () => {/* 查看详情 */}\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"\\u7F16\\u8F91\",\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"link\",\n                size: \"small\",\n                icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 498,\n                  columnNumber: 29\n                }, this),\n                onClick: () => handleEdit(record),\n                disabled: record.status === AuctionItemStatus.ONGOING\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 495,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 494,\n              columnNumber: 19\n            }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n              title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u62CD\\u5356\\u5546\\u54C1\\u5417\\uFF1F\",\n              onConfirm: () => handleDelete(record.id),\n              okText: \"\\u786E\\u5B9A\",\n              cancelText: \"\\u53D6\\u6D88\",\n              children: /*#__PURE__*/_jsxDEV(Tooltip, {\n                title: \"\\u5220\\u9664\",\n                children: /*#__PURE__*/_jsxDEV(Button, {\n                  type: \"link\",\n                  size: \"small\",\n                  danger: true,\n                  icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 31\n                  }, this),\n                  disabled: record.status === AuctionItemStatus.ONGOING\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 510,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 509,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 503,\n              columnNumber: 19\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 17\n          }, this)\n        }],\n        dataSource: auctionItems,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 379,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 378,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingItem ? '编辑拍卖商品' : '添加拍卖商品',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"auctionId\",\n          label: \"\\u62CD\\u5356\\u4F1A\",\n          rules: [{\n            required: true,\n            message: '请选择拍卖会'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u62CD\\u5356\\u4F1A\",\n            children: auctions.map(auction => /*#__PURE__*/_jsxDEV(Option, {\n              value: auction.id,\n              children: auction.title\n            }, auction.id, false, {\n              fileName: _jsxFileName,\n              lineNumber: 567,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 560,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"productId\",\n          label: \"\\u5546\\u54C1\",\n          rules: [{\n            required: true,\n            message: '请选择商品'\n          }],\n          children: /*#__PURE__*/_jsxDEV(Select, {\n            placeholder: \"\\u8BF7\\u9009\\u62E9\\u5546\\u54C1\",\n            showSearch: true,\n            optionFilterProp: \"children\",\n            children: products.map(product => /*#__PURE__*/_jsxDEV(Option, {\n              value: product.id,\n              children: [product.name, \" - \", product.code]\n            }, product.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 581,\n              columnNumber: 17\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 579,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 574,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"startingPrice\",\n              label: \"\\u8D77\\u62CD\\u4EF7\",\n              rules: [{\n                required: true,\n                message: '请输入起拍价'\n              }, {\n                type: 'number',\n                min: 0.01,\n                message: '起拍价必须大于0'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u8D77\\u62CD\\u4EF7\",\n                precision: 2,\n                min: 0.01,\n                addonBefore: \"\\xA5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 598,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 590,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 589,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"reservePrice\",\n              label: \"\\u4FDD\\u7559\\u4EF7\\uFF08\\u53EF\\u9009\\uFF09\",\n              rules: [{\n                type: 'number',\n                min: 0.01,\n                message: '保留价必须大于0'\n              }],\n              children: /*#__PURE__*/_jsxDEV(InputNumber, {\n                style: {\n                  width: '100%'\n                },\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u4FDD\\u7559\\u4EF7\",\n                precision: 2,\n                min: 0.01,\n                addonBefore: \"\\xA5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 615,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 588,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"bidIncrement\",\n          label: \"\\u52A0\\u4EF7\\u5E45\\u5EA6\",\n          rules: [{\n            required: true,\n            message: '请输入加价幅度'\n          }, {\n            type: 'number',\n            min: 0.01,\n            message: '加价幅度必须大于0'\n          }],\n          children: /*#__PURE__*/_jsxDEV(InputNumber, {\n            style: {\n              width: '100%'\n            },\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u52A0\\u4EF7\\u5E45\\u5EA6\",\n            precision: 2,\n            min: 0.01,\n            addonBefore: \"\\xA5\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 634,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 626,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingItem ? '更新' : '添加'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 648,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 554,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 547,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 258,\n    columnNumber: 5\n  }, this);\n};\n_s(AuctionItems, \"bx8HqCy6XdInLkobVK35dKuH8/8=\", false, function () {\n  return [Form.useForm];\n});\n_c = AuctionItems;\nexport default AuctionItems;\nvar _c;\n$RefreshReg$(_c, \"AuctionItems\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Typography", "Row", "Col", "InputNumber", "Image", "Popconfirm", "Statistic", "Badge", "<PERSON><PERSON><PERSON>", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "EyeOutlined", "ReloadOutlined", "auctionService", "productService", "jsxDEV", "_jsxDEV", "Title", "Option", "AuctionItemStatus", "AuctionItems", "_s", "auctionItems", "setAuctionItems", "products", "setProducts", "auctions", "setAuctions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "editingItem", "setEditingItem", "statistics", "setStatistics", "totalItems", "pendingItems", "ongoingItems", "soldItems", "totalValue", "form", "useForm", "itemStatusMap", "PENDING", "label", "color", "ONGOING", "SOLD", "UNSOLD", "WITHDRAWN", "fetchAuctionItems", "mockData", "success", "data", "list", "error", "fetchProducts", "response", "getProductList", "status", "console", "fetchAuctions", "getAuctionList", "fetchStatistics", "handleSearch", "values", "handleReset", "handleAdd", "resetFields", "handleEdit", "item", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "id", "handleSave", "addAuctionItem", "auctionId", "productId", "startingPrice", "reservePrice", "bidIncrement", "style", "padding", "children", "level", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "gutter", "marginBottom", "xs", "sm", "md", "title", "value", "prefix", "GavelOutlined", "valueStyle", "className", "size", "layout", "onFinish", "autoComplete", "width", "<PERSON><PERSON>", "name", "placeholder", "allowClear", "map", "auction", "type", "htmlType", "icon", "onClick", "justify", "align", "columns", "dataIndex", "key", "render", "_", "record", "display", "alignItems", "images", "length", "height", "src", "marginRight", "borderRadius", "fallback", "fontWeight", "productName", "fontSize", "productCode", "quantity", "unit", "statusInfo", "text", "toFixed", "currentPrice", "bidCount", "highestBidder", "Date", "toLocaleString", "fixed", "disabled", "onConfirm", "okText", "cancelText", "danger", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "open", "onCancel", "footer", "rules", "required", "showSearch", "optionFilterProp", "product", "code", "span", "min", "precision", "addonBefore", "justifyContent", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Typography,\n  Row,\n  Col,\n  InputNumber,\n  Image,\n  Popconfirm,\n  Statistic,\n  Badge,\n  Tooltip,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  EyeOutlined,\n  ReloadOutlined,\n  AuditOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { auctionService } from '../../../services/auctionService';\nimport { productService } from '../../../services/productService';\n\nconst { Title } = Typography;\nconst { Option } = Select;\n\n// 拍卖商品状态枚举\nexport enum AuctionItemStatus {\n  PENDING = 'pending',     // 待拍卖\n  ONGOING = 'ongoing',     // 拍卖中\n  SOLD = 'sold',          // 已成交\n  UNSOLD = 'unsold',      // 流拍\n  WITHDRAWN = 'withdrawn', // 撤回\n}\n\n// 拍卖商品接口\nexport interface AuctionItem {\n  id: number;\n  auctionId: number;\n  productId: number;\n  productName: string;\n  productCode: string;\n  startingPrice: number;\n  currentPrice: number;\n  reservePrice?: number;\n  bidIncrement: number;\n  bidCount: number;\n  status: AuctionItemStatus;\n  images?: string[];\n  category: string;\n  quality: string;\n  quantity: number;\n  unit: string;\n  highestBidder?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数接口\ninterface AuctionItemQueryParams {\n  auctionId?: number;\n  productName?: string;\n  status?: AuctionItemStatus;\n  category?: string;\n  page: number;\n  pageSize: number;\n}\n\nconst AuctionItems: React.FC = () => {\n  const [auctionItems, setAuctionItems] = useState<AuctionItem[]>([]);\n  const [products, setProducts] = useState<any[]>([]);\n  const [auctions, setAuctions] = useState<any[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<AuctionItemQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingItem, setEditingItem] = useState<AuctionItem | null>(null);\n  const [statistics, setStatistics] = useState({\n    totalItems: 0,\n    pendingItems: 0,\n    ongoingItems: 0,\n    soldItems: 0,\n    totalValue: 0,\n  });\n  const [form] = Form.useForm();\n\n  // 拍卖商品状态映射\n  const itemStatusMap = {\n    [AuctionItemStatus.PENDING]: { label: '待拍卖', color: 'default' },\n    [AuctionItemStatus.ONGOING]: { label: '拍卖中', color: 'blue' },\n    [AuctionItemStatus.SOLD]: { label: '已成交', color: 'green' },\n    [AuctionItemStatus.UNSOLD]: { label: '流拍', color: 'orange' },\n    [AuctionItemStatus.WITHDRAWN]: { label: '撤回', color: 'red' },\n  };\n\n  // 获取拍卖商品列表\n  const fetchAuctionItems = async () => {\n    setLoading(true);\n    try {\n      // 这里需要调用后端API获取拍卖商品列表\n      // 暂时使用模拟数据\n      const mockData = {\n        success: true,\n        data: {\n          list: [],\n          total: 0,\n          page: queryParams.page,\n          pageSize: queryParams.pageSize,\n        },\n      };\n\n      setAuctionItems(mockData.data.list);\n      setTotal(mockData.data.total);\n    } catch (error: any) {\n      message.error(error.message || '获取拍卖商品列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取商品列表（用于添加拍卖商品）\n  const fetchProducts = async () => {\n    try {\n      const response = await productService.getProductList({\n        page: 1,\n        pageSize: 100,\n        status: 1, // 只获取已审核的商品\n      });\n      if (response.success) {\n        setProducts(response.data.list);\n      }\n    } catch (error: any) {\n      console.error('获取商品列表失败:', error);\n    }\n  };\n\n  // 获取拍卖会列表\n  const fetchAuctions = async () => {\n    try {\n      const response = await auctionService.getAuctionList({\n        page: 1,\n        pageSize: 100,\n      });\n      if (response.success) {\n        setAuctions(response.data.list);\n      }\n    } catch (error: any) {\n      console.error('获取拍卖会列表失败:', error);\n    }\n  };\n\n  // 获取统计信息\n  const fetchStatistics = async () => {\n    try {\n      // 这里需要调用后端API获取统计信息\n      // 暂时使用模拟数据\n      setStatistics({\n        totalItems: 0,\n        pendingItems: 0,\n        ongoingItems: 0,\n        soldItems: 0,\n        totalValue: 0,\n      });\n    } catch (error: any) {\n      console.error('获取统计信息失败:', error);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchAuctionItems();\n    fetchProducts();\n    fetchAuctions();\n    fetchStatistics();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增拍卖商品\n  const handleAdd = () => {\n    setEditingItem(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑拍卖商品\n  const handleEdit = (item: AuctionItem) => {\n    setEditingItem(item);\n    form.setFieldsValue(item);\n    setIsModalVisible(true);\n  };\n\n  // 删除拍卖商品\n  const handleDelete = async (id: number) => {\n    try {\n      // 调用删除API\n      message.success('删除成功');\n      fetchAuctionItems();\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存拍卖商品\n  const handleSave = async (values: any) => {\n    try {\n      if (editingItem) {\n        // 更新拍卖商品\n        message.success('更新成功');\n      } else {\n        // 添加拍卖商品\n        await auctionService.addAuctionItem(values.auctionId, {\n          productId: values.productId,\n          startingPrice: values.startingPrice,\n          reservePrice: values.reservePrice,\n          bidIncrement: values.bidIncrement,\n        });\n        message.success('添加成功');\n      }\n      setIsModalVisible(false);\n      fetchAuctionItems();\n    } catch (error: any) {\n      message.error(error.message || '保存失败');\n    }\n  };\n\n  return (\n    <div style={{ padding: 24 }}>\n      <Title level={2}>拍卖商品管理</Title>\n\n      {/* 统计卡片 */}\n      <Row gutter={16} style={{ marginBottom: 24 }}>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"总商品数\"\n              value={statistics.totalItems}\n              prefix={<GavelOutlined />}\n              valueStyle={{ color: '#3f8600' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"待拍卖\"\n              value={statistics.pendingItems}\n              valueStyle={{ color: '#1890ff' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"拍卖中\"\n              value={statistics.ongoingItems}\n              valueStyle={{ color: '#722ed1' }}\n            />\n          </Card>\n        </Col>\n        <Col xs={24} sm={12} md={6}>\n          <Card>\n            <Statistic\n              title=\"已成交\"\n              value={statistics.soldItems}\n              valueStyle={{ color: '#52c41a' }}\n            />\n          </Card>\n        </Col>\n      </Row>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\" style={{ marginBottom: 16 }}>\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"productName\" label=\"商品名称\">\n                <Input placeholder=\"请输入商品名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"拍卖状态\">\n                <Select placeholder=\"请选择拍卖状态\" allowClear>\n                  <Option value={AuctionItemStatus.PENDING}>待拍卖</Option>\n                  <Option value={AuctionItemStatus.ONGOING}>拍卖中</Option>\n                  <Option value={AuctionItemStatus.SOLD}>已成交</Option>\n                  <Option value={AuctionItemStatus.UNSOLD}>流拍</Option>\n                  <Option value={AuctionItemStatus.WITHDRAWN}>撤回</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"auctionId\" label=\"拍卖会\">\n                <Select placeholder=\"请选择拍卖会\" allowClear>\n                  {auctions.map(auction => (\n                    <Option key={auction.id} value={auction.id}>\n                      {auction.title}\n                    </Option>\n                  ))}\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\" style={{ marginBottom: 16 }}>\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              添加拍卖商品\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchAuctionItems}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 拍卖商品列表表格 */}\n      <Card>\n        <Table\n          columns={[\n            {\n              title: 'ID',\n              dataIndex: 'id',\n              key: 'id',\n              width: 80,\n            },\n            {\n              title: '商品信息',\n              key: 'product',\n              width: 250,\n              render: (_, record: AuctionItem) => (\n                <div style={{ display: 'flex', alignItems: 'center' }}>\n                  {record.images && record.images.length > 0 && (\n                    <Image\n                      width={60}\n                      height={60}\n                      src={record.images[0]}\n                      style={{ marginRight: 12, borderRadius: 4 }}\n                      fallback=\"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN\"\n                    />\n                  )}\n                  <div>\n                    <div style={{ fontWeight: 500 }}>{record.productName}</div>\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      编号: {record.productCode}\n                    </div>\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      {record.quantity} {record.unit}\n                    </div>\n                  </div>\n                </div>\n              ),\n            },\n            {\n              title: '拍卖状态',\n              dataIndex: 'status',\n              key: 'status',\n              width: 100,\n              render: (status: AuctionItemStatus) => {\n                const statusInfo = itemStatusMap[status];\n                return (\n                  <Badge\n                    status={\n                      status === AuctionItemStatus.ONGOING ? 'processing' :\n                      status === AuctionItemStatus.SOLD ? 'success' :\n                      status === AuctionItemStatus.UNSOLD ? 'warning' :\n                      status === AuctionItemStatus.WITHDRAWN ? 'error' : 'default'\n                    }\n                    text={\n                      <Tag color={statusInfo?.color || 'default'}>\n                        {statusInfo?.label || '未知'}\n                      </Tag>\n                    }\n                  />\n                );\n              },\n            },\n            {\n              title: '价格信息',\n              key: 'price',\n              width: 150,\n              render: (_, record: AuctionItem) => (\n                <div>\n                  <div>起拍: ¥{record.startingPrice.toFixed(2)}</div>\n                  <div style={{ color: '#f50', fontWeight: 500 }}>\n                    当前: ¥{record.currentPrice.toFixed(2)}\n                  </div>\n                  {record.reservePrice && (\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      保留: ¥{record.reservePrice.toFixed(2)}\n                    </div>\n                  )}\n                </div>\n              ),\n            },\n            {\n              title: '竞价信息',\n              key: 'bid',\n              width: 120,\n              render: (_, record: AuctionItem) => (\n                <div>\n                  <div>出价次数: {record.bidCount}</div>\n                  <div>加价幅度: ¥{record.bidIncrement.toFixed(2)}</div>\n                  {record.highestBidder && (\n                    <div style={{ fontSize: 12, color: '#999' }}>\n                      最高出价人: {record.highestBidder}\n                    </div>\n                  )}\n                </div>\n              ),\n            },\n            {\n              title: '创建时间',\n              dataIndex: 'createdAt',\n              key: 'createdAt',\n              width: 160,\n              render: (text: string) => new Date(text).toLocaleString(),\n            },\n            {\n              title: '操作',\n              key: 'action',\n              width: 200,\n              fixed: 'right',\n              render: (_, record: AuctionItem) => (\n                <Space size=\"small\">\n                  <Tooltip title=\"查看详情\">\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      icon={<EyeOutlined />}\n                      onClick={() => {/* 查看详情 */}}\n                    />\n                  </Tooltip>\n                  <Tooltip title=\"编辑\">\n                    <Button\n                      type=\"link\"\n                      size=\"small\"\n                      icon={<EditOutlined />}\n                      onClick={() => handleEdit(record)}\n                      disabled={record.status === AuctionItemStatus.ONGOING}\n                    />\n                  </Tooltip>\n                  <Popconfirm\n                    title=\"确定要删除这个拍卖商品吗？\"\n                    onConfirm={() => handleDelete(record.id)}\n                    okText=\"确定\"\n                    cancelText=\"取消\"\n                  >\n                    <Tooltip title=\"删除\">\n                      <Button\n                        type=\"link\"\n                        size=\"small\"\n                        danger\n                        icon={<DeleteOutlined />}\n                        disabled={record.status === AuctionItemStatus.ONGOING}\n                      />\n                    </Tooltip>\n                  </Popconfirm>\n                </Space>\n              ),\n            },\n          ]}\n          dataSource={auctionItems}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 拍卖商品编辑模态框 */}\n      <Modal\n        title={editingItem ? '编辑拍卖商品' : '添加拍卖商品'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Form.Item\n            name=\"auctionId\"\n            label=\"拍卖会\"\n            rules={[{ required: true, message: '请选择拍卖会' }]}\n          >\n            <Select placeholder=\"请选择拍卖会\">\n              {auctions.map(auction => (\n                <Option key={auction.id} value={auction.id}>\n                  {auction.title}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Form.Item\n            name=\"productId\"\n            label=\"商品\"\n            rules={[{ required: true, message: '请选择商品' }]}\n          >\n            <Select placeholder=\"请选择商品\" showSearch optionFilterProp=\"children\">\n              {products.map(product => (\n                <Option key={product.id} value={product.id}>\n                  {product.name} - {product.code}\n                </Option>\n              ))}\n            </Select>\n          </Form.Item>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"startingPrice\"\n                label=\"起拍价\"\n                rules={[\n                  { required: true, message: '请输入起拍价' },\n                  { type: 'number', min: 0.01, message: '起拍价必须大于0' },\n                ]}\n              >\n                <InputNumber\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入起拍价\"\n                  precision={2}\n                  min={0.01}\n                  addonBefore=\"¥\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"reservePrice\"\n                label=\"保留价（可选）\"\n                rules={[\n                  { type: 'number', min: 0.01, message: '保留价必须大于0' },\n                ]}\n              >\n                <InputNumber\n                  style={{ width: '100%' }}\n                  placeholder=\"请输入保留价\"\n                  precision={2}\n                  min={0.01}\n                  addonBefore=\"¥\"\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"bidIncrement\"\n            label=\"加价幅度\"\n            rules={[\n              { required: true, message: '请输入加价幅度' },\n              { type: 'number', min: 0.01, message: '加价幅度必须大于0' },\n            ]}\n          >\n            <InputNumber\n              style={{ width: '100%' }}\n              placeholder=\"请输入加价幅度\"\n              precision={2}\n              min={0.01}\n              addonBefore=\"¥\"\n            />\n          </Form.Item>\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setIsModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingItem ? '更新' : '添加'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default AuctionItems;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,WAAW,EACXC,KAAK,EACLC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,OAAO,QACF,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,WAAW,EACXC,cAAc,QAET,mBAAmB;AAE1B,SAASC,cAAc,QAAQ,kCAAkC;AACjE,SAASC,cAAc,QAAQ,kCAAkC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAM;EAAEC;AAAM,CAAC,GAAGnB,UAAU;AAC5B,MAAM;EAAEoB;AAAO,CAAC,GAAGzB,MAAM;;AAEzB;AACA,WAAY0B,iBAAiB,0BAAjBA,iBAAiB;EAAjBA,iBAAiB;EACF;EADfA,iBAAiB;EAEF;EAFfA,iBAAiB;EAGH;EAHdA,iBAAiB;EAIH;EAJdA,iBAAiB,6BAKF;EAAA,OALfA,iBAAiB;AAAA;;AAQ7B;;AAuBA;;AAUA,MAAMC,YAAsB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACnC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGrC,QAAQ,CAAgB,EAAE,CAAC;EACnE,MAAM,CAACsC,QAAQ,EAAEC,WAAW,CAAC,GAAGvC,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAACwC,QAAQ,EAAEC,WAAW,CAAC,GAAGzC,QAAQ,CAAQ,EAAE,CAAC;EACnD,MAAM,CAAC0C,OAAO,EAAEC,UAAU,CAAC,GAAG3C,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAAC4C,KAAK,EAAEC,QAAQ,CAAC,GAAG7C,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC8C,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAyB;IACrEgD,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGnD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACoD,WAAW,EAAEC,cAAc,CAAC,GAAGrD,QAAQ,CAAqB,IAAI,CAAC;EACxE,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC;IAC3CwD,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE,CAAC;IACfC,SAAS,EAAE,CAAC;IACZC,UAAU,EAAE;EACd,CAAC,CAAC;EACF,MAAM,CAACC,IAAI,CAAC,GAAGnD,IAAI,CAACoD,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,aAAa,GAAG;IACpB,CAAC9B,iBAAiB,CAAC+B,OAAO,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC;IAC/D,CAACjC,iBAAiB,CAACkC,OAAO,GAAG;MAAEF,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAC;IAC5D,CAACjC,iBAAiB,CAACmC,IAAI,GAAG;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAQ,CAAC;IAC1D,CAACjC,iBAAiB,CAACoC,MAAM,GAAG;MAAEJ,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAS,CAAC;IAC5D,CAACjC,iBAAiB,CAACqC,SAAS,GAAG;MAAEL,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAM;EAC7D,CAAC;;EAED;EACA,MAAMK,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC5B,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF;MACA;MACA,MAAM6B,QAAQ,GAAG;QACfC,OAAO,EAAE,IAAI;QACbC,IAAI,EAAE;UACJC,IAAI,EAAE,EAAE;UACR/B,KAAK,EAAE,CAAC;UACRI,IAAI,EAAEF,WAAW,CAACE,IAAI;UACtBC,QAAQ,EAAEH,WAAW,CAACG;QACxB;MACF,CAAC;MAEDZ,eAAe,CAACmC,QAAQ,CAACE,IAAI,CAACC,IAAI,CAAC;MACnC9B,QAAQ,CAAC2B,QAAQ,CAACE,IAAI,CAAC9B,KAAK,CAAC;IAC/B,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBjE,OAAO,CAACiE,KAAK,CAACA,KAAK,CAACjE,OAAO,IAAI,YAAY,CAAC;IAC9C,CAAC,SAAS;MACRgC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMkC,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMlD,cAAc,CAACmD,cAAc,CAAC;QACnD/B,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE,GAAG;QACb+B,MAAM,EAAE,CAAC,CAAE;MACb,CAAC,CAAC;MACF,IAAIF,QAAQ,CAACL,OAAO,EAAE;QACpBlC,WAAW,CAACuC,QAAQ,CAACJ,IAAI,CAACC,IAAI,CAAC;MACjC;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBK,OAAO,CAACL,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMM,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChC,IAAI;MACF,MAAMJ,QAAQ,GAAG,MAAMnD,cAAc,CAACwD,cAAc,CAAC;QACnDnC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI6B,QAAQ,CAACL,OAAO,EAAE;QACpBhC,WAAW,CAACqC,QAAQ,CAACJ,IAAI,CAACC,IAAI,CAAC;MACjC;IACF,CAAC,CAAC,OAAOC,KAAU,EAAE;MACnBK,OAAO,CAACL,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMQ,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClC,IAAI;MACF;MACA;MACA7B,aAAa,CAAC;QACZC,UAAU,EAAE,CAAC;QACbC,YAAY,EAAE,CAAC;QACfC,YAAY,EAAE,CAAC;QACfC,SAAS,EAAE,CAAC;QACZC,UAAU,EAAE;MACd,CAAC,CAAC;IACJ,CAAC,CAAC,OAAOgB,KAAU,EAAE;MACnBK,OAAO,CAACL,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA3E,SAAS,CAAC,MAAM;IACdsE,iBAAiB,CAAC,CAAC;IACnBM,aAAa,CAAC,CAAC;IACfK,aAAa,CAAC,CAAC;IACfE,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,CAACtC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMuC,YAAY,GAAIC,MAAW,IAAK;IACpCvC,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAGwC,MAAM;MACTtC,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMuC,WAAW,GAAGA,CAAA,KAAM;IACxBxC,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMuC,SAAS,GAAGA,CAAA,KAAM;IACtBnC,cAAc,CAAC,IAAI,CAAC;IACpBQ,IAAI,CAAC4B,WAAW,CAAC,CAAC;IAClBtC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMuC,UAAU,GAAIC,IAAiB,IAAK;IACxCtC,cAAc,CAACsC,IAAI,CAAC;IACpB9B,IAAI,CAAC+B,cAAc,CAACD,IAAI,CAAC;IACzBxC,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM0C,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF;MACAnF,OAAO,CAAC8D,OAAO,CAAC,MAAM,CAAC;MACvBF,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAU,EAAE;MACnBjE,OAAO,CAACiE,KAAK,CAACA,KAAK,CAACjE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMoF,UAAU,GAAG,MAAOT,MAAW,IAAK;IACxC,IAAI;MACF,IAAIlC,WAAW,EAAE;QACf;QACAzC,OAAO,CAAC8D,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACL;QACA,MAAM9C,cAAc,CAACqE,cAAc,CAACV,MAAM,CAACW,SAAS,EAAE;UACpDC,SAAS,EAAEZ,MAAM,CAACY,SAAS;UAC3BC,aAAa,EAAEb,MAAM,CAACa,aAAa;UACnCC,YAAY,EAAEd,MAAM,CAACc,YAAY;UACjCC,YAAY,EAAEf,MAAM,CAACe;QACvB,CAAC,CAAC;QACF1F,OAAO,CAAC8D,OAAO,CAAC,MAAM,CAAC;MACzB;MACAtB,iBAAiB,CAAC,KAAK,CAAC;MACxBoB,iBAAiB,CAAC,CAAC;IACrB,CAAC,CAAC,OAAOK,KAAU,EAAE;MACnBjE,OAAO,CAACiE,KAAK,CAACA,KAAK,CAACjE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;EAED,oBACEmB,OAAA;IAAKwE,KAAK,EAAE;MAAEC,OAAO,EAAE;IAAG,CAAE;IAAAC,QAAA,gBAC1B1E,OAAA,CAACC,KAAK;MAAC0E,KAAK,EAAE,CAAE;MAAAD,QAAA,EAAC;IAAM;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG/B/E,OAAA,CAACjB,GAAG;MAACiG,MAAM,EAAE,EAAG;MAACR,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,gBAC3C1E,OAAA,CAAChB,GAAG;QAACkG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACzB1E,OAAA,CAAC5B,IAAI;UAAAsG,QAAA,eACH1E,OAAA,CAACZ,SAAS;YACRiG,KAAK,EAAC,0BAAM;YACZC,KAAK,EAAE9D,UAAU,CAACE,UAAW;YAC7B6D,MAAM,eAAEvF,OAAA,CAACwF,aAAa;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BU,UAAU,EAAE;cAAErD,KAAK,EAAE;YAAU;UAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/E,OAAA,CAAChB,GAAG;QAACkG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACzB1E,OAAA,CAAC5B,IAAI;UAAAsG,QAAA,eACH1E,OAAA,CAACZ,SAAS;YACRiG,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE9D,UAAU,CAACG,YAAa;YAC/B8D,UAAU,EAAE;cAAErD,KAAK,EAAE;YAAU;UAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/E,OAAA,CAAChB,GAAG;QAACkG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACzB1E,OAAA,CAAC5B,IAAI;UAAAsG,QAAA,eACH1E,OAAA,CAACZ,SAAS;YACRiG,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE9D,UAAU,CAACI,YAAa;YAC/B6D,UAAU,EAAE;cAAErD,KAAK,EAAE;YAAU;UAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eACN/E,OAAA,CAAChB,GAAG;QAACkG,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,EAAG;QAACC,EAAE,EAAE,CAAE;QAAAV,QAAA,eACzB1E,OAAA,CAAC5B,IAAI;UAAAsG,QAAA,eACH1E,OAAA,CAACZ,SAAS;YACRiG,KAAK,EAAC,oBAAK;YACXC,KAAK,EAAE9D,UAAU,CAACK,SAAU;YAC5B4D,UAAU,EAAE;cAAErD,KAAK,EAAE;YAAU;UAAE;YAAAwC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN/E,OAAA,CAAC5B,IAAI;MAACsH,SAAS,EAAC,aAAa;MAACC,IAAI,EAAC,OAAO;MAACnB,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,eACrE1E,OAAA,CAACpB,IAAI;QACHgH,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEtC,YAAa;QACvBuC,YAAY,EAAC,KAAK;QAAApB,QAAA,eAElB1E,OAAA,CAACjB,GAAG;UAACiG,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACR,KAAK,EAAE;YAAEuB,KAAK,EAAE;UAAO,CAAE;UAAArB,QAAA,gBAC9C1E,OAAA,CAAChB,GAAG;YAACkG,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACzB1E,OAAA,CAACpB,IAAI,CAACoH,IAAI;cAACC,IAAI,EAAC,aAAa;cAAC9D,KAAK,EAAC,0BAAM;cAAAuC,QAAA,eACxC1E,OAAA,CAACxB,KAAK;gBAAC0H,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/E,OAAA,CAAChB,GAAG;YAACkG,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACzB1E,OAAA,CAACpB,IAAI,CAACoH,IAAI;cAACC,IAAI,EAAC,QAAQ;cAAC9D,KAAK,EAAC,0BAAM;cAAAuC,QAAA,eACnC1E,OAAA,CAACvB,MAAM;gBAACyH,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAzB,QAAA,gBACtC1E,OAAA,CAACE,MAAM;kBAACoF,KAAK,EAAEnF,iBAAiB,CAAC+B,OAAQ;kBAAAwC,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtD/E,OAAA,CAACE,MAAM;kBAACoF,KAAK,EAAEnF,iBAAiB,CAACkC,OAAQ;kBAAAqC,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACtD/E,OAAA,CAACE,MAAM;kBAACoF,KAAK,EAAEnF,iBAAiB,CAACmC,IAAK;kBAAAoC,QAAA,EAAC;gBAAG;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACnD/E,OAAA,CAACE,MAAM;kBAACoF,KAAK,EAAEnF,iBAAiB,CAACoC,MAAO;kBAAAmC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACpD/E,OAAA,CAACE,MAAM;kBAACoF,KAAK,EAAEnF,iBAAiB,CAACqC,SAAU;kBAAAkC,QAAA,EAAC;gBAAE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/E,OAAA,CAAChB,GAAG;YAACkG,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACzB1E,OAAA,CAACpB,IAAI,CAACoH,IAAI;cAACC,IAAI,EAAC,WAAW;cAAC9D,KAAK,EAAC,oBAAK;cAAAuC,QAAA,eACrC1E,OAAA,CAACvB,MAAM;gBAACyH,WAAW,EAAC,sCAAQ;gBAACC,UAAU;gBAAAzB,QAAA,EACpChE,QAAQ,CAAC0F,GAAG,CAACC,OAAO,iBACnBrG,OAAA,CAACE,MAAM;kBAAkBoF,KAAK,EAAEe,OAAO,CAACrC,EAAG;kBAAAU,QAAA,EACxC2B,OAAO,CAAChB;gBAAK,GADHgB,OAAO,CAACrC,EAAE;kBAAAY,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEf,CACT;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/E,OAAA,CAAChB,GAAG;YAACkG,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAAV,QAAA,eACzB1E,OAAA,CAACpB,IAAI,CAACoH,IAAI;cAAAtB,QAAA,eACR1E,OAAA,CAACzB,KAAK;gBAAAmG,QAAA,gBACJ1E,OAAA,CAAC1B,MAAM;kBAACgI,IAAI,EAAC,SAAS;kBAACC,QAAQ,EAAC,QAAQ;kBAACC,IAAI,eAAExG,OAAA,CAACR,cAAc;oBAAAoF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAL,QAAA,EAAC;gBAEnE;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/E,OAAA,CAAC1B,MAAM;kBAACmI,OAAO,EAAEhD,WAAY;kBAAC+C,IAAI,eAAExG,OAAA,CAACJ,cAAc;oBAAAgF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAL,QAAA,EAAC;gBAExD;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP/E,OAAA,CAAC5B,IAAI;MAACsH,SAAS,EAAC,aAAa;MAACC,IAAI,EAAC,OAAO;MAACnB,KAAK,EAAE;QAAES,YAAY,EAAE;MAAG,CAAE;MAAAP,QAAA,eACrE1E,OAAA,CAACjB,GAAG;QAAC2H,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAjC,QAAA,gBACzC1E,OAAA,CAAChB,GAAG;UAAA0F,QAAA,eACF1E,OAAA,CAAC1B,MAAM;YACLgI,IAAI,EAAC,SAAS;YACdE,IAAI,eAAExG,OAAA,CAACT,YAAY;cAAAqF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvB0B,OAAO,EAAE/C,SAAU;YAAAgB,QAAA,EACpB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/E,OAAA,CAAChB,GAAG;UAAA0F,QAAA,eACF1E,OAAA,CAAC1B,MAAM;YACLkI,IAAI,eAAExG,OAAA,CAACJ,cAAc;cAAAgF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzB0B,OAAO,EAAEhE,iBAAkB;YAC3B7B,OAAO,EAAEA,OAAQ;YAAA8D,QAAA,EAClB;UAED;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP/E,OAAA,CAAC5B,IAAI;MAAAsG,QAAA,eACH1E,OAAA,CAAC3B,KAAK;QACJuI,OAAO,EAAE,CACP;UACEvB,KAAK,EAAE,IAAI;UACXwB,SAAS,EAAE,IAAI;UACfC,GAAG,EAAE,IAAI;UACTf,KAAK,EAAE;QACT,CAAC,EACD;UACEV,KAAK,EAAE,MAAM;UACbyB,GAAG,EAAE,SAAS;UACdf,KAAK,EAAE,GAAG;UACVgB,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAmB,kBAC7BjH,OAAA;YAAKwE,KAAK,EAAE;cAAE0C,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAzC,QAAA,GACnDuC,MAAM,CAACG,MAAM,IAAIH,MAAM,CAACG,MAAM,CAACC,MAAM,GAAG,CAAC,iBACxCrH,OAAA,CAACd,KAAK;cACJ6G,KAAK,EAAE,EAAG;cACVuB,MAAM,EAAE,EAAG;cACXC,GAAG,EAAEN,MAAM,CAACG,MAAM,CAAC,CAAC,CAAE;cACtB5C,KAAK,EAAE;gBAAEgD,WAAW,EAAE,EAAE;gBAAEC,YAAY,EAAE;cAAE,CAAE;cAC5CC,QAAQ,EAAC;YAAgoB;cAAA9C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1oB,CACF,eACD/E,OAAA;cAAA0E,QAAA,gBACE1E,OAAA;gBAAKwE,KAAK,EAAE;kBAAEmD,UAAU,EAAE;gBAAI,CAAE;gBAAAjD,QAAA,EAAEuC,MAAM,CAACW;cAAW;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC3D/E,OAAA;gBAAKwE,KAAK,EAAE;kBAAEqD,QAAQ,EAAE,EAAE;kBAAEzF,KAAK,EAAE;gBAAO,CAAE;gBAAAsC,QAAA,GAAC,gBACvC,EAACuC,MAAM,CAACa,WAAW;cAAA;gBAAAlD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB,CAAC,eACN/E,OAAA;gBAAKwE,KAAK,EAAE;kBAAEqD,QAAQ,EAAE,EAAE;kBAAEzF,KAAK,EAAE;gBAAO,CAAE;gBAAAsC,QAAA,GACzCuC,MAAM,CAACc,QAAQ,EAAC,GAAC,EAACd,MAAM,CAACe,IAAI;cAAA;gBAAApD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC3B,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAET,CAAC,EACD;UACEM,KAAK,EAAE,MAAM;UACbwB,SAAS,EAAE,QAAQ;UACnBC,GAAG,EAAE,QAAQ;UACbf,KAAK,EAAE,GAAG;UACVgB,MAAM,EAAG7D,MAAyB,IAAK;YACrC,MAAM+E,UAAU,GAAGhG,aAAa,CAACiB,MAAM,CAAC;YACxC,oBACElD,OAAA,CAACX,KAAK;cACJ6D,MAAM,EACJA,MAAM,KAAK/C,iBAAiB,CAACkC,OAAO,GAAG,YAAY,GACnDa,MAAM,KAAK/C,iBAAiB,CAACmC,IAAI,GAAG,SAAS,GAC7CY,MAAM,KAAK/C,iBAAiB,CAACoC,MAAM,GAAG,SAAS,GAC/CW,MAAM,KAAK/C,iBAAiB,CAACqC,SAAS,GAAG,OAAO,GAAG,SACpD;cACD0F,IAAI,eACFlI,OAAA,CAACtB,GAAG;gBAAC0D,KAAK,EAAE,CAAA6F,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE7F,KAAK,KAAI,SAAU;gBAAAsC,QAAA,EACxC,CAAAuD,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAE9F,KAAK,KAAI;cAAI;gBAAAyC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YACN;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAEN;QACF,CAAC,EACD;UACEM,KAAK,EAAE,MAAM;UACbyB,GAAG,EAAE,OAAO;UACZf,KAAK,EAAE,GAAG;UACVgB,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAmB,kBAC7BjH,OAAA;YAAA0E,QAAA,gBACE1E,OAAA;cAAA0E,QAAA,GAAK,oBAAK,EAACuC,MAAM,CAAC5C,aAAa,CAAC8D,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjD/E,OAAA;cAAKwE,KAAK,EAAE;gBAAEpC,KAAK,EAAE,MAAM;gBAAEuF,UAAU,EAAE;cAAI,CAAE;cAAAjD,QAAA,GAAC,oBACzC,EAACuC,MAAM,CAACmB,YAAY,CAACD,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,EACLkC,MAAM,CAAC3C,YAAY,iBAClBtE,OAAA;cAAKwE,KAAK,EAAE;gBAAEqD,QAAQ,EAAE,EAAE;gBAAEzF,KAAK,EAAE;cAAO,CAAE;cAAAsC,QAAA,GAAC,oBACtC,EAACuC,MAAM,CAAC3C,YAAY,CAAC6D,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAET,CAAC,EACD;UACEM,KAAK,EAAE,MAAM;UACbyB,GAAG,EAAE,KAAK;UACVf,KAAK,EAAE,GAAG;UACVgB,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAmB,kBAC7BjH,OAAA;YAAA0E,QAAA,gBACE1E,OAAA;cAAA0E,QAAA,GAAK,4BAAM,EAACuC,MAAM,CAACoB,QAAQ;YAAA;cAAAzD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAClC/E,OAAA;cAAA0E,QAAA,GAAK,gCAAO,EAACuC,MAAM,CAAC1C,YAAY,CAAC4D,OAAO,CAAC,CAAC,CAAC;YAAA;cAAAvD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,EACjDkC,MAAM,CAACqB,aAAa,iBACnBtI,OAAA;cAAKwE,KAAK,EAAE;gBAAEqD,QAAQ,EAAE,EAAE;gBAAEzF,KAAK,EAAE;cAAO,CAAE;cAAAsC,QAAA,GAAC,kCACpC,EAACuC,MAAM,CAACqB,aAAa;YAAA;cAAA1D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CACN;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE;QAET,CAAC,EACD;UACEM,KAAK,EAAE,MAAM;UACbwB,SAAS,EAAE,WAAW;UACtBC,GAAG,EAAE,WAAW;UAChBf,KAAK,EAAE,GAAG;UACVgB,MAAM,EAAGmB,IAAY,IAAK,IAAIK,IAAI,CAACL,IAAI,CAAC,CAACM,cAAc,CAAC;QAC1D,CAAC,EACD;UACEnD,KAAK,EAAE,IAAI;UACXyB,GAAG,EAAE,QAAQ;UACbf,KAAK,EAAE,GAAG;UACV0C,KAAK,EAAE,OAAO;UACd1B,MAAM,EAAEA,CAACC,CAAC,EAAEC,MAAmB,kBAC7BjH,OAAA,CAACzB,KAAK;YAACoH,IAAI,EAAC,OAAO;YAAAjB,QAAA,gBACjB1E,OAAA,CAACV,OAAO;cAAC+F,KAAK,EAAC,0BAAM;cAAAX,QAAA,eACnB1E,OAAA,CAAC1B,MAAM;gBACLgI,IAAI,EAAC,MAAM;gBACXX,IAAI,EAAC,OAAO;gBACZa,IAAI,eAAExG,OAAA,CAACL,WAAW;kBAAAiF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACtB0B,OAAO,EAAEA,CAAA,KAAM,CAAC;cAAY;gBAAA7B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACV/E,OAAA,CAACV,OAAO;cAAC+F,KAAK,EAAC,cAAI;cAAAX,QAAA,eACjB1E,OAAA,CAAC1B,MAAM;gBACLgI,IAAI,EAAC,MAAM;gBACXX,IAAI,EAAC,OAAO;gBACZa,IAAI,eAAExG,OAAA,CAACP,YAAY;kBAAAmF,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACvB0B,OAAO,EAAEA,CAAA,KAAM7C,UAAU,CAACqD,MAAM,CAAE;gBAClCyB,QAAQ,EAAEzB,MAAM,CAAC/D,MAAM,KAAK/C,iBAAiB,CAACkC;cAAQ;gBAAAuC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACK,CAAC,eACV/E,OAAA,CAACb,UAAU;cACTkG,KAAK,EAAC,gFAAe;cACrBsD,SAAS,EAAEA,CAAA,KAAM5E,YAAY,CAACkD,MAAM,CAACjD,EAAE,CAAE;cACzC4E,MAAM,EAAC,cAAI;cACXC,UAAU,EAAC,cAAI;cAAAnE,QAAA,eAEf1E,OAAA,CAACV,OAAO;gBAAC+F,KAAK,EAAC,cAAI;gBAAAX,QAAA,eACjB1E,OAAA,CAAC1B,MAAM;kBACLgI,IAAI,EAAC,MAAM;kBACXX,IAAI,EAAC,OAAO;kBACZmD,MAAM;kBACNtC,IAAI,eAAExG,OAAA,CAACN,cAAc;oBAAAkF,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBACzB2D,QAAQ,EAAEzB,MAAM,CAAC/D,MAAM,KAAK/C,iBAAiB,CAACkC;gBAAQ;kBAAAuC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACvD;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR;QAEX,CAAC,CACD;QACFgE,UAAU,EAAEzI,YAAa;QACzB0I,MAAM,EAAC,IAAI;QACXpI,OAAO,EAAEA,OAAQ;QACjBqI,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAEpI,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZuI,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACzI,KAAK,EAAE0I,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ1I,KAAK,IAAI;UAC5C2I,QAAQ,EAAEA,CAACvI,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAAyD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP/E,OAAA,CAACrB,KAAK;MACJ0G,KAAK,EAAE/D,WAAW,GAAG,QAAQ,GAAG,QAAS;MACzCoI,IAAI,EAAEtI,cAAe;MACrBuI,QAAQ,EAAEA,CAAA,KAAMtI,iBAAiB,CAAC,KAAK,CAAE;MACzCuI,MAAM,EAAE,IAAK;MACb7D,KAAK,EAAE,GAAI;MAAArB,QAAA,eAEX1E,OAAA,CAACpB,IAAI;QACHmD,IAAI,EAAEA,IAAK;QACX6D,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAE5B,UAAW;QACrB6B,YAAY,EAAC,KAAK;QAAApB,QAAA,gBAElB1E,OAAA,CAACpB,IAAI,CAACoH,IAAI;UACRC,IAAI,EAAC,WAAW;UAChB9D,KAAK,EAAC,oBAAK;UACX0H,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEjL,OAAO,EAAE;UAAS,CAAC,CAAE;UAAA6F,QAAA,eAE/C1E,OAAA,CAACvB,MAAM;YAACyH,WAAW,EAAC,sCAAQ;YAAAxB,QAAA,EACzBhE,QAAQ,CAAC0F,GAAG,CAACC,OAAO,iBACnBrG,OAAA,CAACE,MAAM;cAAkBoF,KAAK,EAAEe,OAAO,CAACrC,EAAG;cAAAU,QAAA,EACxC2B,OAAO,CAAChB;YAAK,GADHgB,OAAO,CAACrC,EAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ/E,OAAA,CAACpB,IAAI,CAACoH,IAAI;UACRC,IAAI,EAAC,WAAW;UAChB9D,KAAK,EAAC,cAAI;UACV0H,KAAK,EAAE,CAAC;YAAEC,QAAQ,EAAE,IAAI;YAAEjL,OAAO,EAAE;UAAQ,CAAC,CAAE;UAAA6F,QAAA,eAE9C1E,OAAA,CAACvB,MAAM;YAACyH,WAAW,EAAC,gCAAO;YAAC6D,UAAU;YAACC,gBAAgB,EAAC,UAAU;YAAAtF,QAAA,EAC/DlE,QAAQ,CAAC4F,GAAG,CAAC6D,OAAO,iBACnBjK,OAAA,CAACE,MAAM;cAAkBoF,KAAK,EAAE2E,OAAO,CAACjG,EAAG;cAAAU,QAAA,GACxCuF,OAAO,CAAChE,IAAI,EAAC,KAAG,EAACgE,OAAO,CAACC,IAAI;YAAA,GADnBD,OAAO,CAACjG,EAAE;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAEf,CACT;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAEZ/E,OAAA,CAACjB,GAAG;UAACiG,MAAM,EAAE,EAAG;UAAAN,QAAA,gBACd1E,OAAA,CAAChB,GAAG;YAACmL,IAAI,EAAE,EAAG;YAAAzF,QAAA,eACZ1E,OAAA,CAACpB,IAAI,CAACoH,IAAI;cACRC,IAAI,EAAC,eAAe;cACpB9D,KAAK,EAAC,oBAAK;cACX0H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEjL,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAEyH,IAAI,EAAE,QAAQ;gBAAE8D,GAAG,EAAE,IAAI;gBAAEvL,OAAO,EAAE;cAAW,CAAC,CAClD;cAAA6F,QAAA,eAEF1E,OAAA,CAACf,WAAW;gBACVuF,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO,CAAE;gBACzBG,WAAW,EAAC,sCAAQ;gBACpBmE,SAAS,EAAE,CAAE;gBACbD,GAAG,EAAE,IAAK;gBACVE,WAAW,EAAC;cAAG;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/E,OAAA,CAAChB,GAAG;YAACmL,IAAI,EAAE,EAAG;YAAAzF,QAAA,eACZ1E,OAAA,CAACpB,IAAI,CAACoH,IAAI;cACRC,IAAI,EAAC,cAAc;cACnB9D,KAAK,EAAC,4CAAS;cACf0H,KAAK,EAAE,CACL;gBAAEvD,IAAI,EAAE,QAAQ;gBAAE8D,GAAG,EAAE,IAAI;gBAAEvL,OAAO,EAAE;cAAW,CAAC,CAClD;cAAA6F,QAAA,eAEF1E,OAAA,CAACf,WAAW;gBACVuF,KAAK,EAAE;kBAAEuB,KAAK,EAAE;gBAAO,CAAE;gBACzBG,WAAW,EAAC,sCAAQ;gBACpBmE,SAAS,EAAE,CAAE;gBACbD,GAAG,EAAE,IAAK;gBACVE,WAAW,EAAC;cAAG;gBAAA1F,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/E,OAAA,CAACpB,IAAI,CAACoH,IAAI;UACRC,IAAI,EAAC,cAAc;UACnB9D,KAAK,EAAC,0BAAM;UACZ0H,KAAK,EAAE,CACL;YAAEC,QAAQ,EAAE,IAAI;YAAEjL,OAAO,EAAE;UAAU,CAAC,EACtC;YAAEyH,IAAI,EAAE,QAAQ;YAAE8D,GAAG,EAAE,IAAI;YAAEvL,OAAO,EAAE;UAAY,CAAC,CACnD;UAAA6F,QAAA,eAEF1E,OAAA,CAACf,WAAW;YACVuF,KAAK,EAAE;cAAEuB,KAAK,EAAE;YAAO,CAAE;YACzBG,WAAW,EAAC,4CAAS;YACrBmE,SAAS,EAAE,CAAE;YACbD,GAAG,EAAE,IAAK;YACVE,WAAW,EAAC;UAAG;YAAA1F,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/E,OAAA,CAACpB,IAAI,CAACoH,IAAI;UAAAtB,QAAA,eACR1E,OAAA,CAACzB,KAAK;YAACiG,KAAK,EAAE;cAAEuB,KAAK,EAAE,MAAM;cAAEwE,cAAc,EAAE;YAAW,CAAE;YAAA7F,QAAA,gBAC1D1E,OAAA,CAAC1B,MAAM;cAACmI,OAAO,EAAEA,CAAA,KAAMpF,iBAAiB,CAAC,KAAK,CAAE;cAAAqD,QAAA,EAAC;YAEjD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/E,OAAA,CAAC1B,MAAM;cAACgI,IAAI,EAAC,SAAS;cAACC,QAAQ,EAAC,QAAQ;cAAA7B,QAAA,EACrCpD,WAAW,GAAG,IAAI,GAAG;YAAI;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1E,EAAA,CAhkBID,YAAsB;EAAA,QAmBXxB,IAAI,CAACoD,OAAO;AAAA;AAAAwI,EAAA,GAnBvBpK,YAAsB;AAkkB5B,eAAeA,YAAY;AAAC,IAAAoK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}