{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { messages as defaultMessages, newMessages } from \"./messages\";\nimport { asyncMap, complementError, convertFieldsError, deepMerge, format, warning } from \"./util\";\nimport validators from \"./validator/index\";\nexport * from \"./interface\";\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\nvar Schema = /*#__PURE__*/function () {\n  function Schema(descriptor) {\n    _classCallCheck(this, Schema);\n    // ======================== Instance ========================\n    _defineProperty(this, \"rules\", null);\n    _defineProperty(this, \"_messages\", defaultMessages);\n    this.define(descriptor);\n  }\n  _createClass(Schema, [{\n    key: \"define\",\n    value: function define(rules) {\n      var _this = this;\n      if (!rules) {\n        throw new Error('Cannot configure a schema with no rules');\n      }\n      if (_typeof(rules) !== 'object' || Array.isArray(rules)) {\n        throw new Error('Rules must be an object');\n      }\n      this.rules = {};\n      Object.keys(rules).forEach(function (name) {\n        var item = rules[name];\n        _this.rules[name] = Array.isArray(item) ? item : [item];\n      });\n    }\n  }, {\n    key: \"messages\",\n    value: function messages(_messages) {\n      if (_messages) {\n        this._messages = deepMerge(newMessages(), _messages);\n      }\n      return this._messages;\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(source_) {\n      var _this2 = this;\n      var o = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var oc = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {};\n      var source = source_;\n      var options = o;\n      var callback = oc;\n      if (typeof options === 'function') {\n        callback = options;\n        options = {};\n      }\n      if (!this.rules || Object.keys(this.rules).length === 0) {\n        if (callback) {\n          callback(null, source);\n        }\n        return Promise.resolve(source);\n      }\n      function complete(results) {\n        var errors = [];\n        var fields = {};\n        function add(e) {\n          if (Array.isArray(e)) {\n            var _errors;\n            errors = (_errors = errors).concat.apply(_errors, _toConsumableArray(e));\n          } else {\n            errors.push(e);\n          }\n        }\n        for (var i = 0; i < results.length; i++) {\n          add(results[i]);\n        }\n        if (!errors.length) {\n          callback(null, source);\n        } else {\n          fields = convertFieldsError(errors);\n          callback(errors, fields);\n        }\n      }\n      if (options.messages) {\n        var messages = this.messages();\n        if (messages === defaultMessages) {\n          messages = newMessages();\n        }\n        deepMerge(messages, options.messages);\n        options.messages = messages;\n      } else {\n        options.messages = this.messages();\n      }\n      var series = {};\n      var keys = options.keys || Object.keys(this.rules);\n      keys.forEach(function (z) {\n        var arr = _this2.rules[z];\n        var value = source[z];\n        arr.forEach(function (r) {\n          var rule = r;\n          if (typeof rule.transform === 'function') {\n            if (source === source_) {\n              source = _objectSpread({}, source);\n            }\n            value = source[z] = rule.transform(value);\n            if (value !== undefined && value !== null) {\n              rule.type = rule.type || (Array.isArray(value) ? 'array' : _typeof(value));\n            }\n          }\n          if (typeof rule === 'function') {\n            rule = {\n              validator: rule\n            };\n          } else {\n            rule = _objectSpread({}, rule);\n          }\n\n          // Fill validator. Skip if nothing need to validate\n          rule.validator = _this2.getValidationMethod(rule);\n          if (!rule.validator) {\n            return;\n          }\n          rule.field = z;\n          rule.fullField = rule.fullField || z;\n          rule.type = _this2.getType(rule);\n          series[z] = series[z] || [];\n          series[z].push({\n            rule: rule,\n            value: value,\n            source: source,\n            field: z\n          });\n        });\n      });\n      var errorFields = {};\n      return asyncMap(series, options, function (data, doIt) {\n        var rule = data.rule;\n        var deep = (rule.type === 'object' || rule.type === 'array') && (_typeof(rule.fields) === 'object' || _typeof(rule.defaultField) === 'object');\n        deep = deep && (rule.required || !rule.required && data.value);\n        rule.field = data.field;\n        function addFullField(key, schema) {\n          return _objectSpread(_objectSpread({}, schema), {}, {\n            fullField: \"\".concat(rule.fullField, \".\").concat(key),\n            fullFields: rule.fullFields ? [].concat(_toConsumableArray(rule.fullFields), [key]) : [key]\n          });\n        }\n        function cb() {\n          var e = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n          var errorList = Array.isArray(e) ? e : [e];\n          if (!options.suppressWarning && errorList.length) {\n            Schema.warning('async-validator:', errorList);\n          }\n          if (errorList.length && rule.message !== undefined) {\n            errorList = [].concat(rule.message);\n          }\n\n          // Fill error info\n          var filledErrors = errorList.map(complementError(rule, source));\n          if (options.first && filledErrors.length) {\n            errorFields[rule.field] = 1;\n            return doIt(filledErrors);\n          }\n          if (!deep) {\n            doIt(filledErrors);\n          } else {\n            // if rule is required but the target object\n            // does not exist fail at the rule level and don't\n            // go deeper\n            if (rule.required && !data.value) {\n              if (rule.message !== undefined) {\n                filledErrors = [].concat(rule.message).map(complementError(rule, source));\n              } else if (options.error) {\n                filledErrors = [options.error(rule, format(options.messages.required, rule.field))];\n              }\n              return doIt(filledErrors);\n            }\n            var fieldsSchema = {};\n            if (rule.defaultField) {\n              Object.keys(data.value).map(function (key) {\n                fieldsSchema[key] = rule.defaultField;\n              });\n            }\n            fieldsSchema = _objectSpread(_objectSpread({}, fieldsSchema), data.rule.fields);\n            var paredFieldsSchema = {};\n            Object.keys(fieldsSchema).forEach(function (field) {\n              var fieldSchema = fieldsSchema[field];\n              var fieldSchemaList = Array.isArray(fieldSchema) ? fieldSchema : [fieldSchema];\n              paredFieldsSchema[field] = fieldSchemaList.map(addFullField.bind(null, field));\n            });\n            var schema = new Schema(paredFieldsSchema);\n            schema.messages(options.messages);\n            if (data.rule.options) {\n              data.rule.options.messages = options.messages;\n              data.rule.options.error = options.error;\n            }\n            schema.validate(data.value, data.rule.options || options, function (errs) {\n              var finalErrors = [];\n              if (filledErrors && filledErrors.length) {\n                finalErrors.push.apply(finalErrors, _toConsumableArray(filledErrors));\n              }\n              if (errs && errs.length) {\n                finalErrors.push.apply(finalErrors, _toConsumableArray(errs));\n              }\n              doIt(finalErrors.length ? finalErrors : null);\n            });\n          }\n        }\n        var res;\n        if (rule.asyncValidator) {\n          res = rule.asyncValidator(rule, data.value, cb, data.source, options);\n        } else if (rule.validator) {\n          try {\n            res = rule.validator(rule, data.value, cb, data.source, options);\n          } catch (error) {\n            var _console$error, _console;\n            (_console$error = (_console = console).error) === null || _console$error === void 0 || _console$error.call(_console, error);\n            // rethrow to report error\n            if (!options.suppressValidatorError) {\n              setTimeout(function () {\n                throw error;\n              }, 0);\n            }\n            cb(error.message);\n          }\n          if (res === true) {\n            cb();\n          } else if (res === false) {\n            cb(typeof rule.message === 'function' ? rule.message(rule.fullField || rule.field) : rule.message || \"\".concat(rule.fullField || rule.field, \" fails\"));\n          } else if (res instanceof Array) {\n            cb(res);\n          } else if (res instanceof Error) {\n            cb(res.message);\n          }\n        }\n        if (res && res.then) {\n          res.then(function () {\n            return cb();\n          }, function (e) {\n            return cb(e);\n          });\n        }\n      }, function (results) {\n        complete(results);\n      }, source);\n    }\n  }, {\n    key: \"getType\",\n    value: function getType(rule) {\n      if (rule.type === undefined && rule.pattern instanceof RegExp) {\n        rule.type = 'pattern';\n      }\n      if (typeof rule.validator !== 'function' && rule.type && !validators.hasOwnProperty(rule.type)) {\n        throw new Error(format('Unknown rule type %s', rule.type));\n      }\n      return rule.type || 'string';\n    }\n  }, {\n    key: \"getValidationMethod\",\n    value: function getValidationMethod(rule) {\n      if (typeof rule.validator === 'function') {\n        return rule.validator;\n      }\n      var keys = Object.keys(rule);\n      var messageIndex = keys.indexOf('message');\n      if (messageIndex !== -1) {\n        keys.splice(messageIndex, 1);\n      }\n      if (keys.length === 1 && keys[0] === 'required') {\n        return validators.required;\n      }\n      return validators[this.getType(rule)] || undefined;\n    }\n  }]);\n  return Schema;\n}();\n// ========================= Static =========================\n_defineProperty(Schema, \"register\", function register(type, validator) {\n  if (typeof validator !== 'function') {\n    throw new Error('Cannot register a validator by type, validator is not a function');\n  }\n  validators[type] = validator;\n});\n_defineProperty(Schema, \"warning\", warning);\n_defineProperty(Schema, \"messages\", defaultMessages);\n_defineProperty(Schema, \"validators\", validators);\nexport default Schema;", "map": {"version": 3, "names": ["_objectSpread", "_toConsumableArray", "_typeof", "_classCallCheck", "_createClass", "_defineProperty", "messages", "defaultMessages", "newMessages", "asyncMap", "complementError", "convertFieldsError", "deepMerge", "format", "warning", "validators", "<PERSON><PERSON><PERSON>", "descriptor", "define", "key", "value", "rules", "_this", "Error", "Array", "isArray", "Object", "keys", "for<PERSON>ach", "name", "item", "_messages", "validate", "source_", "_this2", "o", "arguments", "length", "undefined", "oc", "source", "options", "callback", "Promise", "resolve", "complete", "results", "errors", "fields", "add", "e", "_errors", "concat", "apply", "push", "i", "series", "z", "arr", "r", "rule", "transform", "type", "validator", "getValidationMethod", "field", "fullField", "getType", "errorFields", "data", "doIt", "deep", "defaultField", "required", "addFullField", "schema", "fullFields", "cb", "errorList", "suppressWarning", "message", "filledErrors", "map", "first", "error", "fieldsSchema", "paredFieldsSchema", "fieldSchema", "fieldSchemaList", "bind", "errs", "finalErrors", "res", "asyncValidator", "_console$error", "_console", "console", "call", "suppressValidatorError", "setTimeout", "then", "pattern", "RegExp", "hasOwnProperty", "messageIndex", "indexOf", "splice", "register"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@rc-component/async-validator/es/index.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport { messages as defaultMessages, newMessages } from \"./messages\";\nimport { asyncMap, complementError, convertFieldsError, deepMerge, format, warning } from \"./util\";\nimport validators from \"./validator/index\";\nexport * from \"./interface\";\n\n/**\n *  Encapsulates a validation schema.\n *\n *  @param descriptor An object declaring validation rules\n *  for this schema.\n */\nvar Schema = /*#__PURE__*/function () {\n  function Schema(descriptor) {\n    _classCallCheck(this, Schema);\n    // ======================== Instance ========================\n    _defineProperty(this, \"rules\", null);\n    _defineProperty(this, \"_messages\", defaultMessages);\n    this.define(descriptor);\n  }\n  _createClass(Schema, [{\n    key: \"define\",\n    value: function define(rules) {\n      var _this = this;\n      if (!rules) {\n        throw new Error('Cannot configure a schema with no rules');\n      }\n      if (_typeof(rules) !== 'object' || Array.isArray(rules)) {\n        throw new Error('Rules must be an object');\n      }\n      this.rules = {};\n      Object.keys(rules).forEach(function (name) {\n        var item = rules[name];\n        _this.rules[name] = Array.isArray(item) ? item : [item];\n      });\n    }\n  }, {\n    key: \"messages\",\n    value: function messages(_messages) {\n      if (_messages) {\n        this._messages = deepMerge(newMessages(), _messages);\n      }\n      return this._messages;\n    }\n  }, {\n    key: \"validate\",\n    value: function validate(source_) {\n      var _this2 = this;\n      var o = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n      var oc = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : function () {};\n      var source = source_;\n      var options = o;\n      var callback = oc;\n      if (typeof options === 'function') {\n        callback = options;\n        options = {};\n      }\n      if (!this.rules || Object.keys(this.rules).length === 0) {\n        if (callback) {\n          callback(null, source);\n        }\n        return Promise.resolve(source);\n      }\n      function complete(results) {\n        var errors = [];\n        var fields = {};\n        function add(e) {\n          if (Array.isArray(e)) {\n            var _errors;\n            errors = (_errors = errors).concat.apply(_errors, _toConsumableArray(e));\n          } else {\n            errors.push(e);\n          }\n        }\n        for (var i = 0; i < results.length; i++) {\n          add(results[i]);\n        }\n        if (!errors.length) {\n          callback(null, source);\n        } else {\n          fields = convertFieldsError(errors);\n          callback(errors, fields);\n        }\n      }\n      if (options.messages) {\n        var messages = this.messages();\n        if (messages === defaultMessages) {\n          messages = newMessages();\n        }\n        deepMerge(messages, options.messages);\n        options.messages = messages;\n      } else {\n        options.messages = this.messages();\n      }\n      var series = {};\n      var keys = options.keys || Object.keys(this.rules);\n      keys.forEach(function (z) {\n        var arr = _this2.rules[z];\n        var value = source[z];\n        arr.forEach(function (r) {\n          var rule = r;\n          if (typeof rule.transform === 'function') {\n            if (source === source_) {\n              source = _objectSpread({}, source);\n            }\n            value = source[z] = rule.transform(value);\n            if (value !== undefined && value !== null) {\n              rule.type = rule.type || (Array.isArray(value) ? 'array' : _typeof(value));\n            }\n          }\n          if (typeof rule === 'function') {\n            rule = {\n              validator: rule\n            };\n          } else {\n            rule = _objectSpread({}, rule);\n          }\n\n          // Fill validator. Skip if nothing need to validate\n          rule.validator = _this2.getValidationMethod(rule);\n          if (!rule.validator) {\n            return;\n          }\n          rule.field = z;\n          rule.fullField = rule.fullField || z;\n          rule.type = _this2.getType(rule);\n          series[z] = series[z] || [];\n          series[z].push({\n            rule: rule,\n            value: value,\n            source: source,\n            field: z\n          });\n        });\n      });\n      var errorFields = {};\n      return asyncMap(series, options, function (data, doIt) {\n        var rule = data.rule;\n        var deep = (rule.type === 'object' || rule.type === 'array') && (_typeof(rule.fields) === 'object' || _typeof(rule.defaultField) === 'object');\n        deep = deep && (rule.required || !rule.required && data.value);\n        rule.field = data.field;\n        function addFullField(key, schema) {\n          return _objectSpread(_objectSpread({}, schema), {}, {\n            fullField: \"\".concat(rule.fullField, \".\").concat(key),\n            fullFields: rule.fullFields ? [].concat(_toConsumableArray(rule.fullFields), [key]) : [key]\n          });\n        }\n        function cb() {\n          var e = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n          var errorList = Array.isArray(e) ? e : [e];\n          if (!options.suppressWarning && errorList.length) {\n            Schema.warning('async-validator:', errorList);\n          }\n          if (errorList.length && rule.message !== undefined) {\n            errorList = [].concat(rule.message);\n          }\n\n          // Fill error info\n          var filledErrors = errorList.map(complementError(rule, source));\n          if (options.first && filledErrors.length) {\n            errorFields[rule.field] = 1;\n            return doIt(filledErrors);\n          }\n          if (!deep) {\n            doIt(filledErrors);\n          } else {\n            // if rule is required but the target object\n            // does not exist fail at the rule level and don't\n            // go deeper\n            if (rule.required && !data.value) {\n              if (rule.message !== undefined) {\n                filledErrors = [].concat(rule.message).map(complementError(rule, source));\n              } else if (options.error) {\n                filledErrors = [options.error(rule, format(options.messages.required, rule.field))];\n              }\n              return doIt(filledErrors);\n            }\n            var fieldsSchema = {};\n            if (rule.defaultField) {\n              Object.keys(data.value).map(function (key) {\n                fieldsSchema[key] = rule.defaultField;\n              });\n            }\n            fieldsSchema = _objectSpread(_objectSpread({}, fieldsSchema), data.rule.fields);\n            var paredFieldsSchema = {};\n            Object.keys(fieldsSchema).forEach(function (field) {\n              var fieldSchema = fieldsSchema[field];\n              var fieldSchemaList = Array.isArray(fieldSchema) ? fieldSchema : [fieldSchema];\n              paredFieldsSchema[field] = fieldSchemaList.map(addFullField.bind(null, field));\n            });\n            var schema = new Schema(paredFieldsSchema);\n            schema.messages(options.messages);\n            if (data.rule.options) {\n              data.rule.options.messages = options.messages;\n              data.rule.options.error = options.error;\n            }\n            schema.validate(data.value, data.rule.options || options, function (errs) {\n              var finalErrors = [];\n              if (filledErrors && filledErrors.length) {\n                finalErrors.push.apply(finalErrors, _toConsumableArray(filledErrors));\n              }\n              if (errs && errs.length) {\n                finalErrors.push.apply(finalErrors, _toConsumableArray(errs));\n              }\n              doIt(finalErrors.length ? finalErrors : null);\n            });\n          }\n        }\n        var res;\n        if (rule.asyncValidator) {\n          res = rule.asyncValidator(rule, data.value, cb, data.source, options);\n        } else if (rule.validator) {\n          try {\n            res = rule.validator(rule, data.value, cb, data.source, options);\n          } catch (error) {\n            var _console$error, _console;\n            (_console$error = (_console = console).error) === null || _console$error === void 0 || _console$error.call(_console, error);\n            // rethrow to report error\n            if (!options.suppressValidatorError) {\n              setTimeout(function () {\n                throw error;\n              }, 0);\n            }\n            cb(error.message);\n          }\n          if (res === true) {\n            cb();\n          } else if (res === false) {\n            cb(typeof rule.message === 'function' ? rule.message(rule.fullField || rule.field) : rule.message || \"\".concat(rule.fullField || rule.field, \" fails\"));\n          } else if (res instanceof Array) {\n            cb(res);\n          } else if (res instanceof Error) {\n            cb(res.message);\n          }\n        }\n        if (res && res.then) {\n          res.then(function () {\n            return cb();\n          }, function (e) {\n            return cb(e);\n          });\n        }\n      }, function (results) {\n        complete(results);\n      }, source);\n    }\n  }, {\n    key: \"getType\",\n    value: function getType(rule) {\n      if (rule.type === undefined && rule.pattern instanceof RegExp) {\n        rule.type = 'pattern';\n      }\n      if (typeof rule.validator !== 'function' && rule.type && !validators.hasOwnProperty(rule.type)) {\n        throw new Error(format('Unknown rule type %s', rule.type));\n      }\n      return rule.type || 'string';\n    }\n  }, {\n    key: \"getValidationMethod\",\n    value: function getValidationMethod(rule) {\n      if (typeof rule.validator === 'function') {\n        return rule.validator;\n      }\n      var keys = Object.keys(rule);\n      var messageIndex = keys.indexOf('message');\n      if (messageIndex !== -1) {\n        keys.splice(messageIndex, 1);\n      }\n      if (keys.length === 1 && keys[0] === 'required') {\n        return validators.required;\n      }\n      return validators[this.getType(rule)] || undefined;\n    }\n  }]);\n  return Schema;\n}();\n// ========================= Static =========================\n_defineProperty(Schema, \"register\", function register(type, validator) {\n  if (typeof validator !== 'function') {\n    throw new Error('Cannot register a validator by type, validator is not a function');\n  }\n  validators[type] = validator;\n});\n_defineProperty(Schema, \"warning\", warning);\n_defineProperty(Schema, \"messages\", defaultMessages);\n_defineProperty(Schema, \"validators\", validators);\nexport default Schema;"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,eAAe,MAAM,2CAA2C;AACvE,OAAOC,YAAY,MAAM,wCAAwC;AACjE,OAAOC,eAAe,MAAM,2CAA2C;AACvE,SAASC,QAAQ,IAAIC,eAAe,EAAEC,WAAW,QAAQ,YAAY;AACrE,SAASC,QAAQ,EAAEC,eAAe,EAAEC,kBAAkB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,OAAO,QAAQ,QAAQ;AAClG,OAAOC,UAAU,MAAM,mBAAmB;AAC1C,cAAc,aAAa;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA,IAAIC,MAAM,GAAG,aAAa,YAAY;EACpC,SAASA,MAAMA,CAACC,UAAU,EAAE;IAC1Bd,eAAe,CAAC,IAAI,EAAEa,MAAM,CAAC;IAC7B;IACAX,eAAe,CAAC,IAAI,EAAE,OAAO,EAAE,IAAI,CAAC;IACpCA,eAAe,CAAC,IAAI,EAAE,WAAW,EAAEE,eAAe,CAAC;IACnD,IAAI,CAACW,MAAM,CAACD,UAAU,CAAC;EACzB;EACAb,YAAY,CAACY,MAAM,EAAE,CAAC;IACpBG,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,SAASF,MAAMA,CAACG,KAAK,EAAE;MAC5B,IAAIC,KAAK,GAAG,IAAI;MAChB,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIE,KAAK,CAAC,yCAAyC,CAAC;MAC5D;MACA,IAAIrB,OAAO,CAACmB,KAAK,CAAC,KAAK,QAAQ,IAAIG,KAAK,CAACC,OAAO,CAACJ,KAAK,CAAC,EAAE;QACvD,MAAM,IAAIE,KAAK,CAAC,yBAAyB,CAAC;MAC5C;MACA,IAAI,CAACF,KAAK,GAAG,CAAC,CAAC;MACfK,MAAM,CAACC,IAAI,CAACN,KAAK,CAAC,CAACO,OAAO,CAAC,UAAUC,IAAI,EAAE;QACzC,IAAIC,IAAI,GAAGT,KAAK,CAACQ,IAAI,CAAC;QACtBP,KAAK,CAACD,KAAK,CAACQ,IAAI,CAAC,GAAGL,KAAK,CAACC,OAAO,CAACK,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;MACzD,CAAC,CAAC;IACJ;EACF,CAAC,EAAE;IACDX,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,SAASd,QAAQA,CAACyB,SAAS,EAAE;MAClC,IAAIA,SAAS,EAAE;QACb,IAAI,CAACA,SAAS,GAAGnB,SAAS,CAACJ,WAAW,CAAC,CAAC,EAAEuB,SAAS,CAAC;MACtD;MACA,OAAO,IAAI,CAACA,SAAS;IACvB;EACF,CAAC,EAAE;IACDZ,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,SAASY,QAAQA,CAACC,OAAO,EAAE;MAChC,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIC,CAAC,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;MAC9E,IAAIG,EAAE,GAAGH,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,CAAC;MAC3F,IAAII,MAAM,GAAGP,OAAO;MACpB,IAAIQ,OAAO,GAAGN,CAAC;MACf,IAAIO,QAAQ,GAAGH,EAAE;MACjB,IAAI,OAAOE,OAAO,KAAK,UAAU,EAAE;QACjCC,QAAQ,GAAGD,OAAO;QAClBA,OAAO,GAAG,CAAC,CAAC;MACd;MACA,IAAI,CAAC,IAAI,CAACpB,KAAK,IAAIK,MAAM,CAACC,IAAI,CAAC,IAAI,CAACN,KAAK,CAAC,CAACgB,MAAM,KAAK,CAAC,EAAE;QACvD,IAAIK,QAAQ,EAAE;UACZA,QAAQ,CAAC,IAAI,EAAEF,MAAM,CAAC;QACxB;QACA,OAAOG,OAAO,CAACC,OAAO,CAACJ,MAAM,CAAC;MAChC;MACA,SAASK,QAAQA,CAACC,OAAO,EAAE;QACzB,IAAIC,MAAM,GAAG,EAAE;QACf,IAAIC,MAAM,GAAG,CAAC,CAAC;QACf,SAASC,GAAGA,CAACC,CAAC,EAAE;UACd,IAAI1B,KAAK,CAACC,OAAO,CAACyB,CAAC,CAAC,EAAE;YACpB,IAAIC,OAAO;YACXJ,MAAM,GAAG,CAACI,OAAO,GAAGJ,MAAM,EAAEK,MAAM,CAACC,KAAK,CAACF,OAAO,EAAElD,kBAAkB,CAACiD,CAAC,CAAC,CAAC;UAC1E,CAAC,MAAM;YACLH,MAAM,CAACO,IAAI,CAACJ,CAAC,CAAC;UAChB;QACF;QACA,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGT,OAAO,CAACT,MAAM,EAAEkB,CAAC,EAAE,EAAE;UACvCN,GAAG,CAACH,OAAO,CAACS,CAAC,CAAC,CAAC;QACjB;QACA,IAAI,CAACR,MAAM,CAACV,MAAM,EAAE;UAClBK,QAAQ,CAAC,IAAI,EAAEF,MAAM,CAAC;QACxB,CAAC,MAAM;UACLQ,MAAM,GAAGrC,kBAAkB,CAACoC,MAAM,CAAC;UACnCL,QAAQ,CAACK,MAAM,EAAEC,MAAM,CAAC;QAC1B;MACF;MACA,IAAIP,OAAO,CAACnC,QAAQ,EAAE;QACpB,IAAIA,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;QAC9B,IAAIA,QAAQ,KAAKC,eAAe,EAAE;UAChCD,QAAQ,GAAGE,WAAW,CAAC,CAAC;QAC1B;QACAI,SAAS,CAACN,QAAQ,EAAEmC,OAAO,CAACnC,QAAQ,CAAC;QACrCmC,OAAO,CAACnC,QAAQ,GAAGA,QAAQ;MAC7B,CAAC,MAAM;QACLmC,OAAO,CAACnC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC,CAAC;MACpC;MACA,IAAIkD,MAAM,GAAG,CAAC,CAAC;MACf,IAAI7B,IAAI,GAAGc,OAAO,CAACd,IAAI,IAAID,MAAM,CAACC,IAAI,CAAC,IAAI,CAACN,KAAK,CAAC;MAClDM,IAAI,CAACC,OAAO,CAAC,UAAU6B,CAAC,EAAE;QACxB,IAAIC,GAAG,GAAGxB,MAAM,CAACb,KAAK,CAACoC,CAAC,CAAC;QACzB,IAAIrC,KAAK,GAAGoB,MAAM,CAACiB,CAAC,CAAC;QACrBC,GAAG,CAAC9B,OAAO,CAAC,UAAU+B,CAAC,EAAE;UACvB,IAAIC,IAAI,GAAGD,CAAC;UACZ,IAAI,OAAOC,IAAI,CAACC,SAAS,KAAK,UAAU,EAAE;YACxC,IAAIrB,MAAM,KAAKP,OAAO,EAAE;cACtBO,MAAM,GAAGxC,aAAa,CAAC,CAAC,CAAC,EAAEwC,MAAM,CAAC;YACpC;YACApB,KAAK,GAAGoB,MAAM,CAACiB,CAAC,CAAC,GAAGG,IAAI,CAACC,SAAS,CAACzC,KAAK,CAAC;YACzC,IAAIA,KAAK,KAAKkB,SAAS,IAAIlB,KAAK,KAAK,IAAI,EAAE;cACzCwC,IAAI,CAACE,IAAI,GAAGF,IAAI,CAACE,IAAI,KAAKtC,KAAK,CAACC,OAAO,CAACL,KAAK,CAAC,GAAG,OAAO,GAAGlB,OAAO,CAACkB,KAAK,CAAC,CAAC;YAC5E;UACF;UACA,IAAI,OAAOwC,IAAI,KAAK,UAAU,EAAE;YAC9BA,IAAI,GAAG;cACLG,SAAS,EAAEH;YACb,CAAC;UACH,CAAC,MAAM;YACLA,IAAI,GAAG5D,aAAa,CAAC,CAAC,CAAC,EAAE4D,IAAI,CAAC;UAChC;;UAEA;UACAA,IAAI,CAACG,SAAS,GAAG7B,MAAM,CAAC8B,mBAAmB,CAACJ,IAAI,CAAC;UACjD,IAAI,CAACA,IAAI,CAACG,SAAS,EAAE;YACnB;UACF;UACAH,IAAI,CAACK,KAAK,GAAGR,CAAC;UACdG,IAAI,CAACM,SAAS,GAAGN,IAAI,CAACM,SAAS,IAAIT,CAAC;UACpCG,IAAI,CAACE,IAAI,GAAG5B,MAAM,CAACiC,OAAO,CAACP,IAAI,CAAC;UAChCJ,MAAM,CAACC,CAAC,CAAC,GAAGD,MAAM,CAACC,CAAC,CAAC,IAAI,EAAE;UAC3BD,MAAM,CAACC,CAAC,CAAC,CAACH,IAAI,CAAC;YACbM,IAAI,EAAEA,IAAI;YACVxC,KAAK,EAAEA,KAAK;YACZoB,MAAM,EAAEA,MAAM;YACdyB,KAAK,EAAER;UACT,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;MACF,IAAIW,WAAW,GAAG,CAAC,CAAC;MACpB,OAAO3D,QAAQ,CAAC+C,MAAM,EAAEf,OAAO,EAAE,UAAU4B,IAAI,EAAEC,IAAI,EAAE;QACrD,IAAIV,IAAI,GAAGS,IAAI,CAACT,IAAI;QACpB,IAAIW,IAAI,GAAG,CAACX,IAAI,CAACE,IAAI,KAAK,QAAQ,IAAIF,IAAI,CAACE,IAAI,KAAK,OAAO,MAAM5D,OAAO,CAAC0D,IAAI,CAACZ,MAAM,CAAC,KAAK,QAAQ,IAAI9C,OAAO,CAAC0D,IAAI,CAACY,YAAY,CAAC,KAAK,QAAQ,CAAC;QAC9ID,IAAI,GAAGA,IAAI,KAAKX,IAAI,CAACa,QAAQ,IAAI,CAACb,IAAI,CAACa,QAAQ,IAAIJ,IAAI,CAACjD,KAAK,CAAC;QAC9DwC,IAAI,CAACK,KAAK,GAAGI,IAAI,CAACJ,KAAK;QACvB,SAASS,YAAYA,CAACvD,GAAG,EAAEwD,MAAM,EAAE;UACjC,OAAO3E,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2E,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YAClDT,SAAS,EAAE,EAAE,CAACd,MAAM,CAACQ,IAAI,CAACM,SAAS,EAAE,GAAG,CAAC,CAACd,MAAM,CAACjC,GAAG,CAAC;YACrDyD,UAAU,EAAEhB,IAAI,CAACgB,UAAU,GAAG,EAAE,CAACxB,MAAM,CAACnD,kBAAkB,CAAC2D,IAAI,CAACgB,UAAU,CAAC,EAAE,CAACzD,GAAG,CAAC,CAAC,GAAG,CAACA,GAAG;UAC5F,CAAC,CAAC;QACJ;QACA,SAAS0D,EAAEA,CAAA,EAAG;UACZ,IAAI3B,CAAC,GAAGd,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;UAC9E,IAAI0C,SAAS,GAAGtD,KAAK,CAACC,OAAO,CAACyB,CAAC,CAAC,GAAGA,CAAC,GAAG,CAACA,CAAC,CAAC;UAC1C,IAAI,CAACT,OAAO,CAACsC,eAAe,IAAID,SAAS,CAACzC,MAAM,EAAE;YAChDrB,MAAM,CAACF,OAAO,CAAC,kBAAkB,EAAEgE,SAAS,CAAC;UAC/C;UACA,IAAIA,SAAS,CAACzC,MAAM,IAAIuB,IAAI,CAACoB,OAAO,KAAK1C,SAAS,EAAE;YAClDwC,SAAS,GAAG,EAAE,CAAC1B,MAAM,CAACQ,IAAI,CAACoB,OAAO,CAAC;UACrC;;UAEA;UACA,IAAIC,YAAY,GAAGH,SAAS,CAACI,GAAG,CAACxE,eAAe,CAACkD,IAAI,EAAEpB,MAAM,CAAC,CAAC;UAC/D,IAAIC,OAAO,CAAC0C,KAAK,IAAIF,YAAY,CAAC5C,MAAM,EAAE;YACxC+B,WAAW,CAACR,IAAI,CAACK,KAAK,CAAC,GAAG,CAAC;YAC3B,OAAOK,IAAI,CAACW,YAAY,CAAC;UAC3B;UACA,IAAI,CAACV,IAAI,EAAE;YACTD,IAAI,CAACW,YAAY,CAAC;UACpB,CAAC,MAAM;YACL;YACA;YACA;YACA,IAAIrB,IAAI,CAACa,QAAQ,IAAI,CAACJ,IAAI,CAACjD,KAAK,EAAE;cAChC,IAAIwC,IAAI,CAACoB,OAAO,KAAK1C,SAAS,EAAE;gBAC9B2C,YAAY,GAAG,EAAE,CAAC7B,MAAM,CAACQ,IAAI,CAACoB,OAAO,CAAC,CAACE,GAAG,CAACxE,eAAe,CAACkD,IAAI,EAAEpB,MAAM,CAAC,CAAC;cAC3E,CAAC,MAAM,IAAIC,OAAO,CAAC2C,KAAK,EAAE;gBACxBH,YAAY,GAAG,CAACxC,OAAO,CAAC2C,KAAK,CAACxB,IAAI,EAAE/C,MAAM,CAAC4B,OAAO,CAACnC,QAAQ,CAACmE,QAAQ,EAAEb,IAAI,CAACK,KAAK,CAAC,CAAC,CAAC;cACrF;cACA,OAAOK,IAAI,CAACW,YAAY,CAAC;YAC3B;YACA,IAAII,YAAY,GAAG,CAAC,CAAC;YACrB,IAAIzB,IAAI,CAACY,YAAY,EAAE;cACrB9C,MAAM,CAACC,IAAI,CAAC0C,IAAI,CAACjD,KAAK,CAAC,CAAC8D,GAAG,CAAC,UAAU/D,GAAG,EAAE;gBACzCkE,YAAY,CAAClE,GAAG,CAAC,GAAGyC,IAAI,CAACY,YAAY;cACvC,CAAC,CAAC;YACJ;YACAa,YAAY,GAAGrF,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEqF,YAAY,CAAC,EAAEhB,IAAI,CAACT,IAAI,CAACZ,MAAM,CAAC;YAC/E,IAAIsC,iBAAiB,GAAG,CAAC,CAAC;YAC1B5D,MAAM,CAACC,IAAI,CAAC0D,YAAY,CAAC,CAACzD,OAAO,CAAC,UAAUqC,KAAK,EAAE;cACjD,IAAIsB,WAAW,GAAGF,YAAY,CAACpB,KAAK,CAAC;cACrC,IAAIuB,eAAe,GAAGhE,KAAK,CAACC,OAAO,CAAC8D,WAAW,CAAC,GAAGA,WAAW,GAAG,CAACA,WAAW,CAAC;cAC9ED,iBAAiB,CAACrB,KAAK,CAAC,GAAGuB,eAAe,CAACN,GAAG,CAACR,YAAY,CAACe,IAAI,CAAC,IAAI,EAAExB,KAAK,CAAC,CAAC;YAChF,CAAC,CAAC;YACF,IAAIU,MAAM,GAAG,IAAI3D,MAAM,CAACsE,iBAAiB,CAAC;YAC1CX,MAAM,CAACrE,QAAQ,CAACmC,OAAO,CAACnC,QAAQ,CAAC;YACjC,IAAI+D,IAAI,CAACT,IAAI,CAACnB,OAAO,EAAE;cACrB4B,IAAI,CAACT,IAAI,CAACnB,OAAO,CAACnC,QAAQ,GAAGmC,OAAO,CAACnC,QAAQ;cAC7C+D,IAAI,CAACT,IAAI,CAACnB,OAAO,CAAC2C,KAAK,GAAG3C,OAAO,CAAC2C,KAAK;YACzC;YACAT,MAAM,CAAC3C,QAAQ,CAACqC,IAAI,CAACjD,KAAK,EAAEiD,IAAI,CAACT,IAAI,CAACnB,OAAO,IAAIA,OAAO,EAAE,UAAUiD,IAAI,EAAE;cACxE,IAAIC,WAAW,GAAG,EAAE;cACpB,IAAIV,YAAY,IAAIA,YAAY,CAAC5C,MAAM,EAAE;gBACvCsD,WAAW,CAACrC,IAAI,CAACD,KAAK,CAACsC,WAAW,EAAE1F,kBAAkB,CAACgF,YAAY,CAAC,CAAC;cACvE;cACA,IAAIS,IAAI,IAAIA,IAAI,CAACrD,MAAM,EAAE;gBACvBsD,WAAW,CAACrC,IAAI,CAACD,KAAK,CAACsC,WAAW,EAAE1F,kBAAkB,CAACyF,IAAI,CAAC,CAAC;cAC/D;cACApB,IAAI,CAACqB,WAAW,CAACtD,MAAM,GAAGsD,WAAW,GAAG,IAAI,CAAC;YAC/C,CAAC,CAAC;UACJ;QACF;QACA,IAAIC,GAAG;QACP,IAAIhC,IAAI,CAACiC,cAAc,EAAE;UACvBD,GAAG,GAAGhC,IAAI,CAACiC,cAAc,CAACjC,IAAI,EAAES,IAAI,CAACjD,KAAK,EAAEyD,EAAE,EAAER,IAAI,CAAC7B,MAAM,EAAEC,OAAO,CAAC;QACvE,CAAC,MAAM,IAAImB,IAAI,CAACG,SAAS,EAAE;UACzB,IAAI;YACF6B,GAAG,GAAGhC,IAAI,CAACG,SAAS,CAACH,IAAI,EAAES,IAAI,CAACjD,KAAK,EAAEyD,EAAE,EAAER,IAAI,CAAC7B,MAAM,EAAEC,OAAO,CAAC;UAClE,CAAC,CAAC,OAAO2C,KAAK,EAAE;YACd,IAAIU,cAAc,EAAEC,QAAQ;YAC5B,CAACD,cAAc,GAAG,CAACC,QAAQ,GAAGC,OAAO,EAAEZ,KAAK,MAAM,IAAI,IAAIU,cAAc,KAAK,KAAK,CAAC,IAAIA,cAAc,CAACG,IAAI,CAACF,QAAQ,EAAEX,KAAK,CAAC;YAC3H;YACA,IAAI,CAAC3C,OAAO,CAACyD,sBAAsB,EAAE;cACnCC,UAAU,CAAC,YAAY;gBACrB,MAAMf,KAAK;cACb,CAAC,EAAE,CAAC,CAAC;YACP;YACAP,EAAE,CAACO,KAAK,CAACJ,OAAO,CAAC;UACnB;UACA,IAAIY,GAAG,KAAK,IAAI,EAAE;YAChBf,EAAE,CAAC,CAAC;UACN,CAAC,MAAM,IAAIe,GAAG,KAAK,KAAK,EAAE;YACxBf,EAAE,CAAC,OAAOjB,IAAI,CAACoB,OAAO,KAAK,UAAU,GAAGpB,IAAI,CAACoB,OAAO,CAACpB,IAAI,CAACM,SAAS,IAAIN,IAAI,CAACK,KAAK,CAAC,GAAGL,IAAI,CAACoB,OAAO,IAAI,EAAE,CAAC5B,MAAM,CAACQ,IAAI,CAACM,SAAS,IAAIN,IAAI,CAACK,KAAK,EAAE,QAAQ,CAAC,CAAC;UACzJ,CAAC,MAAM,IAAI2B,GAAG,YAAYpE,KAAK,EAAE;YAC/BqD,EAAE,CAACe,GAAG,CAAC;UACT,CAAC,MAAM,IAAIA,GAAG,YAAYrE,KAAK,EAAE;YAC/BsD,EAAE,CAACe,GAAG,CAACZ,OAAO,CAAC;UACjB;QACF;QACA,IAAIY,GAAG,IAAIA,GAAG,CAACQ,IAAI,EAAE;UACnBR,GAAG,CAACQ,IAAI,CAAC,YAAY;YACnB,OAAOvB,EAAE,CAAC,CAAC;UACb,CAAC,EAAE,UAAU3B,CAAC,EAAE;YACd,OAAO2B,EAAE,CAAC3B,CAAC,CAAC;UACd,CAAC,CAAC;QACJ;MACF,CAAC,EAAE,UAAUJ,OAAO,EAAE;QACpBD,QAAQ,CAACC,OAAO,CAAC;MACnB,CAAC,EAAEN,MAAM,CAAC;IACZ;EACF,CAAC,EAAE;IACDrB,GAAG,EAAE,SAAS;IACdC,KAAK,EAAE,SAAS+C,OAAOA,CAACP,IAAI,EAAE;MAC5B,IAAIA,IAAI,CAACE,IAAI,KAAKxB,SAAS,IAAIsB,IAAI,CAACyC,OAAO,YAAYC,MAAM,EAAE;QAC7D1C,IAAI,CAACE,IAAI,GAAG,SAAS;MACvB;MACA,IAAI,OAAOF,IAAI,CAACG,SAAS,KAAK,UAAU,IAAIH,IAAI,CAACE,IAAI,IAAI,CAAC/C,UAAU,CAACwF,cAAc,CAAC3C,IAAI,CAACE,IAAI,CAAC,EAAE;QAC9F,MAAM,IAAIvC,KAAK,CAACV,MAAM,CAAC,sBAAsB,EAAE+C,IAAI,CAACE,IAAI,CAAC,CAAC;MAC5D;MACA,OAAOF,IAAI,CAACE,IAAI,IAAI,QAAQ;IAC9B;EACF,CAAC,EAAE;IACD3C,GAAG,EAAE,qBAAqB;IAC1BC,KAAK,EAAE,SAAS4C,mBAAmBA,CAACJ,IAAI,EAAE;MACxC,IAAI,OAAOA,IAAI,CAACG,SAAS,KAAK,UAAU,EAAE;QACxC,OAAOH,IAAI,CAACG,SAAS;MACvB;MACA,IAAIpC,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACiC,IAAI,CAAC;MAC5B,IAAI4C,YAAY,GAAG7E,IAAI,CAAC8E,OAAO,CAAC,SAAS,CAAC;MAC1C,IAAID,YAAY,KAAK,CAAC,CAAC,EAAE;QACvB7E,IAAI,CAAC+E,MAAM,CAACF,YAAY,EAAE,CAAC,CAAC;MAC9B;MACA,IAAI7E,IAAI,CAACU,MAAM,KAAK,CAAC,IAAIV,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;QAC/C,OAAOZ,UAAU,CAAC0D,QAAQ;MAC5B;MACA,OAAO1D,UAAU,CAAC,IAAI,CAACoD,OAAO,CAACP,IAAI,CAAC,CAAC,IAAItB,SAAS;IACpD;EACF,CAAC,CAAC,CAAC;EACH,OAAOtB,MAAM;AACf,CAAC,CAAC,CAAC;AACH;AACAX,eAAe,CAACW,MAAM,EAAE,UAAU,EAAE,SAAS2F,QAAQA,CAAC7C,IAAI,EAAEC,SAAS,EAAE;EACrE,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;IACnC,MAAM,IAAIxC,KAAK,CAAC,kEAAkE,CAAC;EACrF;EACAR,UAAU,CAAC+C,IAAI,CAAC,GAAGC,SAAS;AAC9B,CAAC,CAAC;AACF1D,eAAe,CAACW,MAAM,EAAE,SAAS,EAAEF,OAAO,CAAC;AAC3CT,eAAe,CAACW,MAAM,EAAE,UAAU,EAAET,eAAe,CAAC;AACpDF,eAAe,CAACW,MAAM,EAAE,YAAY,EAAED,UAAU,CAAC;AACjD,eAAeC,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}