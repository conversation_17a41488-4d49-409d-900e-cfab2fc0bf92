{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar isFF = (typeof navigator === \"undefined\" ? \"undefined\" : _typeof(navigator)) === 'object' && /Firefox/i.test(navigator.userAgent);\nexport default isFF;", "map": {"version": 3, "names": ["_typeof", "isFF", "navigator", "test", "userAgent"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/rc-virtual-list/es/utils/isFirefox.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nvar isFF = (typeof navigator === \"undefined\" ? \"undefined\" : _typeof(navigator)) === 'object' && /Firefox/i.test(navigator.userAgent);\nexport default isFF;"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,IAAIC,IAAI,GAAG,CAAC,OAAOC,SAAS,KAAK,WAAW,GAAG,WAAW,GAAGF,OAAO,CAACE,SAAS,CAAC,MAAM,QAAQ,IAAI,UAAU,CAACC,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC;AACrI,eAAeH,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}