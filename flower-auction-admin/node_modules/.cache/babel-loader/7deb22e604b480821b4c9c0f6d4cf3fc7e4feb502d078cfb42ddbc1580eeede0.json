{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/AuthProvider/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { setUser, clearUser, setLoading } from '../../store/slices/authSlice';\nimport { authService } from '../../services/authService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\n// 全局初始化状态，确保只初始化一次\nlet hasInitialized = false;\nconst AuthProvider = ({\n  children\n}) => {\n  _s();\n  const dispatch = useDispatch();\n  const [isInitializing, setIsInitializing] = useState(!hasInitialized);\n  useEffect(() => {\n    const initializeAuth = async () => {\n      // 如果已经初始化过，直接返回\n      if (hasInitialized) {\n        setIsInitializing(false);\n        return;\n      }\n      const token = localStorage.getItem('token');\n      if (!token) {\n        dispatch(clearUser());\n        hasInitialized = true;\n        setIsInitializing(false);\n        return;\n      }\n      try {\n        dispatch(setLoading(true));\n        const response = await authService.getUserInfo();\n        if (response.success && response.data) {\n          dispatch(setUser(response.data));\n        } else {\n          // token无效，清除认证信息\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          dispatch(clearUser());\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        dispatch(clearUser());\n      } finally {\n        dispatch(setLoading(false));\n        hasInitialized = true;\n        setIsInitializing(false);\n      }\n    };\n    initializeAuth();\n  }, [dispatch]);\n\n  // 如果正在初始化，显示加载状态\n  if (isInitializing) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        flexDirection: 'column',\n        gap: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        children: \"\\u6B63\\u5728\\u521D\\u59CB\\u5316...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: children\n  }, void 0, false);\n};\n_s(AuthProvider, \"TgPzSd8skvJOixlMVEXtGbiCPyo=\", false, function () {\n  return [useDispatch];\n});\n_c = AuthProvider;\nexport default AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useDispatch", "setUser", "clearUser", "setLoading", "authService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "hasInitialized", "<PERSON>th<PERSON><PERSON><PERSON>", "children", "_s", "dispatch", "isInitializing", "setIsInitializing", "initializeAuth", "token", "localStorage", "getItem", "response", "getUserInfo", "success", "data", "removeItem", "error", "console", "style", "display", "justifyContent", "alignItems", "height", "flexDirection", "gap", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/AuthProvider/index.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { setUser, clearUser, setLoading } from '../../store/slices/authSlice';\nimport { authService } from '../../services/authService';\n\ninterface AuthProviderProps {\n  children: React.ReactNode;\n}\n\n// 全局初始化状态，确保只初始化一次\nlet hasInitialized = false;\n\nconst AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {\n  const dispatch = useDispatch();\n  const [isInitializing, setIsInitializing] = useState(!hasInitialized);\n\n  useEffect(() => {\n    const initializeAuth = async () => {\n      // 如果已经初始化过，直接返回\n      if (hasInitialized) {\n        setIsInitializing(false);\n        return;\n      }\n\n      const token = localStorage.getItem('token');\n      if (!token) {\n        dispatch(clearUser());\n        hasInitialized = true;\n        setIsInitializing(false);\n        return;\n      }\n\n      try {\n        dispatch(setLoading(true));\n        const response = await authService.getUserInfo();\n        if (response.success && response.data) {\n          dispatch(setUser(response.data));\n        } else {\n          // token无效，清除认证信息\n          localStorage.removeItem('token');\n          localStorage.removeItem('refreshToken');\n          dispatch(clearUser());\n        }\n      } catch (error) {\n        console.error('Auth initialization error:', error);\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        dispatch(clearUser());\n      } finally {\n        dispatch(setLoading(false));\n        hasInitialized = true;\n        setIsInitializing(false);\n      }\n    };\n\n    initializeAuth();\n  }, [dispatch]);\n\n  // 如果正在初始化，显示加载状态\n  if (isInitializing) {\n    return (\n      <div style={{\n        display: 'flex',\n        justifyContent: 'center',\n        alignItems: 'center',\n        height: '100vh',\n        flexDirection: 'column',\n        gap: '16px'\n      }}>\n        <div>正在初始化...</div>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n};\n\nexport default AuthProvider;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,OAAO,EAAEC,SAAS,EAAEC,UAAU,QAAQ,8BAA8B;AAC7E,SAASC,WAAW,QAAQ,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAMzD;AACA,IAAIC,cAAc,GAAG,KAAK;AAE1B,MAAMC,YAAyC,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAClE,MAAMC,QAAQ,GAAGb,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACc,cAAc,EAAEC,iBAAiB,CAAC,GAAGhB,QAAQ,CAAC,CAACU,cAAc,CAAC;EAErEX,SAAS,CAAC,MAAM;IACd,MAAMkB,cAAc,GAAG,MAAAA,CAAA,KAAY;MACjC;MACA,IAAIP,cAAc,EAAE;QAClBM,iBAAiB,CAAC,KAAK,CAAC;QACxB;MACF;MAEA,MAAME,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;MAC3C,IAAI,CAACF,KAAK,EAAE;QACVJ,QAAQ,CAACX,SAAS,CAAC,CAAC,CAAC;QACrBO,cAAc,GAAG,IAAI;QACrBM,iBAAiB,CAAC,KAAK,CAAC;QACxB;MACF;MAEA,IAAI;QACFF,QAAQ,CAACV,UAAU,CAAC,IAAI,CAAC,CAAC;QAC1B,MAAMiB,QAAQ,GAAG,MAAMhB,WAAW,CAACiB,WAAW,CAAC,CAAC;QAChD,IAAID,QAAQ,CAACE,OAAO,IAAIF,QAAQ,CAACG,IAAI,EAAE;UACrCV,QAAQ,CAACZ,OAAO,CAACmB,QAAQ,CAACG,IAAI,CAAC,CAAC;QAClC,CAAC,MAAM;UACL;UACAL,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;UAChCN,YAAY,CAACM,UAAU,CAAC,cAAc,CAAC;UACvCX,QAAQ,CAACX,SAAS,CAAC,CAAC,CAAC;QACvB;MACF,CAAC,CAAC,OAAOuB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClDP,YAAY,CAACM,UAAU,CAAC,OAAO,CAAC;QAChCN,YAAY,CAACM,UAAU,CAAC,cAAc,CAAC;QACvCX,QAAQ,CAACX,SAAS,CAAC,CAAC,CAAC;MACvB,CAAC,SAAS;QACRW,QAAQ,CAACV,UAAU,CAAC,KAAK,CAAC,CAAC;QAC3BM,cAAc,GAAG,IAAI;QACrBM,iBAAiB,CAAC,KAAK,CAAC;MAC1B;IACF,CAAC;IAEDC,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;;EAEd;EACA,IAAIC,cAAc,EAAE;IAClB,oBACER,OAAA;MAAKqB,KAAK,EAAE;QACVC,OAAO,EAAE,MAAM;QACfC,cAAc,EAAE,QAAQ;QACxBC,UAAU,EAAE,QAAQ;QACpBC,MAAM,EAAE,OAAO;QACfC,aAAa,EAAE,QAAQ;QACvBC,GAAG,EAAE;MACP,CAAE;MAAAtB,QAAA,eACAL,OAAA;QAAAK,QAAA,EAAK;MAAQ;QAAAuB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChB,CAAC;EAEV;EAEA,oBAAO/B,OAAA,CAAAE,SAAA;IAAAG,QAAA,EAAGA;EAAQ,gBAAG,CAAC;AACxB,CAAC;AAACC,EAAA,CA/DIF,YAAyC;EAAA,QAC5BV,WAAW;AAAA;AAAAsC,EAAA,GADxB5B,YAAyC;AAiE/C,eAAeA,YAAY;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}