{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Modal, Form, message, Popconfirm, Typography, Row, Col, Tree, Switch, Tag, Descriptions, Select } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, SettingOutlined, ReloadOutlined, UserOutlined } from '@ant-design/icons';\nimport { roleService } from '../../../services/roleService';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  TextArea\n} = Input;\n\n// 角色数据接口\n\n// 权限数据接口\n\n// 查询参数接口\n\nconst RoleManagement = () => {\n  _s();\n  const [roles, setRoles] = useState([]);\n  const [permissions, setPermissions] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);\n  const [editingRole, setEditingRole] = useState(null);\n  const [selectedRole, setSelectedRole] = useState(null);\n  const [checkedPermissions, setCheckedPermissions] = useState([]);\n  const [expandedKeys, setExpandedKeys] = useState([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const response = await roleService.getRoleList(queryParams);\n      if (response.success) {\n        // 处理后端返回的数据，添加缺失的字段\n        const processedRoles = response.data.list.map(role => ({\n          ...role,\n          userCount: role.userCount || 0,\n          permissions: role.permissions || []\n        }));\n        setRoles(processedRoles);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取角色列表失败');\n        setRoles([]);\n        setTotal(0);\n      }\n    } catch (error) {\n      console.error('获取角色列表失败:', error);\n      let errorMsg = '获取角色列表失败';\n      if (error.response) {\n        const {\n          status\n        } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问角色列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setRoles([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取权限列表\n  const fetchPermissions = async () => {\n    try {\n      const response = await roleService.getPermissionList();\n      if (response.success) {\n        // 处理后端返回的权限数据，转换为前端期望的格式\n        const rawData = response.data;\n        const permissionList = rawData.list || rawData || [];\n        const processedPermissions = permissionList.map(permission => ({\n          id: permission.id,\n          name: permission.name,\n          code: permission.code,\n          type: permission.type || 'menu',\n          parentId: permission.parent_id,\n          path: permission.path,\n          children: []\n        }));\n\n        // 构建树形结构\n        const permissionTree = buildPermissionTree(processedPermissions);\n        setPermissions(permissionTree);\n      }\n    } catch (error) {\n      console.error('获取权限列表失败:', error);\n    }\n  };\n\n  // 构建权限树形结构\n  const buildPermissionTree = permissions => {\n    const map = new Map();\n    const roots = [];\n\n    // 创建映射\n    permissions.forEach(permission => {\n      map.set(permission.id, {\n        ...permission,\n        children: []\n      });\n    });\n\n    // 构建树形结构\n    permissions.forEach(permission => {\n      const node = map.get(permission.id);\n      if (permission.parentId && map.has(permission.parentId)) {\n        map.get(permission.parentId).children.push(node);\n      } else {\n        roots.push(node);\n      }\n    });\n    return roots;\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增角色\n  const handleAdd = () => {\n    setEditingRole(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑角色\n  const handleEdit = role => {\n    setEditingRole(role);\n    form.setFieldsValue({\n      ...role,\n      status: role.status === 1\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除角色\n  const handleDelete = async id => {\n    try {\n      const response = await roleService.deleteRole(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchRoles();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存角色\n  const handleSave = async values => {\n    setSaving(true);\n    try {\n      const roleData = {\n        ...values,\n        status: values.status ? 1 : 0\n      };\n      let response;\n      if (editingRole) {\n        response = await roleService.updateRole(editingRole.id, roleData);\n      } else {\n        response = await roleService.createRole(roleData);\n      }\n      if (response.success) {\n        const successMsg = editingRole ? '角色信息更新成功！' : '角色创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3\n        });\n        setIsModalVisible(false);\n        form.resetFields();\n        setEditingRole(null);\n        fetchRoles();\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('name')) {\n            errorMsg = '角色名称已存在，请使用其他名称';\n          } else if (response.message.includes('code')) {\n            errorMsg = '角色编码已存在，请使用其他编码';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n        message.error({\n          content: errorMsg,\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('保存角色失败:', error);\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const {\n          status,\n          data\n        } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('name')) {\n            errorMsg = '角色名称格式不正确或已存在';\n          } else if (data.error && data.error.includes('code')) {\n            errorMsg = '角色编码格式不正确或已存在';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '角色信息冲突，名称或编码可能已存在';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n      message.error({\n        content: errorMsg,\n        duration: 5\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 配置权限\n  const handleConfigPermissions = role => {\n    setSelectedRole(role);\n    setCheckedPermissions(role.permissions || []);\n    setIsPermissionModalVisible(true);\n  };\n\n  // 保存权限配置\n  const handleSavePermissions = async () => {\n    if (!selectedRole) return;\n    try {\n      const response = await roleService.updateRolePermissions(selectedRole.id, checkedPermissions);\n      if (response.success) {\n        message.success('权限配置成功');\n        setIsPermissionModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '权限配置失败');\n      }\n    } catch (error) {\n      message.error(error.message || '权限配置失败');\n    }\n  };\n\n  // 转换权限数据为树形结构\n  const convertPermissionsToTreeData = permissions => {\n    return permissions.map(permission => ({\n      title: permission.name,\n      key: permission.id,\n      children: permission.children ? convertPermissionsToTreeData(permission.children) : undefined\n    }));\n  };\n\n  // 权限树选择处理\n  const handlePermissionCheck = checkedKeys => {\n    if (Array.isArray(checkedKeys)) {\n      setCheckedPermissions(checkedKeys);\n    } else {\n      setCheckedPermissions(checkedKeys.checked);\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '角色名称',\n    dataIndex: 'name',\n    key: 'name',\n    width: 120\n  }, {\n    title: '角色编码',\n    dataIndex: 'code',\n    key: 'code',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Tag, {\n      color: \"blue\",\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 384,\n      columnNumber: 33\n    }, this)\n  }, {\n    title: '描述',\n    dataIndex: 'description',\n    key: 'description',\n    width: 200,\n    render: text => text || '-'\n  }, {\n    title: '用户数量',\n    dataIndex: 'userCount',\n    key: 'userCount',\n    width: 100,\n    render: count => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 400,\n        columnNumber: 11\n      }, this), count]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 399,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: status => /*#__PURE__*/_jsxDEV(Tag, {\n      color: status === 1 ? 'green' : 'red',\n      children: status === 1 ? '启用' : '禁用'\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 200,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 433,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleConfigPermissions(record),\n        children: \"\\u914D\\u7F6E\\u6743\\u9650\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 441,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 438,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u89D2\\u8272\\u5417\\uFF1F\",\n        description: \"\\u5220\\u9664\\u540E\\u8BE5\\u89D2\\u8272\\u4E0B\\u7684\\u7528\\u6237\\u5C06\\u5931\\u53BB\\u76F8\\u5E94\\u6743\\u9650\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 21\n          }, this),\n          disabled: record.userCount > 0,\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 446,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 429,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"role-management-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u89D2\\u8272\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 470,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u540D\\u79F0\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 481,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u7F16\\u7801\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 487,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 486,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 485,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: 1,\n                  children: \"\\u542F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 493,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: 0,\n                  children: \"\\u7981\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 492,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 491,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 501,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 501,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 504,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 504,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 500,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 499,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 498,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 474,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 473,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            type: \"primary\",\n            icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 520,\n              columnNumber: 21\n            }, this),\n            onClick: handleAdd,\n            children: \"\\u65B0\\u589E\\u89D2\\u8272\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 518,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 517,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 528,\n              columnNumber: 21\n            }, this),\n            onClick: fetchRoles,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 527,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 526,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 516,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 515,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: roles,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1000\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 540,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 539,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingRole ? '编辑角色' : '新增角色',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"name\",\n              label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n              rules: [{\n                required: true,\n                message: '请输入角色名称'\n              }, {\n                max: 50,\n                message: '角色名称不能超过50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u540D\\u79F0\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"code\",\n              label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入角色编码'\n              }, {\n                pattern: /^[A-Z_]+$/,\n                message: '角色编码只能包含大写字母和下划线'\n              }, {\n                max: 50,\n                message: '角色编码不能超过50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u7F16\\u7801\",\n                disabled: !!editingRole\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 603,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 593,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"description\",\n          label: \"\\u89D2\\u8272\\u63CF\\u8FF0\",\n          rules: [{\n            max: 200,\n            message: '角色描述不能超过200个字符'\n          }],\n          children: /*#__PURE__*/_jsxDEV(TextArea, {\n            placeholder: \"\\u8BF7\\u8F93\\u5165\\u89D2\\u8272\\u63CF\\u8FF0\",\n            rows: 4,\n            showCount: true,\n            maxLength: 200\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 608,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          name: \"status\",\n          label: \"\\u72B6\\u6001\",\n          valuePropName: \"checked\",\n          children: /*#__PURE__*/_jsxDEV(Switch, {\n            checkedChildren: \"\\u542F\\u7528\",\n            unCheckedChildren: \"\\u7981\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 623,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsModalVisible(false),\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 633,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              children: editingRole ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 636,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 632,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 631,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 574,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 566,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: `配置权限 - ${selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name}`,\n      open: isPermissionModalVisible,\n      onCancel: () => setIsPermissionModalVisible(false),\n      onOk: handleSavePermissions,\n      width: 800,\n      destroyOnClose: true,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: 16\n        },\n        children: /*#__PURE__*/_jsxDEV(Descriptions, {\n          size: \"small\",\n          column: 2,\n          children: [/*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u89D2\\u8272\\u540D\\u79F0\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.name\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 655,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u89D2\\u8272\\u7F16\\u7801\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.code\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 656,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u7528\\u6237\\u6570\\u91CF\",\n            children: selectedRole === null || selectedRole === void 0 ? void 0 : selectedRole.userCount\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 657,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Descriptions.Item, {\n            label: \"\\u5F53\\u524D\\u6743\\u9650\\u6570\\u91CF\",\n            children: checkedPermissions.length\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 658,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 654,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 653,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          border: '1px solid #d9d9d9',\n          borderRadius: 6,\n          padding: 16,\n          maxHeight: 400,\n          overflow: 'auto'\n        },\n        children: /*#__PURE__*/_jsxDEV(Tree, {\n          checkable: true,\n          checkedKeys: checkedPermissions,\n          expandedKeys: expandedKeys,\n          onCheck: handlePermissionCheck,\n          onExpand: setExpandedKeys,\n          treeData: convertPermissionsToTreeData(permissions),\n          height: 350\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 663,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 662,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 645,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 469,\n    columnNumber: 5\n  }, this);\n};\n_s(RoleManagement, \"pWX0hyl+c2/X5FvYRQEgQ/DVS6A=\", false, function () {\n  return [Form.useForm, Form.useForm];\n});\n_c = RoleManagement;\nexport default RoleManagement;\nvar _c;\n$RefreshReg$(_c, \"RoleManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Modal", "Form", "message", "Popconfirm", "Typography", "Row", "Col", "Tree", "Switch", "Tag", "Descriptions", "Select", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "SettingOutlined", "ReloadOutlined", "UserOutlined", "roleService", "jsxDEV", "_jsxDEV", "Title", "Option", "TextArea", "RoleManagement", "_s", "roles", "setRoles", "permissions", "setPermissions", "loading", "setLoading", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "isPermissionModalVisible", "setIsPermissionModalVisible", "editingRole", "setEditingRole", "selectedR<PERSON>", "setSelectedRole", "checkedPermissions", "setCheckedPermissions", "expandedKeys", "setExpandedKeys", "saving", "setSaving", "form", "useForm", "searchForm", "fetchRoles", "response", "getRoleList", "success", "processedRoles", "data", "list", "map", "role", "userCount", "error", "console", "errorMsg", "status", "fetchPermissions", "getPermissionList", "rawData", "permissionList", "processedPermissions", "permission", "id", "name", "code", "type", "parentId", "parent_id", "path", "children", "permissionTree", "buildPermissionTree", "Map", "roots", "for<PERSON>ach", "set", "node", "get", "has", "push", "handleSearch", "values", "handleReset", "resetFields", "handleAdd", "handleEdit", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handleDelete", "deleteRole", "handleSave", "roleData", "updateRole", "createRole", "successMsg", "content", "duration", "includes", "handleConfigPermissions", "handleSavePermissions", "updateRolePermissions", "convertPermissionsToTreeData", "title", "key", "undefined", "handlePermissionCheck", "checked<PERSON>eys", "Array", "isArray", "checked", "columns", "dataIndex", "width", "render", "text", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "count", "Date", "toLocaleString", "fixed", "_", "record", "size", "icon", "onClick", "description", "onConfirm", "okText", "cancelText", "danger", "disabled", "className", "level", "layout", "onFinish", "autoComplete", "gutter", "style", "xs", "sm", "md", "<PERSON><PERSON>", "label", "placeholder", "allowClear", "value", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "onChange", "open", "onCancel", "footer", "destroyOnClose", "span", "rules", "required", "max", "pattern", "rows", "showCount", "max<PERSON><PERSON><PERSON>", "valuePropName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "justifyContent", "onOk", "marginBottom", "column", "length", "border", "borderRadius", "padding", "maxHeight", "overflow", "checkable", "onCheck", "onExpand", "treeData", "height", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  Tree,\n  Switch,\n  Tag,\n  Descriptions,\n  Select,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  SettingOutlined,\n  ReloadOutlined,\n  UserOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport type { DataNode } from 'antd/es/tree';\nimport { roleService } from '../../../services/roleService';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { TextArea } = Input;\n\n// 角色数据接口\nexport interface Role {\n  id: number;\n  name: string;\n  code: string;\n  description?: string;\n  status: number;\n  userCount: number;\n  permissions: number[];\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 权限数据接口\nexport interface Permission {\n  id: number;\n  name: string;\n  code: string;\n  type: 'menu' | 'button' | 'api';\n  parentId?: number;\n  path?: string;\n  children?: Permission[];\n}\n\n// 查询参数接口\ninterface RoleQueryParams {\n  name?: string;\n  code?: string;\n  status?: number;\n  page: number;\n  pageSize: number;\n}\n\nconst RoleManagement: React.FC = () => {\n  const [roles, setRoles] = useState<Role[]>([]);\n  const [permissions, setPermissions] = useState<Permission[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<RoleQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);\n  const [editingRole, setEditingRole] = useState<Role | null>(null);\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null);\n  const [checkedPermissions, setCheckedPermissions] = useState<number[]>([]);\n  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);\n  const [saving, setSaving] = useState(false);\n  const [form] = Form.useForm();\n  const [searchForm] = Form.useForm();\n\n  // 获取角色列表\n  const fetchRoles = async () => {\n    setLoading(true);\n    try {\n      const response = await roleService.getRoleList(queryParams);\n      if (response.success) {\n        // 处理后端返回的数据，添加缺失的字段\n        const processedRoles = response.data.list.map((role: any) => ({\n          ...role,\n          userCount: role.userCount || 0,\n          permissions: role.permissions || [],\n        }));\n        setRoles(processedRoles);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取角色列表失败');\n        setRoles([]);\n        setTotal(0);\n      }\n    } catch (error: any) {\n      console.error('获取角色列表失败:', error);\n      let errorMsg = '获取角色列表失败';\n      if (error.response) {\n        const { status } = error.response;\n        if (status === 401) {\n          errorMsg = '登录已过期，请重新登录';\n        } else if (status === 403) {\n          errorMsg = '没有权限访问角色列表';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请稍后重试';\n        }\n      }\n      message.error(errorMsg);\n      setRoles([]);\n      setTotal(0);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 获取权限列表\n  const fetchPermissions = async () => {\n    try {\n      const response = await roleService.getPermissionList();\n      if (response.success) {\n        // 处理后端返回的权限数据，转换为前端期望的格式\n        const rawData = response.data as any;\n        const permissionList = rawData.list || rawData || [];\n        const processedPermissions = permissionList.map((permission: any) => ({\n          id: permission.id,\n          name: permission.name,\n          code: permission.code,\n          type: permission.type || 'menu',\n          parentId: permission.parent_id,\n          path: permission.path,\n          children: [],\n        }));\n\n        // 构建树形结构\n        const permissionTree = buildPermissionTree(processedPermissions);\n        setPermissions(permissionTree);\n      }\n    } catch (error: any) {\n      console.error('获取权限列表失败:', error);\n    }\n  };\n\n  // 构建权限树形结构\n  const buildPermissionTree = (permissions: any[]): Permission[] => {\n    const map = new Map();\n    const roots: Permission[] = [];\n\n    // 创建映射\n    permissions.forEach(permission => {\n      map.set(permission.id, { ...permission, children: [] });\n    });\n\n    // 构建树形结构\n    permissions.forEach(permission => {\n      const node = map.get(permission.id);\n      if (permission.parentId && map.has(permission.parentId)) {\n        map.get(permission.parentId).children.push(node);\n      } else {\n        roots.push(node);\n      }\n    });\n\n    return roots;\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchRoles();\n    fetchPermissions();\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    searchForm.resetFields();\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增角色\n  const handleAdd = () => {\n    setEditingRole(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑角色\n  const handleEdit = (role: Role) => {\n    setEditingRole(role);\n    form.setFieldsValue({\n      ...role,\n      status: role.status === 1,\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除角色\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await roleService.deleteRole(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchRoles();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存角色\n  const handleSave = async (values: any) => {\n    setSaving(true);\n\n    try {\n      const roleData = {\n        ...values,\n        status: values.status ? 1 : 0,\n      };\n\n      let response;\n      if (editingRole) {\n        response = await roleService.updateRole(editingRole.id, roleData);\n      } else {\n        response = await roleService.createRole(roleData);\n      }\n\n      if (response.success) {\n        const successMsg = editingRole ? '角色信息更新成功！' : '角色创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3,\n        });\n        setIsModalVisible(false);\n        form.resetFields();\n        setEditingRole(null);\n        fetchRoles();\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('name')) {\n            errorMsg = '角色名称已存在，请使用其他名称';\n          } else if (response.message.includes('code')) {\n            errorMsg = '角色编码已存在，请使用其他编码';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n\n        message.error({\n          content: errorMsg,\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('保存角色失败:', error);\n\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const { status, data } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('name')) {\n            errorMsg = '角色名称格式不正确或已存在';\n          } else if (data.error && data.error.includes('code')) {\n            errorMsg = '角色编码格式不正确或已存在';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '角色信息冲突，名称或编码可能已存在';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n\n      message.error({\n        content: errorMsg,\n        duration: 5,\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 配置权限\n  const handleConfigPermissions = (role: Role) => {\n    setSelectedRole(role);\n    setCheckedPermissions(role.permissions || []);\n    setIsPermissionModalVisible(true);\n  };\n\n  // 保存权限配置\n  const handleSavePermissions = async () => {\n    if (!selectedRole) return;\n\n    try {\n      const response = await roleService.updateRolePermissions(\n        selectedRole.id,\n        checkedPermissions\n      );\n      if (response.success) {\n        message.success('权限配置成功');\n        setIsPermissionModalVisible(false);\n        fetchRoles();\n      } else {\n        message.error(response.message || '权限配置失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '权限配置失败');\n    }\n  };\n\n  // 转换权限数据为树形结构\n  const convertPermissionsToTreeData = (permissions: Permission[]): DataNode[] => {\n    return permissions.map(permission => ({\n      title: permission.name,\n      key: permission.id,\n      children: permission.children ? convertPermissionsToTreeData(permission.children) : undefined,\n    }));\n  };\n\n  // 权限树选择处理\n  const handlePermissionCheck = (checkedKeys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {\n    if (Array.isArray(checkedKeys)) {\n      setCheckedPermissions(checkedKeys as number[]);\n    } else {\n      setCheckedPermissions(checkedKeys.checked as number[]);\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<Role> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '角色名称',\n      dataIndex: 'name',\n      key: 'name',\n      width: 120,\n    },\n    {\n      title: '角色编码',\n      dataIndex: 'code',\n      key: 'code',\n      width: 120,\n      render: (text: string) => <Tag color=\"blue\">{text}</Tag>,\n    },\n    {\n      title: '描述',\n      dataIndex: 'description',\n      key: 'description',\n      width: 200,\n      render: (text: string) => text || '-',\n    },\n    {\n      title: '用户数量',\n      dataIndex: 'userCount',\n      key: 'userCount',\n      width: 100,\n      render: (count: number) => (\n        <Space>\n          <UserOutlined />\n          {count}\n        </Space>\n      ),\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: number) => (\n        <Tag color={status === 1 ? 'green' : 'red'}>\n          {status === 1 ? '启用' : '禁用'}\n        </Tag>\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 200,\n      fixed: 'right',\n      render: (_, record: Role) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<SettingOutlined />}\n            onClick={() => handleConfigPermissions(record)}\n          >\n            配置权限\n          </Button>\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个角色吗？\"\n            description=\"删除后该角色下的用户将失去相应权限\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n              disabled={record.userCount > 0}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"role-management-container\">\n      <Title level={2}>角色管理</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"name\" label=\"角色名称\">\n                <Input placeholder=\"请输入角色名称\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"code\" label=\"角色编码\">\n                <Input placeholder=\"请输入角色编码\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"状态\">\n                <Select placeholder=\"请选择状态\" allowClear>\n                  <Option value={1}>启用</Option>\n                  <Option value={0}>禁用</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Button\n              type=\"primary\"\n              icon={<PlusOutlined />}\n              onClick={handleAdd}\n            >\n              新增角色\n            </Button>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchRoles}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 角色列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={roles}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1000 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 角色编辑模态框 */}\n      <Modal\n        title={editingRole ? '编辑角色' : '新增角色'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"name\"\n                label=\"角色名称\"\n                rules={[\n                  { required: true, message: '请输入角色名称' },\n                  { max: 50, message: '角色名称不能超过50个字符' },\n                ]}\n              >\n                <Input placeholder=\"请输入角色名称\" />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"code\"\n                label=\"角色编码\"\n                rules={[\n                  { required: true, message: '请输入角色编码' },\n                  { pattern: /^[A-Z_]+$/, message: '角色编码只能包含大写字母和下划线' },\n                  { max: 50, message: '角色编码不能超过50个字符' },\n                ]}\n              >\n                <Input placeholder=\"请输入角色编码\" disabled={!!editingRole} />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Form.Item\n            name=\"description\"\n            label=\"角色描述\"\n            rules={[\n              { max: 200, message: '角色描述不能超过200个字符' },\n            ]}\n          >\n            <TextArea\n              placeholder=\"请输入角色描述\"\n              rows={4}\n              showCount\n              maxLength={200}\n            />\n          </Form.Item>\n\n          <Form.Item\n            name=\"status\"\n            label=\"状态\"\n            valuePropName=\"checked\"\n          >\n            <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n          </Form.Item>\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button onClick={() => setIsModalVisible(false)}>\n                取消\n              </Button>\n              <Button type=\"primary\" htmlType=\"submit\">\n                {editingRole ? '更新' : '创建'}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n\n      {/* 权限配置模态框 */}\n      <Modal\n        title={`配置权限 - ${selectedRole?.name}`}\n        open={isPermissionModalVisible}\n        onCancel={() => setIsPermissionModalVisible(false)}\n        onOk={handleSavePermissions}\n        width={800}\n        destroyOnClose\n      >\n        <div style={{ marginBottom: 16 }}>\n          <Descriptions size=\"small\" column={2}>\n            <Descriptions.Item label=\"角色名称\">{selectedRole?.name}</Descriptions.Item>\n            <Descriptions.Item label=\"角色编码\">{selectedRole?.code}</Descriptions.Item>\n            <Descriptions.Item label=\"用户数量\">{selectedRole?.userCount}</Descriptions.Item>\n            <Descriptions.Item label=\"当前权限数量\">{checkedPermissions.length}</Descriptions.Item>\n          </Descriptions>\n        </div>\n\n        <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16, maxHeight: 400, overflow: 'auto' }}>\n          <Tree\n            checkable\n            checkedKeys={checkedPermissions}\n            expandedKeys={expandedKeys}\n            onCheck={handlePermissionCheck}\n            onExpand={setExpandedKeys}\n            treeData={convertPermissionsToTreeData(permissions)}\n            height={350}\n          />\n        </div>\n      </Modal>\n    </div>\n  );\n};\n\nexport default RoleManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,IAAI,EACJC,MAAM,EACNC,GAAG,EACHC,YAAY,EACZC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,eAAe,EACfC,cAAc,EACdC,YAAY,QACP,mBAAmB;AAG1B,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGlB,UAAU;AAC5B,MAAM;EAAEmB;AAAO,CAAC,GAAGZ,MAAM;AACzB,MAAM;EAAEa;AAAS,CAAC,GAAGzB,KAAK;;AAE1B;;AAaA;;AAWA;;AASA,MAAM0B,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGnC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACoC,WAAW,EAAEC,cAAc,CAAC,GAAGrC,QAAQ,CAAe,EAAE,CAAC;EAChE,MAAM,CAACsC,OAAO,EAAEC,UAAU,CAAC,GAAGvC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACwC,KAAK,EAAEC,QAAQ,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC0C,WAAW,EAAEC,cAAc,CAAC,GAAG3C,QAAQ,CAAkB;IAC9D4C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAG/C,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACgD,wBAAwB,EAAEC,2BAA2B,CAAC,GAAGjD,QAAQ,CAAC,KAAK,CAAC;EAC/E,MAAM,CAACkD,WAAW,EAAEC,cAAc,CAAC,GAAGnD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACoD,YAAY,EAAEC,eAAe,CAAC,GAAGrD,QAAQ,CAAc,IAAI,CAAC;EACnE,MAAM,CAACsD,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGvD,QAAQ,CAAW,EAAE,CAAC;EAC1E,MAAM,CAACwD,YAAY,EAAEC,eAAe,CAAC,GAAGzD,QAAQ,CAAc,EAAE,CAAC;EACjE,MAAM,CAAC0D,MAAM,EAAEC,SAAS,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAAC4D,IAAI,CAAC,GAAGpD,IAAI,CAACqD,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,UAAU,CAAC,GAAGtD,IAAI,CAACqD,OAAO,CAAC,CAAC;;EAEnC;EACA,MAAME,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMyB,QAAQ,GAAG,MAAMtC,WAAW,CAACuC,WAAW,CAACvB,WAAW,CAAC;MAC3D,IAAIsB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMC,cAAc,GAAGH,QAAQ,CAACI,IAAI,CAACC,IAAI,CAACC,GAAG,CAAEC,IAAS,KAAM;UAC5D,GAAGA,IAAI;UACPC,SAAS,EAAED,IAAI,CAACC,SAAS,IAAI,CAAC;UAC9BpC,WAAW,EAAEmC,IAAI,CAACnC,WAAW,IAAI;QACnC,CAAC,CAAC,CAAC;QACHD,QAAQ,CAACgC,cAAc,CAAC;QACxB1B,QAAQ,CAACuB,QAAQ,CAACI,IAAI,CAAC5B,KAAK,CAAC;MAC/B,CAAC,MAAM;QACL/B,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,UAAU,CAAC;QAC7C0B,QAAQ,CAAC,EAAE,CAAC;QACZM,QAAQ,CAAC,CAAC,CAAC;MACb;IACF,CAAC,CAAC,OAAOgC,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,IAAIE,QAAQ,GAAG,UAAU;MACzB,IAAIF,KAAK,CAACT,QAAQ,EAAE;QAClB,MAAM;UAAEY;QAAO,CAAC,GAAGH,KAAK,CAACT,QAAQ;QACjC,IAAIY,MAAM,KAAK,GAAG,EAAE;UAClBD,QAAQ,GAAG,aAAa;QAC1B,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,YAAY;QACzB,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,eAAe;QAC5B;MACF;MACAlE,OAAO,CAACgE,KAAK,CAACE,QAAQ,CAAC;MACvBxC,QAAQ,CAAC,EAAE,CAAC;MACZM,QAAQ,CAAC,CAAC,CAAC;IACb,CAAC,SAAS;MACRF,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACA,MAAMsC,gBAAgB,GAAG,MAAAA,CAAA,KAAY;IACnC,IAAI;MACF,MAAMb,QAAQ,GAAG,MAAMtC,WAAW,CAACoD,iBAAiB,CAAC,CAAC;MACtD,IAAId,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMa,OAAO,GAAGf,QAAQ,CAACI,IAAW;QACpC,MAAMY,cAAc,GAAGD,OAAO,CAACV,IAAI,IAAIU,OAAO,IAAI,EAAE;QACpD,MAAME,oBAAoB,GAAGD,cAAc,CAACV,GAAG,CAAEY,UAAe,KAAM;UACpEC,EAAE,EAAED,UAAU,CAACC,EAAE;UACjBC,IAAI,EAAEF,UAAU,CAACE,IAAI;UACrBC,IAAI,EAAEH,UAAU,CAACG,IAAI;UACrBC,IAAI,EAAEJ,UAAU,CAACI,IAAI,IAAI,MAAM;UAC/BC,QAAQ,EAAEL,UAAU,CAACM,SAAS;UAC9BC,IAAI,EAAEP,UAAU,CAACO,IAAI;UACrBC,QAAQ,EAAE;QACZ,CAAC,CAAC,CAAC;;QAEH;QACA,MAAMC,cAAc,GAAGC,mBAAmB,CAACX,oBAAoB,CAAC;QAChE5C,cAAc,CAACsD,cAAc,CAAC;MAChC;IACF,CAAC,CAAC,OAAOlB,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMmB,mBAAmB,GAAIxD,WAAkB,IAAmB;IAChE,MAAMkC,GAAG,GAAG,IAAIuB,GAAG,CAAC,CAAC;IACrB,MAAMC,KAAmB,GAAG,EAAE;;IAE9B;IACA1D,WAAW,CAAC2D,OAAO,CAACb,UAAU,IAAI;MAChCZ,GAAG,CAAC0B,GAAG,CAACd,UAAU,CAACC,EAAE,EAAE;QAAE,GAAGD,UAAU;QAAEQ,QAAQ,EAAE;MAAG,CAAC,CAAC;IACzD,CAAC,CAAC;;IAEF;IACAtD,WAAW,CAAC2D,OAAO,CAACb,UAAU,IAAI;MAChC,MAAMe,IAAI,GAAG3B,GAAG,CAAC4B,GAAG,CAAChB,UAAU,CAACC,EAAE,CAAC;MACnC,IAAID,UAAU,CAACK,QAAQ,IAAIjB,GAAG,CAAC6B,GAAG,CAACjB,UAAU,CAACK,QAAQ,CAAC,EAAE;QACvDjB,GAAG,CAAC4B,GAAG,CAAChB,UAAU,CAACK,QAAQ,CAAC,CAACG,QAAQ,CAACU,IAAI,CAACH,IAAI,CAAC;MAClD,CAAC,MAAM;QACLH,KAAK,CAACM,IAAI,CAACH,IAAI,CAAC;MAClB;IACF,CAAC,CAAC;IAEF,OAAOH,KAAK;EACd,CAAC;;EAED;EACA7F,SAAS,CAAC,MAAM;IACd8D,UAAU,CAAC,CAAC;IACZc,gBAAgB,CAAC,CAAC;IACpB;EACA,CAAC,EAAE,CAACnC,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAM2D,YAAY,GAAIC,MAAW,IAAK;IACpC3D,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAG4D,MAAM;MACT1D,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM2D,WAAW,GAAGA,CAAA,KAAM;IACxBzC,UAAU,CAAC0C,WAAW,CAAC,CAAC;IACxB7D,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAM4D,SAAS,GAAGA,CAAA,KAAM;IACtBtD,cAAc,CAAC,IAAI,CAAC;IACpBS,IAAI,CAAC4C,WAAW,CAAC,CAAC;IAClBzD,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM2D,UAAU,GAAInC,IAAU,IAAK;IACjCpB,cAAc,CAACoB,IAAI,CAAC;IACpBX,IAAI,CAAC+C,cAAc,CAAC;MAClB,GAAGpC,IAAI;MACPK,MAAM,EAAEL,IAAI,CAACK,MAAM,KAAK;IAC1B,CAAC,CAAC;IACF7B,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM6D,YAAY,GAAG,MAAOzB,EAAU,IAAK;IACzC,IAAI;MACF,MAAMnB,QAAQ,GAAG,MAAMtC,WAAW,CAACmF,UAAU,CAAC1B,EAAE,CAAC;MACjD,IAAInB,QAAQ,CAACE,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAC,MAAM,CAAC;QACvBH,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLtD,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMqG,UAAU,GAAG,MAAOR,MAAW,IAAK;IACxC3C,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAMoD,QAAQ,GAAG;QACf,GAAGT,MAAM;QACT1B,MAAM,EAAE0B,MAAM,CAAC1B,MAAM,GAAG,CAAC,GAAG;MAC9B,CAAC;MAED,IAAIZ,QAAQ;MACZ,IAAId,WAAW,EAAE;QACfc,QAAQ,GAAG,MAAMtC,WAAW,CAACsF,UAAU,CAAC9D,WAAW,CAACiC,EAAE,EAAE4B,QAAQ,CAAC;MACnE,CAAC,MAAM;QACL/C,QAAQ,GAAG,MAAMtC,WAAW,CAACuF,UAAU,CAACF,QAAQ,CAAC;MACnD;MAEA,IAAI/C,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMgD,UAAU,GAAGhE,WAAW,GAAG,WAAW,GAAG,SAAS;QACxDzC,OAAO,CAACyD,OAAO,CAAC;UACdiD,OAAO,EAAED,UAAU;UACnBE,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFrE,iBAAiB,CAAC,KAAK,CAAC;QACxBa,IAAI,CAAC4C,WAAW,CAAC,CAAC;QAClBrD,cAAc,CAAC,IAAI,CAAC;QACpBY,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACL;QACA,IAAIY,QAAQ,GAAG,MAAM;QACrB,IAAIX,QAAQ,CAACvD,OAAO,EAAE;UACpB,IAAIuD,QAAQ,CAACvD,OAAO,CAAC4G,QAAQ,CAAC,MAAM,CAAC,EAAE;YACrC1C,QAAQ,GAAG,iBAAiB;UAC9B,CAAC,MAAM,IAAIX,QAAQ,CAACvD,OAAO,CAAC4G,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC5C1C,QAAQ,GAAG,iBAAiB;UAC9B,CAAC,MAAM,IAAIX,QAAQ,CAACvD,OAAO,CAAC4G,QAAQ,CAAC,YAAY,CAAC,EAAE;YAClD1C,QAAQ,GAAG,kBAAkB;UAC/B,CAAC,MAAM;YACLA,QAAQ,GAAGX,QAAQ,CAACvD,OAAO;UAC7B;QACF;QAEAA,OAAO,CAACgE,KAAK,CAAC;UACZ0C,OAAO,EAAExC,QAAQ;UACjByC,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAO3C,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAE/B,IAAIE,QAAQ,GAAG,YAAY;MAC3B,IAAIF,KAAK,CAACT,QAAQ,EAAE;QAClB,MAAM;UAAEY,MAAM;UAAER;QAAK,CAAC,GAAGK,KAAK,CAACT,QAAQ;QACvC,IAAIY,MAAM,KAAK,GAAG,EAAE;UAClB,IAAIR,IAAI,CAACK,KAAK,IAAIL,IAAI,CAACK,KAAK,CAAC4C,QAAQ,CAAC,MAAM,CAAC,EAAE;YAC7C1C,QAAQ,GAAG,eAAe;UAC5B,CAAC,MAAM,IAAIP,IAAI,CAACK,KAAK,IAAIL,IAAI,CAACK,KAAK,CAAC4C,QAAQ,CAAC,MAAM,CAAC,EAAE;YACpD1C,QAAQ,GAAG,eAAe;UAC5B,CAAC,MAAM;YACLA,QAAQ,GAAGP,IAAI,CAACK,KAAK,IAAI,gBAAgB;UAC3C;QACF,CAAC,MAAM,IAAIG,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,mBAAmB;QAChC,CAAC,MAAM,IAAIC,MAAM,KAAK,GAAG,EAAE;UACzBD,QAAQ,GAAG,gBAAgB;QAC7B,CAAC,MAAM;UACLA,QAAQ,GAAG,SAASC,MAAM,SAAS;QACrC;MACF,CAAC,MAAM,IAAIH,KAAK,CAAChE,OAAO,EAAE;QACxBkE,QAAQ,GAAGF,KAAK,CAAChE,OAAO;MAC1B;MAEAA,OAAO,CAACgE,KAAK,CAAC;QACZ0C,OAAO,EAAExC,QAAQ;QACjByC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRzD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAM2D,uBAAuB,GAAI/C,IAAU,IAAK;IAC9ClB,eAAe,CAACkB,IAAI,CAAC;IACrBhB,qBAAqB,CAACgB,IAAI,CAACnC,WAAW,IAAI,EAAE,CAAC;IAC7Ca,2BAA2B,CAAC,IAAI,CAAC;EACnC,CAAC;;EAED;EACA,MAAMsE,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAI,CAACnE,YAAY,EAAE;IAEnB,IAAI;MACF,MAAMY,QAAQ,GAAG,MAAMtC,WAAW,CAAC8F,qBAAqB,CACtDpE,YAAY,CAAC+B,EAAE,EACf7B,kBACF,CAAC;MACD,IAAIU,QAAQ,CAACE,OAAO,EAAE;QACpBzD,OAAO,CAACyD,OAAO,CAAC,QAAQ,CAAC;QACzBjB,2BAA2B,CAAC,KAAK,CAAC;QAClCc,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLtD,OAAO,CAACgE,KAAK,CAACT,QAAQ,CAACvD,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOgE,KAAU,EAAE;MACnBhE,OAAO,CAACgE,KAAK,CAACA,KAAK,CAAChE,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMgH,4BAA4B,GAAIrF,WAAyB,IAAiB;IAC9E,OAAOA,WAAW,CAACkC,GAAG,CAACY,UAAU,KAAK;MACpCwC,KAAK,EAAExC,UAAU,CAACE,IAAI;MACtBuC,GAAG,EAAEzC,UAAU,CAACC,EAAE;MAClBO,QAAQ,EAAER,UAAU,CAACQ,QAAQ,GAAG+B,4BAA4B,CAACvC,UAAU,CAACQ,QAAQ,CAAC,GAAGkC;IACtF,CAAC,CAAC,CAAC;EACL,CAAC;;EAED;EACA,MAAMC,qBAAqB,GAAIC,WAA6E,IAAK;IAC/G,IAAIC,KAAK,CAACC,OAAO,CAACF,WAAW,CAAC,EAAE;MAC9BvE,qBAAqB,CAACuE,WAAuB,CAAC;IAChD,CAAC,MAAM;MACLvE,qBAAqB,CAACuE,WAAW,CAACG,OAAmB,CAAC;IACxD;EACF,CAAC;;EAED;EACA,MAAMC,OAA0B,GAAG,CACjC;IACER,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,IAAI;IACfR,GAAG,EAAE,IAAI;IACTS,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,MAAM;IACjBR,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE;EACT,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,MAAM;IACjBR,GAAG,EAAE,MAAM;IACXS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBAAK1G,OAAA,CAACZ,GAAG;MAACuH,KAAK,EAAC,MAAM;MAAA7C,QAAA,EAAE4C;IAAI;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM;EACzD,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,aAAa;IACxBR,GAAG,EAAE,aAAa;IAClBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAKA,IAAI,IAAI;EACpC,CAAC,EACD;IACEZ,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,WAAW;IACtBR,GAAG,EAAE,WAAW;IAChBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGO,KAAa,iBACpBhH,OAAA,CAACvB,KAAK;MAAAqF,QAAA,gBACJ9D,OAAA,CAACH,YAAY;QAAA+G,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACfC,KAAK;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD;EAEX,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXS,SAAS,EAAE,QAAQ;IACnBR,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGzD,MAAc,iBACrBhD,OAAA,CAACZ,GAAG;MAACuH,KAAK,EAAE3D,MAAM,KAAK,CAAC,GAAG,OAAO,GAAG,KAAM;MAAAc,QAAA,EACxCd,MAAM,KAAK,CAAC,GAAG,IAAI,GAAG;IAAI;MAAA4D,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACxB;EAET,CAAC,EACD;IACEjB,KAAK,EAAE,MAAM;IACbS,SAAS,EAAE,WAAW;IACtBR,GAAG,EAAE,WAAW;IAChBS,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAK,IAAIO,IAAI,CAACP,IAAI,CAAC,CAACQ,cAAc,CAAC;EAC1D,CAAC,EACD;IACEpB,KAAK,EAAE,IAAI;IACXC,GAAG,EAAE,QAAQ;IACbS,KAAK,EAAE,GAAG;IACVW,KAAK,EAAE,OAAO;IACdV,MAAM,EAAEA,CAACW,CAAC,EAAEC,MAAY,kBACtBrH,OAAA,CAACvB,KAAK;MAAC6I,IAAI,EAAC,OAAO;MAAAxD,QAAA,gBACjB9D,OAAA,CAACxB,MAAM;QACLkF,IAAI,EAAC,MAAM;QACX4D,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEvH,OAAA,CAACL,eAAe;UAAAiH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1BS,OAAO,EAAEA,CAAA,KAAM9B,uBAAuB,CAAC2B,MAAM,CAAE;QAAAvD,QAAA,EAChD;MAED;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/G,OAAA,CAACxB,MAAM;QACLkF,IAAI,EAAC,MAAM;QACX4D,IAAI,EAAC,OAAO;QACZC,IAAI,eAAEvH,OAAA,CAACP,YAAY;UAAAmH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBS,OAAO,EAAEA,CAAA,KAAM1C,UAAU,CAACuC,MAAM,CAAE;QAAAvD,QAAA,EACnC;MAED;QAAA8C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACT/G,OAAA,CAAClB,UAAU;QACTgH,KAAK,EAAC,oEAAa;QACnB2B,WAAW,EAAC,wGAAmB;QAC/BC,SAAS,EAAEA,CAAA,KAAM1C,YAAY,CAACqC,MAAM,CAAC9D,EAAE,CAAE;QACzCoE,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAA9D,QAAA,eAEf9D,OAAA,CAACxB,MAAM;UACLkF,IAAI,EAAC,MAAM;UACX4D,IAAI,EAAC,OAAO;UACZO,MAAM;UACNN,IAAI,eAAEvH,OAAA,CAACN,cAAc;YAAAkH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UACzBe,QAAQ,EAAET,MAAM,CAACzE,SAAS,GAAG,CAAE;UAAAkB,QAAA,EAChC;QAED;UAAA8C,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACE/G,OAAA;IAAK+H,SAAS,EAAC,2BAA2B;IAAAjE,QAAA,gBACxC9D,OAAA,CAACC,KAAK;MAAC+H,KAAK,EAAE,CAAE;MAAAlE,QAAA,EAAC;IAAI;MAAA8C,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7B/G,OAAA,CAAC1B,IAAI;MAACyJ,SAAS,EAAC,aAAa;MAACT,IAAI,EAAC,OAAO;MAAAxD,QAAA,eACxC9D,OAAA,CAACpB,IAAI;QACHqJ,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEzD,YAAa;QACvB0D,YAAY,EAAC,KAAK;QAAArE,QAAA,eAElB9D,OAAA,CAAChB,GAAG;UAACoJ,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACC,KAAK,EAAE;YAAE7B,KAAK,EAAE;UAAO,CAAE;UAAA1C,QAAA,gBAC9C9D,OAAA,CAACf,GAAG;YAACqJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1E,QAAA,eACzB9D,OAAA,CAACpB,IAAI,CAAC6J,IAAI;cAACjF,IAAI,EAAC,MAAM;cAACkF,KAAK,EAAC,0BAAM;cAAA5E,QAAA,eACjC9D,OAAA,CAACtB,KAAK;gBAACiK,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/G,OAAA,CAACf,GAAG;YAACqJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1E,QAAA,eACzB9D,OAAA,CAACpB,IAAI,CAAC6J,IAAI;cAACjF,IAAI,EAAC,MAAM;cAACkF,KAAK,EAAC,0BAAM;cAAA5E,QAAA,eACjC9D,OAAA,CAACtB,KAAK;gBAACiK,WAAW,EAAC,4CAAS;gBAACC,UAAU;cAAA;gBAAAhC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/G,OAAA,CAACf,GAAG;YAACqJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1E,QAAA,eACzB9D,OAAA,CAACpB,IAAI,CAAC6J,IAAI;cAACjF,IAAI,EAAC,QAAQ;cAACkF,KAAK,EAAC,cAAI;cAAA5E,QAAA,eACjC9D,OAAA,CAACV,MAAM;gBAACqJ,WAAW,EAAC,gCAAO;gBAACC,UAAU;gBAAA9E,QAAA,gBACpC9D,OAAA,CAACE,MAAM;kBAAC2I,KAAK,EAAE,CAAE;kBAAA/E,QAAA,EAAC;gBAAE;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC7B/G,OAAA,CAACE,MAAM;kBAAC2I,KAAK,EAAE,CAAE;kBAAA/E,QAAA,EAAC;gBAAE;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/G,OAAA,CAACf,GAAG;YAACqJ,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA1E,QAAA,eACzB9D,OAAA,CAACpB,IAAI,CAAC6J,IAAI;cAAA3E,QAAA,eACR9D,OAAA,CAACvB,KAAK;gBAAAqF,QAAA,gBACJ9D,OAAA,CAACxB,MAAM;kBAACkF,IAAI,EAAC,SAAS;kBAACoF,QAAQ,EAAC,QAAQ;kBAACvB,IAAI,eAAEvH,OAAA,CAACR,cAAc;oBAAAoH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAjD,QAAA,EAAC;gBAEnE;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACT/G,OAAA,CAACxB,MAAM;kBAACgJ,OAAO,EAAE7C,WAAY;kBAAC4C,IAAI,eAAEvH,OAAA,CAACJ,cAAc;oBAAAgH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAjD,QAAA,EAAC;gBAExD;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGP/G,OAAA,CAAC1B,IAAI;MAACyJ,SAAS,EAAC,aAAa;MAACT,IAAI,EAAC,OAAO;MAAAxD,QAAA,eACxC9D,OAAA,CAAChB,GAAG;QAAC+J,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAlF,QAAA,gBACzC9D,OAAA,CAACf,GAAG;UAAA6E,QAAA,eACF9D,OAAA,CAACxB,MAAM;YACLkF,IAAI,EAAC,SAAS;YACd6D,IAAI,eAAEvH,OAAA,CAACT,YAAY;cAAAqH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACvBS,OAAO,EAAE3C,SAAU;YAAAf,QAAA,EACpB;UAED;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACN/G,OAAA,CAACf,GAAG;UAAA6E,QAAA,eACF9D,OAAA,CAACxB,MAAM;YACL+I,IAAI,eAAEvH,OAAA,CAACJ,cAAc;cAAAgH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBS,OAAO,EAAErF,UAAW;YACpBzB,OAAO,EAAEA,OAAQ;YAAAoD,QAAA,EAClB;UAED;YAAA8C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGP/G,OAAA,CAAC1B,IAAI;MAAAwF,QAAA,eACH9D,OAAA,CAACzB,KAAK;QACJ+H,OAAO,EAAEA,OAAQ;QACjB2C,UAAU,EAAE3I,KAAM;QAClB4I,MAAM,EAAC,IAAI;QACXxI,OAAO,EAAEA,OAAQ;QACjByI,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAExI,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZ2I,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAAC7I,KAAK,EAAE8I,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQ9I,KAAK,IAAI;UAC5C+I,QAAQ,EAAEA,CAAC3I,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAA2F,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGP/G,OAAA,CAACrB,KAAK;MACJmH,KAAK,EAAExE,WAAW,GAAG,MAAM,GAAG,MAAO;MACrCsI,IAAI,EAAE1I,cAAe;MACrB2I,QAAQ,EAAEA,CAAA,KAAM1I,iBAAiB,CAAC,KAAK,CAAE;MACzC2I,MAAM,EAAE,IAAK;MACbtD,KAAK,EAAE,GAAI;MACXuD,cAAc;MAAAjG,QAAA,eAEd9D,OAAA,CAACpB,IAAI;QACHoD,IAAI,EAAEA,IAAK;QACXiG,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAEhD,UAAW;QACrBiD,YAAY,EAAC,KAAK;QAAArE,QAAA,gBAElB9D,OAAA,CAAChB,GAAG;UAACoJ,MAAM,EAAE,EAAG;UAAAtE,QAAA,gBACd9D,OAAA,CAACf,GAAG;YAAC+K,IAAI,EAAE,EAAG;YAAAlG,QAAA,eACZ9D,OAAA,CAACpB,IAAI,CAAC6J,IAAI;cACRjF,IAAI,EAAC,MAAM;cACXkF,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAErL,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEsL,GAAG,EAAE,EAAE;gBAAEtL,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAAiF,QAAA,eAEF9D,OAAA,CAACtB,KAAK;gBAACiK,WAAW,EAAC;cAAS;gBAAA/B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACN/G,OAAA,CAACf,GAAG;YAAC+K,IAAI,EAAE,EAAG;YAAAlG,QAAA,eACZ9D,OAAA,CAACpB,IAAI,CAAC6J,IAAI;cACRjF,IAAI,EAAC,MAAM;cACXkF,KAAK,EAAC,0BAAM;cACZuB,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAErL,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEuL,OAAO,EAAE,WAAW;gBAAEvL,OAAO,EAAE;cAAmB,CAAC,EACrD;gBAAEsL,GAAG,EAAE,EAAE;gBAAEtL,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAAiF,QAAA,eAEF9D,OAAA,CAACtB,KAAK;gBAACiK,WAAW,EAAC,4CAAS;gBAACb,QAAQ,EAAE,CAAC,CAACxG;cAAY;gBAAAsF,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN/G,OAAA,CAACpB,IAAI,CAAC6J,IAAI;UACRjF,IAAI,EAAC,aAAa;UAClBkF,KAAK,EAAC,0BAAM;UACZuB,KAAK,EAAE,CACL;YAAEE,GAAG,EAAE,GAAG;YAAEtL,OAAO,EAAE;UAAiB,CAAC,CACvC;UAAAiF,QAAA,eAEF9D,OAAA,CAACG,QAAQ;YACPwI,WAAW,EAAC,4CAAS;YACrB0B,IAAI,EAAE,CAAE;YACRC,SAAS;YACTC,SAAS,EAAE;UAAI;YAAA3D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACO,CAAC,eAEZ/G,OAAA,CAACpB,IAAI,CAAC6J,IAAI;UACRjF,IAAI,EAAC,QAAQ;UACbkF,KAAK,EAAC,cAAI;UACV8B,aAAa,EAAC,SAAS;UAAA1G,QAAA,eAEvB9D,OAAA,CAACb,MAAM;YAACsL,eAAe,EAAC,cAAI;YAACC,iBAAiB,EAAC;UAAI;YAAA9D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eAEZ/G,OAAA,CAACpB,IAAI,CAAC6J,IAAI;UAAA3E,QAAA,eACR9D,OAAA,CAACvB,KAAK;YAAC4J,KAAK,EAAE;cAAE7B,KAAK,EAAE,MAAM;cAAEmE,cAAc,EAAE;YAAW,CAAE;YAAA7G,QAAA,gBAC1D9D,OAAA,CAACxB,MAAM;cAACgJ,OAAO,EAAEA,CAAA,KAAMrG,iBAAiB,CAAC,KAAK,CAAE;cAAA2C,QAAA,EAAC;YAEjD;cAAA8C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACT/G,OAAA,CAACxB,MAAM;cAACkF,IAAI,EAAC,SAAS;cAACoF,QAAQ,EAAC,QAAQ;cAAAhF,QAAA,EACrCxC,WAAW,GAAG,IAAI,GAAG;YAAI;cAAAsF,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpB,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGR/G,OAAA,CAACrB,KAAK;MACJmH,KAAK,EAAE,UAAUtE,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgC,IAAI,EAAG;MACtCoG,IAAI,EAAExI,wBAAyB;MAC/ByI,QAAQ,EAAEA,CAAA,KAAMxI,2BAA2B,CAAC,KAAK,CAAE;MACnDuJ,IAAI,EAAEjF,qBAAsB;MAC5Ba,KAAK,EAAE,GAAI;MACXuD,cAAc;MAAAjG,QAAA,gBAEd9D,OAAA;QAAKqI,KAAK,EAAE;UAAEwC,YAAY,EAAE;QAAG,CAAE;QAAA/G,QAAA,eAC/B9D,OAAA,CAACX,YAAY;UAACiI,IAAI,EAAC,OAAO;UAACwD,MAAM,EAAE,CAAE;UAAAhH,QAAA,gBACnC9D,OAAA,CAACX,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA5E,QAAA,EAAEtC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEgC;UAAI;YAAAoD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACxE/G,OAAA,CAACX,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA5E,QAAA,EAAEtC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEiC;UAAI;YAAAmD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eACxE/G,OAAA,CAACX,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,0BAAM;YAAA5E,QAAA,EAAEtC,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEoB;UAAS;YAAAgE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC,eAC7E/G,OAAA,CAACX,YAAY,CAACoJ,IAAI;YAACC,KAAK,EAAC,sCAAQ;YAAA5E,QAAA,EAAEpC,kBAAkB,CAACqJ;UAAM;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAoB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACZ,CAAC,eAEN/G,OAAA;QAAKqI,KAAK,EAAE;UAAE2C,MAAM,EAAE,mBAAmB;UAAEC,YAAY,EAAE,CAAC;UAAEC,OAAO,EAAE,EAAE;UAAEC,SAAS,EAAE,GAAG;UAAEC,QAAQ,EAAE;QAAO,CAAE;QAAAtH,QAAA,eAC1G9D,OAAA,CAACd,IAAI;UACHmM,SAAS;UACTnF,WAAW,EAAExE,kBAAmB;UAChCE,YAAY,EAAEA,YAAa;UAC3B0J,OAAO,EAAErF,qBAAsB;UAC/BsF,QAAQ,EAAE1J,eAAgB;UAC1B2J,QAAQ,EAAE3F,4BAA4B,CAACrF,WAAW,CAAE;UACpDiL,MAAM,EAAE;QAAI;UAAA7E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC1G,EAAA,CA5lBID,cAAwB;EAAA,QAgBbxB,IAAI,CAACqD,OAAO,EACNrD,IAAI,CAACqD,OAAO;AAAA;AAAAyJ,EAAA,GAjB7BtL,cAAwB;AA8lB9B,eAAeA,cAAc;AAAC,IAAAsL,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}