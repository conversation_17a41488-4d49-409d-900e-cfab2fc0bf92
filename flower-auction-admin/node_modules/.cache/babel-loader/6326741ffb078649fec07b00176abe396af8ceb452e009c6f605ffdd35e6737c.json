{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Table, Button, Space, Input, Select, Tag, Modal, Form, message, Popconfirm, Typography, Row, Col, DatePicker, Switch } from 'antd';\nimport { PlusOutlined, SearchOutlined, EditOutlined, DeleteOutlined, UserOutlined, ExportOutlined, ReloadOutlined } from '@ant-design/icons';\nimport { userService } from '../../../services/userService';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title\n} = Typography;\nconst {\n  Option\n} = Select;\nconst {\n  RangePicker\n} = DatePicker;\n\n// 用户类型枚举\nexport let UserType = /*#__PURE__*/function (UserType) {\n  UserType[UserType[\"AUCTIONEER\"] = 1] = \"AUCTIONEER\";\n  // 拍卖师\n  UserType[UserType[\"BUYER\"] = 2] = \"BUYER\";\n  // 买家\n  UserType[UserType[\"ADMIN\"] = 3] = \"ADMIN\";\n  // 管理员\n  UserType[UserType[\"QUALITY_INSPECTOR\"] = 4] = \"QUALITY_INSPECTOR\"; // 质检员\n  return UserType;\n}({});\n\n// 用户状态枚举\nexport let UserStatus = /*#__PURE__*/function (UserStatus) {\n  UserStatus[UserStatus[\"DISABLED\"] = 0] = \"DISABLED\";\n  // 禁用\n  UserStatus[UserStatus[\"ENABLED\"] = 1] = \"ENABLED\"; // 启用\n  return UserStatus;\n}({});\n\n// 用户数据接口\n\n// 查询参数接口\n\nconst UserList = () => {\n  _s();\n  const [users, setUsers] = useState([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState({\n    page: 1,\n    pageSize: 10\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState(null);\n  const [form] = Form.useForm();\n\n  // 用户类型映射\n  const userTypeMap = {\n    [UserType.AUCTIONEER]: {\n      label: '拍卖师',\n      color: 'blue'\n    },\n    [UserType.BUYER]: {\n      label: '买家',\n      color: 'green'\n    },\n    [UserType.ADMIN]: {\n      label: '管理员',\n      color: 'red'\n    },\n    [UserType.QUALITY_INSPECTOR]: {\n      label: '质检员',\n      color: 'orange'\n    }\n  };\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    setLoading(true);\n    try {\n      const response = await userService.getUserList(queryParams);\n      if (response.success) {\n        setUsers(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取用户列表失败');\n      }\n    } catch (error) {\n      message.error(error.message || '获取用户列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchUsers();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = values => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10\n    });\n  };\n\n  // 新增用户\n  const handleAdd = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑用户\n  const handleEdit = user => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      ...user,\n      userType: user.userType,\n      status: user.status === UserStatus.ENABLED\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除用户\n  const handleDelete = async id => {\n    try {\n      const response = await userService.deleteUser(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchUsers();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存用户\n  const handleSave = async values => {\n    setSaving(true);\n    try {\n      const userData = {\n        ...values,\n        status: values.status ? UserStatus.ENABLED : UserStatus.DISABLED\n      };\n\n      // 删除确认密码字段，后端不需要\n      delete userData.confirmPassword;\n      let response;\n      if (editingUser) {\n        response = await userService.updateUser(editingUser.id, userData);\n      } else {\n        response = await userService.createUser(userData);\n      }\n      if (response.success) {\n        const successMsg = editingUser ? '用户信息更新成功！' : '用户创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3\n        });\n        setIsModalVisible(false);\n        form.resetFields(); // 重置表单\n        fetchUsers(); // 刷新列表\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('username')) {\n            errorMsg = '用户名已存在，请使用其他用户名';\n          } else if (response.message.includes('phone')) {\n            errorMsg = '手机号已被使用，请使用其他手机号';\n          } else if (response.message.includes('email')) {\n            errorMsg = '邮箱已被使用，请使用其他邮箱';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n        message.error({\n          content: errorMsg,\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('保存用户失败:', error);\n\n      // 处理网络错误和其他异常\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const {\n          status,\n          data\n        } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('username')) {\n            errorMsg = '用户名已存在或格式不正确';\n          } else if (data.error && data.error.includes('phone')) {\n            errorMsg = '手机号格式不正确或已被使用';\n          } else if (data.error && data.error.includes('email')) {\n            errorMsg = '邮箱格式不正确或已被使用';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '用户信息冲突，用户名、手机号或邮箱已被使用';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n      message.error({\n        content: errorMsg,\n        duration: 5\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 切换用户状态\n  const handleToggleStatus = async user => {\n    try {\n      const newStatus = user.status === UserStatus.ENABLED ? UserStatus.DISABLED : UserStatus.ENABLED;\n      const response = await userService.updateUserStatus(user.id, newStatus);\n      if (response.success) {\n        message.success('状态更新成功');\n        fetchUsers();\n      } else {\n        message.error(response.message || '状态更新失败');\n      }\n    } catch (error) {\n      message.error(error.message || '状态更新失败');\n    }\n  };\n\n  // 导出用户数据\n  const handleExport = async () => {\n    try {\n      const response = await userService.exportUsers(queryParams);\n      if (response.success) {\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]));\n        const link = document.createElement('a');\n        link.href = url;\n        link.setAttribute('download', `users_${new Date().getTime()}.xlsx`);\n        document.body.appendChild(link);\n        link.click();\n        link.remove();\n        window.URL.revokeObjectURL(url);\n        message.success('导出成功');\n      } else {\n        message.error(response.message || '导出失败');\n      }\n    } catch (error) {\n      message.error(error.message || '导出失败');\n    }\n  };\n\n  // 表格列定义\n  const columns = [{\n    title: 'ID',\n    dataIndex: 'id',\n    key: 'id',\n    width: 80\n  }, {\n    title: '用户名',\n    dataIndex: 'username',\n    key: 'username',\n    width: 120,\n    render: text => /*#__PURE__*/_jsxDEV(Space, {\n      children: [/*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 311,\n        columnNumber: 11\n      }, this), text]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 310,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '真实姓名',\n    dataIndex: 'realName',\n    key: 'realName',\n    width: 100,\n    render: text => text || '-'\n  }, {\n    title: '手机号',\n    dataIndex: 'phone',\n    key: 'phone',\n    width: 120\n  }, {\n    title: '邮箱',\n    dataIndex: 'email',\n    key: 'email',\n    width: 180,\n    render: text => text || '-'\n  }, {\n    title: '用户类型',\n    dataIndex: 'userType',\n    key: 'userType',\n    width: 100,\n    render: userType => {\n      const typeInfo = userTypeMap[userType];\n      return /*#__PURE__*/_jsxDEV(Tag, {\n        color: (typeInfo === null || typeInfo === void 0 ? void 0 : typeInfo.color) || 'default',\n        children: (typeInfo === null || typeInfo === void 0 ? void 0 : typeInfo.label) || '未知'\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 344,\n        columnNumber: 11\n      }, this);\n    }\n  }, {\n    title: '状态',\n    dataIndex: 'status',\n    key: 'status',\n    width: 100,\n    render: (status, record) => /*#__PURE__*/_jsxDEV(Switch, {\n      checked: status === UserStatus.ENABLED,\n      onChange: () => handleToggleStatus(record),\n      checkedChildren: \"\\u542F\\u7528\",\n      unCheckedChildren: \"\\u7981\\u7528\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 9\n    }, this)\n  }, {\n    title: '创建时间',\n    dataIndex: 'createdAt',\n    key: 'createdAt',\n    width: 160,\n    render: text => new Date(text).toLocaleString()\n  }, {\n    title: '操作',\n    key: 'action',\n    width: 150,\n    fixed: 'right',\n    render: (_, record) => /*#__PURE__*/_jsxDEV(Space, {\n      size: \"small\",\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        type: \"link\",\n        size: \"small\",\n        icon: /*#__PURE__*/_jsxDEV(EditOutlined, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 381,\n          columnNumber: 19\n        }, this),\n        onClick: () => handleEdit(record),\n        children: \"\\u7F16\\u8F91\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 378,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Popconfirm, {\n        title: \"\\u786E\\u5B9A\\u8981\\u5220\\u9664\\u8FD9\\u4E2A\\u7528\\u6237\\u5417\\uFF1F\",\n        onConfirm: () => handleDelete(record.id),\n        okText: \"\\u786E\\u5B9A\",\n        cancelText: \"\\u53D6\\u6D88\",\n        children: /*#__PURE__*/_jsxDEV(Button, {\n          type: \"link\",\n          size: \"small\",\n          danger: true,\n          icon: /*#__PURE__*/_jsxDEV(DeleteOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 396,\n            columnNumber: 21\n          }, this),\n          children: \"\\u5220\\u9664\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 392,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 386,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 377,\n      columnNumber: 9\n    }, this)\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"user-list-container\",\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: \"\\u7528\\u6237\\u7BA1\\u7406\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"search-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        layout: \"inline\",\n        onFinish: handleSearch,\n        autoComplete: \"off\",\n        children: /*#__PURE__*/_jsxDEV(Row, {\n          gutter: [16, 16],\n          style: {\n            width: '100%'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              label: \"\\u7528\\u6237\\u540D\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 419,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 418,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"phone\",\n              label: \"\\u624B\\u673A\\u53F7\",\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u624B\\u673A\\u53F7\",\n                allowClear: true\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 425,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 424,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 423,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"userType\",\n              label: \"\\u7528\\u6237\\u7C7B\\u578B\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u7528\\u6237\\u7C7B\\u578B\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.AUCTIONEER,\n                  children: \"\\u62CD\\u5356\\u5E08\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 431,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.BUYER,\n                  children: \"\\u4E70\\u5BB6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 432,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.ADMIN,\n                  children: \"\\u7BA1\\u7406\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 433,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.QUALITY_INSPECTOR,\n                  children: \"\\u8D28\\u68C0\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 434,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 430,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 428,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 6,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u72B6\\u6001\",\n                allowClear: true,\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: UserStatus.ENABLED,\n                  children: \"\\u542F\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 441,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserStatus.DISABLED,\n                  children: \"\\u7981\\u7528\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 442,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 440,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 439,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 438,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 8,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"dateRange\",\n              label: \"\\u521B\\u5EFA\\u65F6\\u95F4\",\n              children: /*#__PURE__*/_jsxDEV(RangePicker, {\n                style: {\n                  width: '100%'\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            sm: 12,\n            md: 4,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(Button, {\n                  type: \"primary\",\n                  htmlType: \"submit\",\n                  icon: /*#__PURE__*/_jsxDEV(SearchOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 454,\n                    columnNumber: 66\n                  }, this),\n                  children: \"\\u641C\\u7D22\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 454,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Button, {\n                  onClick: handleReset,\n                  icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 457,\n                    columnNumber: 55\n                  }, this),\n                  children: \"\\u91CD\\u7F6E\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 457,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 453,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 452,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 451,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 417,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 412,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 411,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      className: \"action-card\",\n      size: \"small\",\n      children: /*#__PURE__*/_jsxDEV(Row, {\n        justify: \"space-between\",\n        align: \"middle\",\n        children: [/*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(PlusOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 474,\n                columnNumber: 23\n              }, this),\n              onClick: handleAdd,\n              children: \"\\u65B0\\u589E\\u7528\\u6237\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ExportOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 480,\n                columnNumber: 23\n              }, this),\n              onClick: handleExport,\n              children: \"\\u5BFC\\u51FA\\u6570\\u636E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 479,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Col, {\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 489,\n              columnNumber: 21\n            }, this),\n            onClick: fetchUsers,\n            loading: loading,\n            children: \"\\u5237\\u65B0\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 469,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 468,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Card, {\n      children: /*#__PURE__*/_jsxDEV(Table, {\n        columns: columns,\n        dataSource: users,\n        rowKey: \"id\",\n        loading: loading,\n        scroll: {\n          x: 1200\n        },\n        pagination: {\n          current: queryParams.page,\n          pageSize: queryParams.pageSize,\n          total: total,\n          showSizeChanger: true,\n          showQuickJumper: true,\n          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n          onChange: (page, pageSize) => {\n            setQueryParams({\n              ...queryParams,\n              page,\n              pageSize: pageSize || 10\n            });\n          }\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 501,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 500,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Modal, {\n      title: editingUser ? '编辑用户' : '新增用户',\n      open: isModalVisible,\n      onCancel: () => setIsModalVisible(false),\n      footer: null,\n      width: 600,\n      destroyOnClose: true,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        onFinish: handleSave,\n        autoComplete: \"off\",\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              label: \"\\u7528\\u6237\\u540D\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 3,\n                max: 20,\n                message: '用户名长度为3-20个字符'\n              }, {\n                pattern: /^[a-zA-Z0-9_]+$/,\n                message: '用户名只能包含字母、数字和下划线'\n              }],\n              extra: !editingUser ? \"用户名创建后不可修改，请谨慎填写\" : undefined,\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u7528\\u6237\\u540D\\uFF083-20\\u4E2A\\u5B57\\u7B26\\uFF09\",\n                disabled: !!editingUser,\n                showCount: true,\n                maxLength: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 543,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 542,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"realName\",\n              label: \"\\u771F\\u5B9E\\u59D3\\u540D\",\n              rules: [{\n                required: true,\n                message: '请输入真实姓名'\n              }, {\n                max: 20,\n                message: '真实姓名不能超过20个字符'\n              }, {\n                pattern: /^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/,\n                message: '真实姓名只能包含中文、英文和空格'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u771F\\u5B9E\\u59D3\\u540D\",\n                showCount: true,\n                maxLength: 20\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 571,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 562,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 561,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 541,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"phone\",\n              label: \"\\u624B\\u673A\\u53F7\",\n              rules: [{\n                required: true,\n                message: '请输入手机号'\n              }, {\n                pattern: /^1[3-9]\\d{9}$/,\n                message: '请输入正确的11位手机号码'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u516511\\u4F4D\\u624B\\u673A\\u53F7\\u7801\",\n                maxLength: 11,\n                addonBefore: \"+86\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 590,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 582,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 581,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"email\",\n              label: \"\\u90AE\\u7BB1\",\n              rules: [{\n                required: true,\n                message: '请输入邮箱'\n              }, {\n                type: 'email',\n                message: '请输入正确的邮箱地址'\n              }, {\n                max: 50,\n                message: '邮箱长度不能超过50个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u90AE\\u7BB1\\u5730\\u5740\",\n                type: \"email\",\n                maxLength: 50\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 607,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 598,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 580,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"userType\",\n              label: \"\\u7528\\u6237\\u7C7B\\u578B\",\n              rules: [{\n                required: true,\n                message: '请选择用户类型'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Select, {\n                placeholder: \"\\u8BF7\\u9009\\u62E9\\u7528\\u6237\\u7C7B\\u578B\",\n                children: [/*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.AUCTIONEER,\n                  children: \"\\u62CD\\u5356\\u5E08\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 624,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.BUYER,\n                  children: \"\\u4E70\\u5BB6\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 625,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.ADMIN,\n                  children: \"\\u7BA1\\u7406\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 626,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Option, {\n                  value: UserType.QUALITY_INSPECTOR,\n                  children: \"\\u8D28\\u68C0\\u5458\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 627,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 623,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 618,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 617,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"status\",\n              label: \"\\u72B6\\u6001\",\n              valuePropName: \"checked\",\n              children: /*#__PURE__*/_jsxDEV(Switch, {\n                checkedChildren: \"\\u542F\\u7528\",\n                unCheckedChildren: \"\\u7981\\u7528\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 637,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 632,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 631,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 616,\n          columnNumber: 11\n        }, this), !editingUser && /*#__PURE__*/_jsxDEV(Row, {\n          gutter: 16,\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              label: \"\\u5BC6\\u7801\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                max: 32,\n                message: '密码长度为6-32个字符'\n              }, {\n                pattern: /^(?=.*[a-zA-Z])(?=.*\\d)/,\n                message: '密码必须包含字母和数字'\n              }],\n              extra: \"\\u5BC6\\u7801\\u9700\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF0C\\u957F\\u5EA66-32\\u4F4D\",\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                placeholder: \"\\u8BF7\\u8F93\\u5165\\u5BC6\\u7801\\uFF086-32\\u4F4D\\uFF0C\\u5305\\u542B\\u5B57\\u6BCD\\u548C\\u6570\\u5B57\\uFF09\",\n                showCount: true,\n                maxLength: 32\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 655,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 645,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 644,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            span: 12,\n            children: /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"confirmPassword\",\n              label: \"\\u786E\\u8BA4\\u5BC6\\u7801\",\n              dependencies: ['password'],\n              rules: [{\n                required: true,\n                message: '请确认密码'\n              }, ({\n                getFieldValue\n              }) => ({\n                validator(_, value) {\n                  if (!value || getFieldValue('password') === value) {\n                    return Promise.resolve();\n                  }\n                  return Promise.reject(new Error('两次输入的密码不一致'));\n                }\n              })],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                placeholder: \"\\u8BF7\\u518D\\u6B21\\u8F93\\u5165\\u5BC6\\u7801\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 679,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 663,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 662,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 643,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            style: {\n              width: '100%',\n              justifyContent: 'flex-end'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              onClick: () => setIsModalVisible(false),\n              disabled: saving,\n              children: \"\\u53D6\\u6D88\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 687,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              htmlType: \"submit\",\n              loading: saving,\n              disabled: saving,\n              children: saving ? '保存中...' : editingUser ? '更新' : '创建'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 693,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 686,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 685,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 535,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 407,\n    columnNumber: 5\n  }, this);\n};\n_s(UserList, \"ZWgTyqKGjJj0IFABKrhgTBu2HcI=\", false, function () {\n  return [Form.useForm];\n});\n_c = UserList;\nexport default UserList;\nvar _c;\n$RefreshReg$(_c, \"UserList\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Table", "<PERSON><PERSON>", "Space", "Input", "Select", "Tag", "Modal", "Form", "message", "Popconfirm", "Typography", "Row", "Col", "DatePicker", "Switch", "PlusOutlined", "SearchOutlined", "EditOutlined", "DeleteOutlined", "UserOutlined", "ExportOutlined", "ReloadOutlined", "userService", "jsxDEV", "_jsxDEV", "Title", "Option", "RangePicker", "UserType", "UserStatus", "UserList", "_s", "users", "setUsers", "loading", "setLoading", "saving", "setSaving", "total", "setTotal", "queryParams", "setQueryParams", "page", "pageSize", "isModalVisible", "setIsModalVisible", "editingUser", "setEditingUser", "form", "useForm", "userTypeMap", "AUCTIONEER", "label", "color", "BUYER", "ADMIN", "QUALITY_INSPECTOR", "fetchUsers", "response", "getUserList", "success", "data", "list", "error", "handleSearch", "values", "handleReset", "handleAdd", "resetFields", "handleEdit", "user", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "userType", "status", "ENABLED", "handleDelete", "id", "deleteUser", "handleSave", "userData", "DISABLED", "confirmPassword", "updateUser", "createUser", "successMsg", "content", "duration", "errorMsg", "includes", "console", "handleToggleStatus", "newStatus", "updateUserStatus", "handleExport", "exportUsers", "url", "window", "URL", "createObjectURL", "Blob", "link", "document", "createElement", "href", "setAttribute", "Date", "getTime", "body", "append<PERSON><PERSON><PERSON>", "click", "remove", "revokeObjectURL", "columns", "title", "dataIndex", "key", "width", "render", "text", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "typeInfo", "record", "checked", "onChange", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unChecked<PERSON><PERSON><PERSON>n", "toLocaleString", "fixed", "_", "size", "type", "icon", "onClick", "onConfirm", "okText", "cancelText", "danger", "className", "level", "layout", "onFinish", "autoComplete", "gutter", "style", "xs", "sm", "md", "<PERSON><PERSON>", "name", "placeholder", "allowClear", "value", "htmlType", "justify", "align", "dataSource", "<PERSON><PERSON><PERSON>", "scroll", "x", "pagination", "current", "showSizeChanger", "showQuickJumper", "showTotal", "range", "open", "onCancel", "footer", "destroyOnClose", "span", "rules", "required", "min", "max", "pattern", "extra", "undefined", "disabled", "showCount", "max<PERSON><PERSON><PERSON>", "addonBefore", "valuePropName", "Password", "dependencies", "getFieldValue", "validator", "Promise", "resolve", "reject", "Error", "justifyContent", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Table,\n  Button,\n  Space,\n  Input,\n  Select,\n  Tag,\n  Modal,\n  Form,\n  message,\n  Popconfirm,\n  Typography,\n  Row,\n  Col,\n  DatePicker,\n  Switch,\n} from 'antd';\nimport {\n  PlusOutlined,\n  SearchOutlined,\n  EditOutlined,\n  DeleteOutlined,\n  UserOutlined,\n  ExportOutlined,\n  ReloadOutlined,\n} from '@ant-design/icons';\nimport type { ColumnsType } from 'antd/es/table';\nimport { userService } from '../../../services/userService';\nimport './index.css';\n\nconst { Title } = Typography;\nconst { Option } = Select;\nconst { RangePicker } = DatePicker;\n\n// 用户类型枚举\nexport enum UserType {\n  AUCTIONEER = 1, // 拍卖师\n  BUYER = 2,      // 买家\n  ADMIN = 3,      // 管理员\n  QUALITY_INSPECTOR = 4, // 质检员\n}\n\n// 用户状态枚举\nexport enum UserStatus {\n  DISABLED = 0, // 禁用\n  ENABLED = 1,  // 启用\n}\n\n// 用户数据接口\nexport interface User {\n  id: number;\n  username: string;\n  realName?: string;\n  phone: string;\n  email?: string;\n  userType: UserType;\n  status: UserStatus;\n  createdAt: string;\n  updatedAt: string;\n}\n\n// 查询参数接口\ninterface UserQueryParams {\n  username?: string;\n  phone?: string;\n  userType?: UserType;\n  status?: UserStatus;\n  dateRange?: [string, string];\n  page: number;\n  pageSize: number;\n}\n\nconst UserList: React.FC = () => {\n  const [users, setUsers] = useState<User[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [saving, setSaving] = useState(false);\n  const [total, setTotal] = useState(0);\n  const [queryParams, setQueryParams] = useState<UserQueryParams>({\n    page: 1,\n    pageSize: 10,\n  });\n  const [isModalVisible, setIsModalVisible] = useState(false);\n  const [editingUser, setEditingUser] = useState<User | null>(null);\n  const [form] = Form.useForm();\n\n  // 用户类型映射\n  const userTypeMap = {\n    [UserType.AUCTIONEER]: { label: '拍卖师', color: 'blue' },\n    [UserType.BUYER]: { label: '买家', color: 'green' },\n    [UserType.ADMIN]: { label: '管理员', color: 'red' },\n    [UserType.QUALITY_INSPECTOR]: { label: '质检员', color: 'orange' },\n  };\n\n  // 获取用户列表\n  const fetchUsers = async () => {\n    setLoading(true);\n    try {\n      const response = await userService.getUserList(queryParams);\n      if (response.success) {\n        setUsers(response.data.list);\n        setTotal(response.data.total);\n      } else {\n        message.error(response.message || '获取用户列表失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '获取用户列表失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // 初始化加载\n  useEffect(() => {\n    fetchUsers();\n  }, [queryParams]);\n\n  // 搜索处理\n  const handleSearch = (values: any) => {\n    setQueryParams({\n      ...queryParams,\n      ...values,\n      page: 1,\n    });\n  };\n\n  // 重置搜索\n  const handleReset = () => {\n    setQueryParams({\n      page: 1,\n      pageSize: 10,\n    });\n  };\n\n  // 新增用户\n  const handleAdd = () => {\n    setEditingUser(null);\n    form.resetFields();\n    setIsModalVisible(true);\n  };\n\n  // 编辑用户\n  const handleEdit = (user: User) => {\n    setEditingUser(user);\n    form.setFieldsValue({\n      ...user,\n      userType: user.userType,\n      status: user.status === UserStatus.ENABLED,\n    });\n    setIsModalVisible(true);\n  };\n\n  // 删除用户\n  const handleDelete = async (id: number) => {\n    try {\n      const response = await userService.deleteUser(id);\n      if (response.success) {\n        message.success('删除成功');\n        fetchUsers();\n      } else {\n        message.error(response.message || '删除失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '删除失败');\n    }\n  };\n\n  // 保存用户\n  const handleSave = async (values: any) => {\n    setSaving(true);\n\n    try {\n      const userData = {\n        ...values,\n        status: values.status ? UserStatus.ENABLED : UserStatus.DISABLED,\n      };\n\n      // 删除确认密码字段，后端不需要\n      delete userData.confirmPassword;\n\n      let response;\n      if (editingUser) {\n        response = await userService.updateUser(editingUser.id, userData);\n      } else {\n        response = await userService.createUser(userData);\n      }\n\n      if (response.success) {\n        const successMsg = editingUser ? '用户信息更新成功！' : '用户创建成功！';\n        message.success({\n          content: successMsg,\n          duration: 3,\n        });\n        setIsModalVisible(false);\n        form.resetFields(); // 重置表单\n        fetchUsers(); // 刷新列表\n      } else {\n        // 处理具体的错误信息\n        let errorMsg = '操作失败';\n        if (response.message) {\n          if (response.message.includes('username')) {\n            errorMsg = '用户名已存在，请使用其他用户名';\n          } else if (response.message.includes('phone')) {\n            errorMsg = '手机号已被使用，请使用其他手机号';\n          } else if (response.message.includes('email')) {\n            errorMsg = '邮箱已被使用，请使用其他邮箱';\n          } else if (response.message.includes('validation')) {\n            errorMsg = '输入信息格式不正确，请检查后重试';\n          } else {\n            errorMsg = response.message;\n          }\n        }\n\n        message.error({\n          content: errorMsg,\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('保存用户失败:', error);\n\n      // 处理网络错误和其他异常\n      let errorMsg = '保存失败，请稍后重试';\n      if (error.response) {\n        const { status, data } = error.response;\n        if (status === 400) {\n          if (data.error && data.error.includes('username')) {\n            errorMsg = '用户名已存在或格式不正确';\n          } else if (data.error && data.error.includes('phone')) {\n            errorMsg = '手机号格式不正确或已被使用';\n          } else if (data.error && data.error.includes('email')) {\n            errorMsg = '邮箱格式不正确或已被使用';\n          } else {\n            errorMsg = data.error || '请求参数错误，请检查输入信息';\n          }\n        } else if (status === 409) {\n          errorMsg = '用户信息冲突，用户名、手机号或邮箱已被使用';\n        } else if (status === 500) {\n          errorMsg = '服务器内部错误，请联系管理员';\n        } else {\n          errorMsg = `请求失败 (${status})，请稍后重试`;\n        }\n      } else if (error.message) {\n        errorMsg = error.message;\n      }\n\n      message.error({\n        content: errorMsg,\n        duration: 5,\n      });\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  // 切换用户状态\n  const handleToggleStatus = async (user: User) => {\n    try {\n      const newStatus = user.status === UserStatus.ENABLED ? UserStatus.DISABLED : UserStatus.ENABLED;\n      const response = await userService.updateUserStatus(user.id, newStatus);\n      if (response.success) {\n        message.success('状态更新成功');\n        fetchUsers();\n      } else {\n        message.error(response.message || '状态更新失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '状态更新失败');\n    }\n  };\n\n  // 导出用户数据\n  const handleExport = async () => {\n    try {\n      const response = await userService.exportUsers(queryParams);\n      if (response.success) {\n        // 创建下载链接\n        const url = window.URL.createObjectURL(new Blob([response.data]));\n        const link = document.createElement('a');\n        link.href = url;\n        link.setAttribute('download', `users_${new Date().getTime()}.xlsx`);\n        document.body.appendChild(link);\n        link.click();\n        link.remove();\n        window.URL.revokeObjectURL(url);\n        message.success('导出成功');\n      } else {\n        message.error(response.message || '导出失败');\n      }\n    } catch (error: any) {\n      message.error(error.message || '导出失败');\n    }\n  };\n\n  // 表格列定义\n  const columns: ColumnsType<User> = [\n    {\n      title: 'ID',\n      dataIndex: 'id',\n      key: 'id',\n      width: 80,\n    },\n    {\n      title: '用户名',\n      dataIndex: 'username',\n      key: 'username',\n      width: 120,\n      render: (text: string) => (\n        <Space>\n          <UserOutlined />\n          {text}\n        </Space>\n      ),\n    },\n    {\n      title: '真实姓名',\n      dataIndex: 'realName',\n      key: 'realName',\n      width: 100,\n      render: (text: string) => text || '-',\n    },\n    {\n      title: '手机号',\n      dataIndex: 'phone',\n      key: 'phone',\n      width: 120,\n    },\n    {\n      title: '邮箱',\n      dataIndex: 'email',\n      key: 'email',\n      width: 180,\n      render: (text: string) => text || '-',\n    },\n    {\n      title: '用户类型',\n      dataIndex: 'userType',\n      key: 'userType',\n      width: 100,\n      render: (userType: UserType) => {\n        const typeInfo = userTypeMap[userType];\n        return (\n          <Tag color={typeInfo?.color || 'default'}>\n            {typeInfo?.label || '未知'}\n          </Tag>\n        );\n      },\n    },\n    {\n      title: '状态',\n      dataIndex: 'status',\n      key: 'status',\n      width: 100,\n      render: (status: UserStatus, record: User) => (\n        <Switch\n          checked={status === UserStatus.ENABLED}\n          onChange={() => handleToggleStatus(record)}\n          checkedChildren=\"启用\"\n          unCheckedChildren=\"禁用\"\n        />\n      ),\n    },\n    {\n      title: '创建时间',\n      dataIndex: 'createdAt',\n      key: 'createdAt',\n      width: 160,\n      render: (text: string) => new Date(text).toLocaleString(),\n    },\n    {\n      title: '操作',\n      key: 'action',\n      width: 150,\n      fixed: 'right',\n      render: (_, record: User) => (\n        <Space size=\"small\">\n          <Button\n            type=\"link\"\n            size=\"small\"\n            icon={<EditOutlined />}\n            onClick={() => handleEdit(record)}\n          >\n            编辑\n          </Button>\n          <Popconfirm\n            title=\"确定要删除这个用户吗？\"\n            onConfirm={() => handleDelete(record.id)}\n            okText=\"确定\"\n            cancelText=\"取消\"\n          >\n            <Button\n              type=\"link\"\n              size=\"small\"\n              danger\n              icon={<DeleteOutlined />}\n            >\n              删除\n            </Button>\n          </Popconfirm>\n        </Space>\n      ),\n    },\n  ];\n\n  return (\n    <div className=\"user-list-container\">\n      <Title level={2}>用户管理</Title>\n\n      {/* 搜索表单 */}\n      <Card className=\"search-card\" size=\"small\">\n        <Form\n          layout=\"inline\"\n          onFinish={handleSearch}\n          autoComplete=\"off\"\n        >\n          <Row gutter={[16, 16]} style={{ width: '100%' }}>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"username\" label=\"用户名\">\n                <Input placeholder=\"请输入用户名\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"phone\" label=\"手机号\">\n                <Input placeholder=\"请输入手机号\" allowClear />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"userType\" label=\"用户类型\">\n                <Select placeholder=\"请选择用户类型\" allowClear>\n                  <Option value={UserType.AUCTIONEER}>拍卖师</Option>\n                  <Option value={UserType.BUYER}>买家</Option>\n                  <Option value={UserType.ADMIN}>管理员</Option>\n                  <Option value={UserType.QUALITY_INSPECTOR}>质检员</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={6}>\n              <Form.Item name=\"status\" label=\"状态\">\n                <Select placeholder=\"请选择状态\" allowClear>\n                  <Option value={UserStatus.ENABLED}>启用</Option>\n                  <Option value={UserStatus.DISABLED}>禁用</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={8}>\n              <Form.Item name=\"dateRange\" label=\"创建时间\">\n                <RangePicker style={{ width: '100%' }} />\n              </Form.Item>\n            </Col>\n            <Col xs={24} sm={12} md={4}>\n              <Form.Item>\n                <Space>\n                  <Button type=\"primary\" htmlType=\"submit\" icon={<SearchOutlined />}>\n                    搜索\n                  </Button>\n                  <Button onClick={handleReset} icon={<ReloadOutlined />}>\n                    重置\n                  </Button>\n                </Space>\n              </Form.Item>\n            </Col>\n          </Row>\n        </Form>\n      </Card>\n\n      {/* 操作按钮 */}\n      <Card className=\"action-card\" size=\"small\">\n        <Row justify=\"space-between\" align=\"middle\">\n          <Col>\n            <Space>\n              <Button\n                type=\"primary\"\n                icon={<PlusOutlined />}\n                onClick={handleAdd}\n              >\n                新增用户\n              </Button>\n              <Button\n                icon={<ExportOutlined />}\n                onClick={handleExport}\n              >\n                导出数据\n              </Button>\n            </Space>\n          </Col>\n          <Col>\n            <Button\n              icon={<ReloadOutlined />}\n              onClick={fetchUsers}\n              loading={loading}\n            >\n              刷新\n            </Button>\n          </Col>\n        </Row>\n      </Card>\n\n      {/* 用户列表表格 */}\n      <Card>\n        <Table\n          columns={columns}\n          dataSource={users}\n          rowKey=\"id\"\n          loading={loading}\n          scroll={{ x: 1200 }}\n          pagination={{\n            current: queryParams.page,\n            pageSize: queryParams.pageSize,\n            total: total,\n            showSizeChanger: true,\n            showQuickJumper: true,\n            showTotal: (total, range) =>\n              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,\n            onChange: (page, pageSize) => {\n              setQueryParams({\n                ...queryParams,\n                page,\n                pageSize: pageSize || 10,\n              });\n            },\n          }}\n        />\n      </Card>\n\n      {/* 用户编辑模态框 */}\n      <Modal\n        title={editingUser ? '编辑用户' : '新增用户'}\n        open={isModalVisible}\n        onCancel={() => setIsModalVisible(false)}\n        footer={null}\n        width={600}\n        destroyOnClose\n      >\n        <Form\n          form={form}\n          layout=\"vertical\"\n          onFinish={handleSave}\n          autoComplete=\"off\"\n        >\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"username\"\n                label=\"用户名\"\n                rules={[\n                  { required: true, message: '请输入用户名' },\n                  { min: 3, max: 20, message: '用户名长度为3-20个字符' },\n                  { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线' },\n                ]}\n                extra={!editingUser ? \"用户名创建后不可修改，请谨慎填写\" : undefined}\n              >\n                <Input\n                  placeholder=\"请输入用户名（3-20个字符）\"\n                  disabled={!!editingUser}\n                  showCount\n                  maxLength={20}\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"realName\"\n                label=\"真实姓名\"\n                rules={[\n                  { required: true, message: '请输入真实姓名' },\n                  { max: 20, message: '真实姓名不能超过20个字符' },\n                  { pattern: /^[\\u4e00-\\u9fa5a-zA-Z\\s]+$/, message: '真实姓名只能包含中文、英文和空格' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入真实姓名\"\n                  showCount\n                  maxLength={20}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"phone\"\n                label=\"手机号\"\n                rules={[\n                  { required: true, message: '请输入手机号' },\n                  { pattern: /^1[3-9]\\d{9}$/, message: '请输入正确的11位手机号码' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入11位手机号码\"\n                  maxLength={11}\n                  addonBefore=\"+86\"\n                />\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"email\"\n                label=\"邮箱\"\n                rules={[\n                  { required: true, message: '请输入邮箱' },\n                  { type: 'email', message: '请输入正确的邮箱地址' },\n                  { max: 50, message: '邮箱长度不能超过50个字符' },\n                ]}\n              >\n                <Input\n                  placeholder=\"请输入邮箱地址\"\n                  type=\"email\"\n                  maxLength={50}\n                />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          <Row gutter={16}>\n            <Col span={12}>\n              <Form.Item\n                name=\"userType\"\n                label=\"用户类型\"\n                rules={[{ required: true, message: '请选择用户类型' }]}\n              >\n                <Select placeholder=\"请选择用户类型\">\n                  <Option value={UserType.AUCTIONEER}>拍卖师</Option>\n                  <Option value={UserType.BUYER}>买家</Option>\n                  <Option value={UserType.ADMIN}>管理员</Option>\n                  <Option value={UserType.QUALITY_INSPECTOR}>质检员</Option>\n                </Select>\n              </Form.Item>\n            </Col>\n            <Col span={12}>\n              <Form.Item\n                name=\"status\"\n                label=\"状态\"\n                valuePropName=\"checked\"\n              >\n                <Switch checkedChildren=\"启用\" unCheckedChildren=\"禁用\" />\n              </Form.Item>\n            </Col>\n          </Row>\n\n          {!editingUser && (\n            <Row gutter={16}>\n              <Col span={12}>\n                <Form.Item\n                  name=\"password\"\n                  label=\"密码\"\n                  rules={[\n                    { required: true, message: '请输入密码' },\n                    { min: 6, max: 32, message: '密码长度为6-32个字符' },\n                    { pattern: /^(?=.*[a-zA-Z])(?=.*\\d)/, message: '密码必须包含字母和数字' },\n                  ]}\n                  extra=\"密码需包含字母和数字，长度6-32位\"\n                >\n                  <Input.Password\n                    placeholder=\"请输入密码（6-32位，包含字母和数字）\"\n                    showCount\n                    maxLength={32}\n                  />\n                </Form.Item>\n              </Col>\n              <Col span={12}>\n                <Form.Item\n                  name=\"confirmPassword\"\n                  label=\"确认密码\"\n                  dependencies={['password']}\n                  rules={[\n                    { required: true, message: '请确认密码' },\n                    ({ getFieldValue }) => ({\n                      validator(_, value) {\n                        if (!value || getFieldValue('password') === value) {\n                          return Promise.resolve();\n                        }\n                        return Promise.reject(new Error('两次输入的密码不一致'));\n                      },\n                    }),\n                  ]}\n                >\n                  <Input.Password placeholder=\"请再次输入密码\" />\n                </Form.Item>\n              </Col>\n            </Row>\n          )}\n\n          <Form.Item>\n            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>\n              <Button\n                onClick={() => setIsModalVisible(false)}\n                disabled={saving}\n              >\n                取消\n              </Button>\n              <Button\n                type=\"primary\"\n                htmlType=\"submit\"\n                loading={saving}\n                disabled={saving}\n              >\n                {saving ? '保存中...' : (editingUser ? '更新' : '创建')}\n              </Button>\n            </Space>\n          </Form.Item>\n        </Form>\n      </Modal>\n    </div>\n  );\n};\n\nexport default UserList;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,KAAK,EACLC,MAAM,EACNC,GAAG,EACHC,KAAK,EACLC,IAAI,EACJC,OAAO,EACPC,UAAU,EACVC,UAAU,EACVC,GAAG,EACHC,GAAG,EACHC,UAAU,EACVC,MAAM,QACD,MAAM;AACb,SACEC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,YAAY,EACZC,cAAc,EACdC,cAAc,QACT,mBAAmB;AAE1B,SAASC,WAAW,QAAQ,+BAA+B;AAC3D,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErB,MAAM;EAAEC;AAAM,CAAC,GAAGf,UAAU;AAC5B,MAAM;EAAEgB;AAAO,CAAC,GAAGtB,MAAM;AACzB,MAAM;EAAEuB;AAAY,CAAC,GAAGd,UAAU;;AAElC;AACA,WAAYe,QAAQ,0BAARA,QAAQ;EAARA,QAAQ,CAARA,QAAQ;EACF;EADNA,QAAQ,CAARA,QAAQ;EAEF;EAFNA,QAAQ,CAARA,QAAQ;EAGF;EAHNA,QAAQ,CAARA,QAAQ,kDAIK;EAAA,OAJbA,QAAQ;AAAA;;AAOpB;AACA,WAAYC,UAAU,0BAAVA,UAAU;EAAVA,UAAU,CAAVA,UAAU;EACN;EADJA,UAAU,CAAVA,UAAU,8BAEN;EAAA,OAFJA,UAAU;AAAA;;AAKtB;;AAaA;;AAWA,MAAMC,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGpC,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAACqC,OAAO,EAAEC,UAAU,CAAC,GAAGtC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACuC,MAAM,EAAEC,SAAS,CAAC,GAAGxC,QAAQ,CAAC,KAAK,CAAC;EAC3C,MAAM,CAACyC,KAAK,EAAEC,QAAQ,CAAC,GAAG1C,QAAQ,CAAC,CAAC,CAAC;EACrC,MAAM,CAAC2C,WAAW,EAAEC,cAAc,CAAC,GAAG5C,QAAQ,CAAkB;IAC9D6C,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ,CAAC,CAAC;EACF,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGhD,QAAQ,CAAC,KAAK,CAAC;EAC3D,MAAM,CAACiD,WAAW,EAAEC,cAAc,CAAC,GAAGlD,QAAQ,CAAc,IAAI,CAAC;EACjE,MAAM,CAACmD,IAAI,CAAC,GAAGzC,IAAI,CAAC0C,OAAO,CAAC,CAAC;;EAE7B;EACA,MAAMC,WAAW,GAAG;IAClB,CAACtB,QAAQ,CAACuB,UAAU,GAAG;MAAEC,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAO,CAAC;IACtD,CAACzB,QAAQ,CAAC0B,KAAK,GAAG;MAAEF,KAAK,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAQ,CAAC;IACjD,CAACzB,QAAQ,CAAC2B,KAAK,GAAG;MAAEH,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAM,CAAC;IAChD,CAACzB,QAAQ,CAAC4B,iBAAiB,GAAG;MAAEJ,KAAK,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAS;EAChE,CAAC;;EAED;EACA,MAAMI,UAAU,GAAG,MAAAA,CAAA,KAAY;IAC7BtB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMuB,QAAQ,GAAG,MAAMpC,WAAW,CAACqC,WAAW,CAACnB,WAAW,CAAC;MAC3D,IAAIkB,QAAQ,CAACE,OAAO,EAAE;QACpB3B,QAAQ,CAACyB,QAAQ,CAACG,IAAI,CAACC,IAAI,CAAC;QAC5BvB,QAAQ,CAACmB,QAAQ,CAACG,IAAI,CAACvB,KAAK,CAAC;MAC/B,CAAC,MAAM;QACL9B,OAAO,CAACuD,KAAK,CAACL,QAAQ,CAAClD,OAAO,IAAI,UAAU,CAAC;MAC/C;IACF,CAAC,CAAC,OAAOuD,KAAU,EAAE;MACnBvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,IAAI,UAAU,CAAC;IAC5C,CAAC,SAAS;MACR2B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;;EAED;EACArC,SAAS,CAAC,MAAM;IACd2D,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,CAACjB,WAAW,CAAC,CAAC;;EAEjB;EACA,MAAMwB,YAAY,GAAIC,MAAW,IAAK;IACpCxB,cAAc,CAAC;MACb,GAAGD,WAAW;MACd,GAAGyB,MAAM;MACTvB,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwB,WAAW,GAAGA,CAAA,KAAM;IACxBzB,cAAc,CAAC;MACbC,IAAI,EAAE,CAAC;MACPC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMwB,SAAS,GAAGA,CAAA,KAAM;IACtBpB,cAAc,CAAC,IAAI,CAAC;IACpBC,IAAI,CAACoB,WAAW,CAAC,CAAC;IAClBvB,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAMwB,UAAU,GAAIC,IAAU,IAAK;IACjCvB,cAAc,CAACuB,IAAI,CAAC;IACpBtB,IAAI,CAACuB,cAAc,CAAC;MAClB,GAAGD,IAAI;MACPE,QAAQ,EAAEF,IAAI,CAACE,QAAQ;MACvBC,MAAM,EAAEH,IAAI,CAACG,MAAM,KAAK5C,UAAU,CAAC6C;IACrC,CAAC,CAAC;IACF7B,iBAAiB,CAAC,IAAI,CAAC;EACzB,CAAC;;EAED;EACA,MAAM8B,YAAY,GAAG,MAAOC,EAAU,IAAK;IACzC,IAAI;MACF,MAAMlB,QAAQ,GAAG,MAAMpC,WAAW,CAACuD,UAAU,CAACD,EAAE,CAAC;MACjD,IAAIlB,QAAQ,CAACE,OAAO,EAAE;QACpBpD,OAAO,CAACoD,OAAO,CAAC,MAAM,CAAC;QACvBH,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLjD,OAAO,CAACuD,KAAK,CAACL,QAAQ,CAAClD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOuD,KAAU,EAAE;MACnBvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMsE,UAAU,GAAG,MAAOb,MAAW,IAAK;IACxC5B,SAAS,CAAC,IAAI,CAAC;IAEf,IAAI;MACF,MAAM0C,QAAQ,GAAG;QACf,GAAGd,MAAM;QACTQ,MAAM,EAAER,MAAM,CAACQ,MAAM,GAAG5C,UAAU,CAAC6C,OAAO,GAAG7C,UAAU,CAACmD;MAC1D,CAAC;;MAED;MACA,OAAOD,QAAQ,CAACE,eAAe;MAE/B,IAAIvB,QAAQ;MACZ,IAAIZ,WAAW,EAAE;QACfY,QAAQ,GAAG,MAAMpC,WAAW,CAAC4D,UAAU,CAACpC,WAAW,CAAC8B,EAAE,EAAEG,QAAQ,CAAC;MACnE,CAAC,MAAM;QACLrB,QAAQ,GAAG,MAAMpC,WAAW,CAAC6D,UAAU,CAACJ,QAAQ,CAAC;MACnD;MAEA,IAAIrB,QAAQ,CAACE,OAAO,EAAE;QACpB,MAAMwB,UAAU,GAAGtC,WAAW,GAAG,WAAW,GAAG,SAAS;QACxDtC,OAAO,CAACoD,OAAO,CAAC;UACdyB,OAAO,EAAED,UAAU;UACnBE,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFzC,iBAAiB,CAAC,KAAK,CAAC;QACxBG,IAAI,CAACoB,WAAW,CAAC,CAAC,CAAC,CAAC;QACpBX,UAAU,CAAC,CAAC,CAAC,CAAC;MAChB,CAAC,MAAM;QACL;QACA,IAAI8B,QAAQ,GAAG,MAAM;QACrB,IAAI7B,QAAQ,CAAClD,OAAO,EAAE;UACpB,IAAIkD,QAAQ,CAAClD,OAAO,CAACgF,QAAQ,CAAC,UAAU,CAAC,EAAE;YACzCD,QAAQ,GAAG,iBAAiB;UAC9B,CAAC,MAAM,IAAI7B,QAAQ,CAAClD,OAAO,CAACgF,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC7CD,QAAQ,GAAG,kBAAkB;UAC/B,CAAC,MAAM,IAAI7B,QAAQ,CAAClD,OAAO,CAACgF,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC7CD,QAAQ,GAAG,gBAAgB;UAC7B,CAAC,MAAM,IAAI7B,QAAQ,CAAClD,OAAO,CAACgF,QAAQ,CAAC,YAAY,CAAC,EAAE;YAClDD,QAAQ,GAAG,kBAAkB;UAC/B,CAAC,MAAM;YACLA,QAAQ,GAAG7B,QAAQ,CAAClD,OAAO;UAC7B;QACF;QAEAA,OAAO,CAACuD,KAAK,CAAC;UACZsB,OAAO,EAAEE,QAAQ;UACjBD,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOvB,KAAU,EAAE;MACnB0B,OAAO,CAAC1B,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;;MAE/B;MACA,IAAIwB,QAAQ,GAAG,YAAY;MAC3B,IAAIxB,KAAK,CAACL,QAAQ,EAAE;QAClB,MAAM;UAAEe,MAAM;UAAEZ;QAAK,CAAC,GAAGE,KAAK,CAACL,QAAQ;QACvC,IAAIe,MAAM,KAAK,GAAG,EAAE;UAClB,IAAIZ,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAACyB,QAAQ,CAAC,UAAU,CAAC,EAAE;YACjDD,QAAQ,GAAG,cAAc;UAC3B,CAAC,MAAM,IAAI1B,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAACyB,QAAQ,CAAC,OAAO,CAAC,EAAE;YACrDD,QAAQ,GAAG,eAAe;UAC5B,CAAC,MAAM,IAAI1B,IAAI,CAACE,KAAK,IAAIF,IAAI,CAACE,KAAK,CAACyB,QAAQ,CAAC,OAAO,CAAC,EAAE;YACrDD,QAAQ,GAAG,cAAc;UAC3B,CAAC,MAAM;YACLA,QAAQ,GAAG1B,IAAI,CAACE,KAAK,IAAI,gBAAgB;UAC3C;QACF,CAAC,MAAM,IAAIU,MAAM,KAAK,GAAG,EAAE;UACzBc,QAAQ,GAAG,uBAAuB;QACpC,CAAC,MAAM,IAAId,MAAM,KAAK,GAAG,EAAE;UACzBc,QAAQ,GAAG,gBAAgB;QAC7B,CAAC,MAAM;UACLA,QAAQ,GAAG,SAASd,MAAM,SAAS;QACrC;MACF,CAAC,MAAM,IAAIV,KAAK,CAACvD,OAAO,EAAE;QACxB+E,QAAQ,GAAGxB,KAAK,CAACvD,OAAO;MAC1B;MAEAA,OAAO,CAACuD,KAAK,CAAC;QACZsB,OAAO,EAAEE,QAAQ;QACjBD,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRjD,SAAS,CAAC,KAAK,CAAC;IAClB;EACF,CAAC;;EAED;EACA,MAAMqD,kBAAkB,GAAG,MAAOpB,IAAU,IAAK;IAC/C,IAAI;MACF,MAAMqB,SAAS,GAAGrB,IAAI,CAACG,MAAM,KAAK5C,UAAU,CAAC6C,OAAO,GAAG7C,UAAU,CAACmD,QAAQ,GAAGnD,UAAU,CAAC6C,OAAO;MAC/F,MAAMhB,QAAQ,GAAG,MAAMpC,WAAW,CAACsE,gBAAgB,CAACtB,IAAI,CAACM,EAAE,EAAEe,SAAS,CAAC;MACvE,IAAIjC,QAAQ,CAACE,OAAO,EAAE;QACpBpD,OAAO,CAACoD,OAAO,CAAC,QAAQ,CAAC;QACzBH,UAAU,CAAC,CAAC;MACd,CAAC,MAAM;QACLjD,OAAO,CAACuD,KAAK,CAACL,QAAQ,CAAClD,OAAO,IAAI,QAAQ,CAAC;MAC7C;IACF,CAAC,CAAC,OAAOuD,KAAU,EAAE;MACnBvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,IAAI,QAAQ,CAAC;IAC1C;EACF,CAAC;;EAED;EACA,MAAMqF,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMnC,QAAQ,GAAG,MAAMpC,WAAW,CAACwE,WAAW,CAACtD,WAAW,CAAC;MAC3D,IAAIkB,QAAQ,CAACE,OAAO,EAAE;QACpB;QACA,MAAMmC,GAAG,GAAGC,MAAM,CAACC,GAAG,CAACC,eAAe,CAAC,IAAIC,IAAI,CAAC,CAACzC,QAAQ,CAACG,IAAI,CAAC,CAAC,CAAC;QACjE,MAAMuC,IAAI,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;QACxCF,IAAI,CAACG,IAAI,GAAGR,GAAG;QACfK,IAAI,CAACI,YAAY,CAAC,UAAU,EAAE,SAAS,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC,OAAO,CAAC;QACnEL,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC;QAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC;QACZT,IAAI,CAACU,MAAM,CAAC,CAAC;QACbd,MAAM,CAACC,GAAG,CAACc,eAAe,CAAChB,GAAG,CAAC;QAC/BvF,OAAO,CAACoD,OAAO,CAAC,MAAM,CAAC;MACzB,CAAC,MAAM;QACLpD,OAAO,CAACuD,KAAK,CAACL,QAAQ,CAAClD,OAAO,IAAI,MAAM,CAAC;MAC3C;IACF,CAAC,CAAC,OAAOuD,KAAU,EAAE;MACnBvD,OAAO,CAACuD,KAAK,CAACA,KAAK,CAACvD,OAAO,IAAI,MAAM,CAAC;IACxC;EACF,CAAC;;EAED;EACA,MAAMwG,OAA0B,GAAG,CACjC;IACEC,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,IAAI;IACfC,GAAG,EAAE,IAAI;IACTC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,iBACnB9F,OAAA,CAACtB,KAAK;MAAAqH,QAAA,gBACJ/F,OAAA,CAACL,YAAY;QAAAqG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EACfL,IAAI;IAAA;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA;EAEX,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAKA,IAAI,IAAI;EACpC,CAAC,EACD;IACEL,KAAK,EAAE,KAAK;IACZC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE;EACT,CAAC,EACD;IACEH,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,OAAO;IAClBC,GAAG,EAAE,OAAO;IACZC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAKA,IAAI,IAAI;EACpC,CAAC,EACD;IACEL,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,UAAU;IACrBC,GAAG,EAAE,UAAU;IACfC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAG7C,QAAkB,IAAK;MAC9B,MAAMoD,QAAQ,GAAG1E,WAAW,CAACsB,QAAQ,CAAC;MACtC,oBACEhD,OAAA,CAACnB,GAAG;QAACgD,KAAK,EAAE,CAAAuE,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEvE,KAAK,KAAI,SAAU;QAAAkE,QAAA,EACtC,CAAAK,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAExE,KAAK,KAAI;MAAI;QAAAoE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACrB,CAAC;IAEV;EACF,CAAC,EACD;IACEV,KAAK,EAAE,IAAI;IACXC,SAAS,EAAE,QAAQ;IACnBC,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAEA,CAAC5C,MAAkB,EAAEoD,MAAY,kBACvCrG,OAAA,CAACV,MAAM;MACLgH,OAAO,EAAErD,MAAM,KAAK5C,UAAU,CAAC6C,OAAQ;MACvCqD,QAAQ,EAAEA,CAAA,KAAMrC,kBAAkB,CAACmC,MAAM,CAAE;MAC3CG,eAAe,EAAC,cAAI;MACpBC,iBAAiB,EAAC;IAAI;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB;EAEL,CAAC,EACD;IACEV,KAAK,EAAE,MAAM;IACbC,SAAS,EAAE,WAAW;IACtBC,GAAG,EAAE,WAAW;IAChBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAGC,IAAY,IAAK,IAAIb,IAAI,CAACa,IAAI,CAAC,CAACY,cAAc,CAAC;EAC1D,CAAC,EACD;IACEjB,KAAK,EAAE,IAAI;IACXE,GAAG,EAAE,QAAQ;IACbC,KAAK,EAAE,GAAG;IACVe,KAAK,EAAE,OAAO;IACdd,MAAM,EAAEA,CAACe,CAAC,EAAEP,MAAY,kBACtBrG,OAAA,CAACtB,KAAK;MAACmI,IAAI,EAAC,OAAO;MAAAd,QAAA,gBACjB/F,OAAA,CAACvB,MAAM;QACLqI,IAAI,EAAC,MAAM;QACXD,IAAI,EAAC,OAAO;QACZE,IAAI,eAAE/G,OAAA,CAACP,YAAY;UAAAuG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QACvBa,OAAO,EAAEA,CAAA,KAAMnE,UAAU,CAACwD,MAAM,CAAE;QAAAN,QAAA,EACnC;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eACTnG,OAAA,CAACf,UAAU;QACTwG,KAAK,EAAC,oEAAa;QACnBwB,SAAS,EAAEA,CAAA,KAAM9D,YAAY,CAACkD,MAAM,CAACjD,EAAE,CAAE;QACzC8D,MAAM,EAAC,cAAI;QACXC,UAAU,EAAC,cAAI;QAAApB,QAAA,eAEf/F,OAAA,CAACvB,MAAM;UACLqI,IAAI,EAAC,MAAM;UACXD,IAAI,EAAC,OAAO;UACZO,MAAM;UACNL,IAAI,eAAE/G,OAAA,CAACN,cAAc;YAAAsG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAJ,QAAA,EAC1B;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR;EAEX,CAAC,CACF;EAED,oBACEnG,OAAA;IAAKqH,SAAS,EAAC,qBAAqB;IAAAtB,QAAA,gBAClC/F,OAAA,CAACC,KAAK;MAACqH,KAAK,EAAE,CAAE;MAAAvB,QAAA,EAAC;IAAI;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAG7BnG,OAAA,CAACzB,IAAI;MAAC8I,SAAS,EAAC,aAAa;MAACR,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxC/F,OAAA,CAACjB,IAAI;QACHwI,MAAM,EAAC,QAAQ;QACfC,QAAQ,EAAEhF,YAAa;QACvBiF,YAAY,EAAC,KAAK;QAAA1B,QAAA,eAElB/F,OAAA,CAACb,GAAG;UAACuI,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAACC,KAAK,EAAE;YAAE/B,KAAK,EAAE;UAAO,CAAE;UAAAG,QAAA,gBAC9C/F,OAAA,CAACZ,GAAG;YAACwI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzB/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,UAAU;cAACpG,KAAK,EAAC,oBAAK;cAAAmE,QAAA,eACpC/F,OAAA,CAACrB,KAAK;gBAACsJ,WAAW,EAAC,sCAAQ;gBAACC,UAAU;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACwI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzB/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,OAAO;cAACpG,KAAK,EAAC,oBAAK;cAAAmE,QAAA,eACjC/F,OAAA,CAACrB,KAAK;gBAACsJ,WAAW,EAAC,sCAAQ;gBAACC,UAAU;cAAA;gBAAAlC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACwI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzB/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,UAAU;cAACpG,KAAK,EAAC,0BAAM;cAAAmE,QAAA,eACrC/F,OAAA,CAACpB,MAAM;gBAACqJ,WAAW,EAAC,4CAAS;gBAACC,UAAU;gBAAAnC,QAAA,gBACtC/F,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE/H,QAAQ,CAACuB,UAAW;kBAAAoE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDnG,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE/H,QAAQ,CAAC0B,KAAM;kBAAAiE,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnG,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE/H,QAAQ,CAAC2B,KAAM;kBAAAgE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CnG,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE/H,QAAQ,CAAC4B,iBAAkB;kBAAA+D,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACwI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzB/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,QAAQ;cAACpG,KAAK,EAAC,cAAI;cAAAmE,QAAA,eACjC/F,OAAA,CAACpB,MAAM;gBAACqJ,WAAW,EAAC,gCAAO;gBAACC,UAAU;gBAAAnC,QAAA,gBACpC/F,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE9H,UAAU,CAAC6C,OAAQ;kBAAA6C,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC9CnG,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE9H,UAAU,CAACmD,QAAS;kBAAAuC,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACwI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzB/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cAACC,IAAI,EAAC,WAAW;cAACpG,KAAK,EAAC,0BAAM;cAAAmE,QAAA,eACtC/F,OAAA,CAACG,WAAW;gBAACwH,KAAK,EAAE;kBAAE/B,KAAK,EAAE;gBAAO;cAAE;gBAAAI,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACwI,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAAA/B,QAAA,eACzB/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cAAAhC,QAAA,eACR/F,OAAA,CAACtB,KAAK;gBAAAqH,QAAA,gBACJ/F,OAAA,CAACvB,MAAM;kBAACqI,IAAI,EAAC,SAAS;kBAACsB,QAAQ,EAAC,QAAQ;kBAACrB,IAAI,eAAE/G,OAAA,CAACR,cAAc;oBAAAwG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAEnE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eACTnG,OAAA,CAACvB,MAAM;kBAACuI,OAAO,EAAEtE,WAAY;kBAACqE,IAAI,eAAE/G,OAAA,CAACH,cAAc;oBAAAmG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAE;kBAAAJ,QAAA,EAAC;gBAExD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGPnG,OAAA,CAACzB,IAAI;MAAC8I,SAAS,EAAC,aAAa;MAACR,IAAI,EAAC,OAAO;MAAAd,QAAA,eACxC/F,OAAA,CAACb,GAAG;QAACkJ,OAAO,EAAC,eAAe;QAACC,KAAK,EAAC,QAAQ;QAAAvC,QAAA,gBACzC/F,OAAA,CAACZ,GAAG;UAAA2G,QAAA,eACF/F,OAAA,CAACtB,KAAK;YAAAqH,QAAA,gBACJ/F,OAAA,CAACvB,MAAM;cACLqI,IAAI,EAAC,SAAS;cACdC,IAAI,eAAE/G,OAAA,CAACT,YAAY;gBAAAyG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBa,OAAO,EAAErE,SAAU;cAAAoD,QAAA,EACpB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA,CAACvB,MAAM;cACLsI,IAAI,eAAE/G,OAAA,CAACJ,cAAc;gBAAAoG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzBa,OAAO,EAAE3C,YAAa;cAAA0B,QAAA,EACvB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACL,CAAC,eACNnG,OAAA,CAACZ,GAAG;UAAA2G,QAAA,eACF/F,OAAA,CAACvB,MAAM;YACLsI,IAAI,eAAE/G,OAAA,CAACH,cAAc;cAAAmG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBa,OAAO,EAAE/E,UAAW;YACpBvB,OAAO,EAAEA,OAAQ;YAAAqF,QAAA,EAClB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGPnG,OAAA,CAACzB,IAAI;MAAAwH,QAAA,eACH/F,OAAA,CAACxB,KAAK;QACJgH,OAAO,EAAEA,OAAQ;QACjB+C,UAAU,EAAE/H,KAAM;QAClBgI,MAAM,EAAC,IAAI;QACX9H,OAAO,EAAEA,OAAQ;QACjB+H,MAAM,EAAE;UAAEC,CAAC,EAAE;QAAK,CAAE;QACpBC,UAAU,EAAE;UACVC,OAAO,EAAE5H,WAAW,CAACE,IAAI;UACzBC,QAAQ,EAAEH,WAAW,CAACG,QAAQ;UAC9BL,KAAK,EAAEA,KAAK;UACZ+H,eAAe,EAAE,IAAI;UACrBC,eAAe,EAAE,IAAI;UACrBC,SAAS,EAAEA,CAACjI,KAAK,EAAEkI,KAAK,KACtB,KAAKA,KAAK,CAAC,CAAC,CAAC,IAAIA,KAAK,CAAC,CAAC,CAAC,QAAQlI,KAAK,IAAI;UAC5CyF,QAAQ,EAAEA,CAACrF,IAAI,EAAEC,QAAQ,KAAK;YAC5BF,cAAc,CAAC;cACb,GAAGD,WAAW;cACdE,IAAI;cACJC,QAAQ,EAAEA,QAAQ,IAAI;YACxB,CAAC,CAAC;UACJ;QACF;MAAE;QAAA6E,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGPnG,OAAA,CAAClB,KAAK;MACJ2G,KAAK,EAAEnE,WAAW,GAAG,MAAM,GAAG,MAAO;MACrC2H,IAAI,EAAE7H,cAAe;MACrB8H,QAAQ,EAAEA,CAAA,KAAM7H,iBAAiB,CAAC,KAAK,CAAE;MACzC8H,MAAM,EAAE,IAAK;MACbvD,KAAK,EAAE,GAAI;MACXwD,cAAc;MAAArD,QAAA,eAEd/F,OAAA,CAACjB,IAAI;QACHyC,IAAI,EAAEA,IAAK;QACX+F,MAAM,EAAC,UAAU;QACjBC,QAAQ,EAAElE,UAAW;QACrBmE,YAAY,EAAC,KAAK;QAAA1B,QAAA,gBAElB/F,OAAA,CAACb,GAAG;UAACuI,MAAM,EAAE,EAAG;UAAA3B,QAAA,gBACd/F,OAAA,CAACZ,GAAG;YAACiK,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cACRC,IAAI,EAAC,UAAU;cACfpG,KAAK,EAAC,oBAAK;cACX0H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAEwK,GAAG,EAAE,CAAC;gBAAEC,GAAG,EAAE,EAAE;gBAAEzK,OAAO,EAAE;cAAgB,CAAC,EAC7C;gBAAE0K,OAAO,EAAE,iBAAiB;gBAAE1K,OAAO,EAAE;cAAmB,CAAC,CAC3D;cACF2K,KAAK,EAAE,CAACrI,WAAW,GAAG,kBAAkB,GAAGsI,SAAU;cAAA7D,QAAA,eAErD/F,OAAA,CAACrB,KAAK;gBACJsJ,WAAW,EAAC,wEAAiB;gBAC7B4B,QAAQ,EAAE,CAAC,CAACvI,WAAY;gBACxBwI,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACiK,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cACRC,IAAI,EAAC,UAAU;cACfpG,KAAK,EAAC,0BAAM;cACZ0H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAU,CAAC,EACtC;gBAAEyK,GAAG,EAAE,EAAE;gBAAEzK,OAAO,EAAE;cAAgB,CAAC,EACrC;gBAAE0K,OAAO,EAAE,4BAA4B;gBAAE1K,OAAO,EAAE;cAAmB,CAAC,CACtE;cAAA+G,QAAA,eAEF/F,OAAA,CAACrB,KAAK;gBACJsJ,WAAW,EAAC,4CAAS;gBACrB6B,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA,CAACb,GAAG;UAACuI,MAAM,EAAE,EAAG;UAAA3B,QAAA,gBACd/F,OAAA,CAACZ,GAAG;YAACiK,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cACRC,IAAI,EAAC,OAAO;cACZpG,KAAK,EAAC,oBAAK;cACX0H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE0K,OAAO,EAAE,eAAe;gBAAE1K,OAAO,EAAE;cAAgB,CAAC,CACtD;cAAA+G,QAAA,eAEF/F,OAAA,CAACrB,KAAK;gBACJsJ,WAAW,EAAC,oDAAY;gBACxB8B,SAAS,EAAE,EAAG;gBACdC,WAAW,EAAC;cAAK;gBAAAhE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACiK,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cACRC,IAAI,EAAC,OAAO;cACZpG,KAAK,EAAC,cAAI;cACV0H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAE8H,IAAI,EAAE,OAAO;gBAAE9H,OAAO,EAAE;cAAa,CAAC,EACxC;gBAAEyK,GAAG,EAAE,EAAE;gBAAEzK,OAAO,EAAE;cAAgB,CAAC,CACrC;cAAA+G,QAAA,eAEF/F,OAAA,CAACrB,KAAK;gBACJsJ,WAAW,EAAC,4CAAS;gBACrBnB,IAAI,EAAC,OAAO;gBACZiD,SAAS,EAAE;cAAG;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENnG,OAAA,CAACb,GAAG;UAACuI,MAAM,EAAE,EAAG;UAAA3B,QAAA,gBACd/F,OAAA,CAACZ,GAAG;YAACiK,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cACRC,IAAI,EAAC,UAAU;cACfpG,KAAK,EAAC,0BAAM;cACZ0H,KAAK,EAAE,CAAC;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAU,CAAC,CAAE;cAAA+G,QAAA,eAEhD/F,OAAA,CAACpB,MAAM;gBAACqJ,WAAW,EAAC,4CAAS;gBAAAlC,QAAA,gBAC3B/F,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE/H,QAAQ,CAACuB,UAAW;kBAAAoE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAChDnG,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE/H,QAAQ,CAAC0B,KAAM;kBAAAiE,QAAA,EAAC;gBAAE;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC1CnG,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE/H,QAAQ,CAAC2B,KAAM;kBAAAgE,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC,eAC3CnG,OAAA,CAACE,MAAM;kBAACiI,KAAK,EAAE/H,QAAQ,CAAC4B,iBAAkB;kBAAA+D,QAAA,EAAC;gBAAG;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACjD;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACiK,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cACRC,IAAI,EAAC,QAAQ;cACbpG,KAAK,EAAC,cAAI;cACVqI,aAAa,EAAC,SAAS;cAAAlE,QAAA,eAEvB/F,OAAA,CAACV,MAAM;gBAACkH,eAAe,EAAC,cAAI;gBAACC,iBAAiB,EAAC;cAAI;gBAAAT,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,EAEL,CAAC7E,WAAW,iBACXtB,OAAA,CAACb,GAAG;UAACuI,MAAM,EAAE,EAAG;UAAA3B,QAAA,gBACd/F,OAAA,CAACZ,GAAG;YAACiK,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cACRC,IAAI,EAAC,UAAU;cACfpG,KAAK,EAAC,cAAI;cACV0H,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAEwK,GAAG,EAAE,CAAC;gBAAEC,GAAG,EAAE,EAAE;gBAAEzK,OAAO,EAAE;cAAe,CAAC,EAC5C;gBAAE0K,OAAO,EAAE,yBAAyB;gBAAE1K,OAAO,EAAE;cAAc,CAAC,CAC9D;cACF2K,KAAK,EAAC,0FAAoB;cAAA5D,QAAA,eAE1B/F,OAAA,CAACrB,KAAK,CAACuL,QAAQ;gBACbjC,WAAW,EAAC,sGAAsB;gBAClC6B,SAAS;gBACTC,SAAS,EAAE;cAAG;gBAAA/D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACf;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACNnG,OAAA,CAACZ,GAAG;YAACiK,IAAI,EAAE,EAAG;YAAAtD,QAAA,eACZ/F,OAAA,CAACjB,IAAI,CAACgJ,IAAI;cACRC,IAAI,EAAC,iBAAiB;cACtBpG,KAAK,EAAC,0BAAM;cACZuI,YAAY,EAAE,CAAC,UAAU,CAAE;cAC3Bb,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAEvK,OAAO,EAAE;cAAQ,CAAC,EACpC,CAAC;gBAAEoL;cAAc,CAAC,MAAM;gBACtBC,SAASA,CAACzD,CAAC,EAAEuB,KAAK,EAAE;kBAClB,IAAI,CAACA,KAAK,IAAIiC,aAAa,CAAC,UAAU,CAAC,KAAKjC,KAAK,EAAE;oBACjD,OAAOmC,OAAO,CAACC,OAAO,CAAC,CAAC;kBAC1B;kBACA,OAAOD,OAAO,CAACE,MAAM,CAAC,IAAIC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAChD;cACF,CAAC,CAAC,CACF;cAAA1E,QAAA,eAEF/F,OAAA,CAACrB,KAAK,CAACuL,QAAQ;gBAACjC,WAAW,EAAC;cAAS;gBAAAjC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,eAEDnG,OAAA,CAACjB,IAAI,CAACgJ,IAAI;UAAAhC,QAAA,eACR/F,OAAA,CAACtB,KAAK;YAACiJ,KAAK,EAAE;cAAE/B,KAAK,EAAE,MAAM;cAAE8E,cAAc,EAAE;YAAW,CAAE;YAAA3E,QAAA,gBAC1D/F,OAAA,CAACvB,MAAM;cACLuI,OAAO,EAAEA,CAAA,KAAM3F,iBAAiB,CAAC,KAAK,CAAE;cACxCwI,QAAQ,EAAEjJ,MAAO;cAAAmF,QAAA,EAClB;YAED;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eACTnG,OAAA,CAACvB,MAAM;cACLqI,IAAI,EAAC,SAAS;cACdsB,QAAQ,EAAC,QAAQ;cACjB1H,OAAO,EAAEE,MAAO;cAChBiJ,QAAQ,EAAEjJ,MAAO;cAAAmF,QAAA,EAEhBnF,MAAM,GAAG,QAAQ,GAAIU,WAAW,GAAG,IAAI,GAAG;YAAK;cAAA0E,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACR;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEV,CAAC;AAAC5F,EAAA,CAxnBID,QAAkB;EAAA,QAWPvB,IAAI,CAAC0C,OAAO;AAAA;AAAAkJ,EAAA,GAXvBrK,QAAkB;AA0nBxB,eAAeA,QAAQ;AAAC,IAAAqK,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}