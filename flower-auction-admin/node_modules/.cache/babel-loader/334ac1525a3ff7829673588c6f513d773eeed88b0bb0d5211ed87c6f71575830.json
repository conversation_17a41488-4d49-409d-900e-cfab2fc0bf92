{"ast": null, "code": "var _jsxFileName = \"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { Form, Input, Button, Card, message } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Login = () => {\n  _s();\n  var _location$state, _location$state$from;\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const {\n    login\n  } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取重定向路径\n  const from = ((_location$state = location.state) === null || _location$state === void 0 ? void 0 : (_location$state$from = _location$state.from) === null || _location$state$from === void 0 ? void 0 : _location$state$from.pathname) || '/dashboard';\n  const handleSubmit = async values => {\n    setLoading(true);\n    try {\n      console.log('开始登录...', values);\n      const result = await login(values);\n      console.log('登录结果:', result);\n      if (result.success) {\n        message.success({\n          content: '登录成功！正在跳转...',\n          duration: 2\n        });\n\n        // 延迟一下让用户看到成功提示\n        setTimeout(() => {\n          console.log('跳转到:', from);\n          navigate(from, {\n            replace: true\n          });\n        }, 500);\n      } else {\n        message.error({\n          content: result.message || '登录失败，请检查用户名和密码',\n          duration: 5\n        });\n      }\n    } catch (error) {\n      console.error('登录异常:', error);\n      message.error({\n        content: error.message || '登录失败，请检查网络连接',\n        duration: 5\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"login-container\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"login-background\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"login-content\",\n        children: /*#__PURE__*/_jsxDEV(Card, {\n          className: \"login-card\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"login-header\",\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              children: \"\\u6606\\u660E\\u82B1\\u5349\\u62CD\\u5356\\u7CFB\\u7EDF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\u7BA1\\u7406\\u540E\\u53F0\\u767B\\u5F55\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Form, {\n            form: form,\n            name: \"login\",\n            onFinish: handleSubmit,\n            autoComplete: \"off\",\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"username\",\n              rules: [{\n                required: true,\n                message: '请输入用户名'\n              }, {\n                min: 3,\n                message: '用户名至少3个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input, {\n                prefix: /*#__PURE__*/_jsxDEV(UserOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 83,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u7528\\u6237\\u540D\",\n                autoComplete: \"username\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 82,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 75,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              name: \"password\",\n              rules: [{\n                required: true,\n                message: '请输入密码'\n              }, {\n                min: 6,\n                message: '密码至少6个字符'\n              }],\n              children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                prefix: /*#__PURE__*/_jsxDEV(LockOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 97,\n                  columnNumber: 27\n                }, this),\n                placeholder: \"\\u5BC6\\u7801\",\n                autoComplete: \"current-password\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 96,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 89,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Form.Item, {\n              children: /*#__PURE__*/_jsxDEV(Button, {\n                type: \"primary\",\n                htmlType: \"submit\",\n                loading: loading,\n                block: true,\n                size: \"large\",\n                children: loading ? '登录中...' : '登录'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"login-footer\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"\\xA9 2024 \\u6606\\u660E\\u82B1\\u5349\\u62CD\\u5356\\u7CFB\\u7EDF. All rights reserved.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 116,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 62,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n};\n_s(Login, \"7p1Ob5drIBa6M2riCSRrxiH+9K4=\", false, function () {\n  return [Form.useForm, useAuth, useNavigate, useLocation];\n});\n_c = Login;\nexport default Login;\nvar _c;\n$RefreshReg$(_c, \"Login\");", "map": {"version": 3, "names": ["React", "useState", "Form", "Input", "<PERSON><PERSON>", "Card", "message", "UserOutlined", "LockOutlined", "useNavigate", "useLocation", "useAuth", "jsxDEV", "_jsxDEV", "<PERSON><PERSON>", "_s", "_location$state", "_location$state$from", "form", "useForm", "loading", "setLoading", "login", "navigate", "location", "from", "state", "pathname", "handleSubmit", "values", "console", "log", "result", "success", "content", "duration", "setTimeout", "replace", "error", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "name", "onFinish", "autoComplete", "size", "<PERSON><PERSON>", "rules", "required", "min", "prefix", "placeholder", "Password", "type", "htmlType", "block", "_c", "$RefreshReg$"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Form, Input, But<PERSON>, Card, message, Spin } from 'antd';\nimport { UserOutlined, LockOutlined } from '@ant-design/icons';\nimport { useNavigate, useLocation } from 'react-router-dom';\nimport { useAuth } from '../../hooks/useAuth';\nimport './index.css';\n\ninterface LoginFormValues {\n  username: string;\n  password: string;\n}\n\nconst Login: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const { login } = useAuth();\n  const navigate = useNavigate();\n  const location = useLocation();\n\n  // 获取重定向路径\n  const from = (location.state as any)?.from?.pathname || '/dashboard';\n\n  const handleSubmit = async (values: LoginFormValues) => {\n    setLoading(true);\n    try {\n      console.log('开始登录...', values);\n      const result = await login(values);\n      console.log('登录结果:', result);\n\n      if (result.success) {\n        message.success({\n          content: '登录成功！正在跳转...',\n          duration: 2,\n        });\n\n        // 延迟一下让用户看到成功提示\n        setTimeout(() => {\n          console.log('跳转到:', from);\n          navigate(from, { replace: true });\n        }, 500);\n      } else {\n        message.error({\n          content: result.message || '登录失败，请检查用户名和密码',\n          duration: 5,\n        });\n      }\n    } catch (error: any) {\n      console.error('登录异常:', error);\n      message.error({\n        content: error.message || '登录失败，请检查网络连接',\n        duration: 5,\n      });\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"login-container\">\n      <div className=\"login-background\">\n        <div className=\"login-content\">\n          <Card className=\"login-card\">\n            <div className=\"login-header\">\n              <h1>昆明花卉拍卖系统</h1>\n              <p>管理后台登录</p>\n            </div>\n\n            <Form\n              form={form}\n              name=\"login\"\n              onFinish={handleSubmit}\n              autoComplete=\"off\"\n              size=\"large\"\n            >\n              <Form.Item\n                name=\"username\"\n                rules={[\n                  { required: true, message: '请输入用户名' },\n                  { min: 3, message: '用户名至少3个字符' },\n                ]}\n              >\n                <Input\n                  prefix={<UserOutlined />}\n                  placeholder=\"用户名\"\n                  autoComplete=\"username\"\n                />\n              </Form.Item>\n\n              <Form.Item\n                name=\"password\"\n                rules={[\n                  { required: true, message: '请输入密码' },\n                  { min: 6, message: '密码至少6个字符' },\n                ]}\n              >\n                <Input.Password\n                  prefix={<LockOutlined />}\n                  placeholder=\"密码\"\n                  autoComplete=\"current-password\"\n                />\n              </Form.Item>\n\n              <Form.Item>\n                <Button\n                  type=\"primary\"\n                  htmlType=\"submit\"\n                  loading={loading}\n                  block\n                  size=\"large\"\n                >\n                  {loading ? '登录中...' : '登录'}\n                </Button>\n              </Form.Item>\n            </Form>\n\n            <div className=\"login-footer\">\n              <p>© 2024 昆明花卉拍卖系统. All rights reserved.</p>\n            </div>\n          </Card>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Login;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,KAAK,EAAEC,MAAM,EAAEC,IAAI,EAAEC,OAAO,QAAc,MAAM;AAC/D,SAASC,YAAY,EAAEC,YAAY,QAAQ,mBAAmB;AAC9D,SAASC,WAAW,EAAEC,WAAW,QAAQ,kBAAkB;AAC3D,SAASC,OAAO,QAAQ,qBAAqB;AAC7C,OAAO,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOrB,MAAMC,KAAe,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,eAAA,EAAAC,oBAAA;EAC5B,MAAM,CAACC,IAAI,CAAC,GAAGhB,IAAI,CAACiB,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM;IAAEqB;EAAM,CAAC,GAAGX,OAAO,CAAC,CAAC;EAC3B,MAAMY,QAAQ,GAAGd,WAAW,CAAC,CAAC;EAC9B,MAAMe,QAAQ,GAAGd,WAAW,CAAC,CAAC;;EAE9B;EACA,MAAMe,IAAI,GAAG,EAAAT,eAAA,GAACQ,QAAQ,CAACE,KAAK,cAAAV,eAAA,wBAAAC,oBAAA,GAAfD,eAAA,CAAyBS,IAAI,cAAAR,oBAAA,uBAA7BA,oBAAA,CAA+BU,QAAQ,KAAI,YAAY;EAEpE,MAAMC,YAAY,GAAG,MAAOC,MAAuB,IAAK;IACtDR,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACFS,OAAO,CAACC,GAAG,CAAC,SAAS,EAAEF,MAAM,CAAC;MAC9B,MAAMG,MAAM,GAAG,MAAMV,KAAK,CAACO,MAAM,CAAC;MAClCC,OAAO,CAACC,GAAG,CAAC,OAAO,EAAEC,MAAM,CAAC;MAE5B,IAAIA,MAAM,CAACC,OAAO,EAAE;QAClB3B,OAAO,CAAC2B,OAAO,CAAC;UACdC,OAAO,EAAE,cAAc;UACvBC,QAAQ,EAAE;QACZ,CAAC,CAAC;;QAEF;QACAC,UAAU,CAAC,MAAM;UACfN,OAAO,CAACC,GAAG,CAAC,MAAM,EAAEN,IAAI,CAAC;UACzBF,QAAQ,CAACE,IAAI,EAAE;YAAEY,OAAO,EAAE;UAAK,CAAC,CAAC;QACnC,CAAC,EAAE,GAAG,CAAC;MACT,CAAC,MAAM;QACL/B,OAAO,CAACgC,KAAK,CAAC;UACZJ,OAAO,EAAEF,MAAM,CAAC1B,OAAO,IAAI,gBAAgB;UAC3C6B,QAAQ,EAAE;QACZ,CAAC,CAAC;MACJ;IACF,CAAC,CAAC,OAAOG,KAAU,EAAE;MACnBR,OAAO,CAACQ,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7BhC,OAAO,CAACgC,KAAK,CAAC;QACZJ,OAAO,EAAEI,KAAK,CAAChC,OAAO,IAAI,cAAc;QACxC6B,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,SAAS;MACRd,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,oBACER,OAAA;IAAK0B,SAAS,EAAC,iBAAiB;IAAAC,QAAA,eAC9B3B,OAAA;MAAK0B,SAAS,EAAC,kBAAkB;MAAAC,QAAA,eAC/B3B,OAAA;QAAK0B,SAAS,EAAC,eAAe;QAAAC,QAAA,eAC5B3B,OAAA,CAACR,IAAI;UAACkC,SAAS,EAAC,YAAY;UAAAC,QAAA,gBAC1B3B,OAAA;YAAK0B,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3B3B,OAAA;cAAA2B,QAAA,EAAI;YAAQ;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACjB/B,OAAA;cAAA2B,QAAA,EAAG;YAAM;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAEN/B,OAAA,CAACX,IAAI;YACHgB,IAAI,EAAEA,IAAK;YACX2B,IAAI,EAAC,OAAO;YACZC,QAAQ,EAAElB,YAAa;YACvBmB,YAAY,EAAC,KAAK;YAClBC,IAAI,EAAC,OAAO;YAAAR,QAAA,gBAEZ3B,OAAA,CAACX,IAAI,CAAC+C,IAAI;cACRJ,IAAI,EAAC,UAAU;cACfK,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7C,OAAO,EAAE;cAAS,CAAC,EACrC;gBAAE8C,GAAG,EAAE,CAAC;gBAAE9C,OAAO,EAAE;cAAY,CAAC,CAChC;cAAAkC,QAAA,eAEF3B,OAAA,CAACV,KAAK;gBACJkD,MAAM,eAAExC,OAAA,CAACN,YAAY;kBAAAkC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBU,WAAW,EAAC,oBAAK;gBACjBP,YAAY,EAAC;cAAU;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACxB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ/B,OAAA,CAACX,IAAI,CAAC+C,IAAI;cACRJ,IAAI,EAAC,UAAU;cACfK,KAAK,EAAE,CACL;gBAAEC,QAAQ,EAAE,IAAI;gBAAE7C,OAAO,EAAE;cAAQ,CAAC,EACpC;gBAAE8C,GAAG,EAAE,CAAC;gBAAE9C,OAAO,EAAE;cAAW,CAAC,CAC/B;cAAAkC,QAAA,eAEF3B,OAAA,CAACV,KAAK,CAACoD,QAAQ;gBACbF,MAAM,eAAExC,OAAA,CAACL,YAAY;kBAAAiC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAE;gBACzBU,WAAW,EAAC,cAAI;gBAChBP,YAAY,EAAC;cAAkB;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAChC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACO,CAAC,eAEZ/B,OAAA,CAACX,IAAI,CAAC+C,IAAI;cAAAT,QAAA,eACR3B,OAAA,CAACT,MAAM;gBACLoD,IAAI,EAAC,SAAS;gBACdC,QAAQ,EAAC,QAAQ;gBACjBrC,OAAO,EAAEA,OAAQ;gBACjBsC,KAAK;gBACLV,IAAI,EAAC,OAAO;gBAAAR,QAAA,EAEXpB,OAAO,GAAG,QAAQ,GAAG;cAAI;gBAAAqB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpB;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACA,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACR,CAAC,eAEP/B,OAAA;YAAK0B,SAAS,EAAC,cAAc;YAAAC,QAAA,eAC3B3B,OAAA;cAAA2B,QAAA,EAAG;YAAqC;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzC,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7B,EAAA,CA/GID,KAAe;EAAA,QACJZ,IAAI,CAACiB,OAAO,EAETR,OAAO,EACRF,WAAW,EACXC,WAAW;AAAA;AAAAiD,EAAA,GALxB7C,KAAe;AAiHrB,eAAeA,KAAK;AAAC,IAAA6C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}