{"ast": null, "code": "// This icon file is generated automatically.\nvar LayoutFilled = {\n  \"icon\": {\n    \"tag\": \"svg\",\n    \"attrs\": {\n      \"viewBox\": \"64 64 896 896\",\n      \"focusable\": \"false\"\n    },\n    \"children\": [{\n      \"tag\": \"path\",\n      \"attrs\": {\n        \"d\": \"M384 912h496c17.7 0 32-14.3 32-32V340H384v572zm496-800H384v164h528V144c0-17.7-14.3-32-32-32zm-768 32v736c0 17.7 14.3 32 32 32h176V112H144c-17.7 0-32 14.3-32 32z\"\n      }\n    }]\n  },\n  \"name\": \"layout\",\n  \"theme\": \"filled\"\n};\nexport default LayoutFilled;", "map": {"version": 3, "names": ["LayoutFilled"], "sources": ["/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/node_modules/@ant-design/icons-svg/es/asn/LayoutFilled.js"], "sourcesContent": ["// This icon file is generated automatically.\nvar LayoutFilled = { \"icon\": { \"tag\": \"svg\", \"attrs\": { \"viewBox\": \"64 64 896 896\", \"focusable\": \"false\" }, \"children\": [{ \"tag\": \"path\", \"attrs\": { \"d\": \"M384 912h496c17.7 0 32-14.3 32-32V340H384v572zm496-800H384v164h528V144c0-17.7-14.3-32-32-32zm-768 32v736c0 17.7 14.3 32 32 32h176V112H144c-17.7 0-32 14.3-32 32z\" } }] }, \"name\": \"layout\", \"theme\": \"filled\" };\nexport default LayoutFilled;\n"], "mappings": "AAAA;AACA,IAAIA,YAAY,GAAG;EAAE,MAAM,EAAE;IAAE,KAAK,EAAE,KAAK;IAAE,OAAO,EAAE;MAAE,SAAS,EAAE,eAAe;MAAE,WAAW,EAAE;IAAQ,CAAC;IAAE,UAAU,EAAE,CAAC;MAAE,KAAK,EAAE,MAAM;MAAE,OAAO,EAAE;QAAE,GAAG,EAAE;MAAmK;IAAE,CAAC;EAAE,CAAC;EAAE,MAAM,EAAE,QAAQ;EAAE,OAAO,EAAE;AAAS,CAAC;AAC1W,eAAeA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}