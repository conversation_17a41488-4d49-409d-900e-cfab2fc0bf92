[{"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/index.tsx": "1", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/App.tsx": "3", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/index.ts": "4", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/routes/index.tsx": "5", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/slices/authSlice.ts": "6", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx": "7", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/layouts/MainLayout/index.tsx": "8", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Dashboard/index.tsx": "9", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx": "10", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx": "14", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx": "12", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx": "13", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx": "15", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx": "16", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx": "17", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx": "18", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/ShippingManagement/index.tsx": "19", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/OrderList/index.tsx": "20", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/TransactionRecords/index.tsx": "21", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx": "22", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/AccountManagement/index.tsx": "23", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/SystemSettings/index.tsx": "24", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/OperationLogs/index.tsx": "25", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/hooks/useAuth.ts": "26", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/productService.ts": "27", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/userService.ts": "28", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/roleService.ts": "29", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/auctionService.ts": "30", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/categoryService.ts": "31", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Header/index.tsx": "32", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Breadcrumb/index.tsx": "33", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/SideMenu/index.tsx": "34", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/orderService.ts": "35", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/authService.ts": "36", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/apiClient.ts": "37", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/AuthProvider/index.tsx": "38"}, {"size": 554, "mtime": 1748429353778, "results": "39", "hashOfConfig": "40"}, {"size": 425, "mtime": 1748429353779, "results": "41", "hashOfConfig": "40"}, {"size": 543, "mtime": 1748513797098, "results": "42", "hashOfConfig": "40"}, {"size": 442, "mtime": 1748485813036, "results": "43", "hashOfConfig": "40"}, {"size": 3705, "mtime": 1748506674634, "results": "44", "hashOfConfig": "40"}, {"size": 883, "mtime": 1748485820870, "results": "45", "hashOfConfig": "40"}, {"size": 7911, "mtime": 1748512837530, "results": "46", "hashOfConfig": "40"}, {"size": 2513, "mtime": 1748512740688, "results": "47", "hashOfConfig": "40"}, {"size": 3004, "mtime": 1748486092239, "results": "48", "hashOfConfig": "40"}, {"size": 31830, "mtime": 1748512088036, "results": "49", "hashOfConfig": "40"}, {"size": 22331, "mtime": 1748509070129, "results": "50", "hashOfConfig": "40"}, {"size": 18574, "mtime": 1748488853584, "results": "51", "hashOfConfig": "40"}, {"size": 13265, "mtime": 1748488117069, "results": "52", "hashOfConfig": "40"}, {"size": 19882, "mtime": 1748510345707, "results": "53", "hashOfConfig": "40"}, {"size": 16730, "mtime": 1748506833114, "results": "54", "hashOfConfig": "40"}, {"size": 20370, "mtime": 1748506796578, "results": "55", "hashOfConfig": "40"}, {"size": 20781, "mtime": 1748510043824, "results": "56", "hashOfConfig": "40"}, {"size": 12491, "mtime": 1748506528072, "results": "57", "hashOfConfig": "40"}, {"size": 368, "mtime": 1748486173012, "results": "58", "hashOfConfig": "40"}, {"size": 20431, "mtime": 1748488574583, "results": "59", "hashOfConfig": "40"}, {"size": 368, "mtime": 1748486186302, "results": "60", "hashOfConfig": "40"}, {"size": 13723, "mtime": 1748506856236, "results": "61", "hashOfConfig": "40"}, {"size": 366, "mtime": 1748486179096, "results": "62", "hashOfConfig": "40"}, {"size": 360, "mtime": 1748486597460, "results": "63", "hashOfConfig": "40"}, {"size": 358, "mtime": 1748486604671, "results": "64", "hashOfConfig": "40"}, {"size": 2483, "mtime": 1748513716030, "results": "65", "hashOfConfig": "40"}, {"size": 7605, "mtime": 1748511966679, "results": "66", "hashOfConfig": "40"}, {"size": 5485, "mtime": 1748511990096, "results": "67", "hashOfConfig": "40"}, {"size": 5922, "mtime": 1748512016971, "results": "68", "hashOfConfig": "40"}, {"size": 7867, "mtime": 1748512040486, "results": "69", "hashOfConfig": "40"}, {"size": 4947, "mtime": 1748487286843, "results": "70", "hashOfConfig": "40"}, {"size": 2295, "mtime": 1748485939039, "results": "71", "hashOfConfig": "40"}, {"size": 1719, "mtime": 1748485639236, "results": "72", "hashOfConfig": "40"}, {"size": 3945, "mtime": 1748506722942, "results": "73", "hashOfConfig": "40"}, {"size": 6335, "mtime": 1748492327930, "results": "74", "hashOfConfig": "40"}, {"size": 4589, "mtime": 1748512704879, "results": "75", "hashOfConfig": "40"}, {"size": 2970, "mtime": 1748513079573, "results": "76", "hashOfConfig": "40"}, {"size": 2243, "mtime": 1748513761744, "results": "77", "hashOfConfig": "40"}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "boqk99", {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/App.tsx", ["192"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/index.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/routes/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/slices/authSlice.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/layouts/MainLayout/index.tsx", ["193", "194"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Dashboard/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx", [], ["195"], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx", ["196"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx", ["197"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx", [], ["198"], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx", ["199", "200", "201", "202", "203", "204", "205"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx", ["206", "207"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx", ["208", "209"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx", ["210", "211", "212", "213"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/ShippingManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/OrderList/index.tsx", ["214"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/TransactionRecords/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx", ["215", "216"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/AccountManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/SystemSettings/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/OperationLogs/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/hooks/useAuth.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/productService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/userService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/roleService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/auctionService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/categoryService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Header/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Breadcrumb/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/SideMenu/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/orderService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/authService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/apiClient.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/AuthProvider/index.tsx", [], ["217"], {"ruleId": "218", "severity": 1, "message": "219", "line": 8, "column": 8, "nodeType": "220", "messageId": "221", "endLine": 8, "endColumn": 20}, {"ruleId": "218", "severity": 1, "message": "222", "line": 15, "column": 10, "nodeType": "220", "messageId": "221", "endLine": 15, "endColumn": 18}, {"ruleId": "218", "severity": 1, "message": "223", "line": 16, "column": 28, "nodeType": "220", "messageId": "221", "endLine": 16, "endColumn": 32}, {"ruleId": "224", "severity": 1, "message": "225", "line": 173, "column": 6, "nodeType": "226", "endLine": 173, "endColumn": 19, "suggestions": "227", "suppressions": "228"}, {"ruleId": "224", "severity": 1, "message": "229", "line": 122, "column": 6, "nodeType": "226", "endLine": 122, "endColumn": 19, "suggestions": "230"}, {"ruleId": "224", "severity": 1, "message": "225", "line": 128, "column": 6, "nodeType": "226", "endLine": 128, "endColumn": 19, "suggestions": "231"}, {"ruleId": "224", "severity": 1, "message": "232", "line": 186, "column": 6, "nodeType": "226", "endLine": 186, "endColumn": 19, "suggestions": "233", "suppressions": "234"}, {"ruleId": "218", "severity": 1, "message": "235", "line": 17, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 17, "endColumn": 10}, {"ruleId": "218", "severity": 1, "message": "236", "line": 22, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 22, "endColumn": 10}, {"ruleId": "218", "severity": 1, "message": "237", "line": 31, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 31, "endColumn": 17}, {"ruleId": "218", "severity": 1, "message": "238", "line": 33, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 33, "endColumn": 18}, {"ruleId": "218", "severity": 1, "message": "239", "line": 37, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 37, "endColumn": 17}, {"ruleId": "224", "severity": 1, "message": "240", "line": 137, "column": 6, "nodeType": "226", "endLine": 137, "endColumn": 8, "suggestions": "241"}, {"ruleId": "224", "severity": 1, "message": "242", "line": 172, "column": 15, "nodeType": "220", "endLine": 172, "endColumn": 22}, {"ruleId": "218", "severity": 1, "message": "243", "line": 32, "column": 15, "nodeType": "220", "messageId": "221", "endLine": 32, "endColumn": 26}, {"ruleId": "224", "severity": 1, "message": "244", "line": 190, "column": 6, "nodeType": "226", "endLine": 190, "endColumn": 19, "suggestions": "245"}, {"ruleId": "218", "severity": 1, "message": "246", "line": 18, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 18, "endColumn": 11}, {"ruleId": "224", "severity": 1, "message": "247", "line": 158, "column": 6, "nodeType": "226", "endLine": 158, "endColumn": 19, "suggestions": "248"}, {"ruleId": "218", "severity": 1, "message": "249", "line": 27, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 27, "endColumn": 22}, {"ruleId": "218", "severity": 1, "message": "250", "line": 34, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 34, "endColumn": 20}, {"ruleId": "224", "severity": 1, "message": "251", "line": 181, "column": 6, "nodeType": "226", "endLine": 181, "endColumn": 19, "suggestions": "252"}, {"ruleId": "218", "severity": 1, "message": "253", "line": 184, "column": 9, "nodeType": "220", "messageId": "221", "endLine": 184, "endColumn": 21}, {"ruleId": "224", "severity": 1, "message": "254", "line": 163, "column": 6, "nodeType": "226", "endLine": 163, "endColumn": 19, "suggestions": "255"}, {"ruleId": "218", "severity": 1, "message": "256", "line": 21, "column": 3, "nodeType": "220", "messageId": "221", "endLine": 21, "endColumn": 15}, {"ruleId": "224", "severity": 1, "message": "257", "line": 155, "column": 6, "nodeType": "226", "endLine": 155, "endColumn": 29, "suggestions": "258"}, {"ruleId": "224", "severity": 1, "message": "259", "line": 58, "column": 6, "nodeType": "226", "endLine": 58, "endColumn": 8, "suggestions": "260", "suppressions": "261"}, "@typescript-eslint/no-unused-vars", "'AuthProvider' is defined but never used.", "Identifier", "unusedVar", "'isMobile' is assigned a value but never used.", "'user' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["262"], ["263"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["264"], ["265"], "React Hook useEffect has missing dependencies: 'fetchPermissions' and 'fetchRoles'. Either include them or remove the dependency array.", ["266"], ["267"], "'Tooltip' is defined but never used.", "'Divider' is defined but never used.", "'DollarOutlined' is defined but never used.", "'WarningOutlined' is defined but never used.", "'TextArea' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'mockAuctionItem' and 'mockBidRecords'. Either include them or remove the dependency array.", ["268"], "The ref value 'wsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'wsRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'ColumnsType' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAuctionItems'. Either include it or remove the dependency array.", ["269"], "'Progress' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAuctions'. Either include it or remove the dependency array.", ["270"], "'ClockCircleOutlined' is defined but never used.", "'RangePicker' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBidRecords'. Either include it or remove the dependency array.", ["271"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["272"], "'FallOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFinanceData'. Either include it or remove the dependency array.", ["273"], "React Hook useEffect has a missing dependency: 'dispatch'. Either include it or remove the dependency array.", ["274"], ["275"], {"desc": "276", "fix": "277"}, {"kind": "278", "justification": "279"}, {"desc": "280", "fix": "281"}, {"desc": "276", "fix": "282"}, {"desc": "283", "fix": "284"}, {"kind": "278", "justification": "279"}, {"desc": "285", "fix": "286"}, {"desc": "287", "fix": "288"}, {"desc": "289", "fix": "290"}, {"desc": "291", "fix": "292"}, {"desc": "293", "fix": "294"}, {"desc": "295", "fix": "296"}, {"desc": "297", "fix": "298"}, {"kind": "278", "justification": "279"}, "Update the dependencies array to be: [fetchProducts, queryParams]", {"range": "299", "text": "300"}, "directive", "", "Update the dependencies array to be: [fetchUsers, queryParams]", {"range": "301", "text": "302"}, {"range": "303", "text": "300"}, "Update the dependencies array to be: [fetchPermissions, fetchRoles, queryParams]", {"range": "304", "text": "305"}, "Update the dependencies array to be: [mockAuctionItem, mockBidRecords]", {"range": "306", "text": "307"}, "Update the dependencies array to be: [fetchAuctionItems, queryParams]", {"range": "308", "text": "309"}, "Update the dependencies array to be: [fetchAuctions, queryParams]", {"range": "310", "text": "311"}, "Update the dependencies array to be: [fetchBidRecords, queryParams]", {"range": "312", "text": "313"}, "Update the dependencies array to be: [fetchOrders, queryParams]", {"range": "314", "text": "315"}, "Update the dependencies array to be: [dateRange, fetchFinanceData, reportType]", {"range": "316", "text": "317"}, "Update the dependencies array to be: [dispatch]", {"range": "318", "text": "319"}, [4126, 4139], "[fetchProducts, queryParams]", [2657, 2670], "[fetchUsers, queryParams]", [3177, 3190], [4781, 4794], "[fetchPermissions, fetchRoles, queryParams]", [3233, 3235], "[mockAuctionItem, mockBidRecords]", [4354, 4367], "[fetchAuctionItems, queryParams]", [3823, 3836], "[fetchAuctions, queryParams]", [3974, 3987], "[fetchBidRecords, queryParams]", [3944, 3957], "[fetchOrders, queryParams]", [3767, 3790], "[date<PERSON><PERSON><PERSON>, fetchFinanceData, reportType]", [1696, 1698], "[dispatch]"]