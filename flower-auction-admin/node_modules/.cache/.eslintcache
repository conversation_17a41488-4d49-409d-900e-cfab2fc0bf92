[{"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/index.tsx": "1", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/App.tsx": "3", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/index.ts": "4", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/routes/index.tsx": "5", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/slices/authSlice.ts": "6", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx": "7", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/layouts/MainLayout/index.tsx": "8", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Dashboard/index.tsx": "9", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx": "10", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx": "14", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx": "12", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx": "13", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx": "15", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx": "16", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx": "17", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx": "18", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/ShippingManagement/index.tsx": "19", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/OrderList/index.tsx": "20", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/TransactionRecords/index.tsx": "21", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx": "22", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/AccountManagement/index.tsx": "23", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/SystemSettings/index.tsx": "24", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/OperationLogs/index.tsx": "25", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/hooks/useAuth.ts": "26", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/productService.ts": "27", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/userService.ts": "28", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/roleService.ts": "29", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/auctionService.ts": "30", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/categoryService.ts": "31", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Header/index.tsx": "32", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Breadcrumb/index.tsx": "33", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/SideMenu/index.tsx": "34", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/orderService.ts": "35", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/authService.ts": "36", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/apiClient.ts": "37"}, {"size": 554, "mtime": 1748429353778, "results": "38", "hashOfConfig": "39"}, {"size": 425, "mtime": 1748429353779, "results": "40", "hashOfConfig": "39"}, {"size": 489, "mtime": 1748486771043, "results": "41", "hashOfConfig": "39"}, {"size": 442, "mtime": 1748485813036, "results": "42", "hashOfConfig": "39"}, {"size": 3705, "mtime": 1748506674634, "results": "43", "hashOfConfig": "39"}, {"size": 883, "mtime": 1748485820870, "results": "44", "hashOfConfig": "39"}, {"size": 3158, "mtime": 1748485862081, "results": "45", "hashOfConfig": "39"}, {"size": 1919, "mtime": 1748498876688, "results": "46", "hashOfConfig": "39"}, {"size": 3004, "mtime": 1748486092239, "results": "47", "hashOfConfig": "39"}, {"size": 14530, "mtime": 1748488510511, "results": "48", "hashOfConfig": "39"}, {"size": 21487, "mtime": 1748508333633, "results": "49", "hashOfConfig": "39"}, {"size": 18574, "mtime": 1748488853584, "results": "50", "hashOfConfig": "39"}, {"size": 13265, "mtime": 1748488117069, "results": "51", "hashOfConfig": "39"}, {"size": 16818, "mtime": 1748499760871, "results": "52", "hashOfConfig": "39"}, {"size": 16730, "mtime": 1748506833114, "results": "53", "hashOfConfig": "39"}, {"size": 20370, "mtime": 1748506796578, "results": "54", "hashOfConfig": "39"}, {"size": 17678, "mtime": 1748488334957, "results": "55", "hashOfConfig": "39"}, {"size": 12491, "mtime": 1748506528072, "results": "56", "hashOfConfig": "39"}, {"size": 368, "mtime": 1748486173012, "results": "57", "hashOfConfig": "39"}, {"size": 20431, "mtime": 1748488574583, "results": "58", "hashOfConfig": "39"}, {"size": 368, "mtime": 1748486186302, "results": "59", "hashOfConfig": "39"}, {"size": 13723, "mtime": 1748506856236, "results": "60", "hashOfConfig": "39"}, {"size": 366, "mtime": 1748486179096, "results": "61", "hashOfConfig": "39"}, {"size": 360, "mtime": 1748486597460, "results": "62", "hashOfConfig": "39"}, {"size": 358, "mtime": 1748486604671, "results": "63", "hashOfConfig": "39"}, {"size": 3443, "mtime": 1748498109322, "results": "64", "hashOfConfig": "39"}, {"size": 6456, "mtime": 1748492833350, "results": "65", "hashOfConfig": "39"}, {"size": 5473, "mtime": 1748508278927, "results": "66", "hashOfConfig": "39"}, {"size": 4961, "mtime": 1748487100875, "results": "67", "hashOfConfig": "39"}, {"size": 7262, "mtime": 1748492889893, "results": "68", "hashOfConfig": "39"}, {"size": 4947, "mtime": 1748487286843, "results": "69", "hashOfConfig": "39"}, {"size": 2295, "mtime": 1748485939039, "results": "70", "hashOfConfig": "39"}, {"size": 1719, "mtime": 1748485639236, "results": "71", "hashOfConfig": "39"}, {"size": 3945, "mtime": 1748506722942, "results": "72", "hashOfConfig": "39"}, {"size": 6335, "mtime": 1748492327930, "results": "73", "hashOfConfig": "39"}, {"size": 3451, "mtime": 1748498025197, "results": "74", "hashOfConfig": "39"}, {"size": 2821, "mtime": 1748496035835, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "boqk99", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/App.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/index.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/routes/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/slices/authSlice.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx", ["187"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/layouts/MainLayout/index.tsx", ["188"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Dashboard/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx", ["189", "190", "191", "192", "193", "194", "195", "196", "197", "198", "199", "200", "201"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx", ["202"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx", ["203"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx", [], ["204"], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx", ["205", "206", "207", "208", "209", "210", "211"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx", ["212", "213"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx", ["214", "215"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx", ["216", "217", "218", "219"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/ShippingManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/OrderList/index.tsx", ["220"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/TransactionRecords/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx", ["221", "222"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/AccountManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/SystemSettings/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/OperationLogs/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/hooks/useAuth.ts", ["223"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/productService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/userService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/roleService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/auctionService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/categoryService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Header/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Breadcrumb/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/SideMenu/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/orderService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/authService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/apiClient.ts", [], [], {"ruleId": "224", "severity": 1, "message": "225", "line": 2, "column": 46, "nodeType": "226", "messageId": "227", "endLine": 2, "endColumn": 50}, {"ruleId": "224", "severity": 1, "message": "228", "line": 15, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 15, "endColumn": 18}, {"ruleId": "224", "severity": 1, "message": "229", "line": 10, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 10, "endColumn": 8}, {"ruleId": "224", "severity": 1, "message": "230", "line": 17, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 17, "endColumn": 9}, {"ruleId": "224", "severity": 1, "message": "231", "line": 19, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 19, "endColumn": 14}, {"ruleId": "224", "severity": 1, "message": "232", "line": 20, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 20, "endColumn": 15}, {"ruleId": "224", "severity": 1, "message": "233", "line": 29, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 29, "endColumn": 17}, {"ruleId": "224", "severity": 1, "message": "234", "line": 31, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 31, "endColumn": 17}, {"ruleId": "224", "severity": 1, "message": "235", "line": 40, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 40, "endColumn": 17}, {"ruleId": "224", "severity": 1, "message": "236", "line": 100, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 100, "endColumn": 24}, {"ruleId": "224", "severity": 1, "message": "237", "line": 101, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 101, "endColumn": 25}, {"ruleId": "224", "severity": 1, "message": "238", "line": 103, "column": 10, "nodeType": "226", "messageId": "227", "endLine": 103, "endColumn": 24}, {"ruleId": "239", "severity": 1, "message": "240", "line": 148, "column": 6, "nodeType": "241", "endLine": 148, "endColumn": 19, "suggestions": "242"}, {"ruleId": "224", "severity": 1, "message": "243", "line": 221, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 221, "endColumn": 19}, {"ruleId": "224", "severity": 1, "message": "244", "line": 265, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 265, "endColumn": 27}, {"ruleId": "239", "severity": 1, "message": "245", "line": 121, "column": 6, "nodeType": "241", "endLine": 121, "endColumn": 19, "suggestions": "246"}, {"ruleId": "239", "severity": 1, "message": "240", "line": 128, "column": 6, "nodeType": "241", "endLine": 128, "endColumn": 19, "suggestions": "247"}, {"ruleId": "239", "severity": 1, "message": "248", "line": 168, "column": 6, "nodeType": "241", "endLine": 168, "endColumn": 19, "suggestions": "249", "suppressions": "250"}, {"ruleId": "224", "severity": 1, "message": "251", "line": 17, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 17, "endColumn": 10}, {"ruleId": "224", "severity": 1, "message": "252", "line": 22, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 22, "endColumn": 10}, {"ruleId": "224", "severity": 1, "message": "253", "line": 31, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 31, "endColumn": 17}, {"ruleId": "224", "severity": 1, "message": "254", "line": 33, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 33, "endColumn": 18}, {"ruleId": "224", "severity": 1, "message": "235", "line": 37, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 37, "endColumn": 17}, {"ruleId": "239", "severity": 1, "message": "255", "line": 137, "column": 6, "nodeType": "241", "endLine": 137, "endColumn": 8, "suggestions": "256"}, {"ruleId": "239", "severity": 1, "message": "257", "line": 172, "column": 15, "nodeType": "226", "endLine": 172, "endColumn": 22}, {"ruleId": "224", "severity": 1, "message": "258", "line": 32, "column": 15, "nodeType": "226", "messageId": "227", "endLine": 32, "endColumn": 26}, {"ruleId": "239", "severity": 1, "message": "259", "line": 190, "column": 6, "nodeType": "241", "endLine": 190, "endColumn": 19, "suggestions": "260"}, {"ruleId": "224", "severity": 1, "message": "261", "line": 18, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 18, "endColumn": 11}, {"ruleId": "239", "severity": 1, "message": "262", "line": 140, "column": 6, "nodeType": "241", "endLine": 140, "endColumn": 19, "suggestions": "263"}, {"ruleId": "224", "severity": 1, "message": "264", "line": 27, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 27, "endColumn": 22}, {"ruleId": "224", "severity": 1, "message": "265", "line": 34, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 34, "endColumn": 20}, {"ruleId": "239", "severity": 1, "message": "266", "line": 181, "column": 6, "nodeType": "241", "endLine": 181, "endColumn": 19, "suggestions": "267"}, {"ruleId": "224", "severity": 1, "message": "268", "line": 184, "column": 9, "nodeType": "226", "messageId": "227", "endLine": 184, "endColumn": 21}, {"ruleId": "239", "severity": 1, "message": "269", "line": 163, "column": 6, "nodeType": "241", "endLine": 163, "endColumn": 19, "suggestions": "270"}, {"ruleId": "224", "severity": 1, "message": "271", "line": 21, "column": 3, "nodeType": "226", "messageId": "227", "endLine": 21, "endColumn": 15}, {"ruleId": "239", "severity": 1, "message": "272", "line": 155, "column": 6, "nodeType": "241", "endLine": 155, "endColumn": 29, "suggestions": "273"}, {"ruleId": "239", "severity": 1, "message": "274", "line": 120, "column": 6, "nodeType": "241", "endLine": 120, "endColumn": 8, "suggestions": "275"}, "@typescript-eslint/no-unused-vars", "'Spin' is defined but never used.", "Identifier", "unusedVar", "'isMobile' is assigned a value but never used.", "'Modal' is defined but never used.", "'Upload' is defined but never used.", "'InputNumber' is defined but never used.", "'Descriptions' is defined but never used.", "'ExportOutlined' is defined but never used.", "'UploadOutlined' is defined but never used.", "'TextArea' is assigned a value but never used.", "'isModalVisible' is assigned a value but never used.", "'isDetailVisible' is assigned a value but never used.", "'viewingProduct' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["276"], "'handleSave' is assigned a value but never used.", "'handleUploadChange' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["277"], ["278"], "React Hook useEffect has missing dependencies: 'fetchPermissions' and 'fetchRoles'. Either include them or remove the dependency array.", ["279"], ["280"], "'Tooltip' is defined but never used.", "'Divider' is defined but never used.", "'DollarOutlined' is defined but never used.", "'WarningOutlined' is defined but never used.", "React Hook useEffect has missing dependencies: 'mockAuctionItem' and 'mockBidRecords'. Either include them or remove the dependency array.", ["281"], "The ref value 'wsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'wsRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'ColumnsType' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAuctionItems'. Either include it or remove the dependency array.", ["282"], "'Progress' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAuctions'. Either include it or remove the dependency array.", ["283"], "'ClockCircleOutlined' is defined but never used.", "'RangePicker' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBidRecords'. Either include it or remove the dependency array.", ["284"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["285"], "'FallOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFinanceData'. Either include it or remove the dependency array.", ["286"], "React Hook useEffect has a missing dependency: 'checkAuth'. Either include it or remove the dependency array.", ["287"], {"desc": "288", "fix": "289"}, {"desc": "290", "fix": "291"}, {"desc": "288", "fix": "292"}, {"desc": "293", "fix": "294"}, {"kind": "295", "justification": "296"}, {"desc": "297", "fix": "298"}, {"desc": "299", "fix": "300"}, {"desc": "301", "fix": "302"}, {"desc": "303", "fix": "304"}, {"desc": "305", "fix": "306"}, {"desc": "307", "fix": "308"}, {"desc": "309", "fix": "310"}, "Update the dependencies array to be: [fetchProducts, queryParams]", {"range": "311", "text": "312"}, "Update the dependencies array to be: [fetchUsers, queryParams]", {"range": "313", "text": "314"}, {"range": "315", "text": "312"}, "Update the dependencies array to be: [fetchPermissions, fetchRoles, queryParams]", {"range": "316", "text": "317"}, "directive", "", "Update the dependencies array to be: [mockAuctionItem, mockBidRecords]", {"range": "318", "text": "319"}, "Update the dependencies array to be: [fetchAuctionItems, queryParams]", {"range": "320", "text": "321"}, "Update the dependencies array to be: [fetchAuctions, queryParams]", {"range": "322", "text": "323"}, "Update the dependencies array to be: [fetchBidRecords, queryParams]", {"range": "324", "text": "325"}, "Update the dependencies array to be: [fetchOrders, queryParams]", {"range": "326", "text": "327"}, "Update the dependencies array to be: [dateRange, fetchFinanceData, reportType]", {"range": "328", "text": "329"}, "Update the dependencies array to be: [checkAuth]", {"range": "330", "text": "331"}, [3343, 3356], "[fetchProducts, queryParams]", [2618, 2631], "[fetchUsers, queryParams]", [3177, 3190], [4256, 4269], "[fetchPermissions, fetchRoles, queryParams]", [3233, 3235], "[mockAuctionItem, mockBidRecords]", [4354, 4367], "[fetchAuctionItems, queryParams]", [3290, 3303], "[fetchAuctions, queryParams]", [3974, 3987], "[fetchBidRecords, queryParams]", [3944, 3957], "[fetchOrders, queryParams]", [3767, 3790], "[date<PERSON><PERSON><PERSON>, fetchFinanceData, reportType]", [3186, 3188], "[checkAuth]"]