[{"/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/index.tsx": "1", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/reportWebVitals.ts": "2", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/App.tsx": "3", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/index.ts": "4", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/routes/index.tsx": "5", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/slices/authSlice.ts": "6", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx": "7", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/layouts/MainLayout/index.tsx": "8", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Dashboard/index.tsx": "9", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx": "10", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx": "14", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx": "12", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx": "13", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx": "15", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx": "16", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx": "17", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx": "18", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/ShippingManagement/index.tsx": "19", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/OrderList/index.tsx": "20", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/TransactionRecords/index.tsx": "21", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx": "22", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/AccountManagement/index.tsx": "23", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/SystemSettings/index.tsx": "24", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/OperationLogs/index.tsx": "25", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/hooks/useAuth.ts": "26", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/productService.ts": "27", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/userService.ts": "28", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/roleService.ts": "29", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/auctionService.ts": "30", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/categoryService.ts": "31", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Header/index.tsx": "32", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Breadcrumb/index.tsx": "33", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/SideMenu/index.tsx": "34", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/orderService.ts": "35", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/authService.ts": "36", "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/apiClient.ts": "37"}, {"size": 554, "mtime": 1748429353778, "results": "38", "hashOfConfig": "39"}, {"size": 425, "mtime": 1748429353779, "results": "40", "hashOfConfig": "39"}, {"size": 489, "mtime": 1748486771043, "results": "41", "hashOfConfig": "39"}, {"size": 442, "mtime": 1748485813036, "results": "42", "hashOfConfig": "39"}, {"size": 3705, "mtime": 1748506674634, "results": "43", "hashOfConfig": "39"}, {"size": 883, "mtime": 1748485820870, "results": "44", "hashOfConfig": "39"}, {"size": 3158, "mtime": 1748485862081, "results": "45", "hashOfConfig": "39"}, {"size": 1919, "mtime": 1748498876688, "results": "46", "hashOfConfig": "39"}, {"size": 3004, "mtime": 1748486092239, "results": "47", "hashOfConfig": "39"}, {"size": 25025, "mtime": 1748509256754, "results": "48", "hashOfConfig": "39"}, {"size": 22331, "mtime": 1748509070129, "results": "49", "hashOfConfig": "39"}, {"size": 18574, "mtime": 1748488853584, "results": "50", "hashOfConfig": "39"}, {"size": 13265, "mtime": 1748488117069, "results": "51", "hashOfConfig": "39"}, {"size": 19882, "mtime": 1748510345707, "results": "52", "hashOfConfig": "39"}, {"size": 16730, "mtime": 1748506833114, "results": "53", "hashOfConfig": "39"}, {"size": 20370, "mtime": 1748506796578, "results": "54", "hashOfConfig": "39"}, {"size": 20781, "mtime": 1748510043824, "results": "55", "hashOfConfig": "39"}, {"size": 12491, "mtime": 1748506528072, "results": "56", "hashOfConfig": "39"}, {"size": 368, "mtime": 1748486173012, "results": "57", "hashOfConfig": "39"}, {"size": 20431, "mtime": 1748488574583, "results": "58", "hashOfConfig": "39"}, {"size": 368, "mtime": 1748486186302, "results": "59", "hashOfConfig": "39"}, {"size": 13723, "mtime": 1748506856236, "results": "60", "hashOfConfig": "39"}, {"size": 366, "mtime": 1748486179096, "results": "61", "hashOfConfig": "39"}, {"size": 360, "mtime": 1748486597460, "results": "62", "hashOfConfig": "39"}, {"size": 358, "mtime": 1748486604671, "results": "63", "hashOfConfig": "39"}, {"size": 3443, "mtime": 1748498109322, "results": "64", "hashOfConfig": "39"}, {"size": 7043, "mtime": 1748509301125, "results": "65", "hashOfConfig": "39"}, {"size": 5473, "mtime": 1748508278927, "results": "66", "hashOfConfig": "39"}, {"size": 5548, "mtime": 1748510378411, "results": "67", "hashOfConfig": "39"}, {"size": 7849, "mtime": 1748510086023, "results": "68", "hashOfConfig": "39"}, {"size": 4947, "mtime": 1748487286843, "results": "69", "hashOfConfig": "39"}, {"size": 2295, "mtime": 1748485939039, "results": "70", "hashOfConfig": "39"}, {"size": 1719, "mtime": 1748485639236, "results": "71", "hashOfConfig": "39"}, {"size": 3945, "mtime": 1748506722942, "results": "72", "hashOfConfig": "39"}, {"size": 6335, "mtime": 1748492327930, "results": "73", "hashOfConfig": "39"}, {"size": 3451, "mtime": 1748498025197, "results": "74", "hashOfConfig": "39"}, {"size": 2821, "mtime": 1748496035835, "results": "75", "hashOfConfig": "39"}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "boqk99", {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/reportWebVitals.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/App.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/index.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/routes/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/store/slices/authSlice.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Login/index.tsx", ["187"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/layouts/MainLayout/index.tsx", ["188"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Dashboard/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductList/index.tsx", ["189", "190", "191"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx", ["192"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/ProductAudit/index.tsx", ["193"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Products/CategoryManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Users/<USER>/index.tsx", [], ["194"], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/LiveBidding/index.tsx", ["195", "196", "197", "198", "199", "200", "201"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionItems/index.tsx", ["202", "203"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/AuctionList/index.tsx", ["204", "205"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Auctions/BidRecords/index.tsx", ["206", "207", "208", "209"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/ShippingManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Orders/OrderList/index.tsx", ["210"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/TransactionRecords/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/FinanceReports/index.tsx", ["211", "212"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Finance/AccountManagement/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/SystemSettings/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/pages/Settings/OperationLogs/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/hooks/useAuth.ts", ["213"], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/productService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/userService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/roleService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/auctionService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/categoryService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Header/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/Breadcrumb/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/components/SideMenu/index.tsx", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/orderService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/authService.ts", [], [], "/Users/<USER>/Documents/mycode/leishengjie/flower-auction-admin/src/services/apiClient.ts", [], [], {"ruleId": "214", "severity": 1, "message": "215", "line": 2, "column": 46, "nodeType": "216", "messageId": "217", "endLine": 2, "endColumn": 50}, {"ruleId": "214", "severity": 1, "message": "218", "line": 15, "column": 10, "nodeType": "216", "messageId": "217", "endLine": 15, "endColumn": 18}, {"ruleId": "214", "severity": 1, "message": "219", "line": 19, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 19, "endColumn": 14}, {"ruleId": "214", "severity": 1, "message": "220", "line": 29, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 29, "endColumn": 17}, {"ruleId": "221", "severity": 1, "message": "222", "line": 166, "column": 6, "nodeType": "223", "endLine": 166, "endColumn": 19, "suggestions": "224"}, {"ruleId": "221", "severity": 1, "message": "225", "line": 122, "column": 6, "nodeType": "223", "endLine": 122, "endColumn": 19, "suggestions": "226"}, {"ruleId": "221", "severity": 1, "message": "222", "line": 128, "column": 6, "nodeType": "223", "endLine": 128, "endColumn": 19, "suggestions": "227"}, {"ruleId": "221", "severity": 1, "message": "228", "line": 186, "column": 6, "nodeType": "223", "endLine": 186, "endColumn": 19, "suggestions": "229", "suppressions": "230"}, {"ruleId": "214", "severity": 1, "message": "231", "line": 17, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 17, "endColumn": 10}, {"ruleId": "214", "severity": 1, "message": "232", "line": 22, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 22, "endColumn": 10}, {"ruleId": "214", "severity": 1, "message": "233", "line": 31, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 31, "endColumn": 17}, {"ruleId": "214", "severity": 1, "message": "234", "line": 33, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 33, "endColumn": 18}, {"ruleId": "214", "severity": 1, "message": "235", "line": 37, "column": 9, "nodeType": "216", "messageId": "217", "endLine": 37, "endColumn": 17}, {"ruleId": "221", "severity": 1, "message": "236", "line": 137, "column": 6, "nodeType": "223", "endLine": 137, "endColumn": 8, "suggestions": "237"}, {"ruleId": "221", "severity": 1, "message": "238", "line": 172, "column": 15, "nodeType": "216", "endLine": 172, "endColumn": 22}, {"ruleId": "214", "severity": 1, "message": "239", "line": 32, "column": 15, "nodeType": "216", "messageId": "217", "endLine": 32, "endColumn": 26}, {"ruleId": "221", "severity": 1, "message": "240", "line": 190, "column": 6, "nodeType": "223", "endLine": 190, "endColumn": 19, "suggestions": "241"}, {"ruleId": "214", "severity": 1, "message": "242", "line": 18, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 18, "endColumn": 11}, {"ruleId": "221", "severity": 1, "message": "243", "line": 158, "column": 6, "nodeType": "223", "endLine": 158, "endColumn": 19, "suggestions": "244"}, {"ruleId": "214", "severity": 1, "message": "245", "line": 27, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 27, "endColumn": 22}, {"ruleId": "214", "severity": 1, "message": "246", "line": 34, "column": 9, "nodeType": "216", "messageId": "217", "endLine": 34, "endColumn": 20}, {"ruleId": "221", "severity": 1, "message": "247", "line": 181, "column": 6, "nodeType": "223", "endLine": 181, "endColumn": 19, "suggestions": "248"}, {"ruleId": "214", "severity": 1, "message": "249", "line": 184, "column": 9, "nodeType": "216", "messageId": "217", "endLine": 184, "endColumn": 21}, {"ruleId": "221", "severity": 1, "message": "250", "line": 163, "column": 6, "nodeType": "223", "endLine": 163, "endColumn": 19, "suggestions": "251"}, {"ruleId": "214", "severity": 1, "message": "252", "line": 21, "column": 3, "nodeType": "216", "messageId": "217", "endLine": 21, "endColumn": 15}, {"ruleId": "221", "severity": 1, "message": "253", "line": 155, "column": 6, "nodeType": "223", "endLine": 155, "endColumn": 29, "suggestions": "254"}, {"ruleId": "221", "severity": 1, "message": "255", "line": 120, "column": 6, "nodeType": "223", "endLine": 120, "endColumn": 8, "suggestions": "256"}, "@typescript-eslint/no-unused-vars", "'Spin' is defined but never used.", "Identifier", "unusedVar", "'isMobile' is assigned a value but never used.", "'InputNumber' is defined but never used.", "'ExportOutlined' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", "ArrayExpression", ["257"], "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", ["258"], ["259"], "React Hook useEffect has missing dependencies: 'fetchPermissions' and 'fetchRoles'. Either include them or remove the dependency array.", ["260"], ["261"], "'Tooltip' is defined but never used.", "'Divider' is defined but never used.", "'DollarOutlined' is defined but never used.", "'WarningOutlined' is defined but never used.", "'TextArea' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'mockAuctionItem' and 'mockBidRecords'. Either include them or remove the dependency array.", ["262"], "The ref value 'wsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'wsRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "'ColumnsType' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAuctionItems'. Either include it or remove the dependency array.", ["263"], "'Progress' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchAuctions'. Either include it or remove the dependency array.", ["264"], "'ClockCircleOutlined' is defined but never used.", "'RangePicker' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchBidRecords'. Either include it or remove the dependency array.", ["265"], "'handleSearch' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["266"], "'FallOutlined' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchFinanceData'. Either include it or remove the dependency array.", ["267"], "React Hook useEffect has a missing dependency: 'checkAuth'. Either include it or remove the dependency array.", ["268"], {"desc": "269", "fix": "270"}, {"desc": "271", "fix": "272"}, {"desc": "269", "fix": "273"}, {"desc": "274", "fix": "275"}, {"kind": "276", "justification": "277"}, {"desc": "278", "fix": "279"}, {"desc": "280", "fix": "281"}, {"desc": "282", "fix": "283"}, {"desc": "284", "fix": "285"}, {"desc": "286", "fix": "287"}, {"desc": "288", "fix": "289"}, {"desc": "290", "fix": "291"}, "Update the dependencies array to be: [fetchProducts, queryParams]", {"range": "292", "text": "293"}, "Update the dependencies array to be: [fetchUsers, queryParams]", {"range": "294", "text": "295"}, {"range": "296", "text": "293"}, "Update the dependencies array to be: [fetchPermissions, fetchRoles, queryParams]", {"range": "297", "text": "298"}, "directive", "", "Update the dependencies array to be: [mockAuctionItem, mockBidRecords]", {"range": "299", "text": "300"}, "Update the dependencies array to be: [fetchAuctionItems, queryParams]", {"range": "301", "text": "302"}, "Update the dependencies array to be: [fetchAuctions, queryParams]", {"range": "303", "text": "304"}, "Update the dependencies array to be: [fetchBidRecords, queryParams]", {"range": "305", "text": "306"}, "Update the dependencies array to be: [fetchOrders, queryParams]", {"range": "307", "text": "308"}, "Update the dependencies array to be: [dateRange, fetchFinanceData, reportType]", {"range": "309", "text": "310"}, "Update the dependencies array to be: [checkAuth]", {"range": "311", "text": "312"}, [3874, 3887], "[fetchProducts, queryParams]", [2657, 2670], "[fetchUsers, queryParams]", [3177, 3190], [4781, 4794], "[fetchPermissions, fetchRoles, queryParams]", [3233, 3235], "[mockAuctionItem, mockBidRecords]", [4354, 4367], "[fetchAuctionItems, queryParams]", [3823, 3836], "[fetchAuctions, queryParams]", [3974, 3987], "[fetchBidRecords, queryParams]", [3944, 3957], "[fetchOrders, queryParams]", [3767, 3790], "[date<PERSON><PERSON><PERSON>, fetchFinanceData, reportType]", [3186, 3188], "[checkAuth]"]