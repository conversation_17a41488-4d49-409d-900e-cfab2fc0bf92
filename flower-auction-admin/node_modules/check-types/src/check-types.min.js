!function(M){"use strict";var o,r,n,i,c,e,u,f,t,a,l,s,b,p,y,m,R,h;function g(n){return null!=n}function d(n){return"number"==typeof n&&b<n&&n<p}function v(n){return"number"==typeof n&&n%1==0}function j(n,e){return d(n)&&e<n}function E(n,e){return d(n)&&n<e}function V(n,e){return d(n)&&e<=n}function Y(n,e){return d(n)&&n<=e}function O(n){return"string"==typeof n}function _(n){return O(n)&&""!==n}function w(n){return"[object Object]"===t.call(n)}function k(n,e){for(var t in n)if(f.call(n,t)&&e(t,n[t]))return!0;return!1}function S(n,e){try{return n instanceof e}catch(n){return!1}}function z(n,e){if(!g(n)||!g(e))return n===e;for(var t in e)if(f.call(e,t)){if(!f.call(n,t)||typeof n[t]!=typeof e[t])return!1;if(w(n[t])&&!z(n[t],e[t]))return!1}return!0}function I(n,e){if(!g(n)||!g(e))return n===e;for(var t in e)if(f.call(e,t)){if(!f.call(n,t))return!1;if(w(n[t])){if(!I(n[t],e[t]))return!1}else if(n[t]!==e[t])return!1}for(t in n)if(f.call(n,t)){if(!f.call(e,t))return!1;if(w(e[t])){if(!I(e[t],n[t]))return!1}else if(e[t]!==n[t])return!1}return!0}function N(n){return g(n)&&0<=n.length}function B(n){return y?g(n)&&A(n[Symbol.iterator]):N(n)}function G(n,t){var e,r;if(!g(n))return!1;if(R&&S(n,Set))return n.has(t);if(O(n))return-1!==n.indexOf(t);if(y&&n[Symbol.iterator]&&A(n.values)){e=n.values();do{if((r=e.next()).value===t)return!0}while(!r.done);return!1}return k(n,function(n,e){return e===t})}function K(n,e){return!!g(n)&&(m&&S(n,Map)?n.has(e):!(B(n)&&!d(+e)||!n[e]))}function A(n){return"function"==typeof n}function q(n,e){for(var t in n)f.call(n,t)&&e(t,n[t])}function C(n,e){for(var t=0;t<n.length;t+=1)if(n[t]===e)return e;return!e}function x(n,e){var t,r;for(t in n)if(f.call(n,t)){if(w(r=n[t])&&x(r,e)===e)return e;if(r===e)return e}return!e}function H(t,n){return q(n,function(n,e){t[n]=e}),t}function T(r,u){return function(){var e=arguments,n=r.l||r.length,t=e[n],n=e[n+1];return J(r.apply(null,e),_(t)?t:u.replace("{a}",F(e[0])).replace("{e}",F(e[1])).replace("{e2}",F(e[2])).replace("{t}",function(){var n=e[1];return n&&n.name?n.name:n}),A(n)?n:TypeError),e[0]}}function F(n){return function(){return O(n)?'"'+n.replace(/\\/g,"\\\\").replace(/"/g,'\\"')+'"':n&&!0!==n&&n.constructor&&!S(n,RegExp)&&"number"!=typeof n?n.constructor.name:n}}function J(n,e,t){if(n)return n;throw new(t||Error)(e||"assert failed")}function Q(n){function e(){return!n.apply(null,arguments)}return e.l=n.length,e}function U(n){return!n}function W(t,r,u){function n(){var e,n=arguments[0];if("maybe"!==t||!c.assigned(n)){if(!r(n))return!1;n=function(n,e){switch(n){case N:return l.call(e);case w:return a(e).map(function(n){return e[n]});default:return e}}(r,n),e=l.call(arguments,1);try{n.forEach(function(n){if(("maybe"!==t||g(n))&&!u.apply(null,[n].concat(e)))throw 0})}catch(n){return!1}}return!0}return n.l=u.length,n}function L(n,e){return P([n,r,e,""])}function P(r){var u=r.shift(),f=r.pop(),i=r.pop();return q(r.pop(),function(n,e){var t=o[n];t&&f&&(t=t.replace("to",f+"to")),Object.defineProperty(i,n,{configurable:!1,enumerable:!0,writable:!1,value:u.apply(null,r.concat(e,t))})}),i}function D(n,e,t){return P([n,e,{},t])}function X(e,t){u.forEach(function(n){e[n].of=D(t,r[n].of)})}o={},r={},[{n:"equal",f:function(n,e){return n===e},s:"equal {e}"},{n:"undefined",f:function(n){return void 0===n},s:"be undefined"},{n:"null",f:function(n){return null===n},s:"be null"},{n:"assigned",f:g,s:"be assigned"},{n:"primitive",f:function(n){switch(n){case null:case void 0:case!1:case!0:return!0}return"string"==(n=typeof n)||"number"==n||y&&"symbol"==n},s:"be primitive type"},{n:"contains",f:G,s:"contain {e}"},{n:"in",f:function(n,e){return G(e,n)},s:"be in {e}"},{n:"containsKey",f:K,s:"contain key {e}"},{n:"keyIn",f:function(n,e){return K(e,n)},s:"be key in {e}"},{n:"zero",f:function(n){return 0===n},s:"be 0"},{n:"one",f:function(n){return 1===n},s:"be 1"},{n:"infinity",f:function(n){return n===b||n===p},s:"be infinity"},{n:"number",f:d,s:"be Number"},{n:"integer",f:v,s:"be integer"},{n:"float",f:function(n){return d(n)&&n%1!=0},s:"be non-integer number"},{n:"even",f:function(n){return"number"==typeof n&&n%2==0},s:"be even number"},{n:"odd",f:function(n){return v(n)&&n%2!=0},s:"be odd number"},{n:"greater",f:j,s:"be greater than {e}"},{n:"less",f:E,s:"be less than {e}"},{n:"between",f:function(n,e,t){if(e<t)return j(n,e)&&n<t;return E(n,e)&&t<n},s:"be between {e} and {e2}"},{n:"greaterOrEqual",f:V,s:"be greater than or equal to {e}"},{n:"lessOrEqual",f:Y,s:"be less than or equal to {e}"},{n:"inRange",f:function(n,e,t){if(e<t)return V(n,e)&&n<=t;return Y(n,e)&&t<=n},s:"be in the range {e} to {e2}"},{n:"positive",f:function(n){return j(n,0)},s:"be positive number"},{n:"negative",f:function(n){return E(n,0)},s:"be negative number"},{n:"string",f:O,s:"be String"},{n:"emptyString",f:function(n){return""===n},s:"be empty string"},{n:"nonEmptyString",f:_,s:"be non-empty string"},{n:"match",f:function(n,e){return O(n)&&!!n.match(e)},s:"match {e}"},{n:"boolean",f:function(n){return!1===n||!0===n},s:"be Boolean"},{n:"object",f:w,s:"be Object"},{n:"emptyObject",f:function(n){return w(n)&&!k(n,function(){return 1})},s:"be empty object"},{n:"nonEmptyObject",f:function(n){return w(n)&&k(n,function(){return 1})},s:"be non-empty object"},{n:"instanceStrict",f:S,s:"be instanceof {t}"},{n:"thenable",f:function(n){return g(n)&&A(n.then)},s:"be promise-like"},{n:"instance",f:function(n,e){try{return S(n,e)||n.constructor.name===e.name||t.call(n)==="[object "+e.name+"]"}catch(n){return!1}},s:"be {t}"},{n:"like",f:z,s:"be like {e}"},{n:"identical",f:I,s:"be identical to {e}"},{n:"array",f:function(n){return s(n)},s:"be Array"},{n:"emptyArray",f:function(n){return s(n)&&0===n.length},s:"be empty array"},{n:"nonEmptyArray",f:function(n){return s(n)&&0<n.length},s:"be non-empty array"},{n:"arrayLike",f:N,s:"be array-like"},{n:"iterable",f:B,s:"be iterable"},{n:"date",f:function(n){return S(n,Date)&&v(n.getTime())},s:"be valid Date"},{n:"function",f:A,s:"be Function"},{n:"hasLength",f:function(n,e){return g(n)&&n.length===e},s:"have length {e}"},{n:"throws",f:function(n){if(A(n))try{n()}catch(n){return!0}return!1},s:"throw"}].map(function(n){var e=n.n;o[e]="assert failed: expected {a} to "+n.s,r[e]=n.f}),u=["array","arrayLike","iterable","object"],f=Object.prototype.hasOwnProperty,t=Object.prototype.toString,a=Object.keys,l=Array.prototype.slice,s=Array.isArray,b=Number.NEGATIVE_INFINITY,p=Number.POSITIVE_INFINITY,y="function"==typeof Symbol,m="function"==typeof Map,R="function"==typeof Set,n=H({map:function e(r,t){var u;u=s(r)?[]:{};{var f;A(t)?q(r,function(n,e){u[n]=t(e)}):(s(t)||i.object(t),f=a(r||{}),q(t,function(t,n){f.some(function(n,e){return n===t&&(f.splice(e,1),!0)}),A(n)?c.assigned(r)?u[t]=!!n.m:u[t]=n(r[t]):u[t]=e(r[t],n)}))}return u},all:function(n){if(s(n))return C(n,!1);return i.object(n),x(n,!1)},any:function(n){if(s(n))return C(n,!0);return i.object(n),x(n,!0)}},r),i=L(T,J),c=L(Q,U),e=L(function(n){function e(){return!!c.assigned(arguments[0])||n.apply(null,arguments)}return e.l=n.length,e.m=!0,e},function(n){return!g(n)||n}),i.not=D(T,c,"not "),i.maybe=D(T,e,"maybe "),u.forEach(function(n){r[n].of=P([W.bind(null,null),r[n],r,{},""])}),X(i,T),X(c,Q),u.forEach(function(n){e[n].of=P([W.bind(null,"maybe"),r[n],r,{},""]),i.maybe[n].of=D(T,e[n].of),i.not[n].of=D(T,c[n].of)}),h=H(n,{assert:i,not:c,maybe:e}),"function"==typeof define&&define.amd?define(function(){return h}):"undefined"!=typeof module&&null!==module&&module.exports?module.exports=h:M.check=h}(this);