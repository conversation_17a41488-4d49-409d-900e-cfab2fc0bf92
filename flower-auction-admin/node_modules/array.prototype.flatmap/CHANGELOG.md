# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v1.3.3](https://github.com/es-shims/Array.prototype.flatMap/compare/v1.3.2...v1.3.3) - 2024-12-15

### Commits

- [actions] split out node 10-20, and 20+ [`8727281`](https://github.com/es-shims/Array.prototype.flatMap/commit/87272812ed6ade6935c7429ee1daf1cfa54115ef)
- [Tests] add test coverage [`6e78327`](https://github.com/es-shims/Array.prototype.flatMap/commit/6e7832790c36b320880f4b6d38dc31388d1fae3a)
- [Deps] update `call-bind`, `es-abstract` [`e027dd1`](https://github.com/es-shims/Array.prototype.flatMap/commit/e027dd10ee63b3c3a98cc7e6ce8ad42b1100bdc9)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `auto-changelog`, `object-inspect`, `tape` [`7322d84`](https://github.com/es-shims/Array.prototype.flatMap/commit/7322d841e87ab17e4b929e92e73e8a20c3daf544)
- [Dev Deps] update `aud`, `npmignore`, `object-inspect`, `tape` [`958bf5c`](https://github.com/es-shims/Array.prototype.flatMap/commit/958bf5c982cb0c4941dc2a6e873883e9f2c8d535)
- [Deps] update `call-bind`, `define-properties`, `es-abstract`, `es-shim-unscopables` [`b3698fb`](https://github.com/es-shims/Array.prototype.flatMap/commit/b3698fb2320992f5ab85a486d1adef0105f739aa)
- [Tests] replace `aud` with `npm audit` [`e0461ed`](https://github.com/es-shims/Array.prototype.flatMap/commit/e0461ed2288d37e2d878e3e64569313c04af6a64)
- [Dev Deps] add missing peer dep [`e7160b5`](https://github.com/es-shims/Array.prototype.flatMap/commit/e7160b5ef9d624300cc189696e8019adaf6d67fc)

## [v1.3.2](https://github.com/es-shims/Array.prototype.flatMap/compare/v1.3.1...v1.3.2) - 2023-09-06

### Commits

- [Deps] update `define-properties`, `es-abstract` [`1737863`](https://github.com/es-shims/Array.prototype.flatMap/commit/17378634d56fc4b75027764b54804d89c6f8d60f)
- [Dev Deps] update `@es-shims/api`, `@ljharb/eslint-config`, `aud`, `object-inspect`, `tape` [`2337759`](https://github.com/es-shims/Array.prototype.flatMap/commit/23377597983440102805d36f701408505b433ccd)

## [v1.3.1](https://github.com/es-shims/Array.prototype.flatMap/compare/v1.3.0...v1.3.1) - 2022-11-02

### Commits

- [meta] use `npmignore` to autogenerate an npmignore file [`3587a34`](https://github.com/es-shims/Array.prototype.flatMap/commit/3587a34ca111ec36ffc46b4131f5b32d4d8a357c)
- [meta] add `auto-changelog` [`d66bdea`](https://github.com/es-shims/Array.prototype.flatMap/commit/d66bdeac56f2c1803a72695230c80d8270ab2ecf)
- [Deps] update `define-properties`, `es-abstract` [`d64c486`](https://github.com/es-shims/Array.prototype.flatMap/commit/d64c48639ec4958ed9a2627a4d7315ac1404687a)
- [actions] update rebase action to use reusable workflow [`8d657d0`](https://github.com/es-shims/Array.prototype.flatMap/commit/8d657d094a2aafa7948eee73eaa0e56047c5d60d)
- [Dev Deps] update `aud`, `object-inspect`, `tape` [`aa22741`](https://github.com/es-shims/Array.prototype.flatMap/commit/aa22741a4bbe8db6d448cc4ca5417ddec90ac01d)
- [Tests] use `for-each` instead of `foreach` [`748a78d`](https://github.com/es-shims/Array.prototype.flatMap/commit/748a78dbddb08462c75916fde07746d34cfd5c5c)

<!-- auto-changelog-above -->

1.3.0 / 2022-04-11
=================
  * [New] `shim`/`auto`: add `flatMap` to `Symbol.unscopables`
  * [Deps] update `call-bind`, `es-abstract`
  * [actions] reuse common workflows
  * [actions] update codecov uploader
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `auto-changelog`, `object-inspect`, `safe-publish-latest`, `tape`

1.2.5 / 2021-10-01
=================
  * [readme] add github actions/codecov badges; update description; remove travis badge
  * [Deps] update `call-bind`, `es-abstract`; remove unused `function-bind`
  * [meta] use `prepublishOnly`, for npm 7+
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `@es-shims/api`, `aud`, `has-strict-mode`, `object-inspect`, `tape`
  * [actions] update workflows
  * [actions] use `node/install` instead of `node/run`; use `codecov` action
  * [Tests] increase coverage

1.2.4 / 2020-11-18
=================
  * [Deps] update `es-abstract`; use `call-bind` where applicable
  * [meta] do not publish github action workflows
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `object-inspect`, `tape`; add `aud`
  * [Tests] migrate tests to Github Actions
  * [Tests] run `nyc` on all tests
  * [Tests] add `implementation` test; run `es-shim-api` in postlint; use `tape` runner
  * [actions] add "Allow Edits" workflow
  * [actions] switch Automatic Rebase workflow to `pull_request_target` event

1.2.3 / 2019-12-12
=================
  * [Refactor] use split-up `es-abstract` (65% bundle size decrease)
  * [Deps] update `es-abstract`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `object-inspect`
  * [meta] add `funding` field
  * [Tests] use shared travis-ci configs
  * [actions] add automatic rebasing / merge commit blocking

1.2.2 / 2019-10-10
=================
  * [Refactor] rename callback argument to `mapperFunction`, to match spec
  * [Deps] update `es-abstract`, `define-properties`
  * [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `covert`, `evalmd`, `object-inspect`, `safe-publish-latest`, `tape`
  * [meta] create FUNDING.yml
  * [Tests] up to `node` `v12.11`, `v11.15`, `v10.16`, `v9.11`, `v8.16`, `v6.17`, `v4.9`; use `nvm install-latest-npm`
  * [Tests] use `npx aud` instead of `nsp` or `npm audit` with hoops

1.2.1 / 2018-02-23
=================
  * [Fix] Temporarily hack main entry, so it's compatible with other resolvers
  * [Dev Deps] update `eslint`, `nsp`, `tape`
  * [Tests] up to `node` `v9.6`, `v6.13`

1.2.0 / 2018-01-18
=================
  * [New] add "auto" entry point
  * [Fix] Move the receiver length check higher
  * [Fix] spec adjustments
  * [Refactor] adjust shouldFlatten logic
  * [Dev Deps] update `eslint`, `object-inspect`
  * [Tests] up to `node` `v9.4`

1.1.1 / 2017-11-29
=================
  * [Fix] avoid an extra hole in the array (#1)
  * [Deps] update `es-abstract`
  * [Dev Deps] update `eslint`, `nsp`, `object-inspect`
  * [Tests] up to `node` `v9.2`, `v8.9`, `v6.12`; pin included builds to LTS

1.1.0 / 2017-10-03
=================
  * [New] add explicit setting of “length” on target array
  * [Fix] `FlattenIntoArray`: add assertion that `thisArg` and `mapperFunction` are both passed together
  * [Tests] make coverage required

1.0.1 / 2017-10-02
=================
  * Add readme

1.0.0 / 2017-10-01
=================
  * Initial release
