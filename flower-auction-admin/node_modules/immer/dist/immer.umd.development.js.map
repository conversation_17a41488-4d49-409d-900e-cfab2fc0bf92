{"version": 3, "file": "immer.umd.development.js", "sources": ["../src/utils/env.ts", "../src/utils/errors.ts", "../src/utils/common.ts", "../src/utils/plugins.ts", "../src/core/scope.ts", "../src/core/finalize.ts", "../src/core/proxy.ts", "../src/core/immerClass.ts", "../src/core/current.ts", "../src/plugins/es5.ts", "../src/plugins/patches.ts", "../src/plugins/mapset.ts", "../src/plugins/all.ts", "../src/immer.ts"], "sourcesContent": ["// Should be no imports here!\n\n// Some things that should be evaluated before all else...\n\n// We only want to know if non-polyfilled symbols are available\nconst hasSymbol =\n\ttypeof Symbol !== \"undefined\" && typeof Symbol(\"x\") === \"symbol\"\nexport const hasMap = typeof Map !== \"undefined\"\nexport const hasSet = typeof Set !== \"undefined\"\nexport const hasProxies =\n\ttypeof Proxy !== \"undefined\" &&\n\ttypeof Proxy.revocable !== \"undefined\" &&\n\ttypeof Reflect !== \"undefined\"\n\n/**\n * The sentinel value returned by producers to replace the draft with undefined.\n */\nexport const NOTHING: Nothing = hasSymbol\n\t? Symbol.for(\"immer-nothing\")\n\t: ({[\"immer-nothing\"]: true} as any)\n\n/**\n * To let Immer treat your class instances as plain immutable objects\n * (albeit with a custom prototype), you must define either an instance property\n * or a static property on each of your custom classes.\n *\n * Otherwise, your class instance will never be drafted, which means it won't be\n * safe to mutate in a produce callback.\n */\nexport const DRAFTABLE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-draftable\")\n\t: (\"__$immer_draftable\" as any)\n\nexport const DRAFT_STATE: unique symbol = hasSymbol\n\t? Symbol.for(\"immer-state\")\n\t: (\"__$immer_state\" as any)\n\n// Even a polyfilled Symbol might provide Symbol.iterator\nexport const iteratorSymbol: typeof Symbol.iterator =\n\t(typeof Symbol != \"undefined\" && Symbol.iterator) || (\"@@iterator\" as any)\n\n/** Use a class type for `nothing` so its type is unique */\nexport class Nothing {\n\t// This lets us do `Exclude<T, Nothing>`\n\t// @ts-ignore\n\tprivate _!: unique symbol\n}\n", "const errors = {\n\t0: \"Illegal state\",\n\t1: \"Immer drafts cannot have computed properties\",\n\t2: \"This object has been frozen and should not be mutated\",\n\t3(data: any) {\n\t\treturn (\n\t\t\t\"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? \" +\n\t\t\tdata\n\t\t)\n\t},\n\t4: \"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.\",\n\t5: \"Immer forbids circular references\",\n\t6: \"The first or second argument to `produce` must be a function\",\n\t7: \"The third argument to `produce` must be a function or undefined\",\n\t8: \"First argument to `createDraft` must be a plain object, an array, or an immerable object\",\n\t9: \"First argument to `finishDraft` must be a draft returned by `createDraft`\",\n\t10: \"The given draft is already finalized\",\n\t11: \"Object.defineProperty() cannot be used on an Immer draft\",\n\t12: \"Object.setPrototypeOf() cannot be used on an Immer draft\",\n\t13: \"Immer only supports deleting array indices\",\n\t14: \"Immer only supports setting array indices and the 'length' property\",\n\t15(path: string) {\n\t\treturn \"Cannot apply patch, path doesn't resolve: \" + path\n\t},\n\t16: 'Sets cannot have \"replace\" patches.',\n\t17(op: string) {\n\t\treturn \"Unsupported patch operation: \" + op\n\t},\n\t18(plugin: string) {\n\t\treturn `The plugin for '${plugin}' has not been loaded into Immer. To enable the plugin, import and call \\`enable${plugin}()\\` when initializing your application.`\n\t},\n\t20: \"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available\",\n\t21(thing: string) {\n\t\treturn `produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '${thing}'`\n\t},\n\t22(thing: string) {\n\t\treturn `'current' expects a draft, got: ${thing}`\n\t},\n\t23(thing: string) {\n\t\treturn `'original' expects a draft, got: ${thing}`\n\t},\n\t24: \"Patching reserved attributes like __proto__, prototype and constructor is not allowed\"\n} as const\n\nexport function die(error: keyof typeof errors, ...args: any[]): never {\n\tif (__DEV__) {\n\t\tconst e = errors[error]\n\t\tconst msg = !e\n\t\t\t? \"unknown error nr: \" + error\n\t\t\t: typeof e === \"function\"\n\t\t\t? e.apply(null, args as any)\n\t\t\t: e\n\t\tthrow new Error(`[Immer] ${msg}`)\n\t}\n\tthrow new Error(\n\t\t`[Immer] minified error nr: ${error}${\n\t\t\targs.length ? \" \" + args.map(s => `'${s}'`).join(\",\") : \"\"\n\t\t}. Find the full error at: https://bit.ly/3cXEKWf`\n\t)\n}\n", "import {\n\tDRAFT_STATE,\n\tDRAF<PERSON><PERSON><PERSON>,\n\thasSet,\n\tObjectish,\n\tDrafted,\n\tAnyObject,\n\tAnyMap,\n\tAnySet,\n\tImmerState,\n\thasMap,\n\tArchtype,\n\tdie\n} from \"../internal\"\n\n/** Returns true if the given value is an Immer draft */\n/*#__PURE__*/\nexport function isDraft(value: any): boolean {\n\treturn !!value && !!value[DRAFT_STATE]\n}\n\n/** Returns true if the given value can be drafted by Immer */\n/*#__PURE__*/\nexport function isDraftable(value: any): boolean {\n\tif (!value) return false\n\treturn (\n\t\tisPlainObject(value) ||\n\t\tArray.isArray(value) ||\n\t\t!!value[DRAFTABLE] ||\n\t\t!!value.constructor?.[DRAFTABLE] ||\n\t\tisMap(value) ||\n\t\tisSet(value)\n\t)\n}\n\nconst objectCtorString = Object.prototype.constructor.toString()\n/*#__PURE__*/\nexport function isPlainObject(value: any): boolean {\n\tif (!value || typeof value !== \"object\") return false\n\tconst proto = Object.getPrototypeOf(value)\n\tif (proto === null) {\n\t\treturn true\n\t}\n\tconst Ctor =\n\t\tObject.hasOwnProperty.call(proto, \"constructor\") && proto.constructor\n\n\tif (Ctor === Object) return true\n\n\treturn (\n\t\ttypeof Ctor == \"function\" &&\n\t\tFunction.toString.call(Ctor) === objectCtorString\n\t)\n}\n\n/** Get the underlying object that is represented by the given draft */\n/*#__PURE__*/\nexport function original<T>(value: T): T | undefined\nexport function original(value: Drafted<any>): any {\n\tif (!isDraft(value)) die(23, value)\n\treturn value[DRAFT_STATE].base_\n}\n\n/*#__PURE__*/\nexport const ownKeys: (target: AnyObject) => PropertyKey[] =\n\ttypeof Reflect !== \"undefined\" && Reflect.ownKeys\n\t\t? Reflect.ownKeys\n\t\t: typeof Object.getOwnPropertySymbols !== \"undefined\"\n\t\t? obj =>\n\t\t\t\tObject.getOwnPropertyNames(obj).concat(\n\t\t\t\t\tObject.getOwnPropertySymbols(obj) as any\n\t\t\t\t)\n\t\t: /* istanbul ignore next */ Object.getOwnPropertyNames\n\nexport const getOwnPropertyDescriptors =\n\tObject.getOwnPropertyDescriptors ||\n\tfunction getOwnPropertyDescriptors(target: any) {\n\t\t// Polyfill needed for Hermes and IE, see https://github.com/facebook/hermes/issues/274\n\t\tconst res: any = {}\n\t\townKeys(target).forEach(key => {\n\t\t\tres[key] = Object.getOwnPropertyDescriptor(target, key)\n\t\t})\n\t\treturn res\n\t}\n\nexport function each<T extends Objectish>(\n\tobj: T,\n\titer: (key: string | number, value: any, source: T) => void,\n\tenumerableOnly?: boolean\n): void\nexport function each(obj: any, iter: any, enumerableOnly = false) {\n\tif (getArchtype(obj) === Archtype.Object) {\n\t\t;(enumerableOnly ? Object.keys : ownKeys)(obj).forEach(key => {\n\t\t\tif (!enumerableOnly || typeof key !== \"symbol\") iter(key, obj[key], obj)\n\t\t})\n\t} else {\n\t\tobj.forEach((entry: any, index: any) => iter(index, entry, obj))\n\t}\n}\n\n/*#__PURE__*/\nexport function getArchtype(thing: any): Archtype {\n\t/* istanbul ignore next */\n\tconst state: undefined | ImmerState = thing[DRAFT_STATE]\n\treturn state\n\t\t? state.type_ > 3\n\t\t\t? state.type_ - 4 // cause Object and Array map back from 4 and 5\n\t\t\t: (state.type_ as any) // others are the same\n\t\t: Array.isArray(thing)\n\t\t? Archtype.Array\n\t\t: isMap(thing)\n\t\t? Archtype.Map\n\t\t: isSet(thing)\n\t\t? Archtype.Set\n\t\t: Archtype.Object\n}\n\n/*#__PURE__*/\nexport function has(thing: any, prop: PropertyKey): boolean {\n\treturn getArchtype(thing) === Archtype.Map\n\t\t? thing.has(prop)\n\t\t: Object.prototype.hasOwnProperty.call(thing, prop)\n}\n\n/*#__PURE__*/\nexport function get(thing: AnyMap | AnyObject, prop: PropertyKey): any {\n\t// @ts-ignore\n\treturn getArchtype(thing) === Archtype.Map ? thing.get(prop) : thing[prop]\n}\n\n/*#__PURE__*/\nexport function set(thing: any, propOrOldValue: PropertyKey, value: any) {\n\tconst t = getArchtype(thing)\n\tif (t === Archtype.Map) thing.set(propOrOldValue, value)\n\telse if (t === Archtype.Set) {\n\t\tthing.add(value)\n\t} else thing[propOrOldValue] = value\n}\n\n/*#__PURE__*/\nexport function is(x: any, y: any): boolean {\n\t// From: https://github.com/facebook/fbjs/blob/c69904a511b900266935168223063dd8772dfc40/packages/fbjs/src/core/shallowEqual.js\n\tif (x === y) {\n\t\treturn x !== 0 || 1 / x === 1 / y\n\t} else {\n\t\treturn x !== x && y !== y\n\t}\n}\n\n/*#__PURE__*/\nexport function isMap(target: any): target is AnyMap {\n\treturn hasMap && target instanceof Map\n}\n\n/*#__PURE__*/\nexport function isSet(target: any): target is AnySet {\n\treturn hasSet && target instanceof Set\n}\n/*#__PURE__*/\nexport function latest(state: ImmerState): any {\n\treturn state.copy_ || state.base_\n}\n\n/*#__PURE__*/\nexport function shallowCopy(base: any) {\n\tif (Array.isArray(base)) return Array.prototype.slice.call(base)\n\tconst descriptors = getOwnPropertyDescriptors(base)\n\tdelete descriptors[DRAFT_STATE as any]\n\tlet keys = ownKeys(descriptors)\n\tfor (let i = 0; i < keys.length; i++) {\n\t\tconst key: any = keys[i]\n\t\tconst desc = descriptors[key]\n\t\tif (desc.writable === false) {\n\t\t\tdesc.writable = true\n\t\t\tdesc.configurable = true\n\t\t}\n\t\t// like object.assign, we will read any _own_, get/set accessors. This helps in dealing\n\t\t// with libraries that trap values, like mobx or vue\n\t\t// unlike object.assign, non-enumerables will be copied as well\n\t\tif (desc.get || desc.set)\n\t\t\tdescriptors[key] = {\n\t\t\t\tconfigurable: true,\n\t\t\t\twritable: true, // could live with !!desc.set as well here...\n\t\t\t\tenumerable: desc.enumerable,\n\t\t\t\tvalue: base[key]\n\t\t\t}\n\t}\n\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n}\n\n/**\n * Freezes draftable objects. Returns the original object.\n * By default freezes shallowly, but if the second argument is `true` it will freeze recursively.\n *\n * @param obj\n * @param deep\n */\nexport function freeze<T>(obj: T, deep?: boolean): T\nexport function freeze<T>(obj: any, deep: boolean = false): T {\n\tif (isFrozen(obj) || isDraft(obj) || !isDraftable(obj)) return obj\n\tif (getArchtype(obj) > 1 /* Map or Set */) {\n\t\tobj.set = obj.add = obj.clear = obj.delete = dontMutateFrozenCollections as any\n\t}\n\tObject.freeze(obj)\n\tif (deep) each(obj, (key, value) => freeze(value, true), true)\n\treturn obj\n}\n\nfunction dontMutateFrozenCollections() {\n\tdie(2)\n}\n\nexport function isFrozen(obj: any): boolean {\n\tif (obj == null || typeof obj !== \"object\") return true\n\t// See #600, IE dies on non-objects in Object.isFrozen\n\treturn Object.isFrozen(obj)\n}\n", "import {\n\tImmerState,\n\t<PERSON>,\n\t<PERSON>mmer<PERSON>cope,\n\tDrafted,\n\tAnyObject,\n\tImmerBaseState,\n\tAnyMap,\n\tAnySet,\n\tProxyType,\n\tdie\n} from \"../internal\"\n\n/** Plugin utilities */\nconst plugins: {\n\tPatches?: {\n\t\tgeneratePatches_(\n\t\t\tstate: ImmerState,\n\t\t\tbasePath: PatchPath,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tgenerateReplacementPatches_(\n\t\t\tbase: any,\n\t\t\treplacement: any,\n\t\t\tpatches: Patch[],\n\t\t\tinversePatches: Patch[]\n\t\t): void\n\t\tapplyPatches_<T>(draft: T, patches: Patch[]): T\n\t}\n\tES5?: {\n\t\twillFinalizeES5_(scope: ImmerScope, result: any, isReplaced: boolean): void\n\t\tcreateES5Proxy_<T>(\n\t\t\tbase: T,\n\t\t\tparent?: ImmerState\n\t\t): Drafted<T, ES5ObjectState | ES5ArrayState>\n\t\thasChanges_(state: ES5ArrayState | ES5ObjectState): boolean\n\t}\n\tMapSet?: {\n\t\tproxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T\n\t\tproxySet_<T extends AnySet>(target: T, parent?: ImmerState): T\n\t}\n} = {}\n\ntype Plugins = typeof plugins\n\nexport function getPlugin<K extends keyof Plugins>(\n\tpluginKey: K\n): Exclude<Plugins[K], undefined> {\n\tconst plugin = plugins[pluginKey]\n\tif (!plugin) {\n\t\tdie(18, pluginKey)\n\t}\n\t// @ts-ignore\n\treturn plugin\n}\n\nexport function loadPlugin<K extends keyof Plugins>(\n\tpluginKey: K,\n\timplementation: Plugins[K]\n): void {\n\tif (!plugins[pluginKey]) plugins[pluginKey] = implementation\n}\n\n/** ES5 Plugin */\n\ninterface ES5BaseState extends ImmerBaseState {\n\tassigned_: {[key: string]: any}\n\tparent_?: ImmerState\n\trevoked_: boolean\n}\n\nexport interface ES5ObjectState extends ES5BaseState {\n\ttype_: ProxyType.ES5Object\n\tdraft_: Drafted<AnyObject, ES5ObjectState>\n\tbase_: AnyObject\n\tcopy_: AnyObject | null\n}\n\nexport interface ES5ArrayState extends ES5BaseState {\n\ttype_: ProxyType.ES5Array\n\tdraft_: Drafted<AnyObject, ES5ArrayState>\n\tbase_: any\n\tcopy_: any\n}\n\n/** Map / Set plugin */\n\nexport interface MapState extends ImmerBaseState {\n\ttype_: ProxyType.Map\n\tcopy_: AnyMap | undefined\n\tassigned_: Map<any, boolean> | undefined\n\tbase_: AnyMap\n\trevoked_: boolean\n\tdraft_: Drafted<AnyMap, MapState>\n}\n\nexport interface SetState extends ImmerBaseState {\n\ttype_: ProxyType.Set\n\tcopy_: AnySet | undefined\n\tbase_: AnySet\n\tdrafts_: Map<any, Drafted> // maps the original value to the draft value in the new set\n\trevoked_: boolean\n\tdraft_: Drafted<AnySet, SetState>\n}\n\n/** Patches plugin */\n\nexport type PatchPath = (string | number)[]\n", "import {\n\t<PERSON>,\n\tPatchListener,\n\tDrafted,\n\tImmer,\n\tDRAFT_STATE,\n\tImmerState,\n\tProxyType,\n\tgetPlugin\n} from \"../internal\"\nimport {die} from \"../utils/errors\"\n\n/** Each scope represents a `produce` call. */\n\nexport interface ImmerScope {\n\tpatches_?: Patch[]\n\tinversePatches_?: Patch[]\n\tcanAutoFreeze_: boolean\n\tdrafts_: any[]\n\tparent_?: ImmerScope\n\tpatchListener_?: PatchListener\n\timmer_: Immer\n\tunfinalizedDrafts_: number\n}\n\nlet currentScope: ImmerScope | undefined\n\nexport function getCurrentScope() {\n\tif (__DEV__ && !currentScope) die(0)\n\treturn currentScope!\n}\n\nfunction createScope(\n\tparent_: ImmerScope | undefined,\n\timmer_: Immer\n): ImmerScope {\n\treturn {\n\t\tdrafts_: [],\n\t\tparent_,\n\t\timmer_,\n\t\t// Whenever the modified draft contains a draft from another scope, we\n\t\t// need to prevent auto-freezing so the unowned draft can be finalized.\n\t\tcanAutoFreeze_: true,\n\t\tunfinalizedDrafts_: 0\n\t}\n}\n\nexport function usePatchesInScope(\n\tscope: ImmerScope,\n\tpatchListener?: PatchListener\n) {\n\tif (patchListener) {\n\t\tgetPlugin(\"Patches\") // assert we have the plugin\n\t\tscope.patches_ = []\n\t\tscope.inversePatches_ = []\n\t\tscope.patchListener_ = patchListener\n\t}\n}\n\nexport function revokeScope(scope: ImmerScope) {\n\tleaveScope(scope)\n\tscope.drafts_.forEach(revokeDraft)\n\t// @ts-ignore\n\tscope.drafts_ = null\n}\n\nexport function leaveScope(scope: ImmerScope) {\n\tif (scope === currentScope) {\n\t\tcurrentScope = scope.parent_\n\t}\n}\n\nexport function enterScope(immer: Immer) {\n\treturn (currentScope = createScope(currentScope, immer))\n}\n\nfunction revokeDraft(draft: Drafted) {\n\tconst state: ImmerState = draft[DRAFT_STATE]\n\tif (\n\t\tstate.type_ === ProxyType.ProxyObject ||\n\t\tstate.type_ === ProxyType.ProxyArray\n\t)\n\t\tstate.revoke_()\n\telse state.revoked_ = true\n}\n", "import {\n\tImmerScope,\n\tDRAFT_STATE,\n\tisDraftable,\n\tNOTHING,\n\tPatchPath,\n\teach,\n\thas,\n\tfreeze,\n\tImmerState,\n\tisDraft,\n\tSetState,\n\tset,\n\tProxyType,\n\tgetPlugin,\n\tdie,\n\trevokeScope,\n\tisFrozen,\n\tshallowCopy\n} from \"../internal\"\n\nexport function processResult(result: any, scope: ImmerScope) {\n\tscope.unfinalizedDrafts_ = scope.drafts_.length\n\tconst baseDraft = scope.drafts_![0]\n\tconst isReplaced = result !== undefined && result !== baseDraft\n\tif (!scope.immer_.useProxies_)\n\t\tgetPlugin(\"ES5\").willFinalizeES5_(scope, result, isReplaced)\n\tif (isReplaced) {\n\t\tif (baseDraft[DRAFT_STATE].modified_) {\n\t\t\trevokeScope(scope)\n\t\t\tdie(4)\n\t\t}\n\t\tif (isDraftable(result)) {\n\t\t\t// Finalize the result in case it contains (or is) a subset of the draft.\n\t\t\tresult = finalize(scope, result)\n\t\t\tif (!scope.parent_) maybeFreeze(scope, result)\n\t\t}\n\t\tif (scope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(\n\t\t\t\tbaseDraft[DRAFT_STATE].base_,\n\t\t\t\tresult,\n\t\t\t\tscope.patches_,\n\t\t\t\tscope.inversePatches_!\n\t\t\t)\n\t\t}\n\t} else {\n\t\t// Finalize the base draft.\n\t\tresult = finalize(scope, baseDraft, [])\n\t}\n\trevokeScope(scope)\n\tif (scope.patches_) {\n\t\tscope.patchListener_!(scope.patches_, scope.inversePatches_!)\n\t}\n\treturn result !== NOTHING ? result : undefined\n}\n\nfunction finalize(rootScope: ImmerScope, value: any, path?: PatchPath) {\n\t// Don't recurse in tho recursive data structures\n\tif (isFrozen(value)) return value\n\n\tconst state: ImmerState = value[DRAFT_STATE]\n\t// A plain object, might need freezing, might contain drafts\n\tif (!state) {\n\t\teach(\n\t\t\tvalue,\n\t\t\t(key, childValue) =>\n\t\t\t\tfinalizeProperty(rootScope, state, value, key, childValue, path),\n\t\t\ttrue // See #590, don't recurse into non-enumerable of non drafted objects\n\t\t)\n\t\treturn value\n\t}\n\t// Never finalize drafts owned by another scope.\n\tif (state.scope_ !== rootScope) return value\n\t// Unmodified draft, return the (frozen) original\n\tif (!state.modified_) {\n\t\tmaybeFreeze(rootScope, state.base_, true)\n\t\treturn state.base_\n\t}\n\t// Not finalized yet, let's do that now\n\tif (!state.finalized_) {\n\t\tstate.finalized_ = true\n\t\tstate.scope_.unfinalizedDrafts_--\n\t\tconst result =\n\t\t\t// For ES5, create a good copy from the draft first, with added keys and without deleted keys.\n\t\t\tstate.type_ === ProxyType.ES5Object || state.type_ === ProxyType.ES5Array\n\t\t\t\t? (state.copy_ = shallowCopy(state.draft_))\n\t\t\t\t: state.copy_\n\t\t// Finalize all children of the copy\n\t\t// For sets we clone before iterating, otherwise we can get in endless loop due to modifying during iteration, see #628\n\t\t// To preserve insertion order in all cases we then clear the set\n\t\t// And we let finalizeProperty know it needs to re-add non-draft children back to the target\n\t\tlet resultEach = result\n\t\tlet isSet = false\n\t\tif (state.type_ === ProxyType.Set) {\n\t\t\tresultEach = new Set(result)\n\t\t\tresult.clear()\n\t\t\tisSet = true\n\t\t}\n\t\teach(resultEach, (key, childValue) =>\n\t\t\tfinalizeProperty(rootScope, state, result, key, childValue, path, isSet)\n\t\t)\n\t\t// everything inside is frozen, we can freeze here\n\t\tmaybeFreeze(rootScope, result, false)\n\t\t// first time finalizing, let's create those patches\n\t\tif (path && rootScope.patches_) {\n\t\t\tgetPlugin(\"Patches\").generatePatches_(\n\t\t\t\tstate,\n\t\t\t\tpath,\n\t\t\t\trootScope.patches_,\n\t\t\t\trootScope.inversePatches_!\n\t\t\t)\n\t\t}\n\t}\n\treturn state.copy_\n}\n\nfunction finalizeProperty(\n\trootScope: ImmerScope,\n\tparentState: undefined | ImmerState,\n\ttargetObject: any,\n\tprop: string | number,\n\tchildValue: any,\n\trootPath?: PatchPath,\n\ttargetIsSet?: boolean\n) {\n\tif (__DEV__ && childValue === targetObject) die(5)\n\tif (isDraft(childValue)) {\n\t\tconst path =\n\t\t\trootPath &&\n\t\t\tparentState &&\n\t\t\tparentState!.type_ !== ProxyType.Set && // Set objects are atomic since they have no keys.\n\t\t\t!has((parentState as Exclude<ImmerState, SetState>).assigned_!, prop) // Skip deep patches for assigned keys.\n\t\t\t\t? rootPath!.concat(prop)\n\t\t\t\t: undefined\n\t\t// Drafts owned by `scope` are finalized here.\n\t\tconst res = finalize(rootScope, childValue, path)\n\t\tset(targetObject, prop, res)\n\t\t// Drafts from another scope must prevented to be frozen\n\t\t// if we got a draft back from finalize, we're in a nested produce and shouldn't freeze\n\t\tif (isDraft(res)) {\n\t\t\trootScope.canAutoFreeze_ = false\n\t\t} else return\n\t} else if (targetIsSet) {\n\t\ttargetObject.add(childValue)\n\t}\n\t// Search new objects for unfinalized drafts. Frozen objects should never contain drafts.\n\tif (isDraftable(childValue) && !isFrozen(childValue)) {\n\t\tif (!rootScope.immer_.autoFreeze_ && rootScope.unfinalizedDrafts_ < 1) {\n\t\t\t// optimization: if an object is not a draft, and we don't have to\n\t\t\t// deepfreeze everything, and we are sure that no drafts are left in the remaining object\n\t\t\t// cause we saw and finalized all drafts already; we can stop visiting the rest of the tree.\n\t\t\t// This benefits especially adding large data tree's without further processing.\n\t\t\t// See add-data.js perf test\n\t\t\treturn\n\t\t}\n\t\tfinalize(rootScope, childValue)\n\t\t// immer deep freezes plain objects, so if there is no parent state, we freeze as well\n\t\tif (!parentState || !parentState.scope_.parent_)\n\t\t\tmaybeFreeze(rootScope, childValue)\n\t}\n}\n\nfunction maybeFreeze(scope: ImmerScope, value: any, deep = false) {\n\t// we never freeze for a non-root scope; as it would prevent pruning for drafts inside wrapping objects\n\tif (!scope.parent_ && scope.immer_.autoFreeze_ && scope.canAutoFreeze_) {\n\t\tfreeze(value, deep)\n\t}\n}\n", "import {\n\teach,\n\thas,\n\tis,\n\tisDraftable,\n\tshallowCopy,\n\tlatest,\n\tImmerBaseState,\n\tImmerState,\n\tDrafted,\n\tAnyObject,\n\tAnyArray,\n\tObjectish,\n\tgetCurrentScope,\n\tDRAFT_STATE,\n\tdie,\n\tcreateProxy,\n\tProxyType\n} from \"../internal\"\n\ninterface ProxyBaseState extends ImmerBaseState {\n\tassigned_: {\n\t\t[property: string]: boolean\n\t}\n\tparent_?: ImmerState\n\trevoke_(): void\n}\n\nexport interface ProxyObjectState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyObject\n\tbase_: any\n\tcopy_: any\n\tdraft_: Drafted<AnyObject, ProxyObjectState>\n}\n\nexport interface ProxyArrayState extends ProxyBaseState {\n\ttype_: ProxyType.ProxyArray\n\tbase_: AnyArray\n\tcopy_: AnyArray | null\n\tdraft_: Drafted<AnyArray, ProxyArrayState>\n}\n\ntype ProxyState = ProxyObjectState | ProxyArrayState\n\n/**\n * Returns a new draft of the `base` object.\n *\n * The second argument is the parent draft-state (used internally).\n */\nexport function createProxyProxy<T extends Objectish>(\n\tbase: T,\n\tparent?: ImmerState\n): Drafted<T, ProxyState> {\n\tconst isArray = Array.isArray(base)\n\tconst state: ProxyState = {\n\t\ttype_: isArray ? ProxyType.ProxyArray : (ProxyType.ProxyObject as any),\n\t\t// Track which produce call this is associated with.\n\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t// True for both shallow and deep changes.\n\t\tmodified_: false,\n\t\t// Used during finalization.\n\t\tfinalized_: false,\n\t\t// Track which properties have been assigned (true) or deleted (false).\n\t\tassigned_: {},\n\t\t// The parent draft state.\n\t\tparent_: parent,\n\t\t// The base state.\n\t\tbase_: base,\n\t\t// The base proxy.\n\t\tdraft_: null as any, // set below\n\t\t// The base copy with any updated values.\n\t\tcopy_: null,\n\t\t// Called by the `produce` function.\n\t\trevoke_: null as any,\n\t\tisManual_: false\n\t}\n\n\t// the traps must target something, a bit like the 'real' base.\n\t// but also, we need to be able to determine from the target what the relevant state is\n\t// (to avoid creating traps per instance to capture the state in closure,\n\t// and to avoid creating weird hidden properties as well)\n\t// So the trick is to use 'state' as the actual 'target'! (and make sure we intercept everything)\n\t// Note that in the case of an array, we put the state in an array to have better Reflect defaults ootb\n\tlet target: T = state as any\n\tlet traps: ProxyHandler<object | Array<any>> = objectTraps\n\tif (isArray) {\n\t\ttarget = [state] as any\n\t\ttraps = arrayTraps\n\t}\n\n\tconst {revoke, proxy} = Proxy.revocable(target, traps)\n\tstate.draft_ = proxy as any\n\tstate.revoke_ = revoke\n\treturn proxy as any\n}\n\n/**\n * Object drafts\n */\nexport const objectTraps: ProxyHandler<ProxyState> = {\n\tget(state, prop) {\n\t\tif (prop === DRAFT_STATE) return state\n\n\t\tconst source = latest(state)\n\t\tif (!has(source, prop)) {\n\t\t\t// non-existing or non-own property...\n\t\t\treturn readPropFromProto(state, source, prop)\n\t\t}\n\t\tconst value = source[prop]\n\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\treturn value\n\t\t}\n\t\t// Check for existing draft in modified state.\n\t\t// Assigned values are never drafted. This catches any drafts we created, too.\n\t\tif (value === peek(state.base_, prop)) {\n\t\t\tprepareCopy(state)\n\t\t\treturn (state.copy_![prop as any] = createProxy(\n\t\t\t\tstate.scope_.immer_,\n\t\t\t\tvalue,\n\t\t\t\tstate\n\t\t\t))\n\t\t}\n\t\treturn value\n\t},\n\thas(state, prop) {\n\t\treturn prop in latest(state)\n\t},\n\townKeys(state) {\n\t\treturn Reflect.ownKeys(latest(state))\n\t},\n\tset(\n\t\tstate: ProxyObjectState,\n\t\tprop: string /* strictly not, but helps TS */,\n\t\tvalue\n\t) {\n\t\tconst desc = getDescriptorFromProto(latest(state), prop)\n\t\tif (desc?.set) {\n\t\t\t// special case: if this write is captured by a setter, we have\n\t\t\t// to trigger it with the correct context\n\t\t\tdesc.set.call(state.draft_, value)\n\t\t\treturn true\n\t\t}\n\t\tif (!state.modified_) {\n\t\t\t// the last check is because we need to be able to distinguish setting a non-existing to undefined (which is a change)\n\t\t\t// from setting an existing property with value undefined to undefined (which is not a change)\n\t\t\tconst current = peek(latest(state), prop)\n\t\t\t// special case, if we assigning the original value to a draft, we can ignore the assignment\n\t\t\tconst currentState: ProxyObjectState = current?.[DRAFT_STATE]\n\t\t\tif (currentState && currentState.base_ === value) {\n\t\t\t\tstate.copy_![prop] = value\n\t\t\t\tstate.assigned_[prop] = false\n\t\t\t\treturn true\n\t\t\t}\n\t\t\tif (is(value, current) && (value !== undefined || has(state.base_, prop)))\n\t\t\t\treturn true\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t}\n\n\t\tif (\n\t\t\t(state.copy_![prop] === value &&\n\t\t\t\t// special case: handle new props with value 'undefined'\n\t\t\t\t(value !== undefined || prop in state.copy_)) ||\n\t\t\t// special case: NaN\n\t\t\t(Number.isNaN(value) && Number.isNaN(state.copy_![prop]))\n\t\t)\n\t\t\treturn true\n\n\t\t// @ts-ignore\n\t\tstate.copy_![prop] = value\n\t\tstate.assigned_[prop] = true\n\t\treturn true\n\t},\n\tdeleteProperty(state, prop: string) {\n\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\tif (peek(state.base_, prop) !== undefined || prop in state.base_) {\n\t\t\tstate.assigned_[prop] = false\n\t\t\tprepareCopy(state)\n\t\t\tmarkChanged(state)\n\t\t} else {\n\t\t\t// if an originally not assigned property was deleted\n\t\t\tdelete state.assigned_[prop]\n\t\t}\n\t\t// @ts-ignore\n\t\tif (state.copy_) delete state.copy_[prop]\n\t\treturn true\n\t},\n\t// Note: We never coerce `desc.value` into an Immer draft, because we can't make\n\t// the same guarantee in ES5 mode.\n\tgetOwnPropertyDescriptor(state, prop) {\n\t\tconst owner = latest(state)\n\t\tconst desc = Reflect.getOwnPropertyDescriptor(owner, prop)\n\t\tif (!desc) return desc\n\t\treturn {\n\t\t\twritable: true,\n\t\t\tconfigurable: state.type_ !== ProxyType.ProxyArray || prop !== \"length\",\n\t\t\tenumerable: desc.enumerable,\n\t\t\tvalue: owner[prop]\n\t\t}\n\t},\n\tdefineProperty() {\n\t\tdie(11)\n\t},\n\tgetPrototypeOf(state) {\n\t\treturn Object.getPrototypeOf(state.base_)\n\t},\n\tsetPrototypeOf() {\n\t\tdie(12)\n\t}\n}\n\n/**\n * Array drafts\n */\n\nconst arrayTraps: ProxyHandler<[ProxyArrayState]> = {}\neach(objectTraps, (key, fn) => {\n\t// @ts-ignore\n\tarrayTraps[key] = function() {\n\t\targuments[0] = arguments[0][0]\n\t\treturn fn.apply(this, arguments)\n\t}\n})\narrayTraps.deleteProperty = function(state, prop) {\n\tif (__DEV__ && isNaN(parseInt(prop as any))) die(13)\n\t// @ts-ignore\n\treturn arrayTraps.set!.call(this, state, prop, undefined)\n}\narrayTraps.set = function(state, prop, value) {\n\tif (__DEV__ && prop !== \"length\" && isNaN(parseInt(prop as any))) die(14)\n\treturn objectTraps.set!.call(this, state[0], prop, value, state[0])\n}\n\n// Access a property without creating an Immer draft.\nfunction peek(draft: Drafted, prop: PropertyKey) {\n\tconst state = draft[DRAFT_STATE]\n\tconst source = state ? latest(state) : draft\n\treturn source[prop]\n}\n\nfunction readPropFromProto(state: ImmerState, source: any, prop: PropertyKey) {\n\tconst desc = getDescriptorFromProto(source, prop)\n\treturn desc\n\t\t? `value` in desc\n\t\t\t? desc.value\n\t\t\t: // This is a very special case, if the prop is a getter defined by the\n\t\t\t  // prototype, we should invoke it with the draft as context!\n\t\t\t  desc.get?.call(state.draft_)\n\t\t: undefined\n}\n\nfunction getDescriptorFromProto(\n\tsource: any,\n\tprop: PropertyKey\n): PropertyDescriptor | undefined {\n\t// 'in' checks proto!\n\tif (!(prop in source)) return undefined\n\tlet proto = Object.getPrototypeOf(source)\n\twhile (proto) {\n\t\tconst desc = Object.getOwnPropertyDescriptor(proto, prop)\n\t\tif (desc) return desc\n\t\tproto = Object.getPrototypeOf(proto)\n\t}\n\treturn undefined\n}\n\nexport function markChanged(state: ImmerState) {\n\tif (!state.modified_) {\n\t\tstate.modified_ = true\n\t\tif (state.parent_) {\n\t\t\tmarkChanged(state.parent_)\n\t\t}\n\t}\n}\n\nexport function prepareCopy(state: {base_: any; copy_: any}) {\n\tif (!state.copy_) {\n\t\tstate.copy_ = shallowCopy(state.base_)\n\t}\n}\n", "import {\n\tIProduceWithPatches,\n\tIProduce,\n\tImmerState,\n\tDrafted,\n\tisDraftable,\n\tprocessR<PERSON>ult,\n\tPatch,\n\tObjectish,\n\tDRAFT_STATE,\n\tDraft,\n\tPatchListener,\n\tisDraft,\n\tisMap,\n\tisSet,\n\tcreateProxyProxy,\n\tgetPlugin,\n\tdie,\n\thasProxies,\n\tenterScope,\n\trevokeScope,\n\tleaveScope,\n\tusePatchesInScope,\n\tgetCurrentScope,\n\tNOTHING,\n\tfreeze,\n\tcurrent\n} from \"../internal\"\n\ninterface ProducersFns {\n\tproduce: IProduce\n\tproduceWithPatches: IProduceWithPatches\n}\n\nexport class Immer implements ProducersFns {\n\tuseProxies_: boolean = hasProxies\n\n\tautoFreeze_: boolean = true\n\n\tconstructor(config?: {useProxies?: boolean; autoFreeze?: boolean}) {\n\t\tif (typeof config?.useProxies === \"boolean\")\n\t\t\tthis.setUseProxies(config!.useProxies)\n\t\tif (typeof config?.autoFreeze === \"boolean\")\n\t\t\tthis.setAutoFreeze(config!.autoFreeze)\n\t}\n\n\t/**\n\t * The `produce` function takes a value and a \"recipe function\" (whose\n\t * return value often depends on the base state). The recipe function is\n\t * free to mutate its first argument however it wants. All mutations are\n\t * only ever applied to a __copy__ of the base state.\n\t *\n\t * Pass only a function to create a \"curried producer\" which relieves you\n\t * from passing the recipe function every time.\n\t *\n\t * Only plain objects and arrays are made mutable. All other objects are\n\t * considered uncopyable.\n\t *\n\t * Note: This function is __bound__ to its `Immer` instance.\n\t *\n\t * @param {any} base - the initial state\n\t * @param {Function} recipe - function that receives a proxy of the base state as first argument and which can be freely modified\n\t * @param {Function} patchListener - optional function that will be called with all the patches produced here\n\t * @returns {any} a new state, or the initial state if nothing was modified\n\t */\n\tproduce: IProduce = (base: any, recipe?: any, patchListener?: any) => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\" && typeof recipe !== \"function\") {\n\t\t\tconst defaultBase = recipe\n\t\t\trecipe = base\n\n\t\t\tconst self = this\n\t\t\treturn function curriedProduce(\n\t\t\t\tthis: any,\n\t\t\t\tbase = defaultBase,\n\t\t\t\t...args: any[]\n\t\t\t) {\n\t\t\t\treturn self.produce(base, (draft: Drafted) => recipe.call(this, draft, ...args)) // prettier-ignore\n\t\t\t}\n\t\t}\n\n\t\tif (typeof recipe !== \"function\") die(6)\n\t\tif (patchListener !== undefined && typeof patchListener !== \"function\")\n\t\t\tdie(7)\n\n\t\tlet result\n\n\t\t// Only plain objects, arrays, and \"immerable classes\" are drafted.\n\t\tif (isDraftable(base)) {\n\t\t\tconst scope = enterScope(this)\n\t\t\tconst proxy = createProxy(this, base, undefined)\n\t\t\tlet hasError = true\n\t\t\ttry {\n\t\t\t\tresult = recipe(proxy)\n\t\t\t\thasError = false\n\t\t\t} finally {\n\t\t\t\t// finally instead of catch + rethrow better preserves original stack\n\t\t\t\tif (hasError) revokeScope(scope)\n\t\t\t\telse leaveScope(scope)\n\t\t\t}\n\t\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\t\treturn result.then(\n\t\t\t\t\tresult => {\n\t\t\t\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\t\t\t\treturn processResult(result, scope)\n\t\t\t\t\t},\n\t\t\t\t\terror => {\n\t\t\t\t\t\trevokeScope(scope)\n\t\t\t\t\t\tthrow error\n\t\t\t\t\t}\n\t\t\t\t)\n\t\t\t}\n\t\t\tusePatchesInScope(scope, patchListener)\n\t\t\treturn processResult(result, scope)\n\t\t} else if (!base || typeof base !== \"object\") {\n\t\t\tresult = recipe(base)\n\t\t\tif (result === undefined) result = base\n\t\t\tif (result === NOTHING) result = undefined\n\t\t\tif (this.autoFreeze_) freeze(result, true)\n\t\t\tif (patchListener) {\n\t\t\t\tconst p: Patch[] = []\n\t\t\t\tconst ip: Patch[] = []\n\t\t\t\tgetPlugin(\"Patches\").generateReplacementPatches_(base, result, p, ip)\n\t\t\t\tpatchListener(p, ip)\n\t\t\t}\n\t\t\treturn result\n\t\t} else die(21, base)\n\t}\n\n\tproduceWithPatches: IProduceWithPatches = (base: any, recipe?: any): any => {\n\t\t// curried invocation\n\t\tif (typeof base === \"function\") {\n\t\t\treturn (state: any, ...args: any[]) =>\n\t\t\t\tthis.produceWithPatches(state, (draft: any) => base(draft, ...args))\n\t\t}\n\n\t\tlet patches: Patch[], inversePatches: Patch[]\n\t\tconst result = this.produce(base, recipe, (p: Patch[], ip: Patch[]) => {\n\t\t\tpatches = p\n\t\t\tinversePatches = ip\n\t\t})\n\n\t\tif (typeof Promise !== \"undefined\" && result instanceof Promise) {\n\t\t\treturn result.then(nextState => [nextState, patches!, inversePatches!])\n\t\t}\n\t\treturn [result, patches!, inversePatches!]\n\t}\n\n\tcreateDraft<T extends Objectish>(base: T): Draft<T> {\n\t\tif (!isDraftable(base)) die(8)\n\t\tif (isDraft(base)) base = current(base)\n\t\tconst scope = enterScope(this)\n\t\tconst proxy = createProxy(this, base, undefined)\n\t\tproxy[DRAFT_STATE].isManual_ = true\n\t\tleaveScope(scope)\n\t\treturn proxy as any\n\t}\n\n\tfinishDraft<D extends Draft<any>>(\n\t\tdraft: D,\n\t\tpatchListener?: PatchListener\n\t): D extends Draft<infer T> ? T : never {\n\t\tconst state: ImmerState = draft && (draft as any)[DRAFT_STATE]\n\t\tif (__DEV__) {\n\t\t\tif (!state || !state.isManual_) die(9)\n\t\t\tif (state.finalized_) die(10)\n\t\t}\n\t\tconst {scope_: scope} = state\n\t\tusePatchesInScope(scope, patchListener)\n\t\treturn processResult(undefined, scope)\n\t}\n\n\t/**\n\t * Pass true to automatically freeze all copies created by Immer.\n\t *\n\t * By default, auto-freezing is enabled.\n\t */\n\tsetAutoFreeze(value: boolean) {\n\t\tthis.autoFreeze_ = value\n\t}\n\n\t/**\n\t * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n\t * always faster than using ES5 proxies.\n\t *\n\t * By default, feature detection is used, so calling this is rarely necessary.\n\t */\n\tsetUseProxies(value: boolean) {\n\t\tif (value && !hasProxies) {\n\t\t\tdie(20)\n\t\t}\n\t\tthis.useProxies_ = value\n\t}\n\n\tapplyPatches<T extends Objectish>(base: T, patches: Patch[]): T {\n\t\t// If a patch replaces the entire state, take that replacement as base\n\t\t// before applying patches\n\t\tlet i: number\n\t\tfor (i = patches.length - 1; i >= 0; i--) {\n\t\t\tconst patch = patches[i]\n\t\t\tif (patch.path.length === 0 && patch.op === \"replace\") {\n\t\t\t\tbase = patch.value\n\t\t\t\tbreak\n\t\t\t}\n\t\t}\n\t\t// If there was a patch that replaced the entire state, start from the\n\t\t// patch after that.\n\t\tif (i > -1) {\n\t\t\tpatches = patches.slice(i + 1)\n\t\t}\n\n\t\tconst applyPatchesImpl = getPlugin(\"Patches\").applyPatches_\n\t\tif (isDraft(base)) {\n\t\t\t// N.B: never hits if some patch a replacement, patches are never drafts\n\t\t\treturn applyPatchesImpl(base, patches)\n\t\t}\n\t\t// Otherwise, produce a copy of the base state.\n\t\treturn this.produce(base, (draft: Drafted) =>\n\t\t\tapplyPatchesImpl(draft, patches)\n\t\t)\n\t}\n}\n\nexport function createProxy<T extends Objectish>(\n\timmer: Immer,\n\tvalue: T,\n\tparent?: ImmerState\n): Drafted<T, ImmerState> {\n\t// precondition: createProxy should be guarded by isDraftable, so we know we can safely draft\n\tconst draft: Drafted = isMap(value)\n\t\t? getPlugin(\"MapSet\").proxyMap_(value, parent)\n\t\t: isSet(value)\n\t\t? getPlugin(\"MapSet\").proxySet_(value, parent)\n\t\t: immer.useProxies_\n\t\t? createProxyProxy(value, parent)\n\t\t: getPlugin(\"ES5\").createES5Proxy_(value, parent)\n\n\tconst scope = parent ? parent.scope_ : getCurrentScope()\n\tscope.drafts_.push(draft)\n\treturn draft\n}\n", "import {\n\tdie,\n\tisDraft,\n\tshallowCopy,\n\teach,\n\tDRAFT_STATE,\n\tget,\n\tset,\n\tImmerState,\n\tisDraftable,\n\tArchtype,\n\tgetArchtype,\n\tgetPlugin\n} from \"../internal\"\n\n/** Takes a snapshot of the current state of a draft and finalizes it (but without freezing). This is a great utility to print the current state during debugging (no Proxies in the way). The output of current can also be safely leaked outside the producer. */\nexport function current<T>(value: T): T\nexport function current(value: any): any {\n\tif (!isDraft(value)) die(22, value)\n\treturn currentImpl(value)\n}\n\nfunction currentImpl(value: any): any {\n\tif (!isDraftable(value)) return value\n\tconst state: ImmerState | undefined = value[DRAFT_STATE]\n\tlet copy: any\n\tconst archType = getArchtype(value)\n\tif (state) {\n\t\tif (\n\t\t\t!state.modified_ &&\n\t\t\t(state.type_ < 4 || !getPlugin(\"ES5\").hasChanges_(state as any))\n\t\t)\n\t\t\treturn state.base_\n\t\t// Optimization: avoid generating new drafts during copying\n\t\tstate.finalized_ = true\n\t\tcopy = copyHelper(value, archType)\n\t\tstate.finalized_ = false\n\t} else {\n\t\tcopy = copyHelper(value, archType)\n\t}\n\n\teach(copy, (key, childValue) => {\n\t\tif (state && get(state.base_, key) === childValue) return // no need to copy or search in something that didn't change\n\t\tset(copy, key, currentImpl(childValue))\n\t})\n\t// In the future, we might consider freezing here, based on the current settings\n\treturn archType === Archtype.Set ? new Set(copy) : copy\n}\n\nfunction copyHelper(value: any, archType: number): any {\n\t// creates a shallow copy, even if it is a map or set\n\tswitch (archType) {\n\t\tcase Archtype.Map:\n\t\t\treturn new Map(value)\n\t\tcase Archtype.Set:\n\t\t\t// Set will be cloned as array temporarily, so that we can replace individual items\n\t\t\treturn Array.from(value)\n\t}\n\treturn shallowCopy(value)\n}\n", "import {\n\tImmerState,\n\tDrafted,\n\tES5ArrayState,\n\tES5ObjectState,\n\teach,\n\thas,\n\tisDraft,\n\tlatest,\n\tDRAFT_STATE,\n\tis,\n\tloadPlugin,\n\tImmerScope,\n\tProxyType,\n\tgetCurrentScope,\n\tdie,\n\tmarkChanged,\n\tobjectTraps,\n\townKeys,\n\tgetOwnPropertyDescriptors\n} from \"../internal\"\n\ntype ES5State = ES5ArrayState | ES5ObjectState\n\nexport function enableES5() {\n\tfunction willFinalizeES5_(\n\t\tscope: ImmerScope,\n\t\tresult: any,\n\t\tisReplaced: boolean\n\t) {\n\t\tif (!isReplaced) {\n\t\t\tif (scope.patches_) {\n\t\t\t\tmarkChangesRecursively(scope.drafts_![0])\n\t\t\t}\n\t\t\t// This is faster when we don't care about which attributes changed.\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t\t// When a child draft is returned, look for changes.\n\t\telse if (\n\t\t\tisDraft(result) &&\n\t\t\t(result[DRAFT_STATE] as ES5State).scope_ === scope\n\t\t) {\n\t\t\tmarkChangesSweep(scope.drafts_)\n\t\t}\n\t}\n\n\tfunction createES5Draft(isArray: boolean, base: any) {\n\t\tif (isArray) {\n\t\t\tconst draft = new Array(base.length)\n\t\t\tfor (let i = 0; i < base.length; i++)\n\t\t\t\tObject.defineProperty(draft, \"\" + i, proxyProperty(i, true))\n\t\t\treturn draft\n\t\t} else {\n\t\t\tconst descriptors = getOwnPropertyDescriptors(base)\n\t\t\tdelete descriptors[DRAFT_STATE as any]\n\t\t\tconst keys = ownKeys(descriptors)\n\t\t\tfor (let i = 0; i < keys.length; i++) {\n\t\t\t\tconst key: any = keys[i]\n\t\t\t\tdescriptors[key] = proxyProperty(\n\t\t\t\t\tkey,\n\t\t\t\t\tisArray || !!descriptors[key].enumerable\n\t\t\t\t)\n\t\t\t}\n\t\t\treturn Object.create(Object.getPrototypeOf(base), descriptors)\n\t\t}\n\t}\n\n\tfunction createES5Proxy_<T>(\n\t\tbase: T,\n\t\tparent?: ImmerState\n\t): Drafted<T, ES5ObjectState | ES5ArrayState> {\n\t\tconst isArray = Array.isArray(base)\n\t\tconst draft = createES5Draft(isArray, base)\n\n\t\tconst state: ES5ObjectState | ES5ArrayState = {\n\t\t\ttype_: isArray ? ProxyType.ES5Array : (ProxyType.ES5Object as any),\n\t\t\tscope_: parent ? parent.scope_ : getCurrentScope(),\n\t\t\tmodified_: false,\n\t\t\tfinalized_: false,\n\t\t\tassigned_: {},\n\t\t\tparent_: parent,\n\t\t\t// base is the object we are drafting\n\t\t\tbase_: base,\n\t\t\t// draft is the draft object itself, that traps all reads and reads from either the base (if unmodified) or copy (if modified)\n\t\t\tdraft_: draft,\n\t\t\tcopy_: null,\n\t\t\trevoked_: false,\n\t\t\tisManual_: false\n\t\t}\n\n\t\tObject.defineProperty(draft, DRAFT_STATE, {\n\t\t\tvalue: state,\n\t\t\t// enumerable: false <- the default\n\t\t\twritable: true\n\t\t})\n\t\treturn draft\n\t}\n\n\t// property descriptors are recycled to make sure we don't create a get and set closure per property,\n\t// but share them all instead\n\tconst descriptors: {[prop: string]: PropertyDescriptor} = {}\n\n\tfunction proxyProperty(\n\t\tprop: string | number,\n\t\tenumerable: boolean\n\t): PropertyDescriptor {\n\t\tlet desc = descriptors[prop]\n\t\tif (desc) {\n\t\t\tdesc.enumerable = enumerable\n\t\t} else {\n\t\t\tdescriptors[prop] = desc = {\n\t\t\t\tconfigurable: true,\n\t\t\t\tenumerable,\n\t\t\t\tget(this: any) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\treturn objectTraps.get(state, prop)\n\t\t\t\t},\n\t\t\t\tset(this: any, value) {\n\t\t\t\t\tconst state = this[DRAFT_STATE]\n\t\t\t\t\tif (__DEV__) assertUnrevoked(state)\n\t\t\t\t\t// @ts-ignore\n\t\t\t\t\tobjectTraps.set(state, prop, value)\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t\treturn desc\n\t}\n\n\t// This looks expensive, but only proxies are visited, and only objects without known changes are scanned.\n\tfunction markChangesSweep(drafts: Drafted<any, ImmerState>[]) {\n\t\t// The natural order of drafts in the `scope` array is based on when they\n\t\t// were accessed. By processing drafts in reverse natural order, we have a\n\t\t// better chance of processing leaf nodes first. When a leaf node is known to\n\t\t// have changed, we can avoid any traversal of its ancestor nodes.\n\t\tfor (let i = drafts.length - 1; i >= 0; i--) {\n\t\t\tconst state: ES5State = drafts[i][DRAFT_STATE]\n\t\t\tif (!state.modified_) {\n\t\t\t\tswitch (state.type_) {\n\t\t\t\t\tcase ProxyType.ES5Array:\n\t\t\t\t\t\tif (hasArrayChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t\tcase ProxyType.ES5Object:\n\t\t\t\t\t\tif (hasObjectChanges(state)) markChanged(state)\n\t\t\t\t\t\tbreak\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction markChangesRecursively(object: any) {\n\t\tif (!object || typeof object !== \"object\") return\n\t\tconst state: ES5State | undefined = object[DRAFT_STATE]\n\t\tif (!state) return\n\t\tconst {base_, draft_, assigned_, type_} = state\n\t\tif (type_ === ProxyType.ES5Object) {\n\t\t\t// Look for added keys.\n\t\t\t// probably there is a faster way to detect changes, as sweep + recurse seems to do some\n\t\t\t// unnecessary work.\n\t\t\t// also: probably we can store the information we detect here, to speed up tree finalization!\n\t\t\teach(draft_, key => {\n\t\t\t\tif ((key as any) === DRAFT_STATE) return\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif ((base_ as any)[key] === undefined && !has(base_, key)) {\n\t\t\t\t\tassigned_[key] = true\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t} else if (!assigned_[key]) {\n\t\t\t\t\t// Only untouched properties trigger recursion.\n\t\t\t\t\tmarkChangesRecursively(draft_[key])\n\t\t\t\t}\n\t\t\t})\n\t\t\t// Look for removed keys.\n\t\t\teach(base_, key => {\n\t\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\t\tif (draft_[key] === undefined && !has(draft_, key)) {\n\t\t\t\t\tassigned_[key] = false\n\t\t\t\t\tmarkChanged(state)\n\t\t\t\t}\n\t\t\t})\n\t\t} else if (type_ === ProxyType.ES5Array) {\n\t\t\tif (hasArrayChanges(state as ES5ArrayState)) {\n\t\t\t\tmarkChanged(state)\n\t\t\t\tassigned_.length = true\n\t\t\t}\n\n\t\t\tif (draft_.length < base_.length) {\n\t\t\t\tfor (let i = draft_.length; i < base_.length; i++) assigned_[i] = false\n\t\t\t} else {\n\t\t\t\tfor (let i = base_.length; i < draft_.length; i++) assigned_[i] = true\n\t\t\t}\n\n\t\t\t// Minimum count is enough, the other parts has been processed.\n\t\t\tconst min = Math.min(draft_.length, base_.length)\n\n\t\t\tfor (let i = 0; i < min; i++) {\n\t\t\t\t// Only untouched indices trigger recursion.\n\t\t\t\tif (!draft_.hasOwnProperty(i)) {\n\t\t\t\t\tassigned_[i] = true\n\t\t\t\t}\n\t\t\t\tif (assigned_[i] === undefined) markChangesRecursively(draft_[i])\n\t\t\t}\n\t\t}\n\t}\n\n\tfunction hasObjectChanges(state: ES5ObjectState) {\n\t\tconst {base_, draft_} = state\n\n\t\t// Search for added keys and changed keys. Start at the back, because\n\t\t// non-numeric keys are ordered by time of definition on the object.\n\t\tconst keys = ownKeys(draft_)\n\t\tfor (let i = keys.length - 1; i >= 0; i--) {\n\t\t\tconst key: any = keys[i]\n\t\t\tif (key === DRAFT_STATE) continue\n\t\t\tconst baseValue = base_[key]\n\t\t\t// The `undefined` check is a fast path for pre-existing keys.\n\t\t\tif (baseValue === undefined && !has(base_, key)) {\n\t\t\t\treturn true\n\t\t\t}\n\t\t\t// Once a base key is deleted, future changes go undetected, because its\n\t\t\t// descriptor is erased. This branch detects any missed changes.\n\t\t\telse {\n\t\t\t\tconst value = draft_[key]\n\t\t\t\tconst state: ImmerState = value && value[DRAFT_STATE]\n\t\t\t\tif (state ? state.base_ !== baseValue : !is(value, baseValue)) {\n\t\t\t\t\treturn true\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// At this point, no keys were added or changed.\n\t\t// Compare key count to determine if keys were deleted.\n\t\tconst baseIsDraft = !!base_[DRAFT_STATE as any]\n\t\treturn keys.length !== ownKeys(base_).length + (baseIsDraft ? 0 : 1) // + 1 to correct for DRAFT_STATE\n\t}\n\n\tfunction hasArrayChanges(state: ES5ArrayState) {\n\t\tconst {draft_} = state\n\t\tif (draft_.length !== state.base_.length) return true\n\t\t// See #116\n\t\t// If we first shorten the length, our array interceptors will be removed.\n\t\t// If after that new items are added, result in the same original length,\n\t\t// those last items will have no intercepting property.\n\t\t// So if there is no own descriptor on the last position, we know that items were removed and added\n\t\t// N.B.: splice, unshift, etc only shift values around, but not prop descriptors, so we only have to check\n\t\t// the last one\n\t\t// last descriptor can be not a trap, if the array was extended\n\t\tconst descriptor = Object.getOwnPropertyDescriptor(\n\t\t\tdraft_,\n\t\t\tdraft_.length - 1\n\t\t)\n\t\t// descriptor can be null, but only for newly created sparse arrays, eg. new Array(10)\n\t\tif (descriptor && !descriptor.get) return true\n\t\t// if we miss a property, it has been deleted, so array probobaly changed\n\t\tfor (let i = 0; i < draft_.length; i++) {\n\t\t\tif (!draft_.hasOwnProperty(i)) return true\n\t\t}\n\t\t// For all other cases, we don't have to compare, as they would have been picked up by the index setters\n\t\treturn false\n\t}\n\n\tfunction hasChanges_(state: ES5State) {\n\t\treturn state.type_ === ProxyType.ES5Object\n\t\t\t? hasObjectChanges(state)\n\t\t\t: hasArrayChanges(state)\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"ES5\", {\n\t\tcreateES5Proxy_,\n\t\twillFinalizeES5_,\n\t\thasChanges_\n\t})\n}\n", "import {immerable} from \"../immer\"\nimport {\n\tImmerState,\n\tPatch,\n\tSetState,\n\tES5ArrayState,\n\tProxyArrayState,\n\tMapState,\n\tES5ObjectState,\n\tProxyObjectState,\n\tPatchPath,\n\tget,\n\teach,\n\thas,\n\tgetArchtype,\n\tisSet,\n\tisMap,\n\tloadPlugin,\n\tProxyType,\n\tArchtype,\n\tdie,\n\tisDraft,\n\tisDraftable,\n\tNOTHING\n} from \"../internal\"\n\nexport function enablePatches() {\n\tconst REPLACE = \"replace\"\n\tconst ADD = \"add\"\n\tconst REMOVE = \"remove\"\n\n\tfunction generatePatches_(\n\t\tstate: ImmerState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tswitch (state.type_) {\n\t\t\tcase ProxyType.ProxyObject:\n\t\t\tcase ProxyType.ES5Object:\n\t\t\tcase ProxyType.Map:\n\t\t\t\treturn generatePatchesFromAssigned(\n\t\t\t\t\tstate,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t\tcase ProxyType.ES5Array:\n\t\t\tcase ProxyType.ProxyArray:\n\t\t\t\treturn generateArrayPatches(state, basePath, patches, inversePatches)\n\t\t\tcase ProxyType.Set:\n\t\t\t\treturn generateSetPatches(\n\t\t\t\t\t(state as any) as SetState,\n\t\t\t\t\tbasePath,\n\t\t\t\t\tpatches,\n\t\t\t\t\tinversePatches\n\t\t\t\t)\n\t\t}\n\t}\n\n\tfunction generateArrayPatches(\n\t\tstate: ES5ArrayState | ProxyArrayState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, assigned_} = state\n\t\tlet copy_ = state.copy_!\n\n\t\t// Reduce complexity by ensuring `base` is never longer.\n\t\tif (copy_.length < base_.length) {\n\t\t\t// @ts-ignore\n\t\t\t;[base_, copy_] = [copy_, base_]\n\t\t\t;[patches, inversePatches] = [inversePatches, patches]\n\t\t}\n\n\t\t// Process replaced indices.\n\t\tfor (let i = 0; i < base_.length; i++) {\n\t\t\tif (assigned_[i] && copy_[i] !== base_[i]) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t\t})\n\t\t\t\tinversePatches.push({\n\t\t\t\t\top: REPLACE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue: clonePatchValueIfNeeded(base_[i])\n\t\t\t\t})\n\t\t\t}\n\t\t}\n\n\t\t// Process added indices.\n\t\tfor (let i = base_.length; i < copy_.length; i++) {\n\t\t\tconst path = basePath.concat([i])\n\t\t\tpatches.push({\n\t\t\t\top: ADD,\n\t\t\t\tpath,\n\t\t\t\t// Need to maybe clone it, as it can in fact be the original value\n\t\t\t\t// due to the base/copy inversion at the start of this function\n\t\t\t\tvalue: clonePatchValueIfNeeded(copy_[i])\n\t\t\t})\n\t\t}\n\t\tif (base_.length < copy_.length) {\n\t\t\tinversePatches.push({\n\t\t\t\top: REPLACE,\n\t\t\t\tpath: basePath.concat([\"length\"]),\n\t\t\t\tvalue: base_.length\n\t\t\t})\n\t\t}\n\t}\n\n\t// This is used for both Map objects and normal objects.\n\tfunction generatePatchesFromAssigned(\n\t\tstate: MapState | ES5ObjectState | ProxyObjectState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tconst {base_, copy_} = state\n\t\teach(state.assigned_!, (key, assignedValue) => {\n\t\t\tconst origValue = get(base_, key)\n\t\t\tconst value = get(copy_!, key)\n\t\t\tconst op = !assignedValue ? REMOVE : has(base_, key) ? REPLACE : ADD\n\t\t\tif (origValue === value && op === REPLACE) return\n\t\t\tconst path = basePath.concat(key as any)\n\t\t\tpatches.push(op === REMOVE ? {op, path} : {op, path, value})\n\t\t\tinversePatches.push(\n\t\t\t\top === ADD\n\t\t\t\t\t? {op: REMOVE, path}\n\t\t\t\t\t: op === REMOVE\n\t\t\t\t\t? {op: ADD, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t\t\t: {op: REPLACE, path, value: clonePatchValueIfNeeded(origValue)}\n\t\t\t)\n\t\t})\n\t}\n\n\tfunction generateSetPatches(\n\t\tstate: SetState,\n\t\tbasePath: PatchPath,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t) {\n\t\tlet {base_, copy_} = state\n\n\t\tlet i = 0\n\t\tbase_.forEach((value: any) => {\n\t\t\tif (!copy_!.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t\ti = 0\n\t\tcopy_!.forEach((value: any) => {\n\t\t\tif (!base_.has(value)) {\n\t\t\t\tconst path = basePath.concat([i])\n\t\t\t\tpatches.push({\n\t\t\t\t\top: ADD,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t\tinversePatches.unshift({\n\t\t\t\t\top: REMOVE,\n\t\t\t\t\tpath,\n\t\t\t\t\tvalue\n\t\t\t\t})\n\t\t\t}\n\t\t\ti++\n\t\t})\n\t}\n\n\tfunction generateReplacementPatches_(\n\t\tbaseValue: any,\n\t\treplacement: any,\n\t\tpatches: Patch[],\n\t\tinversePatches: Patch[]\n\t): void {\n\t\tpatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: replacement === NOTHING ? undefined : replacement\n\t\t})\n\t\tinversePatches.push({\n\t\t\top: REPLACE,\n\t\t\tpath: [],\n\t\t\tvalue: baseValue\n\t\t})\n\t}\n\n\tfunction applyPatches_<T>(draft: T, patches: Patch[]): T {\n\t\tpatches.forEach(patch => {\n\t\t\tconst {path, op} = patch\n\n\t\t\tlet base: any = draft\n\t\t\tfor (let i = 0; i < path.length - 1; i++) {\n\t\t\t\tconst parentType = getArchtype(base)\n\t\t\t\tlet p = path[i]\n\t\t\t\tif (typeof p !== \"string\" && typeof p !== \"number\") {\n\t\t\t\t\tp = \"\" + p\n\t\t\t\t}\n\n\t\t\t\t// See #738, avoid prototype pollution\n\t\t\t\tif (\n\t\t\t\t\t(parentType === Archtype.Object || parentType === Archtype.Array) &&\n\t\t\t\t\t(p === \"__proto__\" || p === \"constructor\")\n\t\t\t\t)\n\t\t\t\t\tdie(24)\n\t\t\t\tif (typeof base === \"function\" && p === \"prototype\") die(24)\n\t\t\t\tbase = get(base, p)\n\t\t\t\tif (typeof base !== \"object\") die(15, path.join(\"/\"))\n\t\t\t}\n\n\t\t\tconst type = getArchtype(base)\n\t\t\tconst value = deepClonePatchValue(patch.value) // used to clone patch to ensure original patch is not modified, see #411\n\t\t\tconst key = path[path.length - 1]\n\t\t\tswitch (op) {\n\t\t\t\tcase REPLACE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\tdie(16)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\t// if value is an object, then it's assigned by reference\n\t\t\t\t\t\t\t// in the following add or remove ops, the value field inside the patch will also be modifyed\n\t\t\t\t\t\t\t// so we use value from the cloned patch\n\t\t\t\t\t\t\t// @ts-ignore\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase ADD:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn key === \"-\"\n\t\t\t\t\t\t\t\t? base.push(value)\n\t\t\t\t\t\t\t\t: base.splice(key as any, 0, value)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.set(key, value)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.add(value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn (base[key] = value)\n\t\t\t\t\t}\n\t\t\t\tcase REMOVE:\n\t\t\t\t\tswitch (type) {\n\t\t\t\t\t\tcase Archtype.Array:\n\t\t\t\t\t\t\treturn base.splice(key as any, 1)\n\t\t\t\t\t\tcase Archtype.Map:\n\t\t\t\t\t\t\treturn base.delete(key)\n\t\t\t\t\t\tcase Archtype.Set:\n\t\t\t\t\t\t\treturn base.delete(patch.value)\n\t\t\t\t\t\tdefault:\n\t\t\t\t\t\t\treturn delete base[key]\n\t\t\t\t\t}\n\t\t\t\tdefault:\n\t\t\t\t\tdie(17, op)\n\t\t\t}\n\t\t})\n\n\t\treturn draft\n\t}\n\n\t// optimize: this is quite a performance hit, can we detect intelligently when it is needed?\n\t// E.g. auto-draft when new objects from outside are assigned and modified?\n\t// (See failing test when deepClone just returns obj)\n\tfunction deepClonePatchValue<T>(obj: T): T\n\tfunction deepClonePatchValue(obj: any) {\n\t\tif (!isDraftable(obj)) return obj\n\t\tif (Array.isArray(obj)) return obj.map(deepClonePatchValue)\n\t\tif (isMap(obj))\n\t\t\treturn new Map(\n\t\t\t\tArray.from(obj.entries()).map(([k, v]) => [k, deepClonePatchValue(v)])\n\t\t\t)\n\t\tif (isSet(obj)) return new Set(Array.from(obj).map(deepClonePatchValue))\n\t\tconst cloned = Object.create(Object.getPrototypeOf(obj))\n\t\tfor (const key in obj) cloned[key] = deepClonePatchValue(obj[key])\n\t\tif (has(obj, immerable)) cloned[immerable] = obj[immerable]\n\t\treturn cloned\n\t}\n\n\tfunction clonePatchValueIfNeeded<T>(obj: T): T {\n\t\tif (isDraft(obj)) {\n\t\t\treturn deepClonePatchValue(obj)\n\t\t} else return obj\n\t}\n\n\tloadPlugin(\"Patches\", {\n\t\tapplyPatches_,\n\t\tgeneratePatches_,\n\t\tgenerateReplacementPatches_\n\t})\n}\n", "// types only!\nimport {\n\tImmerState,\n\tAnyMap,\n\tAnySet,\n\tMapState,\n\tSetState,\n\tDRAFT_STATE,\n\tgetCurrentScope,\n\tlatest,\n\titeratorSymbol,\n\tisDraftable,\n\tcreateProxy,\n\tloadPlugin,\n\tmarkChanged,\n\tProxyType,\n\tdie,\n\teach\n} from \"../internal\"\n\nexport function enableMapSet() {\n\t/* istanbul ignore next */\n\tvar extendStatics = function(d: any, b: any): any {\n\t\textendStatics =\n\t\t\tObject.setPrototypeOf ||\n\t\t\t({__proto__: []} instanceof Array &&\n\t\t\t\tfunction(d, b) {\n\t\t\t\t\td.__proto__ = b\n\t\t\t\t}) ||\n\t\t\tfunction(d, b) {\n\t\t\t\tfor (var p in b) if (b.hasOwnProperty(p)) d[p] = b[p]\n\t\t\t}\n\t\treturn extendStatics(d, b)\n\t}\n\n\t// Ugly hack to resolve #502 and inherit built in Map / Set\n\tfunction __extends(d: any, b: any): any {\n\t\textendStatics(d, b)\n\t\tfunction __(this: any): any {\n\t\t\tthis.constructor = d\n\t\t}\n\t\td.prototype =\n\t\t\t// @ts-ignore\n\t\t\t((__.prototype = b.prototype), new __())\n\t}\n\n\tconst DraftMap = (function(_super) {\n\t\t__extends(DraftMap, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftMap(this: any, target: AnyMap, parent?: ImmerState): any {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Map,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tassigned_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this as any,\n\t\t\t\tisManual_: false,\n\t\t\t\trevoked_: false\n\t\t\t} as MapState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftMap.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: false,\n\t\t\t// configurable: true\n\t\t})\n\n\t\tp.has = function(key: any): boolean {\n\t\t\treturn latest(this[DRAFT_STATE]).has(key)\n\t\t}\n\n\t\tp.set = function(key: any, value: any) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!latest(state).has(key) || latest(state).get(key) !== value) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t\tstate.copy_!.set(key, value)\n\t\t\t\tstate.assigned_!.set(key, true)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(key: any): boolean {\n\t\t\tif (!this.has(key)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareMapCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\tif (state.base_.has(key)) {\n\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t} else {\n\t\t\t\tstate.assigned_!.delete(key)\n\t\t\t}\n\t\t\tstate.copy_!.delete(key)\n\t\t\treturn true\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareMapCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.assigned_ = new Map()\n\t\t\t\teach(state.base_, key => {\n\t\t\t\t\tstate.assigned_!.set(key, false)\n\t\t\t\t})\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.forEach = function(\n\t\t\tcb: (value: any, key: any, self: any) => void,\n\t\t\tthisArg?: any\n\t\t) {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tlatest(state).forEach((_value: any, key: any, _map: any) => {\n\t\t\t\tcb.call(thisArg, this.get(key), key, this)\n\t\t\t})\n\t\t}\n\n\t\tp.get = function(key: any): any {\n\t\t\tconst state: MapState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tconst value = latest(state).get(key)\n\t\t\tif (state.finalized_ || !isDraftable(value)) {\n\t\t\t\treturn value\n\t\t\t}\n\t\t\tif (value !== state.base_.get(key)) {\n\t\t\t\treturn value // either already drafted or reassigned\n\t\t\t}\n\t\t\t// despite what it looks, this creates a draft only once, see above condition\n\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\tprepareMapCopy(state)\n\t\t\tstate.copy_!.set(key, draft)\n\t\t\treturn draft\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn latest(this[DRAFT_STATE]).keys()\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.values(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp.entries = function(): IterableIterator<[any, any]> {\n\t\t\tconst iterator = this.keys()\n\t\t\treturn {\n\t\t\t\t[iteratorSymbol]: () => this.entries(),\n\t\t\t\tnext: () => {\n\t\t\t\t\tconst r = iterator.next()\n\t\t\t\t\t/* istanbul ignore next */\n\t\t\t\t\tif (r.done) return r\n\t\t\t\t\tconst value = this.get(r.value)\n\t\t\t\t\treturn {\n\t\t\t\t\t\tdone: false,\n\t\t\t\t\t\tvalue: [r.value, value]\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} as any\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.entries()\n\t\t}\n\n\t\treturn DraftMap\n\t})(Map)\n\n\tfunction proxyMap_<T extends AnyMap>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftMap(target, parent)\n\t}\n\n\tfunction prepareMapCopy(state: MapState) {\n\t\tif (!state.copy_) {\n\t\t\tstate.assigned_ = new Map()\n\t\t\tstate.copy_ = new Map(state.base_)\n\t\t}\n\t}\n\n\tconst DraftSet = (function(_super) {\n\t\t__extends(DraftSet, _super)\n\t\t// Create class manually, cause #502\n\t\tfunction DraftSet(this: any, target: AnySet, parent?: ImmerState) {\n\t\t\tthis[DRAFT_STATE] = {\n\t\t\t\ttype_: ProxyType.Set,\n\t\t\t\tparent_: parent,\n\t\t\t\tscope_: parent ? parent.scope_ : getCurrentScope()!,\n\t\t\t\tmodified_: false,\n\t\t\t\tfinalized_: false,\n\t\t\t\tcopy_: undefined,\n\t\t\t\tbase_: target,\n\t\t\t\tdraft_: this,\n\t\t\t\tdrafts_: new Map(),\n\t\t\t\trevoked_: false,\n\t\t\t\tisManual_: false\n\t\t\t} as SetState\n\t\t\treturn this\n\t\t}\n\t\tconst p = DraftSet.prototype\n\n\t\tObject.defineProperty(p, \"size\", {\n\t\t\tget: function() {\n\t\t\t\treturn latest(this[DRAFT_STATE]).size\n\t\t\t}\n\t\t\t// enumerable: true,\n\t\t})\n\n\t\tp.has = function(value: any): boolean {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\t// bit of trickery here, to be able to recognize both the value, and the draft of its value\n\t\t\tif (!state.copy_) {\n\t\t\t\treturn state.base_.has(value)\n\t\t\t}\n\t\t\tif (state.copy_.has(value)) return true\n\t\t\tif (state.drafts_.has(value) && state.copy_.has(state.drafts_.get(value)))\n\t\t\t\treturn true\n\t\t\treturn false\n\t\t}\n\n\t\tp.add = function(value: any): any {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (!this.has(value)) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.add(value)\n\t\t\t}\n\t\t\treturn this\n\t\t}\n\n\t\tp.delete = function(value: any): any {\n\t\t\tif (!this.has(value)) {\n\t\t\t\treturn false\n\t\t\t}\n\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\tmarkChanged(state)\n\t\t\treturn (\n\t\t\t\tstate.copy_!.delete(value) ||\n\t\t\t\t(state.drafts_.has(value)\n\t\t\t\t\t? state.copy_!.delete(state.drafts_.get(value))\n\t\t\t\t\t: /* istanbul ignore next */ false)\n\t\t\t)\n\t\t}\n\n\t\tp.clear = function() {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tif (latest(state).size) {\n\t\t\t\tprepareSetCopy(state)\n\t\t\t\tmarkChanged(state)\n\t\t\t\tstate.copy_!.clear()\n\t\t\t}\n\t\t}\n\n\t\tp.values = function(): IterableIterator<any> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.values()\n\t\t}\n\n\t\tp.entries = function entries(): IterableIterator<[any, any]> {\n\t\t\tconst state: SetState = this[DRAFT_STATE]\n\t\t\tassertUnrevoked(state)\n\t\t\tprepareSetCopy(state)\n\t\t\treturn state.copy_!.entries()\n\t\t}\n\n\t\tp.keys = function(): IterableIterator<any> {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp[iteratorSymbol] = function() {\n\t\t\treturn this.values()\n\t\t}\n\n\t\tp.forEach = function forEach(cb: any, thisArg?: any) {\n\t\t\tconst iterator = this.values()\n\t\t\tlet result = iterator.next()\n\t\t\twhile (!result.done) {\n\t\t\t\tcb.call(thisArg, result.value, result.value, this)\n\t\t\t\tresult = iterator.next()\n\t\t\t}\n\t\t}\n\n\t\treturn DraftSet\n\t})(Set)\n\n\tfunction proxySet_<T extends AnySet>(target: T, parent?: ImmerState): T {\n\t\t// @ts-ignore\n\t\treturn new DraftSet(target, parent)\n\t}\n\n\tfunction prepareSetCopy(state: SetState) {\n\t\tif (!state.copy_) {\n\t\t\t// create drafts for all entries to preserve insertion order\n\t\t\tstate.copy_ = new Set()\n\t\t\tstate.base_.forEach(value => {\n\t\t\t\tif (isDraftable(value)) {\n\t\t\t\t\tconst draft = createProxy(state.scope_.immer_, value, state)\n\t\t\t\t\tstate.drafts_.set(value, draft)\n\t\t\t\t\tstate.copy_!.add(draft)\n\t\t\t\t} else {\n\t\t\t\t\tstate.copy_!.add(value)\n\t\t\t\t}\n\t\t\t})\n\t\t}\n\t}\n\n\tfunction assertUnrevoked(state: any /*ES5State | MapState | SetState*/) {\n\t\tif (state.revoked_) die(3, JSON.stringify(latest(state)))\n\t}\n\n\tloadPlugin(\"MapSet\", {proxyMap_, proxySet_})\n}\n", "import {enableES5} from \"./es5\"\nimport {enableMapSet} from \"./mapset\"\nimport {enablePatches} from \"./patches\"\n\nexport function enableAllPlugins() {\n\tenableES5()\n\tenableMapSet()\n\tenablePatches()\n}\n", "import {\n\tIProduce,\n\tIProduceWithPatches,\n\tImmer,\n\tDraft,\n\tImmutable\n} from \"./internal\"\n\nexport {\n\tDraft,\n\tImmutable,\n\tPatch,\n\tPatchListener,\n\toriginal,\n\tcurrent,\n\tisDraft,\n\tisDraftable,\n\tNOTHING as nothing,\n\tDRAFTABLE as immerable,\n\tfreeze\n} from \"./internal\"\n\nconst immer = new Immer()\n\n/**\n * The `produce` function takes a value and a \"recipe function\" (whose\n * return value often depends on the base state). The recipe function is\n * free to mutate its first argument however it wants. All mutations are\n * only ever applied to a __copy__ of the base state.\n *\n * Pass only a function to create a \"curried producer\" which relieves you\n * from passing the recipe function every time.\n *\n * Only plain objects and arrays are made mutable. All other objects are\n * considered uncopyable.\n *\n * Note: This function is __bound__ to its `Immer` instance.\n *\n * @param {any} base - the initial state\n * @param {Function} producer - function that receives a proxy of the base state as first argument and which can be freely modified\n * @param {Function} patchListener - optional function that will be called with all the patches produced here\n * @returns {any} a new state, or the initial state if nothing was modified\n */\nexport const produce: IProduce = immer.produce\nexport default produce\n\n/**\n * Like `produce`, but `produceWithPatches` always returns a tuple\n * [nextState, patches, inversePatches] (instead of just the next state)\n */\nexport const produceWithPatches: IProduceWithPatches = immer.produceWithPatches.bind(\n\timmer\n)\n\n/**\n * Pass true to automatically freeze all copies created by Immer.\n *\n * Always freeze by default, even in production mode\n */\nexport const setAutoFreeze = immer.setAutoFreeze.bind(immer)\n\n/**\n * Pass true to use the ES2015 `Proxy` class when creating drafts, which is\n * always faster than using ES5 proxies.\n *\n * By default, feature detection is used, so calling this is rarely necessary.\n */\nexport const setUseProxies = immer.setUseProxies.bind(immer)\n\n/**\n * Apply an array of Immer patches to the first argument.\n *\n * This function is a producer, which means copy-on-write is in effect.\n */\nexport const applyPatches = immer.applyPatches.bind(immer)\n\n/**\n * Create an Immer draft from the given base state, which may be a draft itself.\n * The draft can be modified until you finalize it with the `finishDraft` function.\n */\nexport const createDraft = immer.createDraft.bind(immer)\n\n/**\n * Finalize an Immer draft from a `createDraft` call, returning the base state\n * (if no changes were made) or a modified copy. The draft must *not* be\n * mutated afterwards.\n *\n * Pass a function as the 2nd argument to generate Immer patches based on the\n * changes that were made.\n */\nexport const finishDraft = immer.finishDraft.bind(immer)\n\n/**\n * This function is actually a no-op, but can be used to cast an immutable type\n * to an draft type and make TypeScript happy\n *\n * @param value\n */\nexport function castDraft<T>(value: T): Draft<T> {\n\treturn value as any\n}\n\n/**\n * This function is actually a no-op, but can be used to cast a mutable type\n * to an immutable type and make TypeScript happy\n * @param value\n */\nexport function castImmutable<T>(value: T): Immutable<T> {\n\treturn value as any\n}\n\nexport {Immer}\n\nexport {enableES5} from \"./plugins/es5\"\nexport {enablePatches} from \"./plugins/patches\"\nexport {enableMapSet} from \"./plugins/mapset\"\nexport {enableAllPlugins} from \"./plugins/all\"\n"], "names": ["hasSymbol", "Symbol", "hasMap", "Map", "hasSet", "Set", "hasProxies", "Proxy", "revocable", "Reflect", "NOTHING", "for", "DRAFTABLE", "DRAFT_STATE", "iteratorSymbol", "iterator", "errors", "data", "path", "op", "plugin", "thing", "die", "error", "args", "e", "msg", "apply", "Error", "isDraft", "value", "isDraftable", "isPlainObject", "Array", "isArray", "constructor", "isMap", "isSet", "objectCtorString", "Object", "prototype", "toString", "proto", "getPrototypeOf", "Ctor", "hasOwnProperty", "call", "Function", "original", "base_", "ownKeys", "getOwnPropertySymbols", "obj", "getOwnPropertyNames", "concat", "getOwnPropertyDescriptors", "target", "res", "for<PERSON>ach", "key", "getOwnPropertyDescriptor", "each", "iter", "enumerableOnly", "getArchtype", "keys", "entry", "index", "state", "type_", "has", "prop", "get", "set", "propOrOldValue", "t", "add", "is", "x", "y", "latest", "copy_", "shallowCopy", "base", "slice", "descriptors", "i", "length", "desc", "writable", "configurable", "enumerable", "create", "freeze", "deep", "isFrozen", "clear", "delete", "dontMutateFrozenCollections", "plugins", "getPlugin", "pluginKey", "loadPlugin", "implementation", "currentScope", "getCurrentScope", "createScope", "parent_", "immer_", "drafts_", "canAutoFreeze_", "unfinalizedDrafts_", "usePatchesInScope", "scope", "patchListener", "patches_", "inversePatches_", "patchListener_", "revokeScope", "leaveScope", "revokeDraft", "enterScope", "immer", "draft", "revoke_", "revoked_", "processResult", "result", "baseDraft", "isReplaced", "undefined", "useProxies_", "willFinalizeES5_", "modified_", "finalize", "<PERSON><PERSON><PERSON><PERSON>", "generateReplacementPatches_", "rootScope", "childValue", "finalizeProperty", "scope_", "finalized_", "draft_", "resultEach", "generatePatches_", "parentState", "targetObject", "rootPath", "targetIsSet", "assigned_", "autoFreeze_", "createProxyProxy", "parent", "isManual_", "traps", "objectTraps", "arrayTraps", "revoke", "proxy", "source", "readPropFromProto", "peek", "prepareCopy", "createProxy", "getDescriptorFromProto", "current", "currentState", "<PERSON><PERSON><PERSON><PERSON>", "Number", "isNaN", "deleteProperty", "owner", "defineProperty", "setPrototypeOf", "fn", "arguments", "parseInt", "Immer", "config", "recipe", "defaultBase", "self", "curriedProduce", "produce", "<PERSON><PERSON><PERSON><PERSON>", "Promise", "then", "p", "ip", "produceWithPatches", "patches", "inversePatches", "nextState", "useProxies", "setUseProxies", "autoFreeze", "setAutoFreeze", "createDraft", "finishDraft", "applyPatches", "patch", "applyPatchesImpl", "applyPatches_", "proxyMap_", "proxySet_", "createES5Proxy_", "push", "currentImpl", "copy", "archType", "hasChanges_", "copyHelper", "from", "enableES5", "mark<PERSON>hangesRecursively", "mark<PERSON><PERSON>esSweep", "createES5Draft", "proxyProperty", "assertUnrevoked", "drafts", "hasArrayChanges", "hasObjectChanges", "object", "min", "Math", "baseValue", "baseIsDraft", "descriptor", "JSON", "stringify", "enablePatches", "REPLACE", "ADD", "REMOVE", "basePath", "generatePatchesFromAssigned", "generateArrayPatches", "generateSetPatches", "clonePatchValueIfNeeded", "assignedValue", "origValue", "unshift", "replacement", "parentType", "join", "type", "deepClonePatchValue", "splice", "map", "entries", "k", "v", "cloned", "immerable", "enableMapSet", "extendStatics", "d", "b", "__proto__", "__extends", "__", "DraftMap", "_super", "size", "prepareMapCopy", "cb", "thisArg", "_value", "_map", "values", "next", "r", "done", "DraftSet", "prepareSetCopy", "enableAllPlugins", "bind", "castDraft", "castImmutable"], "mappings": ";;;;;;;;CAAA;CAEA;CAEA;CACA,IAAMA,SAAS,GACd,OAAOC,MAAP,KAAkB,WAAlB,IAAiC;CAAA;CAAOA,MAAM,CAAC,GAAD,CAAb,KAAuB,QADzD;CAEO,IAAMC,MAAM,GAAG,OAAOC,GAAP,KAAe,WAA9B;CACA,IAAMC,MAAM,GAAG,OAAOC,GAAP,KAAe,WAA9B;CACA,IAAMC,UAAU,GACtB,OAAOC,KAAP,KAAiB,WAAjB,IACA,OAAOA,KAAK,CAACC,SAAb,KAA2B,WAD3B,IAEA,OAAOC,OAAP,KAAmB,WAHb;CAKP;;;;KAGaC,OAAO,GAAYV,SAAS;CAAA;CACtCC,MAAM,CAACU,GAAP,CAAW,eAAX,CADsC,oBAEnC,eAFmC,IAEjB,IAFiB;CAIzC;;;;;;;;;KAQaC,SAAS,GAAkBZ,SAAS;CAAA;CAC9CC,MAAM,CAACU,GAAP,CAAW,iBAAX,CAD8C,GAE7C;CAEG,IAAME,WAAW,GAAkBb,SAAS;CAAA;CAChDC,MAAM,CAACU,GAAP,CAAW,aAAX,CADgD,GAE/C,gBAFG;;CAKA,IAAMG,cAAc,GACzB,OAAOb,MAAP,IAAiB,WAAjB,IAAgCA,MAAM,CAACc,QAAxC,IAAsD,YADhD;;CCtCP,IAAMC,MAAM,GAAG;CACd,KAAG,eADW;CAEd,KAAG,8CAFW;CAGd,KAAG,uDAHW;CAId,GAJc,aAIZC,IAJY;CAKb,WACC,yHACAA,IAFD;CAIA,GATa;CAUd,KAAG,mHAVW;CAWd,KAAG,mCAXW;CAYd,KAAG,8DAZW;CAad,KAAG,iEAbW;CAcd,KAAG,0FAdW;CAed,KAAG,2EAfW;CAgBd,MAAI,sCAhBU;CAiBd,MAAI,0DAjBU;CAkBd,MAAI,0DAlBU;CAmBd,MAAI,4CAnBU;CAoBd,MAAI,qEApBU;CAqBd,IArBc,aAqBXC,IArBW;CAsBb,WAAO,+CAA+CA,IAAtD;CACA,GAvBa;CAwBd,MAAI,qCAxBU;CAyBd,IAzBc,aAyBXC,EAzBW;CA0Bb,WAAO,kCAAkCA,EAAzC;CACA,GA3Ba;CA4Bd,IA5Bc,aA4BXC,MA5BW;CA6Bb,gCAA0BA,MAA1B,uFAAmHA,MAAnH;CACA,GA9Ba;CA+Bd,MAAI,2EA/BU;CAgCd,IAhCc,aAgCXC,KAhCW;CAiCb,mKAA6JA,KAA7J;CACA,GAlCa;CAmCd,IAnCc,aAmCXA,KAnCW;CAoCb,gDAA0CA,KAA1C;CACA,GArCa;CAsCd,IAtCc,aAsCXA,KAtCW;CAuCb,iDAA2CA,KAA3C;CACA,GAxCa;CAyCd,MAAI;CAzCU,CAAf;AA4CA,UAAgBC,IAAIC;qCAA+BC;CAAAA,IAAAA;;;CAClD,EAAa;CACZ,QAAMC,CAAC,GAAGT,MAAM,CAACO,KAAD,CAAhB;CACA,QAAMG,GAAG,GAAG,CAACD,CAAD,GACT,uBAAuBF,KADd,GAET,OAAOE,CAAP,KAAa,UAAb,GACAA,CAAC,CAACE,KAAF,CAAQ,IAAR,EAAcH,IAAd,CADA,GAEAC,CAJH;CAKA,UAAM,IAAIG,KAAJ,cAAqBF,GAArB,CAAN;CACA;CAMD;;CC5CD;;CACA;;AACA,UAAgBG,QAAQC;CACvB,SAAO,CAAC,CAACA,KAAF,IAAW,CAAC,CAACA,KAAK,CAACjB,WAAD,CAAzB;CACA;CAED;;CACA;;AACA,UAAgBkB,YAAYD;;;CAC3B,MAAI,CAACA,KAAL,EAAY,OAAO,KAAP;CACZ,SACCE,aAAa,CAACF,KAAD,CAAb,IACAG,KAAK,CAACC,OAAN,CAAcJ,KAAd,CADA,IAEA,CAAC,CAACA,KAAK,CAAClB,SAAD,CAFP,IAGA,CAAC,wBAACkB,KAAK,CAACK,WAAP,uDAAC,mBAAoBvB,SAApB,CAAD,CAHD,IAIAwB,KAAK,CAACN,KAAD,CAJL,IAKAO,KAAK,CAACP,KAAD,CANN;CAQA;CAED,IAAMQ,gBAAgB;CAAA;CAAGC,MAAM,CAACC,SAAP,CAAiBL,WAAjB,CAA6BM,QAA7B,EAAzB;CACA;;AACA,UAAgBT,cAAcF;CAC7B,MAAI,CAACA,KAAD,IAAU,OAAOA,KAAP,KAAiB,QAA/B,EAAyC,OAAO,KAAP;CACzC,MAAMY,KAAK,GAAGH,MAAM,CAACI,cAAP,CAAsBb,KAAtB,CAAd;;CACA,MAAIY,KAAK,KAAK,IAAd,EAAoB;CACnB,WAAO,IAAP;CACA;;CACD,MAAME,IAAI,GACTL,MAAM,CAACM,cAAP,CAAsBC,IAAtB,CAA2BJ,KAA3B,EAAkC,aAAlC,KAAoDA,KAAK,CAACP,WAD3D;CAGA,MAAIS,IAAI,KAAKL,MAAb,EAAqB,OAAO,IAAP;CAErB,SACC,OAAOK,IAAP,IAAe,UAAf,IACAG,QAAQ,CAACN,QAAT,CAAkBK,IAAlB,CAAuBF,IAAvB,MAAiCN,gBAFlC;CAIA;AAKD,UAAgBU,SAASlB;CACxB,MAAI,CAACD,OAAO,CAACC,KAAD,CAAZ,EAAqBR,GAAG,CAAC,EAAD,EAAKQ,KAAL,CAAH;CACrB,SAAOA,KAAK,CAACjB,WAAD,CAAL,CAAmBoC,KAA1B;CACA;CAED;;AACA,CAAO,IAAMC,OAAO,GACnB,OAAOzC,OAAP,KAAmB,WAAnB,IAAkCA,OAAO,CAACyC,OAA1C,GACGzC,OAAO,CAACyC,OADX,GAEG,OAAOX,MAAM,CAACY,qBAAd,KAAwC,WAAxC,GACA,UAAAC,GAAG;CAAA,SACHb,MAAM,CAACc,mBAAP,CAA2BD,GAA3B,EAAgCE,MAAhC,CACCf,MAAM,CAACY,qBAAP,CAA6BC,GAA7B,CADD,CADG;CAAA,CADH;CAKA;CAA2Bb,MAAM,CAACc,mBAR/B;AAUP,CAAO,IAAME,yBAAyB,GACrChB,MAAM,CAACgB,yBAAP,IACA,SAASA,yBAAT,CAAmCC,MAAnC;CACC;CACA,MAAMC,GAAG,GAAQ,EAAjB;CACAP,EAAAA,OAAO,CAACM,MAAD,CAAP,CAAgBE,OAAhB,CAAwB,UAAAC,GAAG;CAC1BF,IAAAA,GAAG,CAACE,GAAD,CAAH,GAAWpB,MAAM,CAACqB,wBAAP,CAAgCJ,MAAhC,EAAwCG,GAAxC,CAAX;CACA,GAFD;CAGA,SAAOF,GAAP;CACA,CATK;AAgBP,UAAgBI,KAAKT,KAAUU,MAAWC;OAAAA;CAAAA,IAAAA,iBAAiB;;;CAC1D,MAAIC,WAAW,CAACZ,GAAD,CAAX;;CAAJ,IAA0C;AACzC,CAAC,OAACW,cAAc,GAAGxB,MAAM,CAAC0B,IAAV,GAAiBf,OAAhC,EAAyCE,GAAzC,EAA8CM,OAA9C,CAAsD,UAAAC,GAAG;CACzD,YAAI,CAACI,cAAD,IAAmB,OAAOJ,GAAP,KAAe,QAAtC,EAAgDG,IAAI,CAACH,GAAD,EAAMP,GAAG,CAACO,GAAD,CAAT,EAAgBP,GAAhB,CAAJ;CAChD,OAFA;CAGD,KAJD,MAIO;CACNA,IAAAA,GAAG,CAACM,OAAJ,CAAY,UAACQ,KAAD,EAAaC,KAAb;CAAA,aAA4BL,IAAI,CAACK,KAAD,EAAQD,KAAR,EAAed,GAAf,CAAhC;CAAA,KAAZ;CACA;CACD;CAED;;AACA,UAAgBY,YAAY3C;CAC3B;CACA,MAAM+C,KAAK,GAA2B/C,KAAK,CAACR,WAAD,CAA3C;CACA,SAAOuD,KAAK,GACTA,KAAK,CAACC,KAAN,GAAc,CAAd,GACCD,KAAK,CAACC,KAAN,GAAc,CADf;CAAA,IAEED,KAAK,CAACC,KAHC;CAAA,IAITpC,KAAK,CAACC,OAAN,CAAcb,KAAd;;CAAA,IAEAe,KAAK,CAACf,KAAD,CAAL;;CAAA,IAEAgB,KAAK,CAAChB,KAAD,CAAL;;CAAA;;CARH;CAWA;CAED;;AACA,UAAgBiD,IAAIjD,OAAYkD;CAC/B,SAAOP,WAAW,CAAC3C,KAAD,CAAX;;CAAA,IACJA,KAAK,CAACiD,GAAN,CAAUC,IAAV,CADI,GAEJhC,MAAM,CAACC,SAAP,CAAiBK,cAAjB,CAAgCC,IAAhC,CAAqCzB,KAArC,EAA4CkD,IAA5C,CAFH;CAGA;CAED;;AACA,UAAgBC,IAAInD,OAA2BkD;CAC9C;CACA,SAAOP,WAAW,CAAC3C,KAAD,CAAX;;CAAA,IAAsCA,KAAK,CAACmD,GAAN,CAAUD,IAAV,CAAtC,GAAwDlD,KAAK,CAACkD,IAAD,CAApE;CACA;CAED;;AACA,UAAgBE,IAAIpD,OAAYqD,gBAA6B5C;CAC5D,MAAM6C,CAAC,GAAGX,WAAW,CAAC3C,KAAD,CAArB;CACA,MAAIsD,CAAC;;CAAL,IAAwBtD,KAAK,CAACoD,GAAN,CAAUC,cAAV,EAA0B5C,KAA1B,EAAxB,KACK,IAAI6C,CAAC;;CAAL,IAAwB;CAC5BtD,MAAAA,KAAK,CAACuD,GAAN,CAAU9C,KAAV;CACA,KAFI,MAEET,KAAK,CAACqD,cAAD,CAAL,GAAwB5C,KAAxB;CACP;CAED;;AACA,UAAgB+C,GAAGC,GAAQC;CAC1B;CACA,MAAID,CAAC,KAAKC,CAAV,EAAa;CACZ,WAAOD,CAAC,KAAK,CAAN,IAAW,IAAIA,CAAJ,KAAU,IAAIC,CAAhC;CACA,GAFD,MAEO;CACN,WAAOD,CAAC,KAAKA,CAAN,IAAWC,CAAC,KAAKA,CAAxB;CACA;CACD;CAED;;AACA,UAAgB3C,MAAMoB;CACrB,SAAOtD,MAAM,IAAIsD,MAAM,YAAYrD,GAAnC;CACA;CAED;;AACA,UAAgBkC,MAAMmB;CACrB,SAAOpD,MAAM,IAAIoD,MAAM,YAAYnD,GAAnC;CACA;CACD;;AACA,UAAgB2E,OAAOZ;CACtB,SAAOA,KAAK,CAACa,KAAN,IAAeb,KAAK,CAACnB,KAA5B;CACA;CAED;;AACA,UAAgBiC,YAAYC;CAC3B,MAAIlD,KAAK,CAACC,OAAN,CAAciD,IAAd,CAAJ,EAAyB,OAAOlD,KAAK,CAACO,SAAN,CAAgB4C,KAAhB,CAAsBtC,IAAtB,CAA2BqC,IAA3B,CAAP;CACzB,MAAME,WAAW,GAAG9B,yBAAyB,CAAC4B,IAAD,CAA7C;CACA,SAAOE,WAAW,CAACxE,WAAD,CAAlB;CACA,MAAIoD,IAAI,GAAGf,OAAO,CAACmC,WAAD,CAAlB;;CACA,OAAK,IAAIC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrB,IAAI,CAACsB,MAAzB,EAAiCD,CAAC,EAAlC,EAAsC;CACrC,QAAM3B,GAAG,GAAQM,IAAI,CAACqB,CAAD,CAArB;CACA,QAAME,IAAI,GAAGH,WAAW,CAAC1B,GAAD,CAAxB;;CACA,QAAI6B,IAAI,CAACC,QAAL,KAAkB,KAAtB,EAA6B;CAC5BD,MAAAA,IAAI,CAACC,QAAL,GAAgB,IAAhB;CACAD,MAAAA,IAAI,CAACE,YAAL,GAAoB,IAApB;CACA,KANoC;CAQrC;CACA;;;CACA,QAAIF,IAAI,CAAChB,GAAL,IAAYgB,IAAI,CAACf,GAArB,EACCY,WAAW,CAAC1B,GAAD,CAAX,GAAmB;CAClB+B,MAAAA,YAAY,EAAE,IADI;CAElBD,MAAAA,QAAQ,EAAE,IAFQ;CAGlBE,MAAAA,UAAU,EAAEH,IAAI,CAACG,UAHC;CAIlB7D,MAAAA,KAAK,EAAEqD,IAAI,CAACxB,GAAD;CAJO,KAAnB;CAMD;;CACD,SAAOpB,MAAM,CAACqD,MAAP,CAAcrD,MAAM,CAACI,cAAP,CAAsBwC,IAAtB,CAAd,EAA2CE,WAA3C,CAAP;CACA;AAUD,UAAgBQ,OAAUzC,KAAU0C;OAAAA;CAAAA,IAAAA,OAAgB;;;CACnD,MAAIC,QAAQ,CAAC3C,GAAD,CAAR,IAAiBvB,OAAO,CAACuB,GAAD,CAAxB,IAAiC,CAACrB,WAAW,CAACqB,GAAD,CAAjD,EAAwD,OAAOA,GAAP;;CACxD,MAAIY,WAAW,CAACZ,GAAD,CAAX,GAAmB;CAAE;CAAzB,IAA2C;CAC1CA,MAAAA,GAAG,CAACqB,GAAJ,GAAUrB,GAAG,CAACwB,GAAJ,GAAUxB,GAAG,CAAC4C,KAAJ,GAAY5C,GAAG,CAAC6C,MAAJ,GAAaC,2BAA7C;CACA;;CACD3D,EAAAA,MAAM,CAACsD,MAAP,CAAczC,GAAd;CACA,MAAI0C,IAAJ,EAAUjC,IAAI,CAACT,GAAD,EAAM,UAACO,GAAD,EAAM7B,KAAN;CAAA,WAAgB+D,MAAM,CAAC/D,KAAD,EAAQ,IAAR,CAAtB;CAAA,GAAN,EAA2C,IAA3C,CAAJ;CACV,SAAOsB,GAAP;CACA;;CAED,SAAS8C,2BAAT;CACC5E,EAAAA,GAAG,CAAC,CAAD,CAAH;CACA;;AAED,UAAgByE,SAAS3C;CACxB,MAAIA,GAAG,IAAI,IAAP,IAAe,OAAOA,GAAP,KAAe,QAAlC,EAA4C,OAAO,IAAP;;CAE5C,SAAOb,MAAM,CAACwD,QAAP,CAAgB3C,GAAhB,CAAP;CACA;;CC1MD;;CACA,IAAM+C,OAAO,GA4BT,EA5BJ;AAgCA,UAAgBC,UACfC;CAEA,MAAMjF,MAAM,GAAG+E,OAAO,CAACE,SAAD,CAAtB;;CACA,MAAI,CAACjF,MAAL,EAAa;CACZE,IAAAA,GAAG,CAAC,EAAD,EAAK+E,SAAL,CAAH;CACA;;;CAED,SAAOjF,MAAP;CACA;AAED,UAAgBkF,WACfD,WACAE;CAEA,MAAI,CAACJ,OAAO,CAACE,SAAD,CAAZ,EAAyBF,OAAO,CAACE,SAAD,CAAP,GAAqBE,cAArB;CACzB;;CCrCD,IAAIC,YAAJ;AAEA,UAAgBC;CACf,MAAI,CAAW,CAACD,YAAhB,EAA8BlF,GAAG,CAAC,CAAD,CAAH;CAC9B,SAAOkF,YAAP;CACA;;CAED,SAASE,WAAT,CACCC,OADD,EAECC,MAFD;CAIC,SAAO;CACNC,IAAAA,OAAO,EAAE,EADH;CAENF,IAAAA,OAAO,EAAPA,OAFM;CAGNC,IAAAA,MAAM,EAANA,MAHM;CAIN;CACA;CACAE,IAAAA,cAAc,EAAE,IANV;CAONC,IAAAA,kBAAkB,EAAE;CAPd,GAAP;CASA;;AAED,UAAgBC,kBACfC,OACAC;CAEA,MAAIA,aAAJ,EAAmB;CAClBd,IAAAA,SAAS,CAAC,SAAD,CAAT,CADkB;;CAElBa,IAAAA,KAAK,CAACE,QAAN,GAAiB,EAAjB;CACAF,IAAAA,KAAK,CAACG,eAAN,GAAwB,EAAxB;CACAH,IAAAA,KAAK,CAACI,cAAN,GAAuBH,aAAvB;CACA;CACD;AAED,UAAgBI,YAAYL;CAC3BM,EAAAA,UAAU,CAACN,KAAD,CAAV;CACAA,EAAAA,KAAK,CAACJ,OAAN,CAAcnD,OAAd,CAAsB8D,WAAtB;;CAEAP,EAAAA,KAAK,CAACJ,OAAN,GAAgB,IAAhB;CACA;AAED,UAAgBU,WAAWN;CAC1B,MAAIA,KAAK,KAAKT,YAAd,EAA4B;CAC3BA,IAAAA,YAAY,GAAGS,KAAK,CAACN,OAArB;CACA;CACD;AAED,UAAgBc,WAAWC;CAC1B,SAAQlB,YAAY,GAAGE,WAAW,CAACF,YAAD,EAAekB,KAAf,CAAlC;CACA;;CAED,SAASF,WAAT,CAAqBG,KAArB;CACC,MAAMvD,KAAK,GAAeuD,KAAK,CAAC9G,WAAD,CAA/B;CACA,MACCuD,KAAK,CAACC,KAAN;;CAAA,KACAD,KAAK,CAACC,KAAN;;CAFD,IAICD,KAAK,CAACwD,OAAN,GAJD,KAKKxD,KAAK,CAACyD,QAAN,GAAiB,IAAjB;CACL;;UC/DeC,cAAcC,QAAad;CAC1CA,EAAAA,KAAK,CAACF,kBAAN,GAA2BE,KAAK,CAACJ,OAAN,CAActB,MAAzC;CACA,MAAMyC,SAAS,GAAGf,KAAK,CAACJ,OAAN,CAAe,CAAf,CAAlB;CACA,MAAMoB,UAAU,GAAGF,MAAM,KAAKG,SAAX,IAAwBH,MAAM,KAAKC,SAAtD;CACA,MAAI,CAACf,KAAK,CAACL,MAAN,CAAauB,WAAlB,EACC/B,SAAS,CAAC,KAAD,CAAT,CAAiBgC,gBAAjB,CAAkCnB,KAAlC,EAAyCc,MAAzC,EAAiDE,UAAjD;;CACD,MAAIA,UAAJ,EAAgB;CACf,QAAID,SAAS,CAACnH,WAAD,CAAT,CAAuBwH,SAA3B,EAAsC;CACrCf,MAAAA,WAAW,CAACL,KAAD,CAAX;CACA3F,MAAAA,GAAG,CAAC,CAAD,CAAH;CACA;;CACD,QAAIS,WAAW,CAACgG,MAAD,CAAf,EAAyB;CACxB;CACAA,MAAAA,MAAM,GAAGO,QAAQ,CAACrB,KAAD,EAAQc,MAAR,CAAjB;CACA,UAAI,CAACd,KAAK,CAACN,OAAX,EAAoB4B,WAAW,CAACtB,KAAD,EAAQc,MAAR,CAAX;CACpB;;CACD,QAAId,KAAK,CAACE,QAAV,EAAoB;CACnBf,MAAAA,SAAS,CAAC,SAAD,CAAT,CAAqBoC,2BAArB,CACCR,SAAS,CAACnH,WAAD,CAAT,CAAuBoC,KADxB,EAEC8E,MAFD,EAGCd,KAAK,CAACE,QAHP,EAICF,KAAK,CAACG,eAJP;CAMA;CACD,GAlBD,MAkBO;CACN;CACAW,IAAAA,MAAM,GAAGO,QAAQ,CAACrB,KAAD,EAAQe,SAAR,EAAmB,EAAnB,CAAjB;CACA;;CACDV,EAAAA,WAAW,CAACL,KAAD,CAAX;;CACA,MAAIA,KAAK,CAACE,QAAV,EAAoB;CACnBF,IAAAA,KAAK,CAACI,cAAN,CAAsBJ,KAAK,CAACE,QAA5B,EAAsCF,KAAK,CAACG,eAA5C;CACA;;CACD,SAAOW,MAAM,KAAKrH,OAAX,GAAqBqH,MAArB,GAA8BG,SAArC;CACA;;CAED,SAASI,QAAT,CAAkBG,SAAlB,EAAyC3G,KAAzC,EAAqDZ,IAArD;CACC;CACA,MAAI6E,QAAQ,CAACjE,KAAD,CAAZ,EAAqB,OAAOA,KAAP;CAErB,MAAMsC,KAAK,GAAetC,KAAK,CAACjB,WAAD,CAA/B;;CAEA,MAAI,CAACuD,KAAL,EAAY;CACXP,IAAAA,IAAI,CACH/B,KADG,EAEH,UAAC6B,GAAD,EAAM+E,UAAN;CAAA,aACCC,gBAAgB,CAACF,SAAD,EAAYrE,KAAZ,EAAmBtC,KAAnB,EAA0B6B,GAA1B,EAA+B+E,UAA/B,EAA2CxH,IAA3C,CADjB;CAAA,KAFG,EAIH,IAJG;CAAA,KAAJ;CAMA,WAAOY,KAAP;CACA;;;CAED,MAAIsC,KAAK,CAACwE,MAAN,KAAiBH,SAArB,EAAgC,OAAO3G,KAAP;;CAEhC,MAAI,CAACsC,KAAK,CAACiE,SAAX,EAAsB;CACrBE,IAAAA,WAAW,CAACE,SAAD,EAAYrE,KAAK,CAACnB,KAAlB,EAAyB,IAAzB,CAAX;CACA,WAAOmB,KAAK,CAACnB,KAAb;CACA;;;CAED,MAAI,CAACmB,KAAK,CAACyE,UAAX,EAAuB;CACtBzE,IAAAA,KAAK,CAACyE,UAAN,GAAmB,IAAnB;CACAzE,IAAAA,KAAK,CAACwE,MAAN,CAAa7B,kBAAb;CACA,QAAMgB,MAAM;CAEX3D,IAAAA,KAAK,CAACC,KAAN;;CAAA,OAAuCD,KAAK,CAACC,KAAN;;CAAvC,MACID,KAAK,CAACa,KAAN,GAAcC,WAAW,CAACd,KAAK,CAAC0E,MAAP,CAD7B,GAEG1E,KAAK,CAACa,KAJV,CAHsB;CAStB;CACA;CACA;;CACA,QAAI8D,UAAU,GAAGhB,MAAjB;CACA,QAAI1F,KAAK,GAAG,KAAZ;;CACA,QAAI+B,KAAK,CAACC,KAAN;;CAAJ,MAAmC;CAClC0E,QAAAA,UAAU,GAAG,IAAI1I,GAAJ,CAAQ0H,MAAR,CAAb;CACAA,QAAAA,MAAM,CAAC/B,KAAP;CACA3D,QAAAA,KAAK,GAAG,IAAR;CACA;;CACDwB,IAAAA,IAAI,CAACkF,UAAD,EAAa,UAACpF,GAAD,EAAM+E,UAAN;CAAA,aAChBC,gBAAgB,CAACF,SAAD,EAAYrE,KAAZ,EAAmB2D,MAAnB,EAA2BpE,GAA3B,EAAgC+E,UAAhC,EAA4CxH,IAA5C,EAAkDmB,KAAlD,CADA;CAAA,KAAb,CAAJ,CAnBsB;;CAuBtBkG,IAAAA,WAAW,CAACE,SAAD,EAAYV,MAAZ,EAAoB,KAApB,CAAX,CAvBsB;;CAyBtB,QAAI7G,IAAI,IAAIuH,SAAS,CAACtB,QAAtB,EAAgC;CAC/Bf,MAAAA,SAAS,CAAC,SAAD,CAAT,CAAqB4C,gBAArB,CACC5E,KADD,EAEClD,IAFD,EAGCuH,SAAS,CAACtB,QAHX,EAICsB,SAAS,CAACrB,eAJX;CAMA;CACD;;CACD,SAAOhD,KAAK,CAACa,KAAb;CACA;;CAED,SAAS0D,gBAAT,CACCF,SADD,EAECQ,WAFD,EAGCC,YAHD,EAIC3E,IAJD,EAKCmE,UALD,EAMCS,QAND,EAOCC,WAPD;CASC,MAAI,CAAWV,UAAU,KAAKQ,YAA9B,EAA4C5H,GAAG,CAAC,CAAD,CAAH;;CAC5C,MAAIO,OAAO,CAAC6G,UAAD,CAAX,EAAyB;CACxB,QAAMxH,IAAI,GACTiI,QAAQ,IACRF,WADA,IAEAA,WAAY,CAAC5E,KAAb;;CAFA;CAGA,KAACC,GAAG,CAAE2E,WAA6C,CAACI,SAAhD,EAA4D9E,IAA5D,CAHJ;CAAA,MAIG4E,QAAS,CAAC7F,MAAV,CAAiBiB,IAAjB,CAJH,GAKG2D,SANJ,CADwB;;CASxB,QAAMzE,GAAG,GAAG6E,QAAQ,CAACG,SAAD,EAAYC,UAAZ,EAAwBxH,IAAxB,CAApB;CACAuD,IAAAA,GAAG,CAACyE,YAAD,EAAe3E,IAAf,EAAqBd,GAArB,CAAH,CAVwB;CAYxB;;CACA,QAAI5B,OAAO,CAAC4B,GAAD,CAAX,EAAkB;CACjBgF,MAAAA,SAAS,CAAC3B,cAAV,GAA2B,KAA3B;CACA,KAFD,MAEO;CACP,GAhBD,MAgBO,IAAIsC,WAAJ,EAAiB;CACvBF,IAAAA,YAAY,CAACtE,GAAb,CAAiB8D,UAAjB;CACA;;;CAED,MAAI3G,WAAW,CAAC2G,UAAD,CAAX,IAA2B,CAAC3C,QAAQ,CAAC2C,UAAD,CAAxC,EAAsD;CACrD,QAAI,CAACD,SAAS,CAAC7B,MAAV,CAAiB0C,WAAlB,IAAiCb,SAAS,CAAC1B,kBAAV,GAA+B,CAApE,EAAuE;CACtE;CACA;CACA;CACA;CACA;CACA;CACA;;CACDuB,IAAAA,QAAQ,CAACG,SAAD,EAAYC,UAAZ,CAAR,CATqD;;CAWrD,QAAI,CAACO,WAAD,IAAgB,CAACA,WAAW,CAACL,MAAZ,CAAmBjC,OAAxC,EACC4B,WAAW,CAACE,SAAD,EAAYC,UAAZ,CAAX;CACD;CACD;;CAED,SAASH,WAAT,CAAqBtB,KAArB,EAAwCnF,KAAxC,EAAoDgE,IAApD;OAAoDA;CAAAA,IAAAA,OAAO;;;CAC1D;CACA,MAAI,CAACmB,KAAK,CAACN,OAAP,IAAkBM,KAAK,CAACL,MAAN,CAAa0C,WAA/B,IAA8CrC,KAAK,CAACH,cAAxD,EAAwE;CACvEjB,IAAAA,MAAM,CAAC/D,KAAD,EAAQgE,IAAR,CAAN;CACA;CACD;;CC3HD;;;;;;AAKA,UAAgByD,iBACfpE,MACAqE;CAEA,MAAMtH,OAAO,GAAGD,KAAK,CAACC,OAAN,CAAciD,IAAd,CAAhB;CACA,MAAMf,KAAK,GAAe;CACzBC,IAAAA,KAAK,EAAEnC,OAAO;;CAAA,MAA2B;;CADhB;CAEzB;CACA0G,IAAAA,MAAM,EAAEY,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAHvB;CAIzB;CACA4B,IAAAA,SAAS,EAAE,KALc;CAMzB;CACAQ,IAAAA,UAAU,EAAE,KAPa;CAQzB;CACAQ,IAAAA,SAAS,EAAE,EATc;CAUzB;CACA1C,IAAAA,OAAO,EAAE6C,MAXgB;CAYzB;CACAvG,IAAAA,KAAK,EAAEkC,IAbkB;CAczB;CACA2D,IAAAA,MAAM,EAAE,IAfiB;CAgBzB;CACA7D,IAAAA,KAAK,EAAE,IAjBkB;CAkBzB;CACA2C,IAAAA,OAAO,EAAE,IAnBgB;CAoBzB6B,IAAAA,SAAS,EAAE;CApBc,GAA1B;CAwBA;CACA;CACA;CACA;CACA;;CACA,MAAIjG,MAAM,GAAMY,KAAhB;CACA,MAAIsF,KAAK,GAAsCC,WAA/C;;CACA,MAAIzH,OAAJ,EAAa;CACZsB,IAAAA,MAAM,GAAG,CAACY,KAAD,CAAT;CACAsF,IAAAA,KAAK,GAAGE,UAAR;CACA;;0BAEuBrJ,KAAK,CAACC,SAAN,CAAgBgD,MAAhB,EAAwBkG,KAAxB;OAAjBG,0BAAAA;OAAQC,yBAAAA;;CACf1F,EAAAA,KAAK,CAAC0E,MAAN,GAAegB,KAAf;CACA1F,EAAAA,KAAK,CAACwD,OAAN,GAAgBiC,MAAhB;CACA,SAAOC,KAAP;CACA;CAED;;;;AAGA,CAAO,IAAMH,WAAW,GAA6B;CACpDnF,EAAAA,GADoD,eAChDJ,KADgD,EACzCG,IADyC;CAEnD,QAAIA,IAAI,KAAK1D,WAAb,EAA0B,OAAOuD,KAAP;CAE1B,QAAM2F,MAAM,GAAG/E,MAAM,CAACZ,KAAD,CAArB;;CACA,QAAI,CAACE,GAAG,CAACyF,MAAD,EAASxF,IAAT,CAAR,EAAwB;CACvB;CACA,aAAOyF,iBAAiB,CAAC5F,KAAD,EAAQ2F,MAAR,EAAgBxF,IAAhB,CAAxB;CACA;;CACD,QAAMzC,KAAK,GAAGiI,MAAM,CAACxF,IAAD,CAApB;;CACA,QAAIH,KAAK,CAACyE,UAAN,IAAoB,CAAC9G,WAAW,CAACD,KAAD,CAApC,EAA6C;CAC5C,aAAOA,KAAP;CACA;CAED;;;CACA,QAAIA,KAAK,KAAKmI,IAAI,CAAC7F,KAAK,CAACnB,KAAP,EAAcsB,IAAd,CAAlB,EAAuC;CACtC2F,MAAAA,WAAW,CAAC9F,KAAD,CAAX;CACA,aAAQA,KAAK,CAACa,KAAN,CAAaV,IAAb,IAA4B4F,WAAW,CAC9C/F,KAAK,CAACwE,MAAN,CAAahC,MADiC,EAE9C9E,KAF8C,EAG9CsC,KAH8C,CAA/C;CAKA;;CACD,WAAOtC,KAAP;CACA,GAxBmD;CAyBpDwC,EAAAA,GAzBoD,eAyBhDF,KAzBgD,EAyBzCG,IAzByC;CA0BnD,WAAOA,IAAI,IAAIS,MAAM,CAACZ,KAAD,CAArB;CACA,GA3BmD;CA4BpDlB,EAAAA,OA5BoD,mBA4B5CkB,KA5B4C;CA6BnD,WAAO3D,OAAO,CAACyC,OAAR,CAAgB8B,MAAM,CAACZ,KAAD,CAAtB,CAAP;CACA,GA9BmD;CA+BpDK,EAAAA,GA/BoD,eAgCnDL,KAhCmD,EAiCnDG;CAAa;CAjCsC,IAkCnDzC,KAlCmD;CAoCnD,QAAM0D,IAAI,GAAG4E,sBAAsB,CAACpF,MAAM,CAACZ,KAAD,CAAP,EAAgBG,IAAhB,CAAnC;;CACA,QAAIiB,IAAJ,aAAIA,IAAJ,uBAAIA,IAAI,CAAEf,GAAV,EAAe;CACd;CACA;CACAe,MAAAA,IAAI,CAACf,GAAL,CAAS3B,IAAT,CAAcsB,KAAK,CAAC0E,MAApB,EAA4BhH,KAA5B;CACA,aAAO,IAAP;CACA;;CACD,QAAI,CAACsC,KAAK,CAACiE,SAAX,EAAsB;CACrB;CACA;CACA,UAAMgC,OAAO,GAAGJ,IAAI,CAACjF,MAAM,CAACZ,KAAD,CAAP,EAAgBG,IAAhB,CAApB,CAHqB;;CAKrB,UAAM+F,YAAY,GAAqBD,OAArB,aAAqBA,OAArB,uBAAqBA,OAAO,CAAGxJ,WAAH,CAA9C;;CACA,UAAIyJ,YAAY,IAAIA,YAAY,CAACrH,KAAb,KAAuBnB,KAA3C,EAAkD;CACjDsC,QAAAA,KAAK,CAACa,KAAN,CAAaV,IAAb,IAAqBzC,KAArB;CACAsC,QAAAA,KAAK,CAACiF,SAAN,CAAgB9E,IAAhB,IAAwB,KAAxB;CACA,eAAO,IAAP;CACA;;CACD,UAAIM,EAAE,CAAC/C,KAAD,EAAQuI,OAAR,CAAF,KAAuBvI,KAAK,KAAKoG,SAAV,IAAuB5D,GAAG,CAACF,KAAK,CAACnB,KAAP,EAAcsB,IAAd,CAAjD,CAAJ,EACC,OAAO,IAAP;CACD2F,MAAAA,WAAW,CAAC9F,KAAD,CAAX;CACAmG,MAAAA,WAAW,CAACnG,KAAD,CAAX;CACA;;CAED,QACEA,KAAK,CAACa,KAAN,CAAaV,IAAb,MAAuBzC,KAAvB;CAECA,IAAAA,KAAK,KAAKoG,SAAV,IAAuB3D,IAAI,IAAIH,KAAK,CAACa,KAFtC,CAAD;CAICuF,IAAAA,MAAM,CAACC,KAAP,CAAa3I,KAAb,KAAuB0I,MAAM,CAACC,KAAP,CAAarG,KAAK,CAACa,KAAN,CAAaV,IAAb,CAAb,CALzB,EAOC,OAAO,IAAP;;CAGDH,IAAAA,KAAK,CAACa,KAAN,CAAaV,IAAb,IAAqBzC,KAArB;CACAsC,IAAAA,KAAK,CAACiF,SAAN,CAAgB9E,IAAhB,IAAwB,IAAxB;CACA,WAAO,IAAP;CACA,GAzEmD;CA0EpDmG,EAAAA,cA1EoD,0BA0ErCtG,KA1EqC,EA0E9BG,IA1E8B;CA2EnD;CACA,QAAI0F,IAAI,CAAC7F,KAAK,CAACnB,KAAP,EAAcsB,IAAd,CAAJ,KAA4B2D,SAA5B,IAAyC3D,IAAI,IAAIH,KAAK,CAACnB,KAA3D,EAAkE;CACjEmB,MAAAA,KAAK,CAACiF,SAAN,CAAgB9E,IAAhB,IAAwB,KAAxB;CACA2F,MAAAA,WAAW,CAAC9F,KAAD,CAAX;CACAmG,MAAAA,WAAW,CAACnG,KAAD,CAAX;CACA,KAJD,MAIO;CACN;CACA,aAAOA,KAAK,CAACiF,SAAN,CAAgB9E,IAAhB,CAAP;CACA;;;CAED,QAAIH,KAAK,CAACa,KAAV,EAAiB,OAAOb,KAAK,CAACa,KAAN,CAAYV,IAAZ,CAAP;CACjB,WAAO,IAAP;CACA,GAvFmD;CAwFpD;CACA;CACAX,EAAAA,wBA1FoD,oCA0F3BQ,KA1F2B,EA0FpBG,IA1FoB;CA2FnD,QAAMoG,KAAK,GAAG3F,MAAM,CAACZ,KAAD,CAApB;CACA,QAAMoB,IAAI,GAAG/E,OAAO,CAACmD,wBAAR,CAAiC+G,KAAjC,EAAwCpG,IAAxC,CAAb;CACA,QAAI,CAACiB,IAAL,EAAW,OAAOA,IAAP;CACX,WAAO;CACNC,MAAAA,QAAQ,EAAE,IADJ;CAENC,MAAAA,YAAY,EAAEtB,KAAK,CAACC,KAAN;;CAAA,SAAwCE,IAAI,KAAK,QAFzD;CAGNoB,MAAAA,UAAU,EAAEH,IAAI,CAACG,UAHX;CAIN7D,MAAAA,KAAK,EAAE6I,KAAK,CAACpG,IAAD;CAJN,KAAP;CAMA,GApGmD;CAqGpDqG,EAAAA,cArGoD;CAsGnDtJ,IAAAA,GAAG,CAAC,EAAD,CAAH;CACA,GAvGmD;CAwGpDqB,EAAAA,cAxGoD,0BAwGrCyB,KAxGqC;CAyGnD,WAAO7B,MAAM,CAACI,cAAP,CAAsByB,KAAK,CAACnB,KAA5B,CAAP;CACA,GA1GmD;CA2GpD4H,EAAAA,cA3GoD;CA4GnDvJ,IAAAA,GAAG,CAAC,EAAD,CAAH;CACA;CA7GmD,CAA9C;CAgHP;;;;CAIA,IAAMsI,UAAU,GAAoC,EAApD;CACA/F,IAAI,CAAC8F,WAAD,EAAc,UAAChG,GAAD,EAAMmH,EAAN;CACjB;CACAlB,EAAAA,UAAU,CAACjG,GAAD,CAAV,GAAkB;CACjBoH,IAAAA,SAAS,CAAC,CAAD,CAAT,GAAeA,SAAS,CAAC,CAAD,CAAT,CAAa,CAAb,CAAf;CACA,WAAOD,EAAE,CAACnJ,KAAH,CAAS,IAAT,EAAeoJ,SAAf,CAAP;CACA,GAHD;CAIA,CANG,CAAJ;;CAOAnB,UAAU,CAACc,cAAX,GAA4B,UAAStG,KAAT,EAAgBG,IAAhB;CAC3B,MAAI,CAAWkG,KAAK,CAACO,QAAQ,CAACzG,IAAD,CAAT,CAApB,EAA6CjD,GAAG,CAAC,EAAD,CAAH;;CAE7C,SAAOsI,UAAU,CAACnF,GAAX,CAAgB3B,IAAhB,CAAqB,IAArB,EAA2BsB,KAA3B,EAAkCG,IAAlC,EAAwC2D,SAAxC,CAAP;CACA,CAJD;;CAKA0B,UAAU,CAACnF,GAAX,GAAiB,UAASL,KAAT,EAAgBG,IAAhB,EAAsBzC,KAAtB;CAChB,MAAI,CAAWyC,IAAI,KAAK,QAApB,IAAgCkG,KAAK,CAACO,QAAQ,CAACzG,IAAD,CAAT,CAAzC,EAAkEjD,GAAG,CAAC,EAAD,CAAH;CAClE,SAAOqI,WAAW,CAAClF,GAAZ,CAAiB3B,IAAjB,CAAsB,IAAtB,EAA4BsB,KAAK,CAAC,CAAD,CAAjC,EAAsCG,IAAtC,EAA4CzC,KAA5C,EAAmDsC,KAAK,CAAC,CAAD,CAAxD,CAAP;CACA,CAHD;;;CAMA,SAAS6F,IAAT,CAActC,KAAd,EAA8BpD,IAA9B;CACC,MAAMH,KAAK,GAAGuD,KAAK,CAAC9G,WAAD,CAAnB;CACA,MAAMkJ,MAAM,GAAG3F,KAAK,GAAGY,MAAM,CAACZ,KAAD,CAAT,GAAmBuD,KAAvC;CACA,SAAOoC,MAAM,CAACxF,IAAD,CAAb;CACA;;CAED,SAASyF,iBAAT,CAA2B5F,KAA3B,EAA8C2F,MAA9C,EAA2DxF,IAA3D;;;CACC,MAAMiB,IAAI,GAAG4E,sBAAsB,CAACL,MAAD,EAASxF,IAAT,CAAnC;CACA,SAAOiB,IAAI,GACR,WAAWA,IAAX,GACCA,IAAI,CAAC1D,KADN;CAGC;CAHD,eAIC0D,IAAI,CAAChB,GAJN,8CAIC,UAAU1B,IAAV,CAAesB,KAAK,CAAC0E,MAArB,CALO,GAMRZ,SANH;CAOA;;CAED,SAASkC,sBAAT,CACCL,MADD,EAECxF,IAFD;CAIC;CACA,MAAI,EAAEA,IAAI,IAAIwF,MAAV,CAAJ,EAAuB,OAAO7B,SAAP;CACvB,MAAIxF,KAAK,GAAGH,MAAM,CAACI,cAAP,CAAsBoH,MAAtB,CAAZ;;CACA,SAAOrH,KAAP,EAAc;CACb,QAAM8C,IAAI,GAAGjD,MAAM,CAACqB,wBAAP,CAAgClB,KAAhC,EAAuC6B,IAAvC,CAAb;CACA,QAAIiB,IAAJ,EAAU,OAAOA,IAAP;CACV9C,IAAAA,KAAK,GAAGH,MAAM,CAACI,cAAP,CAAsBD,KAAtB,CAAR;CACA;;CACD,SAAOwF,SAAP;CACA;;AAED,UAAgBqC,YAAYnG;CAC3B,MAAI,CAACA,KAAK,CAACiE,SAAX,EAAsB;CACrBjE,IAAAA,KAAK,CAACiE,SAAN,GAAkB,IAAlB;;CACA,QAAIjE,KAAK,CAACuC,OAAV,EAAmB;CAClB4D,MAAAA,WAAW,CAACnG,KAAK,CAACuC,OAAP,CAAX;CACA;CACD;CACD;AAED,UAAgBuD,YAAY9F;CAC3B,MAAI,CAACA,KAAK,CAACa,KAAX,EAAkB;CACjBb,IAAAA,KAAK,CAACa,KAAN,GAAcC,WAAW,CAACd,KAAK,CAACnB,KAAP,CAAzB;CACA;CACD;;KCrPYgI,KAAb;CAAA;CAAA;CAKC,iBAAYC,MAAZ;;;CAJA,oBAAA,GAAuB5K,UAAvB;CAEA,oBAAA,GAAuB,IAAvB;CASA;;;;;;;;;;;;;;;;;;;;CAmBA,gBAAA,GAAoB,UAAC6E,IAAD,EAAYgG,MAAZ,EAA0BjE,aAA1B;CACnB;CACA,UAAI,OAAO/B,IAAP,KAAgB,UAAhB,IAA8B,OAAOgG,MAAP,KAAkB,UAApD,EAAgE;CAC/D,YAAMC,WAAW,GAAGD,MAApB;CACAA,QAAAA,MAAM,GAAGhG,IAAT;CAEA,YAAMkG,IAAI,GAAG,KAAb;CACA,eAAO,SAASC,cAAT,CAENnG,IAFM;;;eAENA;CAAAA,YAAAA,OAAOiG;;;6CACJ5J;CAAAA,YAAAA;;;CAEH,iBAAO6J,IAAI,CAACE,OAAL,CAAapG,IAAb,EAAmB,UAACwC,KAAD;CAAA;;CAAA,mBAAoB,WAAAwD,MAAM,EAACrI,IAAP,iBAAY,MAAZ,EAAkB6E,KAAlB,SAA4BnG,IAA5B,EAApB;CAAA,WAAnB,CAAP;CACA,SAND;CAOA;;CAED,UAAI,OAAO2J,MAAP,KAAkB,UAAtB,EAAkC7J,GAAG,CAAC,CAAD,CAAH;CAClC,UAAI4F,aAAa,KAAKgB,SAAlB,IAA+B,OAAOhB,aAAP,KAAyB,UAA5D,EACC5F,GAAG,CAAC,CAAD,CAAH;CAED,UAAIyG,MAAJ;;CAGA,UAAIhG,WAAW,CAACoD,IAAD,CAAf,EAAuB;CACtB,YAAM8B,KAAK,GAAGQ,UAAU,CAAC,KAAD,CAAxB;CACA,YAAMqC,KAAK,GAAGK,WAAW,CAAC,KAAD,EAAOhF,IAAP,EAAa+C,SAAb,CAAzB;CACA,YAAIsD,QAAQ,GAAG,IAAf;;CACA,YAAI;CACHzD,UAAAA,MAAM,GAAGoD,MAAM,CAACrB,KAAD,CAAf;CACA0B,UAAAA,QAAQ,GAAG,KAAX;CACA,SAHD,SAGU;CACT;CACA,cAAIA,QAAJ,EAAclE,WAAW,CAACL,KAAD,CAAX,CAAd,KACKM,UAAU,CAACN,KAAD,CAAV;CACL;;CACD,YAAI,OAAOwE,OAAP,KAAmB,WAAnB,IAAkC1D,MAAM,YAAY0D,OAAxD,EAAiE;CAChE,iBAAO1D,MAAM,CAAC2D,IAAP,CACN,UAAA3D,MAAM;CACLf,YAAAA,iBAAiB,CAACC,KAAD,EAAQC,aAAR,CAAjB;CACA,mBAAOY,aAAa,CAACC,MAAD,EAASd,KAAT,CAApB;CACA,WAJK,EAKN,UAAA1F,KAAK;CACJ+F,YAAAA,WAAW,CAACL,KAAD,CAAX;CACA,kBAAM1F,KAAN;CACA,WARK,CAAP;CAUA;;CACDyF,QAAAA,iBAAiB,CAACC,KAAD,EAAQC,aAAR,CAAjB;CACA,eAAOY,aAAa,CAACC,MAAD,EAASd,KAAT,CAApB;CACA,OA1BD,MA0BO,IAAI,CAAC9B,IAAD,IAAS,OAAOA,IAAP,KAAgB,QAA7B,EAAuC;CAC7C4C,QAAAA,MAAM,GAAGoD,MAAM,CAAChG,IAAD,CAAf;CACA,YAAI4C,MAAM,KAAKG,SAAf,EAA0BH,MAAM,GAAG5C,IAAT;CAC1B,YAAI4C,MAAM,KAAKrH,OAAf,EAAwBqH,MAAM,GAAGG,SAAT;CACxB,YAAI,KAAI,CAACoB,WAAT,EAAsBzD,MAAM,CAACkC,MAAD,EAAS,IAAT,CAAN;;CACtB,YAAIb,aAAJ,EAAmB;CAClB,cAAMyE,CAAC,GAAY,EAAnB;CACA,cAAMC,EAAE,GAAY,EAApB;CACAxF,UAAAA,SAAS,CAAC,SAAD,CAAT,CAAqBoC,2BAArB,CAAiDrD,IAAjD,EAAuD4C,MAAvD,EAA+D4D,CAA/D,EAAkEC,EAAlE;CACA1E,UAAAA,aAAa,CAACyE,CAAD,EAAIC,EAAJ,CAAb;CACA;;CACD,eAAO7D,MAAP;CACA,OAZM,MAYAzG,GAAG,CAAC,EAAD,EAAK6D,IAAL,CAAH;CACP,KA9DD;;CAgEA,2BAAA,GAA0C,UAACA,IAAD,EAAYgG,MAAZ;CACzC;CACA,UAAI,OAAOhG,IAAP,KAAgB,UAApB,EAAgC;CAC/B,eAAO,UAACf,KAAD;CAAA,6CAAgB5C,IAAhB;CAAgBA,YAAAA,IAAhB;CAAA;;CAAA,iBACN,KAAI,CAACqK,kBAAL,CAAwBzH,KAAxB,EAA+B,UAACuD,KAAD;CAAA,mBAAgBxC,IAAI,MAAJ,UAAKwC,KAAL,SAAenG,IAAf,EAAhB;CAAA,WAA/B,CADM;CAAA,SAAP;CAEA;;CAED,UAAIsK,OAAJ,EAAsBC,cAAtB;;CACA,UAAMhE,MAAM,GAAG,KAAI,CAACwD,OAAL,CAAapG,IAAb,EAAmBgG,MAAnB,EAA2B,UAACQ,CAAD,EAAaC,EAAb;CACzCE,QAAAA,OAAO,GAAGH,CAAV;CACAI,QAAAA,cAAc,GAAGH,EAAjB;CACA,OAHc,CAAf;;CAKA,UAAI,OAAOH,OAAP,KAAmB,WAAnB,IAAkC1D,MAAM,YAAY0D,OAAxD,EAAiE;CAChE,eAAO1D,MAAM,CAAC2D,IAAP,CAAY,UAAAM,SAAS;CAAA,iBAAI,CAACA,SAAD,EAAYF,OAAZ,EAAsBC,cAAtB,CAAJ;CAAA,SAArB,CAAP;CACA;;CACD,aAAO,CAAChE,MAAD,EAAS+D,OAAT,EAAmBC,cAAnB,CAAP;CACA,KAjBD;;CAzFC,QAAI,QAAOb,MAAP,aAAOA,MAAP,uBAAOA,MAAM,CAAEe,UAAf,MAA8B,SAAlC,EACC,KAAKC,aAAL,CAAmBhB,MAAO,CAACe,UAA3B;CACD,QAAI,QAAOf,MAAP,aAAOA,MAAP,uBAAOA,MAAM,CAAEiB,UAAf,MAA8B,SAAlC,EACC,KAAKC,aAAL,CAAmBlB,MAAO,CAACiB,UAA3B;CACD;;CAVF;;CAAA,SAkHCE,WAlHD,GAkHC,qBAAiClH,IAAjC;CACC,QAAI,CAACpD,WAAW,CAACoD,IAAD,CAAhB,EAAwB7D,GAAG,CAAC,CAAD,CAAH;CACxB,QAAIO,OAAO,CAACsD,IAAD,CAAX,EAAmBA,IAAI,GAAGkF,OAAO,CAAClF,IAAD,CAAd;CACnB,QAAM8B,KAAK,GAAGQ,UAAU,CAAC,IAAD,CAAxB;CACA,QAAMqC,KAAK,GAAGK,WAAW,CAAC,IAAD,EAAOhF,IAAP,EAAa+C,SAAb,CAAzB;CACA4B,IAAAA,KAAK,CAACjJ,WAAD,CAAL,CAAmB4I,SAAnB,GAA+B,IAA/B;CACAlC,IAAAA,UAAU,CAACN,KAAD,CAAV;CACA,WAAO6C,KAAP;CACA,GA1HF;;CAAA,SA4HCwC,WA5HD,GA4HC,qBACC3E,KADD,EAECT,aAFD;CAIC,QAAM9C,KAAK,GAAeuD,KAAK,IAAKA,KAAa,CAAC9G,WAAD,CAAjD;;CACA,IAAa;CACZ,UAAI,CAACuD,KAAD,IAAU,CAACA,KAAK,CAACqF,SAArB,EAAgCnI,GAAG,CAAC,CAAD,CAAH;CAChC,UAAI8C,KAAK,CAACyE,UAAV,EAAsBvH,GAAG,CAAC,EAAD,CAAH;CACtB;;SACc2F,QAAS7C,MAAjBwE;CACP5B,IAAAA,iBAAiB,CAACC,KAAD,EAAQC,aAAR,CAAjB;CACA,WAAOY,aAAa,CAACI,SAAD,EAAYjB,KAAZ,CAApB;CACA;CAED;;;;;CA1ID;;CAAA,SA+ICmF,aA/ID,GA+IC,uBAActK,KAAd;CACC,SAAKwH,WAAL,GAAmBxH,KAAnB;CACA;CAED;;;;;;CAnJD;;CAAA,SAyJCoK,aAzJD,GAyJC,uBAAcpK,KAAd;CACC,QAAIA,KAAK,IAAI,CAACxB,UAAd,EAA0B;CACzBgB,MAAAA,GAAG,CAAC,EAAD,CAAH;CACA;;CACD,SAAK6G,WAAL,GAAmBrG,KAAnB;CACA,GA9JF;;CAAA,SAgKCyK,YAhKD,GAgKC,sBAAkCpH,IAAlC,EAA2C2G,OAA3C;CACC;CACA;CACA,QAAIxG,CAAJ;;CACA,SAAKA,CAAC,GAAGwG,OAAO,CAACvG,MAAR,GAAiB,CAA1B,EAA6BD,CAAC,IAAI,CAAlC,EAAqCA,CAAC,EAAtC,EAA0C;CACzC,UAAMkH,KAAK,GAAGV,OAAO,CAACxG,CAAD,CAArB;;CACA,UAAIkH,KAAK,CAACtL,IAAN,CAAWqE,MAAX,KAAsB,CAAtB,IAA2BiH,KAAK,CAACrL,EAAN,KAAa,SAA5C,EAAuD;CACtDgE,QAAAA,IAAI,GAAGqH,KAAK,CAAC1K,KAAb;CACA;CACA;CACD;CAED;;;CACA,QAAIwD,CAAC,GAAG,CAAC,CAAT,EAAY;CACXwG,MAAAA,OAAO,GAAGA,OAAO,CAAC1G,KAAR,CAAcE,CAAC,GAAG,CAAlB,CAAV;CACA;;CAED,QAAMmH,gBAAgB,GAAGrG,SAAS,CAAC,SAAD,CAAT,CAAqBsG,aAA9C;;CACA,QAAI7K,OAAO,CAACsD,IAAD,CAAX,EAAmB;CAClB;CACA,aAAOsH,gBAAgB,CAACtH,IAAD,EAAO2G,OAAP,CAAvB;CACA;;;CAED,WAAO,KAAKP,OAAL,CAAapG,IAAb,EAAmB,UAACwC,KAAD;CAAA,aACzB8E,gBAAgB,CAAC9E,KAAD,EAAQmE,OAAR,CADS;CAAA,KAAnB,CAAP;CAGA,GA1LF;;CAAA;CAAA;AA6LA,UAAgB3B,YACfzC,OACA5F,OACA0H;CAEA;CACA,MAAM7B,KAAK,GAAYvF,KAAK,CAACN,KAAD,CAAL,GACpBsE,SAAS,CAAC,QAAD,CAAT,CAAoBuG,SAApB,CAA8B7K,KAA9B,EAAqC0H,MAArC,CADoB,GAEpBnH,KAAK,CAACP,KAAD,CAAL,GACAsE,SAAS,CAAC,QAAD,CAAT,CAAoBwG,SAApB,CAA8B9K,KAA9B,EAAqC0H,MAArC,CADA,GAEA9B,KAAK,CAACS,WAAN,GACAoB,gBAAgB,CAACzH,KAAD,EAAQ0H,MAAR,CADhB,GAEApD,SAAS,CAAC,KAAD,CAAT,CAAiByG,eAAjB,CAAiC/K,KAAjC,EAAwC0H,MAAxC,CANH;CAQA,MAAMvC,KAAK,GAAGuC,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAAtD;CACAQ,EAAAA,KAAK,CAACJ,OAAN,CAAciG,IAAd,CAAmBnF,KAAnB;CACA,SAAOA,KAAP;CACA;;UC/Ne0C,QAAQvI;CACvB,MAAI,CAACD,OAAO,CAACC,KAAD,CAAZ,EAAqBR,GAAG,CAAC,EAAD,EAAKQ,KAAL,CAAH;CACrB,SAAOiL,WAAW,CAACjL,KAAD,CAAlB;CACA;;CAED,SAASiL,WAAT,CAAqBjL,KAArB;CACC,MAAI,CAACC,WAAW,CAACD,KAAD,CAAhB,EAAyB,OAAOA,KAAP;CACzB,MAAMsC,KAAK,GAA2BtC,KAAK,CAACjB,WAAD,CAA3C;CACA,MAAImM,IAAJ;CACA,MAAMC,QAAQ,GAAGjJ,WAAW,CAAClC,KAAD,CAA5B;;CACA,MAAIsC,KAAJ,EAAW;CACV,QACC,CAACA,KAAK,CAACiE,SAAP,KACCjE,KAAK,CAACC,KAAN,GAAc,CAAd,IAAmB,CAAC+B,SAAS,CAAC,KAAD,CAAT,CAAiB8G,WAAjB,CAA6B9I,KAA7B,CADrB,CADD,EAIC,OAAOA,KAAK,CAACnB,KAAb,CALS;;CAOVmB,IAAAA,KAAK,CAACyE,UAAN,GAAmB,IAAnB;CACAmE,IAAAA,IAAI,GAAGG,UAAU,CAACrL,KAAD,EAAQmL,QAAR,CAAjB;CACA7I,IAAAA,KAAK,CAACyE,UAAN,GAAmB,KAAnB;CACA,GAVD,MAUO;CACNmE,IAAAA,IAAI,GAAGG,UAAU,CAACrL,KAAD,EAAQmL,QAAR,CAAjB;CACA;;CAEDpJ,EAAAA,IAAI,CAACmJ,IAAD,EAAO,UAACrJ,GAAD,EAAM+E,UAAN;CACV,QAAItE,KAAK,IAAII,GAAG,CAACJ,KAAK,CAACnB,KAAP,EAAcU,GAAd,CAAH,KAA0B+E,UAAvC,EAAmD;;CACnDjE,IAAAA,GAAG,CAACuI,IAAD,EAAOrJ,GAAP,EAAYoJ,WAAW,CAACrE,UAAD,CAAvB,CAAH;CACA,GAHG,CAAJ;;CAKA,SAAOuE,QAAQ;;CAAR,IAA4B,IAAI5M,GAAJ,CAAQ2M,IAAR,CAA5B,GAA4CA,IAAnD;CACA;;CAED,SAASG,UAAT,CAAoBrL,KAApB,EAAgCmL,QAAhC;CACC;CACA,UAAQA,QAAR;CACC;;CAAA;CACC,aAAO,IAAI9M,GAAJ,CAAQ2B,KAAR,CAAP;;CACD;;CAAA;CACC;CACA,aAAOG,KAAK,CAACmL,IAAN,CAAWtL,KAAX,CAAP;CALF;;CAOA,SAAOoD,WAAW,CAACpD,KAAD,CAAlB;CACA;;UCnCeuL;CACf,WAASjF,gBAAT,CACCnB,KADD,EAECc,MAFD,EAGCE,UAHD;CAKC,QAAI,CAACA,UAAL,EAAiB;CAChB,UAAIhB,KAAK,CAACE,QAAV,EAAoB;CACnBmG,QAAAA,sBAAsB,CAACrG,KAAK,CAACJ,OAAN,CAAe,CAAf,CAAD,CAAtB;CACA,OAHe;;;CAKhB0G,MAAAA,gBAAgB,CAACtG,KAAK,CAACJ,OAAP,CAAhB;CACA,KAND;CAAA,SAQK,IACJhF,OAAO,CAACkG,MAAD,CAAP,IACCA,MAAM,CAAClH,WAAD,CAAN,CAAiC+H,MAAjC,KAA4C3B,KAFzC,EAGH;CACDsG,QAAAA,gBAAgB,CAACtG,KAAK,CAACJ,OAAP,CAAhB;CACA;CACD;;CAED,WAAS2G,cAAT,CAAwBtL,OAAxB,EAA0CiD,IAA1C;CACC,QAAIjD,OAAJ,EAAa;CACZ,UAAMyF,KAAK,GAAG,IAAI1F,KAAJ,CAAUkD,IAAI,CAACI,MAAf,CAAd;;CACA,WAAK,IAAID,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,IAAI,CAACI,MAAzB,EAAiCD,CAAC,EAAlC;CACC/C,QAAAA,MAAM,CAACqI,cAAP,CAAsBjD,KAAtB,EAA6B,KAAKrC,CAAlC,EAAqCmI,aAAa,CAACnI,CAAD,EAAI,IAAJ,CAAlD;CADD;;CAEA,aAAOqC,KAAP;CACA,KALD,MAKO;CACN,UAAMtC,YAAW,GAAG9B,yBAAyB,CAAC4B,IAAD,CAA7C;;CACA,aAAOE,YAAW,CAACxE,WAAD,CAAlB;CACA,UAAMoD,IAAI,GAAGf,OAAO,CAACmC,YAAD,CAApB;;CACA,WAAK,IAAIC,EAAC,GAAG,CAAb,EAAgBA,EAAC,GAAGrB,IAAI,CAACsB,MAAzB,EAAiCD,EAAC,EAAlC,EAAsC;CACrC,YAAM3B,GAAG,GAAQM,IAAI,CAACqB,EAAD,CAArB;CACAD,QAAAA,YAAW,CAAC1B,GAAD,CAAX,GAAmB8J,aAAa,CAC/B9J,GAD+B,EAE/BzB,OAAO,IAAI,CAAC,CAACmD,YAAW,CAAC1B,GAAD,CAAX,CAAiBgC,UAFC,CAAhC;CAIA;;CACD,aAAOpD,MAAM,CAACqD,MAAP,CAAcrD,MAAM,CAACI,cAAP,CAAsBwC,IAAtB,CAAd,EAA2CE,YAA3C,CAAP;CACA;CACD;;CAED,WAASwH,eAAT,CACC1H,IADD,EAECqE,MAFD;CAIC,QAAMtH,OAAO,GAAGD,KAAK,CAACC,OAAN,CAAciD,IAAd,CAAhB;CACA,QAAMwC,KAAK,GAAG6F,cAAc,CAACtL,OAAD,EAAUiD,IAAV,CAA5B;CAEA,QAAMf,KAAK,GAAmC;CAC7CC,MAAAA,KAAK,EAAEnC,OAAO;;CAAA,QAAyB;;CADM;CAE7C0G,MAAAA,MAAM,EAAEY,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAFH;CAG7C4B,MAAAA,SAAS,EAAE,KAHkC;CAI7CQ,MAAAA,UAAU,EAAE,KAJiC;CAK7CQ,MAAAA,SAAS,EAAE,EALkC;CAM7C1C,MAAAA,OAAO,EAAE6C,MANoC;CAO7C;CACAvG,MAAAA,KAAK,EAAEkC,IARsC;CAS7C;CACA2D,MAAAA,MAAM,EAAEnB,KAVqC;CAW7C1C,MAAAA,KAAK,EAAE,IAXsC;CAY7C4C,MAAAA,QAAQ,EAAE,KAZmC;CAa7C4B,MAAAA,SAAS,EAAE;CAbkC,KAA9C;CAgBAlH,IAAAA,MAAM,CAACqI,cAAP,CAAsBjD,KAAtB,EAA6B9G,WAA7B,EAA0C;CACzCiB,MAAAA,KAAK,EAAEsC,KADkC;CAEzC;CACAqB,MAAAA,QAAQ,EAAE;CAH+B,KAA1C;CAKA,WAAOkC,KAAP;CACA;CAGD;;;CACA,MAAMtC,WAAW,GAAyC,EAA1D;;CAEA,WAASoI,aAAT,CACClJ,IADD,EAECoB,UAFD;CAIC,QAAIH,IAAI,GAAGH,WAAW,CAACd,IAAD,CAAtB;;CACA,QAAIiB,IAAJ,EAAU;CACTA,MAAAA,IAAI,CAACG,UAAL,GAAkBA,UAAlB;CACA,KAFD,MAEO;CACNN,MAAAA,WAAW,CAACd,IAAD,CAAX,GAAoBiB,IAAI,GAAG;CAC1BE,QAAAA,YAAY,EAAE,IADY;CAE1BC,QAAAA,UAAU,EAAVA,UAF0B;CAG1BnB,QAAAA,GAH0B;CAIzB,cAAMJ,KAAK,GAAG,KAAKvD,WAAL,CAAd;CACA,UAAa6M,eAAe,CAACtJ,KAAD,CAAf;;CAEb,iBAAOuF,WAAW,CAACnF,GAAZ,CAAgBJ,KAAhB,EAAuBG,IAAvB,CAAP;CACA,SARyB;CAS1BE,QAAAA,GAT0B,eASX3C,KATW;CAUzB,cAAMsC,KAAK,GAAG,KAAKvD,WAAL,CAAd;CACA,UAAa6M,eAAe,CAACtJ,KAAD,CAAf;;CAEbuF,UAAAA,WAAW,CAAClF,GAAZ,CAAgBL,KAAhB,EAAuBG,IAAvB,EAA6BzC,KAA7B;CACA;CAdyB,OAA3B;CAgBA;;CACD,WAAO0D,IAAP;CACA;;;CAGD,WAAS+H,gBAAT,CAA0BI,MAA1B;CACC;CACA;CACA;CACA;CACA,SAAK,IAAIrI,CAAC,GAAGqI,MAAM,CAACpI,MAAP,GAAgB,CAA7B,EAAgCD,CAAC,IAAI,CAArC,EAAwCA,CAAC,EAAzC,EAA6C;CAC5C,UAAMlB,KAAK,GAAauJ,MAAM,CAACrI,CAAD,CAAN,CAAUzE,WAAV,CAAxB;;CACA,UAAI,CAACuD,KAAK,CAACiE,SAAX,EAAsB;CACrB,gBAAQjE,KAAK,CAACC,KAAd;CACC;;CAAA;CACC,gBAAIuJ,eAAe,CAACxJ,KAAD,CAAnB,EAA4BmG,WAAW,CAACnG,KAAD,CAAX;CAC5B;;CACD;;CAAA;CACC,gBAAIyJ,gBAAgB,CAACzJ,KAAD,CAApB,EAA6BmG,WAAW,CAACnG,KAAD,CAAX;CAC7B;CANF;CAQA;CACD;CACD;;CAED,WAASkJ,sBAAT,CAAgCQ,MAAhC;CACC,QAAI,CAACA,MAAD,IAAW,OAAOA,MAAP,KAAkB,QAAjC,EAA2C;CAC3C,QAAM1J,KAAK,GAAyB0J,MAAM,CAACjN,WAAD,CAA1C;CACA,QAAI,CAACuD,KAAL,EAAY;SACLnB,QAAmCmB,MAAnCnB;SAAO6F,SAA4B1E,MAA5B0E;SAAQO,YAAoBjF,MAApBiF;SAAWhF,QAASD,MAATC;;CACjC,QAAIA,KAAK;;CAAT,MAAmC;CAClC;CACA;CACA;CACA;CACAR,QAAAA,IAAI,CAACiF,MAAD,EAAS,UAAAnF,GAAG;CACf,cAAKA,GAAW,KAAK9C,WAArB,EAAkC;;CAElC,cAAKoC,KAAa,CAACU,GAAD,CAAb,KAAuBuE,SAAvB,IAAoC,CAAC5D,GAAG,CAACrB,KAAD,EAAQU,GAAR,CAA7C,EAA2D;CAC1D0F,YAAAA,SAAS,CAAC1F,GAAD,CAAT,GAAiB,IAAjB;CACA4G,YAAAA,WAAW,CAACnG,KAAD,CAAX;CACA,WAHD,MAGO,IAAI,CAACiF,SAAS,CAAC1F,GAAD,CAAd,EAAqB;CAC3B;CACA2J,YAAAA,sBAAsB,CAACxE,MAAM,CAACnF,GAAD,CAAP,CAAtB;CACA;CACD,SAVG,CAAJ,CALkC;;CAiBlCE,QAAAA,IAAI,CAACZ,KAAD,EAAQ,UAAAU,GAAG;CACd;CACA,cAAImF,MAAM,CAACnF,GAAD,CAAN,KAAgBuE,SAAhB,IAA6B,CAAC5D,GAAG,CAACwE,MAAD,EAASnF,GAAT,CAArC,EAAoD;CACnD0F,YAAAA,SAAS,CAAC1F,GAAD,CAAT,GAAiB,KAAjB;CACA4G,YAAAA,WAAW,CAACnG,KAAD,CAAX;CACA;CACD,SANG,CAAJ;CAOA,OAxBD,MAwBO,IAAIC,KAAK;;CAAT,MAAkC;CACxC,YAAIuJ,eAAe,CAACxJ,KAAD,CAAnB,EAA6C;CAC5CmG,UAAAA,WAAW,CAACnG,KAAD,CAAX;CACAiF,UAAAA,SAAS,CAAC9D,MAAV,GAAmB,IAAnB;CACA;;CAED,YAAIuD,MAAM,CAACvD,MAAP,GAAgBtC,KAAK,CAACsC,MAA1B,EAAkC;CACjC,eAAK,IAAID,CAAC,GAAGwD,MAAM,CAACvD,MAApB,EAA4BD,CAAC,GAAGrC,KAAK,CAACsC,MAAtC,EAA8CD,CAAC,EAA/C;CAAmD+D,YAAAA,SAAS,CAAC/D,CAAD,CAAT,GAAe,KAAf;CAAnD;CACA,SAFD,MAEO;CACN,eAAK,IAAIA,GAAC,GAAGrC,KAAK,CAACsC,MAAnB,EAA2BD,GAAC,GAAGwD,MAAM,CAACvD,MAAtC,EAA8CD,GAAC,EAA/C;CAAmD+D,YAAAA,SAAS,CAAC/D,GAAD,CAAT,GAAe,IAAf;CAAnD;CACA,SAVuC;;;CAaxC,YAAMyI,GAAG,GAAGC,IAAI,CAACD,GAAL,CAASjF,MAAM,CAACvD,MAAhB,EAAwBtC,KAAK,CAACsC,MAA9B,CAAZ;;CAEA,aAAK,IAAID,GAAC,GAAG,CAAb,EAAgBA,GAAC,GAAGyI,GAApB,EAAyBzI,GAAC,EAA1B,EAA8B;CAC7B;CACA,cAAI,CAACwD,MAAM,CAACjG,cAAP,CAAsByC,GAAtB,CAAL,EAA+B;CAC9B+D,YAAAA,SAAS,CAAC/D,GAAD,CAAT,GAAe,IAAf;CACA;;CACD,cAAI+D,SAAS,CAAC/D,GAAD,CAAT,KAAiB4C,SAArB,EAAgCoF,sBAAsB,CAACxE,MAAM,CAACxD,GAAD,CAAP,CAAtB;CAChC;CACD;CACD;;CAED,WAASuI,gBAAT,CAA0BzJ,KAA1B;SACQnB,QAAiBmB,MAAjBnB;SAAO6F,SAAU1E,MAAV0E;CAGd;;CACA,QAAM7E,IAAI,GAAGf,OAAO,CAAC4F,MAAD,CAApB;;CACA,SAAK,IAAIxD,CAAC,GAAGrB,IAAI,CAACsB,MAAL,GAAc,CAA3B,EAA8BD,CAAC,IAAI,CAAnC,EAAsCA,CAAC,EAAvC,EAA2C;CAC1C,UAAM3B,GAAG,GAAQM,IAAI,CAACqB,CAAD,CAArB;CACA,UAAI3B,GAAG,KAAK9C,WAAZ,EAAyB;CACzB,UAAMoN,SAAS,GAAGhL,KAAK,CAACU,GAAD,CAAvB,CAH0C;;CAK1C,UAAIsK,SAAS,KAAK/F,SAAd,IAA2B,CAAC5D,GAAG,CAACrB,KAAD,EAAQU,GAAR,CAAnC,EAAiD;CAChD,eAAO,IAAP;CACA,OAFD;CAIA;CAJA,WAKK;CACJ,cAAM7B,KAAK,GAAGgH,MAAM,CAACnF,GAAD,CAApB;;CACA,cAAMS,MAAK,GAAetC,KAAK,IAAIA,KAAK,CAACjB,WAAD,CAAxC;;CACA,cAAIuD,MAAK,GAAGA,MAAK,CAACnB,KAAN,KAAgBgL,SAAnB,GAA+B,CAACpJ,EAAE,CAAC/C,KAAD,EAAQmM,SAAR,CAA3C,EAA+D;CAC9D,mBAAO,IAAP;CACA;CACD;CACD;CAGD;;;CACA,QAAMC,WAAW,GAAG,CAAC,CAACjL,KAAK,CAACpC,WAAD,CAA3B;CACA,WAAOoD,IAAI,CAACsB,MAAL,KAAgBrC,OAAO,CAACD,KAAD,CAAP,CAAesC,MAAf,IAAyB2I,WAAW,GAAG,CAAH,GAAO,CAA3C,CAAvB;CACA;;CAED,WAASN,eAAT,CAAyBxJ,KAAzB;SACQ0E,SAAU1E,MAAV0E;CACP,QAAIA,MAAM,CAACvD,MAAP,KAAkBnB,KAAK,CAACnB,KAAN,CAAYsC,MAAlC,EAA0C,OAAO,IAAP;CAE1C;CACA;CACA;CACA;CACA;CACA;CACA;;CACA,QAAM4I,UAAU,GAAG5L,MAAM,CAACqB,wBAAP,CAClBkF,MADkB,EAElBA,MAAM,CAACvD,MAAP,GAAgB,CAFE,CAAnB;;CAKA,QAAI4I,UAAU,IAAI,CAACA,UAAU,CAAC3J,GAA9B,EAAmC,OAAO,IAAP;;CAEnC,SAAK,IAAIc,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGwD,MAAM,CAACvD,MAA3B,EAAmCD,CAAC,EAApC,EAAwC;CACvC,UAAI,CAACwD,MAAM,CAACjG,cAAP,CAAsByC,CAAtB,CAAL,EAA+B,OAAO,IAAP;CAC/B;;;CAED,WAAO,KAAP;CACA;;CAED,WAAS4H,WAAT,CAAqB9I,KAArB;CACC,WAAOA,KAAK,CAACC,KAAN;;CAAA,MACJwJ,gBAAgB,CAACzJ,KAAD,CADZ,GAEJwJ,eAAe,CAACxJ,KAAD,CAFlB;CAGA;;CAED,WAASsJ,eAAT,CAAyBtJ;CAAW;CAApC;CACC,QAAIA,KAAK,CAACyD,QAAV,EAAoBvG,GAAG,CAAC,CAAD,EAAI8M,IAAI,CAACC,SAAL,CAAerJ,MAAM,CAACZ,KAAD,CAArB,CAAJ,CAAH;CACpB;;CAEDkC,EAAAA,UAAU,CAAC,KAAD,EAAQ;CACjBuG,IAAAA,eAAe,EAAfA,eADiB;CAEjBzE,IAAAA,gBAAgB,EAAhBA,gBAFiB;CAGjB8E,IAAAA,WAAW,EAAXA;CAHiB,GAAR,CAAV;CAKA;;UC1PeoB;CACf,MAAMC,OAAO,GAAG,SAAhB;CACA,MAAMC,GAAG,GAAG,KAAZ;CACA,MAAMC,MAAM,GAAG,QAAf;;CAEA,WAASzF,gBAAT,CACC5E,KADD,EAECsK,QAFD,EAGC5C,OAHD,EAICC,cAJD;CAMC,YAAQ3H,KAAK,CAACC,KAAd;CACC;;CAAA;CACA;;CAAA;CACA;;CAAA;CACC,eAAOsK,2BAA2B,CACjCvK,KADiC,EAEjCsK,QAFiC,EAGjC5C,OAHiC,EAIjCC,cAJiC,CAAlC;;CAMD;;CAAA;CACA;;CAAA;CACC,eAAO6C,oBAAoB,CAACxK,KAAD,EAAQsK,QAAR,EAAkB5C,OAAlB,EAA2BC,cAA3B,CAA3B;;CACD;;CAAA;CACC,eAAO8C,kBAAkB,CACvBzK,KADuB,EAExBsK,QAFwB,EAGxB5C,OAHwB,EAIxBC,cAJwB,CAAzB;CAdF;CAqBA;;CAED,WAAS6C,oBAAT,CACCxK,KADD,EAECsK,QAFD,EAGC5C,OAHD,EAICC,cAJD;SAMM9I,QAAoBmB,MAApBnB;SAAOoG,YAAajF,MAAbiF;CACZ,QAAIpE,KAAK,GAAGb,KAAK,CAACa,KAAlB;;CAGA,QAAIA,KAAK,CAACM,MAAN,GAAetC,KAAK,CAACsC,MAAzB,EAAiC;AAChC,CADgC,iBAEd,CAACN,KAAD,EAAQhC,KAAR,CAFc;CAE9BA,MAAAA,KAF8B;CAEvBgC,MAAAA,KAFuB;CAAA,kBAGH,CAAC8G,cAAD,EAAiBD,OAAjB,CAHG;CAG9BA,MAAAA,OAH8B;CAGrBC,MAAAA,cAHqB;CAIhC;;;CAGD,SAAK,IAAIzG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGrC,KAAK,CAACsC,MAA1B,EAAkCD,CAAC,EAAnC,EAAuC;CACtC,UAAI+D,SAAS,CAAC/D,CAAD,CAAT,IAAgBL,KAAK,CAACK,CAAD,CAAL,KAAarC,KAAK,CAACqC,CAAD,CAAtC,EAA2C;CAC1C,YAAMpE,IAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgB,CAACgC,CAAD,CAAhB,CAAb;CACAwG,QAAAA,OAAO,CAACgB,IAAR,CAAa;CACZ3L,UAAAA,EAAE,EAAEoN,OADQ;CAEZrN,UAAAA,IAAI,EAAJA,IAFY;CAGZ;CACA;CACAY,UAAAA,KAAK,EAAEgN,uBAAuB,CAAC7J,KAAK,CAACK,CAAD,CAAN;CALlB,SAAb;CAOAyG,QAAAA,cAAc,CAACe,IAAf,CAAoB;CACnB3L,UAAAA,EAAE,EAAEoN,OADe;CAEnBrN,UAAAA,IAAI,EAAJA,IAFmB;CAGnBY,UAAAA,KAAK,EAAEgN,uBAAuB,CAAC7L,KAAK,CAACqC,CAAD,CAAN;CAHX,SAApB;CAKA;CACD;;;CAGD,SAAK,IAAIA,EAAC,GAAGrC,KAAK,CAACsC,MAAnB,EAA2BD,EAAC,GAAGL,KAAK,CAACM,MAArC,EAA6CD,EAAC,EAA9C,EAAkD;CACjD,UAAMpE,KAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgB,CAACgC,EAAD,CAAhB,CAAb;;CACAwG,MAAAA,OAAO,CAACgB,IAAR,CAAa;CACZ3L,QAAAA,EAAE,EAAEqN,GADQ;CAEZtN,QAAAA,IAAI,EAAJA,KAFY;CAGZ;CACA;CACAY,QAAAA,KAAK,EAAEgN,uBAAuB,CAAC7J,KAAK,CAACK,EAAD,CAAN;CALlB,OAAb;CAOA;;CACD,QAAIrC,KAAK,CAACsC,MAAN,GAAeN,KAAK,CAACM,MAAzB,EAAiC;CAChCwG,MAAAA,cAAc,CAACe,IAAf,CAAoB;CACnB3L,QAAAA,EAAE,EAAEoN,OADe;CAEnBrN,QAAAA,IAAI,EAAEwN,QAAQ,CAACpL,MAAT,CAAgB,CAAC,QAAD,CAAhB,CAFa;CAGnBxB,QAAAA,KAAK,EAAEmB,KAAK,CAACsC;CAHM,OAApB;CAKA;CACD;;;CAGD,WAASoJ,2BAAT,CACCvK,KADD,EAECsK,QAFD,EAGC5C,OAHD,EAICC,cAJD;SAMQ9I,QAAgBmB,MAAhBnB;SAAOgC,QAASb,MAATa;CACdpB,IAAAA,IAAI,CAACO,KAAK,CAACiF,SAAP,EAAmB,UAAC1F,GAAD,EAAMoL,aAAN;CACtB,UAAMC,SAAS,GAAGxK,GAAG,CAACvB,KAAD,EAAQU,GAAR,CAArB;CACA,UAAM7B,KAAK,GAAG0C,GAAG,CAACS,KAAD,EAAStB,GAAT,CAAjB;CACA,UAAMxC,EAAE,GAAG,CAAC4N,aAAD,GAAiBN,MAAjB,GAA0BnK,GAAG,CAACrB,KAAD,EAAQU,GAAR,CAAH,GAAkB4K,OAAlB,GAA4BC,GAAjE;CACA,UAAIQ,SAAS,KAAKlN,KAAd,IAAuBX,EAAE,KAAKoN,OAAlC,EAA2C;CAC3C,UAAMrN,IAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgBK,GAAhB,CAAb;CACAmI,MAAAA,OAAO,CAACgB,IAAR,CAAa3L,EAAE,KAAKsN,MAAP,GAAgB;CAACtN,QAAAA,EAAE,EAAFA,EAAD;CAAKD,QAAAA,IAAI,EAAJA;CAAL,OAAhB,GAA6B;CAACC,QAAAA,EAAE,EAAFA,EAAD;CAAKD,QAAAA,IAAI,EAAJA,IAAL;CAAWY,QAAAA,KAAK,EAALA;CAAX,OAA1C;CACAiK,MAAAA,cAAc,CAACe,IAAf,CACC3L,EAAE,KAAKqN,GAAP,GACG;CAACrN,QAAAA,EAAE,EAAEsN,MAAL;CAAavN,QAAAA,IAAI,EAAJA;CAAb,OADH,GAEGC,EAAE,KAAKsN,MAAP,GACA;CAACtN,QAAAA,EAAE,EAAEqN,GAAL;CAAUtN,QAAAA,IAAI,EAAJA,IAAV;CAAgBY,QAAAA,KAAK,EAAEgN,uBAAuB,CAACE,SAAD;CAA9C,OADA,GAEA;CAAC7N,QAAAA,EAAE,EAAEoN,OAAL;CAAcrN,QAAAA,IAAI,EAAJA,IAAd;CAAoBY,QAAAA,KAAK,EAAEgN,uBAAuB,CAACE,SAAD;CAAlD,OALJ;CAOA,KAdG,CAAJ;CAeA;;CAED,WAASH,kBAAT,CACCzK,KADD,EAECsK,QAFD,EAGC5C,OAHD,EAICC,cAJD;SAMM9I,QAAgBmB,MAAhBnB;SAAOgC,QAASb,MAATa;CAEZ,QAAIK,CAAC,GAAG,CAAR;CACArC,IAAAA,KAAK,CAACS,OAAN,CAAc,UAAC5B,KAAD;CACb,UAAI,CAACmD,KAAM,CAACX,GAAP,CAAWxC,KAAX,CAAL,EAAwB;CACvB,YAAMZ,IAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgB,CAACgC,CAAD,CAAhB,CAAb;CACAwG,QAAAA,OAAO,CAACgB,IAAR,CAAa;CACZ3L,UAAAA,EAAE,EAAEsN,MADQ;CAEZvN,UAAAA,IAAI,EAAJA,IAFY;CAGZY,UAAAA,KAAK,EAALA;CAHY,SAAb;CAKAiK,QAAAA,cAAc,CAACkD,OAAf,CAAuB;CACtB9N,UAAAA,EAAE,EAAEqN,GADkB;CAEtBtN,UAAAA,IAAI,EAAJA,IAFsB;CAGtBY,UAAAA,KAAK,EAALA;CAHsB,SAAvB;CAKA;;CACDwD,MAAAA,CAAC;CACD,KAfD;CAgBAA,IAAAA,CAAC,GAAG,CAAJ;CACAL,IAAAA,KAAM,CAACvB,OAAP,CAAe,UAAC5B,KAAD;CACd,UAAI,CAACmB,KAAK,CAACqB,GAAN,CAAUxC,KAAV,CAAL,EAAuB;CACtB,YAAMZ,IAAI,GAAGwN,QAAQ,CAACpL,MAAT,CAAgB,CAACgC,CAAD,CAAhB,CAAb;CACAwG,QAAAA,OAAO,CAACgB,IAAR,CAAa;CACZ3L,UAAAA,EAAE,EAAEqN,GADQ;CAEZtN,UAAAA,IAAI,EAAJA,IAFY;CAGZY,UAAAA,KAAK,EAALA;CAHY,SAAb;CAKAiK,QAAAA,cAAc,CAACkD,OAAf,CAAuB;CACtB9N,UAAAA,EAAE,EAAEsN,MADkB;CAEtBvN,UAAAA,IAAI,EAAJA,IAFsB;CAGtBY,UAAAA,KAAK,EAALA;CAHsB,SAAvB;CAKA;;CACDwD,MAAAA,CAAC;CACD,KAfD;CAgBA;;CAED,WAASkD,2BAAT,CACCyF,SADD,EAECiB,WAFD,EAGCpD,OAHD,EAICC,cAJD;CAMCD,IAAAA,OAAO,CAACgB,IAAR,CAAa;CACZ3L,MAAAA,EAAE,EAAEoN,OADQ;CAEZrN,MAAAA,IAAI,EAAE,EAFM;CAGZY,MAAAA,KAAK,EAAEoN,WAAW,KAAKxO,OAAhB,GAA0BwH,SAA1B,GAAsCgH;CAHjC,KAAb;CAKAnD,IAAAA,cAAc,CAACe,IAAf,CAAoB;CACnB3L,MAAAA,EAAE,EAAEoN,OADe;CAEnBrN,MAAAA,IAAI,EAAE,EAFa;CAGnBY,MAAAA,KAAK,EAAEmM;CAHY,KAApB;CAKA;;CAED,WAASvB,aAAT,CAA0B/E,KAA1B,EAAoCmE,OAApC;CACCA,IAAAA,OAAO,CAACpI,OAAR,CAAgB,UAAA8I,KAAK;WACbtL,OAAYsL,MAAZtL;WAAMC,KAAMqL,MAANrL;CAEb,UAAIgE,IAAI,GAAQwC,KAAhB;;CACA,WAAK,IAAIrC,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGpE,IAAI,CAACqE,MAAL,GAAc,CAAlC,EAAqCD,CAAC,EAAtC,EAA0C;CACzC,YAAM6J,UAAU,GAAGnL,WAAW,CAACmB,IAAD,CAA9B;CACA,YAAIwG,CAAC,GAAGzK,IAAI,CAACoE,CAAD,CAAZ;;CACA,YAAI,OAAOqG,CAAP,KAAa,QAAb,IAAyB,OAAOA,CAAP,KAAa,QAA1C,EAAoD;CACnDA,UAAAA,CAAC,GAAG,KAAKA,CAAT;CACA,SALwC;;;CAQzC,YACC,CAACwD,UAAU;;CAAV,WAAkCA,UAAU;;CAA7C,cACCxD,CAAC,KAAK,WAAN,IAAqBA,CAAC,KAAK,aAD5B,CADD,EAICrK,GAAG,CAAC,EAAD,CAAH;CACD,YAAI,OAAO6D,IAAP,KAAgB,UAAhB,IAA8BwG,CAAC,KAAK,WAAxC,EAAqDrK,GAAG,CAAC,EAAD,CAAH;CACrD6D,QAAAA,IAAI,GAAGX,GAAG,CAACW,IAAD,EAAOwG,CAAP,CAAV;CACA,YAAI,OAAOxG,IAAP,KAAgB,QAApB,EAA8B7D,GAAG,CAAC,EAAD,EAAKJ,IAAI,CAACkO,IAAL,CAAU,GAAV,CAAL,CAAH;CAC9B;;CAED,UAAMC,IAAI,GAAGrL,WAAW,CAACmB,IAAD,CAAxB;CACA,UAAMrD,KAAK,GAAGwN,mBAAmB,CAAC9C,KAAK,CAAC1K,KAAP,CAAjC;;CACA,UAAM6B,GAAG,GAAGzC,IAAI,CAACA,IAAI,CAACqE,MAAL,GAAc,CAAf,CAAhB;;CACA,cAAQpE,EAAR;CACC,aAAKoN,OAAL;CACC,kBAAQc,IAAR;CACC;;CAAA;CACC,qBAAOlK,IAAI,CAACV,GAAL,CAASd,GAAT,EAAc7B,KAAd,CAAP;;CACD;;CACA;;CAAA;CACCR,cAAAA,GAAG,CAAC,EAAD,CAAH;;CACD;CACC;CACA;CACA;CACA;CACA,qBAAQ6D,IAAI,CAACxB,GAAD,CAAJ,GAAY7B,KAApB;CAXF;;CAaD,aAAK0M,GAAL;CACC,kBAAQa,IAAR;CACC;;CAAA;CACC,qBAAO1L,GAAG,KAAK,GAAR,GACJwB,IAAI,CAAC2H,IAAL,CAAUhL,KAAV,CADI,GAEJqD,IAAI,CAACoK,MAAL,CAAY5L,GAAZ,EAAwB,CAAxB,EAA2B7B,KAA3B,CAFH;;CAGD;;CAAA;CACC,qBAAOqD,IAAI,CAACV,GAAL,CAASd,GAAT,EAAc7B,KAAd,CAAP;;CACD;;CAAA;CACC,qBAAOqD,IAAI,CAACP,GAAL,CAAS9C,KAAT,CAAP;;CACD;CACC,qBAAQqD,IAAI,CAACxB,GAAD,CAAJ,GAAY7B,KAApB;CAVF;;CAYD,aAAK2M,MAAL;CACC,kBAAQY,IAAR;CACC;;CAAA;CACC,qBAAOlK,IAAI,CAACoK,MAAL,CAAY5L,GAAZ,EAAwB,CAAxB,CAAP;;CACD;;CAAA;CACC,qBAAOwB,IAAI,CAACc,MAAL,CAAYtC,GAAZ,CAAP;;CACD;;CAAA;CACC,qBAAOwB,IAAI,CAACc,MAAL,CAAYuG,KAAK,CAAC1K,KAAlB,CAAP;;CACD;CACC,qBAAO,OAAOqD,IAAI,CAACxB,GAAD,CAAlB;CARF;;CAUD;CACCrC,UAAAA,GAAG,CAAC,EAAD,EAAKH,EAAL,CAAH;CAxCF;CA0CA,KAnED;CAqEA,WAAOwG,KAAP;CACA;;CAMD,WAAS2H,mBAAT,CAA6BlM,GAA7B;CACC,QAAI,CAACrB,WAAW,CAACqB,GAAD,CAAhB,EAAuB,OAAOA,GAAP;CACvB,QAAInB,KAAK,CAACC,OAAN,CAAckB,GAAd,CAAJ,EAAwB,OAAOA,GAAG,CAACoM,GAAJ,CAAQF,mBAAR,CAAP;CACxB,QAAIlN,KAAK,CAACgB,GAAD,CAAT,EACC,OAAO,IAAIjD,GAAJ,CACN8B,KAAK,CAACmL,IAAN,CAAWhK,GAAG,CAACqM,OAAJ,EAAX,EAA0BD,GAA1B,CAA8B;CAAA,UAAEE,CAAF;CAAA,UAAKC,CAAL;CAAA,aAAY,CAACD,CAAD,EAAIJ,mBAAmB,CAACK,CAAD,CAAvB,CAAZ;CAAA,KAA9B,CADM,CAAP;CAGD,QAAItN,KAAK,CAACe,GAAD,CAAT,EAAgB,OAAO,IAAI/C,GAAJ,CAAQ4B,KAAK,CAACmL,IAAN,CAAWhK,GAAX,EAAgBoM,GAAhB,CAAoBF,mBAApB,CAAR,CAAP;CAChB,QAAMM,MAAM,GAAGrN,MAAM,CAACqD,MAAP,CAAcrD,MAAM,CAACI,cAAP,CAAsBS,GAAtB,CAAd,CAAf;;CACA,SAAK,IAAMO,GAAX,IAAkBP,GAAlB;CAAuBwM,MAAAA,MAAM,CAACjM,GAAD,CAAN,GAAc2L,mBAAmB,CAAClM,GAAG,CAACO,GAAD,CAAJ,CAAjC;CAAvB;;CACA,QAAIW,GAAG,CAAClB,GAAD,EAAMyM,SAAN,CAAP,EAAyBD,MAAM,CAACC,SAAD,CAAN,GAAoBzM,GAAG,CAACyM,SAAD,CAAvB;CACzB,WAAOD,MAAP;CACA;;CAED,WAASd,uBAAT,CAAoC1L,GAApC;CACC,QAAIvB,OAAO,CAACuB,GAAD,CAAX,EAAkB;CACjB,aAAOkM,mBAAmB,CAAClM,GAAD,CAA1B;CACA,KAFD,MAEO,OAAOA,GAAP;CACP;;CAEDkD,EAAAA,UAAU,CAAC,SAAD,EAAY;CACrBoG,IAAAA,aAAa,EAAbA,aADqB;CAErB1D,IAAAA,gBAAgB,EAAhBA,gBAFqB;CAGrBR,IAAAA,2BAA2B,EAA3BA;CAHqB,GAAZ,CAAV;CAKA;;CChTD;AACA,UAmBgBsH;CACf;CACA,MAAIC,cAAa,GAAG,uBAASC,CAAT,EAAiBC,CAAjB;CACnBF,IAAAA,cAAa,GACZxN,MAAM,CAACsI,cAAP,IACC;CAACqF,MAAAA,SAAS,EAAE;CAAZ,iBAA2BjO,KAA3B,IACA,UAAS+N,CAAT,EAAYC,CAAZ;CACCD,MAAAA,CAAC,CAACE,SAAF,GAAcD,CAAd;CACA,KAJF,IAKA,UAASD,CAAT,EAAYC,CAAZ;CACC,WAAK,IAAItE,CAAT,IAAcsE,CAAd;CAAiB,YAAIA,CAAC,CAACpN,cAAF,CAAiB8I,CAAjB,CAAJ,EAAyBqE,CAAC,CAACrE,CAAD,CAAD,GAAOsE,CAAC,CAACtE,CAAD,CAAR;CAA1C;CACA,KARF;;CASA,WAAOoE,cAAa,CAACC,CAAD,EAAIC,CAAJ,CAApB;CACA,GAXD;;;CAcA,WAASE,SAAT,CAAmBH,CAAnB,EAA2BC,CAA3B;CACCF,IAAAA,cAAa,CAACC,CAAD,EAAIC,CAAJ,CAAb;;CACA,aAASG,EAAT;CACC,WAAKjO,WAAL,GAAmB6N,CAAnB;CACA;;CACDA,IAAAA,CAAC,CAACxN,SAAF;CAEG4N,IAAAA,EAAE,CAAC5N,SAAH,GAAeyN,CAAC,CAACzN,SAAlB,EAA8B,IAAI4N,EAAJ,EAFhC;CAGA;;CAED,MAAMC,QAAQ,GAAI,UAASC,MAAT;CACjBH,IAAAA,SAAS,CAACE,QAAD,EAAWC,MAAX,CAAT;;;CAEA,aAASD,QAAT,CAA6B7M,MAA7B,EAA6CgG,MAA7C;CACC,WAAK3I,WAAL,IAAoB;CACnBwD,QAAAA,KAAK;;CADc;CAEnBsC,QAAAA,OAAO,EAAE6C,MAFU;CAGnBZ,QAAAA,MAAM,EAAEY,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAH7B;CAInB4B,QAAAA,SAAS,EAAE,KAJQ;CAKnBQ,QAAAA,UAAU,EAAE,KALO;CAMnB5D,QAAAA,KAAK,EAAEiD,SANY;CAOnBmB,QAAAA,SAAS,EAAEnB,SAPQ;CAQnBjF,QAAAA,KAAK,EAAEO,MARY;CASnBsF,QAAAA,MAAM,EAAE,IATW;CAUnBW,QAAAA,SAAS,EAAE,KAVQ;CAWnB5B,QAAAA,QAAQ,EAAE;CAXS,OAApB;CAaA,aAAO,IAAP;CACA;;CACD,QAAM8D,CAAC,GAAG0E,QAAQ,CAAC7N,SAAnB;CAEAD,IAAAA,MAAM,CAACqI,cAAP,CAAsBe,CAAtB,EAAyB,MAAzB,EAAiC;CAChCnH,MAAAA,GAAG,EAAE;CACJ,eAAOQ,MAAM,CAAC,KAAKnE,WAAL,CAAD,CAAN,CAA0B0P,IAAjC;CACA,OAH+B;CAKhC;;CALgC,KAAjC;;CAQA5E,IAAAA,CAAC,CAACrH,GAAF,GAAQ,UAASX,GAAT;CACP,aAAOqB,MAAM,CAAC,KAAKnE,WAAL,CAAD,CAAN,CAA0ByD,GAA1B,CAA8BX,GAA9B,CAAP;CACA,KAFD;;CAIAgI,IAAAA,CAAC,CAAClH,GAAF,GAAQ,UAASd,GAAT,EAAmB7B,KAAnB;CACP,UAAMsC,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;CACA,UAAI,CAACY,MAAM,CAACZ,KAAD,CAAN,CAAcE,GAAd,CAAkBX,GAAlB,CAAD,IAA2BqB,MAAM,CAACZ,KAAD,CAAN,CAAcI,GAAd,CAAkBb,GAAlB,MAA2B7B,KAA1D,EAAiE;CAChE0O,QAAAA,cAAc,CAACpM,KAAD,CAAd;CACAmG,QAAAA,WAAW,CAACnG,KAAD,CAAX;CACAA,QAAAA,KAAK,CAACiF,SAAN,CAAiB5E,GAAjB,CAAqBd,GAArB,EAA0B,IAA1B;CACAS,QAAAA,KAAK,CAACa,KAAN,CAAaR,GAAb,CAAiBd,GAAjB,EAAsB7B,KAAtB;CACAsC,QAAAA,KAAK,CAACiF,SAAN,CAAiB5E,GAAjB,CAAqBd,GAArB,EAA0B,IAA1B;CACA;;CACD,aAAO,IAAP;CACA,KAXD;;CAaAgI,IAAAA,CAAC,CAAC1F,MAAF,GAAW,UAAStC,GAAT;CACV,UAAI,CAAC,KAAKW,GAAL,CAASX,GAAT,CAAL,EAAoB;CACnB,eAAO,KAAP;CACA;;CAED,UAAMS,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;CACAoM,MAAAA,cAAc,CAACpM,KAAD,CAAd;CACAmG,MAAAA,WAAW,CAACnG,KAAD,CAAX;;CACA,UAAIA,KAAK,CAACnB,KAAN,CAAYqB,GAAZ,CAAgBX,GAAhB,CAAJ,EAA0B;CACzBS,QAAAA,KAAK,CAACiF,SAAN,CAAiB5E,GAAjB,CAAqBd,GAArB,EAA0B,KAA1B;CACA,OAFD,MAEO;CACNS,QAAAA,KAAK,CAACiF,SAAN,CAAiBpD,MAAjB,CAAwBtC,GAAxB;CACA;;CACDS,MAAAA,KAAK,CAACa,KAAN,CAAagB,MAAb,CAAoBtC,GAApB;CACA,aAAO,IAAP;CACA,KAhBD;;CAkBAgI,IAAAA,CAAC,CAAC3F,KAAF,GAAU;CACT,UAAM5B,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;CACA,UAAIY,MAAM,CAACZ,KAAD,CAAN,CAAcmM,IAAlB,EAAwB;CACvBC,QAAAA,cAAc,CAACpM,KAAD,CAAd;CACAmG,QAAAA,WAAW,CAACnG,KAAD,CAAX;CACAA,QAAAA,KAAK,CAACiF,SAAN,GAAkB,IAAIlJ,GAAJ,EAAlB;CACA0D,QAAAA,IAAI,CAACO,KAAK,CAACnB,KAAP,EAAc,UAAAU,GAAG;CACpBS,UAAAA,KAAK,CAACiF,SAAN,CAAiB5E,GAAjB,CAAqBd,GAArB,EAA0B,KAA1B;CACA,SAFG,CAAJ;CAGAS,QAAAA,KAAK,CAACa,KAAN,CAAae,KAAb;CACA;CACD,KAZD;;CAcA2F,IAAAA,CAAC,CAACjI,OAAF,GAAY,UACX+M,EADW,EAEXC,OAFW;;;CAIX,UAAMtM,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACAmE,MAAAA,MAAM,CAACZ,KAAD,CAAN,CAAcV,OAAd,CAAsB,UAACiN,MAAD,EAAchN,GAAd,EAAwBiN,IAAxB;CACrBH,QAAAA,EAAE,CAAC3N,IAAH,CAAQ4N,OAAR,EAAiB,KAAI,CAAClM,GAAL,CAASb,GAAT,CAAjB,EAAgCA,GAAhC,EAAqC,KAArC;CACA,OAFD;CAGA,KARD;;CAUAgI,IAAAA,CAAC,CAACnH,GAAF,GAAQ,UAASb,GAAT;CACP,UAAMS,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;CACA,UAAMtC,KAAK,GAAGkD,MAAM,CAACZ,KAAD,CAAN,CAAcI,GAAd,CAAkBb,GAAlB,CAAd;;CACA,UAAIS,KAAK,CAACyE,UAAN,IAAoB,CAAC9G,WAAW,CAACD,KAAD,CAApC,EAA6C;CAC5C,eAAOA,KAAP;CACA;;CACD,UAAIA,KAAK,KAAKsC,KAAK,CAACnB,KAAN,CAAYuB,GAAZ,CAAgBb,GAAhB,CAAd,EAAoC;CACnC,eAAO7B,KAAP,CADmC;CAEnC;;;CAED,UAAM6F,KAAK,GAAGwC,WAAW,CAAC/F,KAAK,CAACwE,MAAN,CAAahC,MAAd,EAAsB9E,KAAtB,EAA6BsC,KAA7B,CAAzB;CACAoM,MAAAA,cAAc,CAACpM,KAAD,CAAd;CACAA,MAAAA,KAAK,CAACa,KAAN,CAAaR,GAAb,CAAiBd,GAAjB,EAAsBgE,KAAtB;CACA,aAAOA,KAAP;CACA,KAfD;;CAiBAgE,IAAAA,CAAC,CAAC1H,IAAF,GAAS;CACR,aAAOe,MAAM,CAAC,KAAKnE,WAAL,CAAD,CAAN,CAA0BoD,IAA1B,EAAP;CACA,KAFD;;CAIA0H,IAAAA,CAAC,CAACkF,MAAF,GAAW;;;;CACV,UAAM9P,QAAQ,GAAG,KAAKkD,IAAL,EAAjB;CACA,6BACEnD,cADF,IACmB;CAAA,eAAM,MAAI,CAAC+P,MAAL,EAAN;CAAA,OADnB,OAECC,IAFD,GAEO;CACL,YAAMC,CAAC,GAAGhQ,QAAQ,CAAC+P,IAAT,EAAV;CACA;;CACA,YAAIC,CAAC,CAACC,IAAN,EAAY,OAAOD,CAAP;;CACZ,YAAMjP,KAAK,GAAG,MAAI,CAAC0C,GAAL,CAASuM,CAAC,CAACjP,KAAX,CAAd;;CACA,eAAO;CACNkP,UAAAA,IAAI,EAAE,KADA;CAENlP,UAAAA,KAAK,EAALA;CAFM,SAAP;CAIA,OAXF;CAaA,KAfD;;CAiBA6J,IAAAA,CAAC,CAAC8D,OAAF,GAAY;;;;CACX,UAAM1O,QAAQ,GAAG,KAAKkD,IAAL,EAAjB;CACA,+BACEnD,cADF,IACmB;CAAA,eAAM,MAAI,CAAC2O,OAAL,EAAN;CAAA,OADnB,QAECqB,IAFD,GAEO;CACL,YAAMC,CAAC,GAAGhQ,QAAQ,CAAC+P,IAAT,EAAV;CACA;;CACA,YAAIC,CAAC,CAACC,IAAN,EAAY,OAAOD,CAAP;;CACZ,YAAMjP,KAAK,GAAG,MAAI,CAAC0C,GAAL,CAASuM,CAAC,CAACjP,KAAX,CAAd;;CACA,eAAO;CACNkP,UAAAA,IAAI,EAAE,KADA;CAENlP,UAAAA,KAAK,EAAE,CAACiP,CAAC,CAACjP,KAAH,EAAUA,KAAV;CAFD,SAAP;CAIA,OAXF;CAaA,KAfD;;CAiBA6J,IAAAA,CAAC,CAAC7K,cAAD,CAAD,GAAoB;CACnB,aAAO,KAAK2O,OAAL,EAAP;CACA,KAFD;;CAIA,WAAOY,QAAP;CACA,GApJgB,CAoJdlQ,GApJc,CAAjB;;CAsJA,WAASwM,SAAT,CAAqCnJ,MAArC,EAAgDgG,MAAhD;CACC;CACA,WAAO,IAAI6G,QAAJ,CAAa7M,MAAb,EAAqBgG,MAArB,CAAP;CACA;;CAED,WAASgH,cAAT,CAAwBpM,KAAxB;CACC,QAAI,CAACA,KAAK,CAACa,KAAX,EAAkB;CACjBb,MAAAA,KAAK,CAACiF,SAAN,GAAkB,IAAIlJ,GAAJ,EAAlB;CACAiE,MAAAA,KAAK,CAACa,KAAN,GAAc,IAAI9E,GAAJ,CAAQiE,KAAK,CAACnB,KAAd,CAAd;CACA;CACD;;CAED,MAAMgO,QAAQ,GAAI,UAASX,MAAT;CACjBH,IAAAA,SAAS,CAACc,QAAD,EAAWX,MAAX,CAAT;;;CAEA,aAASW,QAAT,CAA6BzN,MAA7B,EAA6CgG,MAA7C;CACC,WAAK3I,WAAL,IAAoB;CACnBwD,QAAAA,KAAK;;CADc;CAEnBsC,QAAAA,OAAO,EAAE6C,MAFU;CAGnBZ,QAAAA,MAAM,EAAEY,MAAM,GAAGA,MAAM,CAACZ,MAAV,GAAmBnC,eAAe,EAH7B;CAInB4B,QAAAA,SAAS,EAAE,KAJQ;CAKnBQ,QAAAA,UAAU,EAAE,KALO;CAMnB5D,QAAAA,KAAK,EAAEiD,SANY;CAOnBjF,QAAAA,KAAK,EAAEO,MAPY;CAQnBsF,QAAAA,MAAM,EAAE,IARW;CASnBjC,QAAAA,OAAO,EAAE,IAAI1G,GAAJ,EATU;CAUnB0H,QAAAA,QAAQ,EAAE,KAVS;CAWnB4B,QAAAA,SAAS,EAAE;CAXQ,OAApB;CAaA,aAAO,IAAP;CACA;;CACD,QAAMkC,CAAC,GAAGsF,QAAQ,CAACzO,SAAnB;CAEAD,IAAAA,MAAM,CAACqI,cAAP,CAAsBe,CAAtB,EAAyB,MAAzB,EAAiC;CAChCnH,MAAAA,GAAG,EAAE;CACJ,eAAOQ,MAAM,CAAC,KAAKnE,WAAL,CAAD,CAAN,CAA0B0P,IAAjC;CACA,OAH+B;;CAAA,KAAjC;;CAOA5E,IAAAA,CAAC,CAACrH,GAAF,GAAQ,UAASxC,KAAT;CACP,UAAMsC,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;CAEA,UAAI,CAACA,KAAK,CAACa,KAAX,EAAkB;CACjB,eAAOb,KAAK,CAACnB,KAAN,CAAYqB,GAAZ,CAAgBxC,KAAhB,CAAP;CACA;;CACD,UAAIsC,KAAK,CAACa,KAAN,CAAYX,GAAZ,CAAgBxC,KAAhB,CAAJ,EAA4B,OAAO,IAAP;CAC5B,UAAIsC,KAAK,CAACyC,OAAN,CAAcvC,GAAd,CAAkBxC,KAAlB,KAA4BsC,KAAK,CAACa,KAAN,CAAYX,GAAZ,CAAgBF,KAAK,CAACyC,OAAN,CAAcrC,GAAd,CAAkB1C,KAAlB,CAAhB,CAAhC,EACC,OAAO,IAAP;CACD,aAAO,KAAP;CACA,KAXD;;CAaA6J,IAAAA,CAAC,CAAC/G,GAAF,GAAQ,UAAS9C,KAAT;CACP,UAAMsC,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;CACA,UAAI,CAAC,KAAKE,GAAL,CAASxC,KAAT,CAAL,EAAsB;CACrBoP,QAAAA,cAAc,CAAC9M,KAAD,CAAd;CACAmG,QAAAA,WAAW,CAACnG,KAAD,CAAX;CACAA,QAAAA,KAAK,CAACa,KAAN,CAAaL,GAAb,CAAiB9C,KAAjB;CACA;;CACD,aAAO,IAAP;CACA,KATD;;CAWA6J,IAAAA,CAAC,CAAC1F,MAAF,GAAW,UAASnE,KAAT;CACV,UAAI,CAAC,KAAKwC,GAAL,CAASxC,KAAT,CAAL,EAAsB;CACrB,eAAO,KAAP;CACA;;CAED,UAAMsC,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;CACA8M,MAAAA,cAAc,CAAC9M,KAAD,CAAd;CACAmG,MAAAA,WAAW,CAACnG,KAAD,CAAX;CACA,aACCA,KAAK,CAACa,KAAN,CAAagB,MAAb,CAAoBnE,KAApB,MACCsC,KAAK,CAACyC,OAAN,CAAcvC,GAAd,CAAkBxC,KAAlB,IACEsC,KAAK,CAACa,KAAN,CAAagB,MAAb,CAAoB7B,KAAK,CAACyC,OAAN,CAAcrC,GAAd,CAAkB1C,KAAlB,CAApB,CADF;CAEE;CAA2B,WAH9B,CADD;CAMA,KAfD;;CAiBA6J,IAAAA,CAAC,CAAC3F,KAAF,GAAU;CACT,UAAM5B,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;;CACA,UAAIY,MAAM,CAACZ,KAAD,CAAN,CAAcmM,IAAlB,EAAwB;CACvBW,QAAAA,cAAc,CAAC9M,KAAD,CAAd;CACAmG,QAAAA,WAAW,CAACnG,KAAD,CAAX;CACAA,QAAAA,KAAK,CAACa,KAAN,CAAae,KAAb;CACA;CACD,KARD;;CAUA2F,IAAAA,CAAC,CAACkF,MAAF,GAAW;CACV,UAAMzM,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;CACA8M,MAAAA,cAAc,CAAC9M,KAAD,CAAd;CACA,aAAOA,KAAK,CAACa,KAAN,CAAa4L,MAAb,EAAP;CACA,KALD;;CAOAlF,IAAAA,CAAC,CAAC8D,OAAF,GAAY,SAASA,OAAT;CACX,UAAMrL,KAAK,GAAa,KAAKvD,WAAL,CAAxB;CACA6M,MAAAA,eAAe,CAACtJ,KAAD,CAAf;CACA8M,MAAAA,cAAc,CAAC9M,KAAD,CAAd;CACA,aAAOA,KAAK,CAACa,KAAN,CAAawK,OAAb,EAAP;CACA,KALD;;CAOA9D,IAAAA,CAAC,CAAC1H,IAAF,GAAS;CACR,aAAO,KAAK4M,MAAL,EAAP;CACA,KAFD;;CAIAlF,IAAAA,CAAC,CAAC7K,cAAD,CAAD,GAAoB;CACnB,aAAO,KAAK+P,MAAL,EAAP;CACA,KAFD;;CAIAlF,IAAAA,CAAC,CAACjI,OAAF,GAAY,SAASA,OAAT,CAAiB+M,EAAjB,EAA0BC,OAA1B;CACX,UAAM3P,QAAQ,GAAG,KAAK8P,MAAL,EAAjB;CACA,UAAI9I,MAAM,GAAGhH,QAAQ,CAAC+P,IAAT,EAAb;;CACA,aAAO,CAAC/I,MAAM,CAACiJ,IAAf,EAAqB;CACpBP,QAAAA,EAAE,CAAC3N,IAAH,CAAQ4N,OAAR,EAAiB3I,MAAM,CAACjG,KAAxB,EAA+BiG,MAAM,CAACjG,KAAtC,EAA6C,IAA7C;CACAiG,QAAAA,MAAM,GAAGhH,QAAQ,CAAC+P,IAAT,EAAT;CACA;CACD,KAPD;;CASA,WAAOG,QAAP;CACA,GA/GgB,CA+Gd5Q,GA/Gc,CAAjB;;CAiHA,WAASuM,SAAT,CAAqCpJ,MAArC,EAAgDgG,MAAhD;CACC;CACA,WAAO,IAAIyH,QAAJ,CAAazN,MAAb,EAAqBgG,MAArB,CAAP;CACA;;CAED,WAAS0H,cAAT,CAAwB9M,KAAxB;CACC,QAAI,CAACA,KAAK,CAACa,KAAX,EAAkB;CACjB;CACAb,MAAAA,KAAK,CAACa,KAAN,GAAc,IAAI5E,GAAJ,EAAd;CACA+D,MAAAA,KAAK,CAACnB,KAAN,CAAYS,OAAZ,CAAoB,UAAA5B,KAAK;CACxB,YAAIC,WAAW,CAACD,KAAD,CAAf,EAAwB;CACvB,cAAM6F,KAAK,GAAGwC,WAAW,CAAC/F,KAAK,CAACwE,MAAN,CAAahC,MAAd,EAAsB9E,KAAtB,EAA6BsC,KAA7B,CAAzB;CACAA,UAAAA,KAAK,CAACyC,OAAN,CAAcpC,GAAd,CAAkB3C,KAAlB,EAAyB6F,KAAzB;CACAvD,UAAAA,KAAK,CAACa,KAAN,CAAaL,GAAb,CAAiB+C,KAAjB;CACA,SAJD,MAIO;CACNvD,UAAAA,KAAK,CAACa,KAAN,CAAaL,GAAb,CAAiB9C,KAAjB;CACA;CACD,OARD;CASA;CACD;;CAED,WAAS4L,eAAT,CAAyBtJ;CAAW;CAApC;CACC,QAAIA,KAAK,CAACyD,QAAV,EAAoBvG,GAAG,CAAC,CAAD,EAAI8M,IAAI,CAACC,SAAL,CAAerJ,MAAM,CAACZ,KAAD,CAArB,CAAJ,CAAH;CACpB;;CAEDkC,EAAAA,UAAU,CAAC,QAAD,EAAW;CAACqG,IAAAA,SAAS,EAATA,SAAD;CAAYC,IAAAA,SAAS,EAATA;CAAZ,GAAX,CAAV;CACA;;UCvVeuE;CACf9D,EAAAA,SAAS;CACTyC,EAAAA,YAAY;CACZxB,EAAAA,aAAa;CACb;;CCcD,IAAM5G,KAAK;CAAA;CAAG,IAAIuD,KAAJ,EAAd;CAEA;;;;;;;;;;;;;;;;;;;;AAmBA,KAAaM,OAAO,GAAa7D,KAAK,CAAC6D,OAAhC;AACP,CAEA;;;;;AAIA,KAAaM,kBAAkB;CAAA;CAAwBnE,KAAK,CAACmE,kBAAN,CAAyBuF,IAAzB,CACtD1J,KADsD,CAAhD;CAIP;;;;;;AAKA,KAAa0E,aAAa;CAAA;CAAG1E,KAAK,CAAC0E,aAAN,CAAoBgF,IAApB,CAAyB1J,KAAzB,CAAtB;CAEP;;;;;;;AAMA,KAAawE,aAAa;CAAA;CAAGxE,KAAK,CAACwE,aAAN,CAAoBkF,IAApB,CAAyB1J,KAAzB,CAAtB;CAEP;;;;;;AAKA,KAAa6E,YAAY;CAAA;CAAG7E,KAAK,CAAC6E,YAAN,CAAmB6E,IAAnB,CAAwB1J,KAAxB,CAArB;CAEP;;;;;AAIA,KAAa2E,WAAW;CAAA;CAAG3E,KAAK,CAAC2E,WAAN,CAAkB+E,IAAlB,CAAuB1J,KAAvB,CAApB;CAEP;;;;;;;;;AAQA,KAAa4E,WAAW;CAAA;CAAG5E,KAAK,CAAC4E,WAAN,CAAkB8E,IAAlB,CAAuB1J,KAAvB,CAApB;CAEP;;;;;;;AAMA,UAAgB2J,UAAavP;CAC5B,SAAOA,KAAP;CACA;CAED;;;;;;AAKA,UAAgBwP,cAAiBxP;CAChC,SAAOA,KAAP;CACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}