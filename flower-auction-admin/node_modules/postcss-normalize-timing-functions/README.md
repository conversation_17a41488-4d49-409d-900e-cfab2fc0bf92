# [postcss][postcss]-normalize-timing-functions

> Normalize timing functions with PostCSS.

## Install

With [npm](https://npmjs.org/package/postcss-normalize-timing-functions) do:

```
npm install postcss-normalize-timing-functions --save
```

## Example

### Input

```css
div {
    animate: fade 3s cubic-bezier(0.42, 0, 1, 1)
}
```

### Output

```css
div {
    animate: fade 3s ease-in
}
``` 

## Usage

See the [PostCSS documentation](https://github.com/postcss/postcss#usage) for
examples for your environment.

## Contributors

See [CONTRIBUTORS.md](https://github.com/cssnano/cssnano/blob/master/CONTRIBUTORS.md).

## License

MIT © [Ben Briggs](http://beneb.info)

[postcss]: https://github.com/postcss/postcss
