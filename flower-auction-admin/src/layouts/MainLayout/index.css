.main-layout {
  min-height: 100vh;
}

.site-layout {
  margin-left: 200px; /* 侧边栏展开时的宽度 */
  transition: margin-left 0.2s;
}

.site-layout-content {
  margin: 0;
  padding: 16px;
  background: #f0f2f5;
  min-height: calc(100vh - 64px);
}

.content-wrapper {
  margin: 0;
  padding: 24px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: calc(100vh - 64px - 32px - 48px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .site-layout-content {
    min-height: calc(100vh - 56px);
  }
}

/* 侧边栏收起时的样式 */
.site-layout.collapsed {
  margin-left: 80px;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .site-layout {
    margin-left: 0 !important;
  }

  .ant-layout-sider {
    position: fixed !important;
    z-index: 999;
    height: 100vh;
  }

  .ant-layout-sider-collapsed {
    margin-left: -200px;
  }
}
