import React, { useState, useEffect } from 'react';
import { Layout, ConfigProvider } from 'antd';
import { Outlet, useNavigate } from 'react-router-dom';
import SideMenu from '../../components/SideMenu';
import HeaderComponent from '../../components/Header';
import BreadcrumbComponent from '../../components/Breadcrumb';
import { useAuth } from '../../hooks/useAuth';
import zhCN from 'antd/lib/locale/zh_CN';
import './index.css';

const { Content } = Layout;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const { isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // 检查认证状态
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  if (!isAuthenticated) {
    return null; // 或者显示加载中
  }

  return (
    <ConfigProvider locale={zhCN}>
      <Layout className="main-layout">
        <SideMenu collapsed={collapsed} onCollapse={setCollapsed} />
        <Layout className="site-layout">
          <HeaderComponent collapsed={collapsed} toggle={toggleCollapsed} />
          <Content className="site-layout-content">
            <BreadcrumbComponent />
            <div className="content-wrapper">
              <Outlet />
            </div>
          </Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default MainLayout;