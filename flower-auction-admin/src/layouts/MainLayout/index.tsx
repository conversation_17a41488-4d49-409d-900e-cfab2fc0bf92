import React, { useState, useEffect } from 'react';
import { Layout, ConfigProvider, Spin } from 'antd';
import { Outlet, useNavigate } from 'react-router-dom';
import SideMenu from '../../components/SideMenu';
import HeaderComponent from '../../components/Header';
import BreadcrumbComponent from '../../components/Breadcrumb';
import { useAuth } from '../../hooks/useAuth';
import zhCN from 'antd/lib/locale/zh_CN';
import './index.css';

const { Content } = Layout;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const { isAuthenticated, user, loading } = useAuth();
  const navigate = useNavigate();

  // 检查认证状态
  useEffect(() => {
    // 如果不在加载中且未认证，跳转到登录页
    if (!loading && !isAuthenticated) {
      navigate('/login', { replace: true });
    }
  }, [isAuthenticated, loading, navigate]);

  // 监听窗口大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
      if (window.innerWidth < 768) {
        setCollapsed(true);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const toggleCollapsed = () => {
    setCollapsed(!collapsed);
  };

  // 如果正在加载认证状态，显示加载页面
  if (loading) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <Spin size="large" />
        <div style={{ color: '#666' }}>正在验证登录状态...</div>
      </div>
    );
  }

  // 如果未认证，不渲染主布局（会被useEffect重定向到登录页）
  if (!isAuthenticated) {
    return null;
  }

  return (
    <ConfigProvider locale={zhCN}>
      <Layout className="main-layout">
        <SideMenu collapsed={collapsed} onCollapse={setCollapsed} />
        <Layout className={`site-layout ${collapsed ? 'collapsed' : ''}`}>
          <HeaderComponent collapsed={collapsed} toggle={toggleCollapsed} />
          <Content className="site-layout-content">
            <BreadcrumbComponent />
            <div className="content-wrapper">
              <Outlet />
            </div>
          </Content>
        </Layout>
      </Layout>
    </ConfigProvider>
  );
};

export default MainLayout;