import React, { useState, useEffect } from 'react';
import {
  Card,
  Tree,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Switch,
  InputNumber,
  Descriptions,
  Tag,
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  FolderOutlined,
  FolderOpenOutlined,
} from '@ant-design/icons';
import type { DataNode } from 'antd/es/tree';
import { categoryService } from '../../../services/categoryService';
import './index.css';

const { Title } = Typography;
const { TextArea } = Input;

// 分类数据接口
export interface Category {
  id: number;
  name: string;
  code: string;
  description?: string;
  parentId?: number;
  level: number;
  sort: number;
  status: number;
  productCount: number;
  children?: Category[];
  createdAt: string;
  updatedAt: string;
}

const CategoryManagement: React.FC = () => {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [form] = Form.useForm();

  // 获取分类列表
  const fetchCategories = async () => {
    setLoading(true);
    try {
      const response = await categoryService.getCategoryTree();
      if (response.success) {
        setCategories(response.data);
        // 默认展开第一级
        const firstLevelKeys = response.data.map((cat: Category) => cat.id);
        setExpandedKeys(firstLevelKeys);
      } else {
        message.error(response.message || '获取分类列表失败');
      }
    } catch (error: any) {
      message.error(error.message || '获取分类列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchCategories();
  }, []);

  // 新增分类
  const handleAdd = (parentCategory?: Category) => {
    setEditingCategory(null);
    form.resetFields();
    if (parentCategory) {
      form.setFieldsValue({
        parentId: parentCategory.id,
        level: parentCategory.level + 1,
      });
    }
    setIsModalVisible(true);
  };

  // 编辑分类
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    form.setFieldsValue({
      ...category,
      status: category.status === 1,
    });
    setIsModalVisible(true);
  };

  // 删除分类
  const handleDelete = async (category: Category) => {
    if (category.productCount > 0) {
      message.error('该分类下还有商品，无法删除');
      return;
    }

    if (category.children && category.children.length > 0) {
      message.error('该分类下还有子分类，无法删除');
      return;
    }

    try {
      const response = await categoryService.deleteCategory(category.id);
      if (response.success) {
        message.success('删除成功');
        fetchCategories();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存分类
  const handleSave = async (values: any) => {
    try {
      const categoryData = {
        ...values,
        status: values.status ? 1 : 0,
      };

      let response;
      if (editingCategory) {
        response = await categoryService.updateCategory(editingCategory.id, categoryData);
      } else {
        response = await categoryService.createCategory(categoryData);
      }

      if (response.success) {
        message.success(editingCategory ? '更新成功' : '创建成功');
        setIsModalVisible(false);
        fetchCategories();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error: any) {
      message.error(error.message || '保存失败');
    }
  };

  // 转换分类数据为树形结构
  const convertCategoriesToTreeData = (categories: Category[]): DataNode[] => {
    return categories.map(category => ({
      title: (
        <div className="category-tree-node">
          <Space>
            <span className="category-name">{category.name}</span>
            <span className="category-code">({category.code})</span>
            <span className="product-count">{category.productCount}个商品</span>
            {category.status === 0 && <span className="status-disabled">已禁用</span>}
          </Space>
          <Space className="category-actions">
            <Button
              type="link"
              size="small"
              icon={<PlusOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleAdd(category);
              }}
            >
              添加子分类
            </Button>
            <Button
              type="link"
              size="small"
              icon={<EditOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleEdit(category);
              }}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定要删除这个分类吗？"
              description="删除后无法恢复"
              onConfirm={(e) => {
                e?.stopPropagation();
                handleDelete(category);
              }}
              onCancel={(e) => e?.stopPropagation()}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => e.stopPropagation()}
                disabled={category.productCount > 0 || (category.children && category.children.length > 0)}
              >
                删除
              </Button>
            </Popconfirm>
          </Space>
        </div>
      ),
      key: category.id,
      icon: category.children && category.children.length > 0 ? <FolderOpenOutlined /> : <FolderOutlined />,
      children: category.children ? convertCategoriesToTreeData(category.children) : undefined,
    }));
  };

  // 树节点选择处理
  const handleTreeSelect = (selectedKeys: React.Key[]) => {
    if (selectedKeys.length > 0) {
      const findCategory = (cats: Category[], id: number): Category | null => {
        for (const cat of cats) {
          if (cat.id === id) return cat;
          if (cat.children) {
            const found = findCategory(cat.children, id);
            if (found) return found;
          }
        }
        return null;
      };

      const category = findCategory(categories, selectedKeys[0] as number);
      setSelectedCategory(category);
    } else {
      setSelectedCategory(null);
    }
  };

  return (
    <div className="category-management-container">
      <Title level={2}>分类管理</Title>

      <Row gutter={24}>
        {/* 左侧分类树 */}
        <Col xs={24} lg={16}>
          <Card
            title="分类树"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleAdd()}
                >
                  添加根分类
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchCategories}
                  loading={loading}
                >
                  刷新
                </Button>
              </Space>
            }
          >
            <Tree
              showIcon
              expandedKeys={expandedKeys}
              onExpand={setExpandedKeys}
              onSelect={handleTreeSelect}
              treeData={convertCategoriesToTreeData(categories)}
              height={600}
            />
          </Card>
        </Col>

        {/* 右侧分类详情 */}
        <Col xs={24} lg={8}>
          <Card title="分类详情">
            {selectedCategory ? (
              <Descriptions column={1} size="small">
                <Descriptions.Item label="分类名称">
                  {selectedCategory.name}
                </Descriptions.Item>
                <Descriptions.Item label="分类编码">
                  {selectedCategory.code}
                </Descriptions.Item>
                <Descriptions.Item label="分类描述">
                  {selectedCategory.description || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="分类级别">
                  第{selectedCategory.level}级
                </Descriptions.Item>
                <Descriptions.Item label="排序">
                  {selectedCategory.sort}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Tag color={selectedCategory.status === 1 ? 'green' : 'red'}>
                    {selectedCategory.status === 1 ? '启用' : '禁用'}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="商品数量">
                  {selectedCategory.productCount}个
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {new Date(selectedCategory.createdAt).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {new Date(selectedCategory.updatedAt).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', color: '#999', padding: '40px 0' }}>
                请选择一个分类查看详情
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 分类编辑模态框 */}
      <Modal
        title={editingCategory ? '编辑分类' : '新增分类'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}

      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="分类名称"
                rules={[
                  { required: true, message: '请输入分类名称' },
                  { max: 50, message: '分类名称不能超过50个字符' },
                ]}
              >
                <Input placeholder="请输入分类名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="分类编码"
                rules={[
                  { required: true, message: '请输入分类编码' },
                  { pattern: /^[A-Z0-9_]+$/, message: '分类编码只能包含大写字母、数字和下划线' },
                  { max: 50, message: '分类编码不能超过50个字符' },
                ]}
              >
                <Input placeholder="请输入分类编码" disabled={!!editingCategory} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="分类描述"
            rules={[
              { max: 200, message: '分类描述不能超过200个字符' },
            ]}
          >
            <TextArea
              placeholder="请输入分类描述"
              rows={4}
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="sort"
                label="排序"
                rules={[{ required: true, message: '请输入排序值' }]}
                initialValue={0}
              >
                <InputNumber
                  placeholder="请输入排序值"
                  min={0}
                  max={9999}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="parentId" hidden>
            <Input />
          </Form.Item>

          <Form.Item name="level" hidden>
            <Input />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingCategory ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoryManagement;