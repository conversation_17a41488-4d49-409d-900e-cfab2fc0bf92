import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Upload,
  Image,
  InputNumber,
  Descriptions,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';
import { productService } from '../../../services/productService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 商品质量等级枚举
export enum QualityLevel {
  EXCELLENT = 1, // 优
  GOOD = 2,      // 良
  MEDIUM = 3,    // 中
}

// 商品状态枚举
export enum ProductStatus {
  OFFLINE = 0, // 下架
  ONLINE = 1,  // 上架
}

// 商品数据接口
export interface Product {
  id: number;
  name: string;
  categoryId: number;
  categoryName: string;
  description?: string;
  qualityLevel: QualityLevel;
  origin: string;
  supplierId: number;
  supplierName: string;
  status: ProductStatus;
  images?: string[];
  createdAt: string;
  updatedAt: string;
}

// 商品类别接口
export interface Category {
  id: number;
  name: string;
  parentId?: number;
  level: number;
}

// 查询参数接口
interface ProductQueryParams {
  name?: string;
  categoryId?: number;
  qualityLevel?: QualityLevel;
  status?: ProductStatus;
  origin?: string;
  page: number;
  pageSize: number;
}

const ProductList: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<ProductQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [form] = Form.useForm();

  // 质量等级映射
  const qualityLevelMap = {
    [QualityLevel.EXCELLENT]: { label: '优', color: 'green' },
    [QualityLevel.GOOD]: { label: '良', color: 'blue' },
    [QualityLevel.MEDIUM]: { label: '中', color: 'orange' },
  };

  // 获取商品列表
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await productService.getProductList(queryParams);
      if (response.success) {
        setProducts(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取商品列表失败');
      }
    } catch (error: any) {
      message.error(error.message || '获取商品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取商品类别
  const fetchCategories = async () => {
    try {
      const response = await productService.getCategoryList();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error: any) {
      console.error('获取商品类别失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增商品
  const handleAdd = () => {
    setEditingProduct(null);
    form.resetFields();
    setFileList([]);
    setIsModalVisible(true);
  };

  // 编辑商品
  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    form.setFieldsValue({
      ...product,
      status: product.status === ProductStatus.ONLINE,
    });

    // 设置图片列表
    if (product.images && product.images.length > 0) {
      const imageFiles = product.images.map((url, index) => ({
        uid: `${index}`,
        name: `image-${index}`,
        status: 'done' as const,
        url: url,
      }));
      setFileList(imageFiles);
    } else {
      setFileList([]);
    }

    setIsModalVisible(true);
  };

  // 查看商品详情
  const handleView = (product: Product) => {
    setViewingProduct(product);
    setIsDetailVisible(true);
  };

  // 删除商品
  const handleDelete = async (id: number) => {
    try {
      const response = await productService.deleteProduct(id);
      if (response.success) {
        message.success('删除成功');
        fetchProducts();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存商品
  const handleSave = async (values: any) => {
    try {
      const productData = {
        ...values,
        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,
        images: fileList.map(file => file.url || file.response?.url).filter(Boolean),
      };

      let response;
      if (editingProduct) {
        response = await productService.updateProduct(editingProduct.id, productData);
      } else {
        response = await productService.createProduct(productData);
      }

      if (response.success) {
        message.success(editingProduct ? '更新成功' : '创建成功');
        setIsModalVisible(false);
        fetchProducts();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error: any) {
      message.error(error.message || '保存失败');
    }
  };

  // 切换商品状态
  const handleToggleStatus = async (product: Product) => {
    try {
      const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;
      const response = await productService.updateProductStatus(product.id, newStatus);
      if (response.success) {
        message.success('状态更新成功');
        fetchProducts();
      } else {
        message.error(response.message || '状态更新失败');
      }
    } catch (error: any) {
      message.error(error.message || '状态更新失败');
    }
  };

  // 图片上传处理
  const handleUploadChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  };

  // 表格列定义
  const columns: ColumnsType<Product> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '商品图片',
      dataIndex: 'images',
      key: 'images',
      width: 100,
      render: (images: string[]) => (
        images && images.length > 0 ? (
          <Image
            width={60}
            height={60}
            src={images[0]}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            无图片
          </div>
        )
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string) => (
        <div style={{ fontWeight: 500 }}>{text}</div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '质量等级',
      dataIndex: 'qualityLevel',
      key: 'qualityLevel',
      width: 100,
      render: (level: QualityLevel) => {
        const levelInfo = qualityLevelMap[level];
        return (
          <Tag color={levelInfo?.color || 'default'}>
            {levelInfo?.label || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '产地',
      dataIndex: 'origin',
      key: 'origin',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ProductStatus, record: Product) => (
        <Switch
          checked={status === ProductStatus.ONLINE}
          onChange={() => handleToggleStatus(record)}
          checkedChildren="上架"
          unCheckedChildren="下架"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record: Product) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个商品吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="product-list-container">
      <Title level={2}>商品管理</Title>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="name" label="商品名称">
                <Input placeholder="请输入商品名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="categoryId" label="商品分类">
                <Select placeholder="请选择商品分类" allowClear>
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="qualityLevel" label="质量等级">
                <Select placeholder="请选择质量等级" allowClear>
                  <Option value={QualityLevel.EXCELLENT}>优</Option>
                  <Option value={QualityLevel.GOOD}>良</Option>
                  <Option value={QualityLevel.MEDIUM}>中</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="商品状态">
                <Select placeholder="请选择商品状态" allowClear>
                  <Option value={ProductStatus.ONLINE}>上架</Option>
                  <Option value={ProductStatus.OFFLINE}>下架</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="origin" label="产地">
                <Input placeholder="请输入产地" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增商品
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchProducts}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 商品列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>
    </div>
  );
};

export default ProductList;