import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Upload,
  Image,

  Descriptions,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';
import { productService } from '../../../services/productService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 商品质量等级枚举
export enum QualityLevel {
  EXCELLENT = 1, // 优
  GOOD = 2,      // 良
  MEDIUM = 3,    // 中
}

// 商品状态枚举
export enum ProductStatus {
  OFFLINE = 0, // 下架
  ONLINE = 1,  // 上架
}

// 商品数据接口
export interface Product {
  id: number;
  name: string;
  categoryId: number;
  categoryName: string;
  description?: string;
  qualityLevel: QualityLevel;
  origin: string;
  supplierId: number;
  supplierName: string;
  status: ProductStatus;
  images?: string[];
  createdAt: string;
  updatedAt: string;
}

// 商品类别接口
export interface Category {
  id: number;
  name: string;
  parentId?: number;
  level: number;
}

// 查询参数接口
interface ProductQueryParams {
  name?: string;
  categoryId?: number;
  qualityLevel?: QualityLevel;
  status?: ProductStatus;
  origin?: string;
  page: number;
  pageSize: number;
}

const ProductList: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<ProductQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [saving, setSaving] = useState(false);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // 质量等级映射
  const qualityLevelMap = {
    [QualityLevel.EXCELLENT]: { label: '优', color: 'green' },
    [QualityLevel.GOOD]: { label: '良', color: 'blue' },
    [QualityLevel.MEDIUM]: { label: '中', color: 'orange' },
  };

  // 获取商品列表
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await productService.getProductList(queryParams);
      if (response.success) {
        setProducts(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取商品列表失败');
        setProducts([]);
        setTotal(0);
      }
    } catch (error: any) {
      console.error('获取商品列表失败:', error);
      let errorMsg = '获取商品列表失败';
      if (error.response) {
        const { status } = error.response;
        if (status === 401) {
          errorMsg = '登录已过期，请重新登录';
        } else if (status === 403) {
          errorMsg = '没有权限访问商品列表';
        } else if (status === 500) {
          errorMsg = '服务器内部错误，请稍后重试';
        }
      }
      message.error(errorMsg);
      setProducts([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 获取商品类别
  const fetchCategories = async () => {
    try {
      const response = await productService.getCategoryList();
      if (response.success) {
        setCategories(response.data);
      } else {
        console.error('获取商品类别失败:', response.message);
        message.warning('获取商品分类失败，部分功能可能受限');
        setCategories([]);
      }
    } catch (error: any) {
      console.error('获取商品类别失败:', error);
      message.error('获取商品分类失败，请刷新页面重试');
      setCategories([]);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    // 过滤掉空值
    const filteredValues = Object.keys(values).reduce((acc, key) => {
      if (values[key] !== undefined && values[key] !== null && values[key] !== '') {
        acc[key] = values[key];
      }
      return acc;
    }, {} as any);

    setQueryParams({
      ...queryParams,
      ...filteredValues,
      page: 1,
    });

    message.info(`正在搜索商品...`);
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    const resetParams = {
      page: 1,
      pageSize: queryParams.pageSize, // 保持当前页面大小
    };
    setQueryParams(resetParams);
    message.success('搜索条件已重置');
  };

  // 快速搜索（输入框回车）
  const handleQuickSearch = (value: string) => {
    if (value.trim()) {
      searchForm.setFieldsValue({ name: value.trim() });
      handleSearch({ name: value.trim() });
    }
  };

  // 新增商品
  const handleAdd = () => {
    setEditingProduct(null);
    form.resetFields();
    setFileList([]);
    setIsModalVisible(true);
  };

  // 编辑商品
  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    form.setFieldsValue({
      ...product,
      status: product.status === ProductStatus.ONLINE,
    });

    // 设置图片列表
    if (product.images && product.images.length > 0) {
      const imageFiles = product.images.map((url, index) => ({
        uid: `${index}`,
        name: `image-${index}`,
        status: 'done' as const,
        url: url,
      }));
      setFileList(imageFiles);
    } else {
      setFileList([]);
    }

    setIsModalVisible(true);
  };

  // 查看商品详情
  const handleView = (product: Product) => {
    setViewingProduct(product);
    setIsDetailVisible(true);
  };

  // 删除商品
  const handleDelete = async (id: number) => {
    try {
      const response = await productService.deleteProduct(id);
      if (response.success) {
        message.success({
          content: '商品删除成功！',
          duration: 3,
        });
        // 如果当前页没有数据了，回到上一页
        if (products.length === 1 && queryParams.page > 1) {
          setQueryParams({
            ...queryParams,
            page: queryParams.page - 1,
          });
        } else {
          fetchProducts();
        }
      } else {
        message.error({
          content: response.message || '删除失败，请稍后重试',
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('删除商品失败:', error);
      message.error({
        content: '删除失败，请检查网络连接后重试',
        duration: 5,
      });
    }
  };

  // 保存商品
  const handleSave = async (values: any) => {
    setSaving(true);

    try {
      const productData = {
        ...values,
        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,
        images: fileList.map(file => file.url || file.response?.url).filter(Boolean),
      };

      let response;
      if (editingProduct) {
        response = await productService.updateProduct(editingProduct.id, productData);
      } else {
        response = await productService.createProduct(productData);
      }

      if (response.success) {
        const successMsg = editingProduct ? '商品信息更新成功！' : '商品创建成功！';
        message.success({
          content: successMsg,
          duration: 3,
        });
        setIsModalVisible(false);
        form.resetFields();
        setFileList([]);
        fetchProducts();
      } else {
        // 处理具体的错误信息
        let errorMsg = '操作失败';
        if (response.message) {
          if (response.message.includes('name')) {
            errorMsg = '商品名称已存在，请使用其他名称';
          } else if (response.message.includes('category')) {
            errorMsg = '商品分类不存在，请重新选择';
          } else if (response.message.includes('validation')) {
            errorMsg = '输入信息格式不正确，请检查后重试';
          } else {
            errorMsg = response.message;
          }
        }

        message.error({
          content: errorMsg,
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('保存商品失败:', error);

      let errorMsg = '保存失败，请稍后重试';
      if (error.response) {
        const { status, data } = error.response;
        if (status === 400) {
          if (data.error && data.error.includes('name')) {
            errorMsg = '商品名称格式不正确或已存在';
          } else if (data.error && data.error.includes('category')) {
            errorMsg = '商品分类无效，请重新选择';
          } else {
            errorMsg = data.error || '请求参数错误，请检查输入信息';
          }
        } else if (status === 409) {
          errorMsg = '商品信息冲突，商品名称可能已存在';
        } else if (status === 500) {
          errorMsg = '服务器内部错误，请联系管理员';
        } else {
          errorMsg = `请求失败 (${status})，请稍后重试`;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }

      message.error({
        content: errorMsg,
        duration: 5,
      });
    } finally {
      setSaving(false);
    }
  };

  // 切换商品状态
  const handleToggleStatus = async (product: Product) => {
    const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;
    const statusText = newStatus === ProductStatus.ONLINE ? '上架' : '下架';

    try {
      const response = await productService.updateProductStatus(product.id, newStatus);
      if (response.success) {
        message.success({
          content: `商品"${product.name}"已成功${statusText}！`,
          duration: 3,
        });
        fetchProducts();
      } else {
        message.error({
          content: response.message || `${statusText}失败，请稍后重试`,
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('更新商品状态失败:', error);
      message.error({
        content: `${statusText}失败，请检查网络连接后重试`,
        duration: 5,
      });
    }
  };

  // 图片上传处理
  const handleUploadChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  };

  // 表格列定义
  const columns: ColumnsType<Product> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '商品图片',
      dataIndex: 'images',
      key: 'images',
      width: 100,
      render: (images: string[]) => (
        images && images.length > 0 ? (
          <Image
            width={60}
            height={60}
            src={images[0]}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            无图片
          </div>
        )
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string) => (
        <div style={{ fontWeight: 500 }}>{text}</div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '质量等级',
      dataIndex: 'qualityLevel',
      key: 'qualityLevel',
      width: 100,
      render: (level: QualityLevel) => {
        const levelInfo = qualityLevelMap[level];
        return (
          <Tag color={levelInfo?.color || 'default'}>
            {levelInfo?.label || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '产地',
      dataIndex: 'origin',
      key: 'origin',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ProductStatus, record: Product) => (
        <Switch
          checked={status === ProductStatus.ONLINE}
          onChange={() => handleToggleStatus(record)}
          checkedChildren="上架"
          unCheckedChildren="下架"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record: Product) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title={
              <div>
                <div style={{ fontWeight: 'bold', marginBottom: '4px' }}>
                  确定要删除商品"{record.name}"吗？
                </div>
                <div style={{ fontSize: '12px', color: '#999' }}>
                  删除后将无法恢复，请谨慎操作
                </div>
              </div>
            }
            onConfirm={() => handleDelete(record.id)}
            okText="确定删除"
            cancelText="取消"
            okButtonProps={{ danger: true }}
            placement="topRight"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="product-list-container">
      <Title level={2}>商品管理</Title>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="name" label="商品名称">
                <Input
                  placeholder="请输入商品名称"
                  allowClear
                  onPressEnter={(e) => handleQuickSearch((e.target as HTMLInputElement).value)}
                  suffix={
                    <SearchOutlined
                      style={{ color: '#1890ff', cursor: 'pointer' }}
                      onClick={() => {
                        const value = searchForm.getFieldValue('name');
                        if (value) handleQuickSearch(value);
                      }}
                    />
                  }
                />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="categoryId" label="商品分类">
                <Select placeholder="请选择商品分类" allowClear>
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="qualityLevel" label="质量等级">
                <Select placeholder="请选择质量等级" allowClear>
                  <Option value={QualityLevel.EXCELLENT}>优</Option>
                  <Option value={QualityLevel.GOOD}>良</Option>
                  <Option value={QualityLevel.MEDIUM}>中</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="商品状态">
                <Select placeholder="请选择商品状态" allowClear>
                  <Option value={ProductStatus.ONLINE}>上架</Option>
                  <Option value={ProductStatus.OFFLINE}>下架</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="origin" label="产地">
                <Input placeholder="请输入产地" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SearchOutlined />}
                    loading={loading}
                  >
                    搜索
                  </Button>
                  <Button
                    onClick={handleReset}
                    icon={<ReloadOutlined />}
                    disabled={loading}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item label="搜索结果">
                <div style={{ color: '#666', fontSize: '14px' }}>
                  {loading ? '搜索中...' : `共找到 ${total} 条商品`}
                </div>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAdd}
                size="middle"
              >
                新增商品
              </Button>
              <Button
                icon={<ExportOutlined />}
                onClick={() => message.info('导出功能开发中...')}
                disabled={loading || total === 0}
              >
                导出数据
              </Button>
            </Space>
          </Col>
          <Col>
            <Space>
              <div style={{ color: '#666', fontSize: '14px' }}>
                {products.length > 0 && (
                  `显示第 ${(queryParams.page - 1) * queryParams.pageSize + 1}-${Math.min(queryParams.page * queryParams.pageSize, total)} 条`
                )}
              </div>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchProducts}
                loading={loading}
                title="刷新数据"
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 商品列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          locale={{
            emptyText: loading ? '加载中...' : (
              <div style={{ padding: '40px 0', textAlign: 'center' }}>
                <div style={{ fontSize: '16px', color: '#999', marginBottom: '8px' }}>
                  暂无商品数据
                </div>
                <div style={{ fontSize: '14px', color: '#ccc' }}>
                  {Object.keys(queryParams).some(key => key !== 'page' && key !== 'pageSize' && queryParams[key as keyof ProductQueryParams])
                    ? '请尝试调整搜索条件'
                    : '点击"新增商品"按钮添加第一个商品'
                  }
                </div>
              </div>
            )
          }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => {
              if (total === 0) return '暂无数据';
              return `第 ${range[0]}-${range[1]} 条/共 ${total} 条`;
            },
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
            onShowSizeChange: (current, size) => {
              setQueryParams({
                ...queryParams,
                page: 1, // 改变页面大小时回到第一页
                pageSize: size,
              });
            },
            pageSizeOptions: ['10', '20', '50', '100'],
            size: 'default',
          }}
        />
      </Card>

      {/* 商品编辑模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            {editingProduct ? <EditOutlined /> : <PlusOutlined />}
            <span>{editingProduct ? `编辑商品 - ${editingProduct.name}` : '新增商品'}</span>
          </div>
        }
        open={isModalVisible}
        onCancel={() => {
          if (saving) {
            message.warning('正在保存中，请稍候...');
            return;
          }
          setIsModalVisible(false);
          form.resetFields();
          setFileList([]);
          setEditingProduct(null);
        }}
        footer={null}
        width={800}
        destroyOnClose
        maskClosable={!saving}
        closable={!saving}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="商品名称"
                rules={[
                  { required: true, message: '请输入商品名称' },
                  { min: 2, max: 50, message: '商品名称长度为2-50个字符' },
                ]}
              >
                <Input
                  placeholder="请输入商品名称"
                  showCount
                  maxLength={50}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="categoryId"
                label="商品分类"
                rules={[{ required: true, message: '请选择商品分类' }]}
              >
                <Select placeholder="请选择商品分类">
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="qualityLevel"
                label="质量等级"
                rules={[{ required: true, message: '请选择质量等级' }]}
              >
                <Select placeholder="请选择质量等级">
                  <Option value={QualityLevel.EXCELLENT}>优</Option>
                  <Option value={QualityLevel.GOOD}>良</Option>
                  <Option value={QualityLevel.MEDIUM}>中</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="origin"
                label="产地"
                rules={[
                  { required: true, message: '请输入产地' },
                  { max: 30, message: '产地名称不能超过30个字符' },
                ]}
              >
                <Input
                  placeholder="请输入产地"
                  showCount
                  maxLength={30}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="supplierId"
                label="供应商"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <Select placeholder="请选择供应商">
                  {/* TODO: 从供应商服务获取数据 */}
                  <Option value={1}>供应商A</Option>
                  <Option value={2}>供应商B</Option>
                  <Option value={3}>供应商C</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="商品状态"
                valuePropName="checked"
              >
                <Switch checkedChildren="上架" unCheckedChildren="下架" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="商品描述"
            rules={[
              { max: 500, message: '商品描述不能超过500个字符' },
            ]}
          >
            <TextArea
              placeholder="请输入商品描述"
              rows={4}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="images"
            label="商品图片"
            extra="支持jpg、png格式，单张图片不超过5MB，最多上传5张"
          >
            <Upload
              listType="picture-card"
              fileList={fileList}
              onChange={handleUploadChange}
              beforeUpload={() => false} // 阻止自动上传
              maxCount={5}
              accept="image/*"
            >
              {fileList.length >= 5 ? null : (
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item style={{ marginBottom: 0, marginTop: '24px' }}>
            <div style={{
              borderTop: '1px solid #f0f0f0',
              paddingTop: '16px',
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center'
            }}>
              <div style={{ color: '#666', fontSize: '12px' }}>
                {editingProduct ? '* 修改后请点击更新按钮保存' : '* 请填写完整信息后创建商品'}
              </div>
              <Space>
                <Button
                  onClick={() => {
                    if (saving) {
                      message.warning('正在保存中，请稍候...');
                      return;
                    }
                    setIsModalVisible(false);
                    form.resetFields();
                    setFileList([]);
                    setEditingProduct(null);
                  }}
                  disabled={saving}
                  size="middle"
                >
                  取消
                </Button>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={saving}
                  disabled={saving}
                  size="middle"
                  icon={editingProduct ? <EditOutlined /> : <PlusOutlined />}
                >
                  {saving ? '保存中...' : (editingProduct ? '更新商品' : '创建商品')}
                </Button>
              </Space>
            </div>
          </Form.Item>
        </Form>
      </Modal>

      {/* 商品详情模态框 */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <EyeOutlined />
            <span>商品详情{viewingProduct ? ` - ${viewingProduct.name}` : ''}</span>
          </div>
        }
        open={isDetailVisible}
        onCancel={() => setIsDetailVisible(false)}
        footer={[
          <Button key="edit" type="primary" onClick={() => {
            if (viewingProduct) {
              setIsDetailVisible(false);
              handleEdit(viewingProduct);
            }
          }}>
            编辑商品
          </Button>,
          <Button key="close" onClick={() => setIsDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {viewingProduct && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="商品ID">{viewingProduct.id}</Descriptions.Item>
            <Descriptions.Item label="商品名称">{viewingProduct.name}</Descriptions.Item>
            <Descriptions.Item label="商品分类">{viewingProduct.categoryName}</Descriptions.Item>
            <Descriptions.Item label="质量等级">
              <Tag color={qualityLevelMap[viewingProduct.qualityLevel]?.color}>
                {qualityLevelMap[viewingProduct.qualityLevel]?.label}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="产地">{viewingProduct.origin}</Descriptions.Item>
            <Descriptions.Item label="供应商">{viewingProduct.supplierName}</Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={viewingProduct.status === ProductStatus.ONLINE ? 'green' : 'red'}>
                {viewingProduct.status === ProductStatus.ONLINE ? '上架' : '下架'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {new Date(viewingProduct.createdAt).toLocaleString()}
            </Descriptions.Item>
            <Descriptions.Item label="商品描述" span={2}>
              {viewingProduct.description || '暂无描述'}
            </Descriptions.Item>
            {viewingProduct.images && viewingProduct.images.length > 0 && (
              <Descriptions.Item label="商品图片" span={2}>
                <Space wrap>
                  {viewingProduct.images.map((image, index) => (
                    <Image
                      key={index}
                      width={100}
                      height={100}
                      src={image}
                      style={{ objectFit: 'cover', borderRadius: 4 }}
                    />
                  ))}
                </Space>
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ProductList;