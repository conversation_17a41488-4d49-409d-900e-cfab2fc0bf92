import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Upload,
  Image,
  InputNumber,
  Descriptions,
  Switch,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';
import { productService } from '../../../services/productService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 商品质量等级枚举
export enum QualityLevel {
  EXCELLENT = 1, // 优
  GOOD = 2,      // 良
  MEDIUM = 3,    // 中
}

// 商品状态枚举
export enum ProductStatus {
  OFFLINE = 0, // 下架
  ONLINE = 1,  // 上架
}

// 商品数据接口
export interface Product {
  id: number;
  name: string;
  categoryId: number;
  categoryName: string;
  description?: string;
  qualityLevel: QualityLevel;
  origin: string;
  supplierId: number;
  supplierName: string;
  status: ProductStatus;
  images?: string[];
  createdAt: string;
  updatedAt: string;
}

// 商品类别接口
export interface Category {
  id: number;
  name: string;
  parentId?: number;
  level: number;
}

// 查询参数接口
interface ProductQueryParams {
  name?: string;
  categoryId?: number;
  qualityLevel?: QualityLevel;
  status?: ProductStatus;
  origin?: string;
  page: number;
  pageSize: number;
}

const ProductList: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<ProductQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [saving, setSaving] = useState(false);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // 质量等级映射
  const qualityLevelMap = {
    [QualityLevel.EXCELLENT]: { label: '优', color: 'green' },
    [QualityLevel.GOOD]: { label: '良', color: 'blue' },
    [QualityLevel.MEDIUM]: { label: '中', color: 'orange' },
  };

  // 获取商品列表
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await productService.getProductList(queryParams);
      if (response.success) {
        setProducts(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取商品列表失败');
        setProducts([]);
        setTotal(0);
      }
    } catch (error: any) {
      console.error('获取商品列表失败:', error);
      let errorMsg = '获取商品列表失败';
      if (error.response) {
        const { status } = error.response;
        if (status === 401) {
          errorMsg = '登录已过期，请重新登录';
        } else if (status === 403) {
          errorMsg = '没有权限访问商品列表';
        } else if (status === 500) {
          errorMsg = '服务器内部错误，请稍后重试';
        }
      }
      message.error(errorMsg);
      setProducts([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 获取商品类别
  const fetchCategories = async () => {
    try {
      const response = await productService.getCategoryList();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error: any) {
      console.error('获取商品类别失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增商品
  const handleAdd = () => {
    setEditingProduct(null);
    form.resetFields();
    setFileList([]);
    setIsModalVisible(true);
  };

  // 编辑商品
  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    form.setFieldsValue({
      ...product,
      status: product.status === ProductStatus.ONLINE,
    });

    // 设置图片列表
    if (product.images && product.images.length > 0) {
      const imageFiles = product.images.map((url, index) => ({
        uid: `${index}`,
        name: `image-${index}`,
        status: 'done' as const,
        url: url,
      }));
      setFileList(imageFiles);
    } else {
      setFileList([]);
    }

    setIsModalVisible(true);
  };

  // 查看商品详情
  const handleView = (product: Product) => {
    setViewingProduct(product);
    setIsDetailVisible(true);
  };

  // 删除商品
  const handleDelete = async (id: number) => {
    try {
      const response = await productService.deleteProduct(id);
      if (response.success) {
        message.success('删除成功');
        fetchProducts();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存商品
  const handleSave = async (values: any) => {
    setSaving(true);

    try {
      const productData = {
        ...values,
        status: values.status ? ProductStatus.ONLINE : ProductStatus.OFFLINE,
        images: fileList.map(file => file.url || file.response?.url).filter(Boolean),
      };

      let response;
      if (editingProduct) {
        response = await productService.updateProduct(editingProduct.id, productData);
      } else {
        response = await productService.createProduct(productData);
      }

      if (response.success) {
        const successMsg = editingProduct ? '商品信息更新成功！' : '商品创建成功！';
        message.success({
          content: successMsg,
          duration: 3,
        });
        setIsModalVisible(false);
        form.resetFields();
        setFileList([]);
        fetchProducts();
      } else {
        // 处理具体的错误信息
        let errorMsg = '操作失败';
        if (response.message) {
          if (response.message.includes('name')) {
            errorMsg = '商品名称已存在，请使用其他名称';
          } else if (response.message.includes('category')) {
            errorMsg = '商品分类不存在，请重新选择';
          } else if (response.message.includes('validation')) {
            errorMsg = '输入信息格式不正确，请检查后重试';
          } else {
            errorMsg = response.message;
          }
        }

        message.error({
          content: errorMsg,
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('保存商品失败:', error);

      let errorMsg = '保存失败，请稍后重试';
      if (error.response) {
        const { status, data } = error.response;
        if (status === 400) {
          if (data.error && data.error.includes('name')) {
            errorMsg = '商品名称格式不正确或已存在';
          } else if (data.error && data.error.includes('category')) {
            errorMsg = '商品分类无效，请重新选择';
          } else {
            errorMsg = data.error || '请求参数错误，请检查输入信息';
          }
        } else if (status === 409) {
          errorMsg = '商品信息冲突，商品名称可能已存在';
        } else if (status === 500) {
          errorMsg = '服务器内部错误，请联系管理员';
        } else {
          errorMsg = `请求失败 (${status})，请稍后重试`;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }

      message.error({
        content: errorMsg,
        duration: 5,
      });
    } finally {
      setSaving(false);
    }
  };

  // 切换商品状态
  const handleToggleStatus = async (product: Product) => {
    try {
      const newStatus = product.status === ProductStatus.ONLINE ? ProductStatus.OFFLINE : ProductStatus.ONLINE;
      const response = await productService.updateProductStatus(product.id, newStatus);
      if (response.success) {
        message.success('状态更新成功');
        fetchProducts();
      } else {
        message.error(response.message || '状态更新失败');
      }
    } catch (error: any) {
      message.error(error.message || '状态更新失败');
    }
  };

  // 图片上传处理
  const handleUploadChange = ({ fileList: newFileList }: any) => {
    setFileList(newFileList);
  };

  // 表格列定义
  const columns: ColumnsType<Product> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '商品图片',
      dataIndex: 'images',
      key: 'images',
      width: 100,
      render: (images: string[]) => (
        images && images.length > 0 ? (
          <Image
            width={60}
            height={60}
            src={images[0]}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            无图片
          </div>
        )
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string) => (
        <div style={{ fontWeight: 500 }}>{text}</div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '质量等级',
      dataIndex: 'qualityLevel',
      key: 'qualityLevel',
      width: 100,
      render: (level: QualityLevel) => {
        const levelInfo = qualityLevelMap[level];
        return (
          <Tag color={levelInfo?.color || 'default'}>
            {levelInfo?.label || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '产地',
      dataIndex: 'origin',
      key: 'origin',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: ProductStatus, record: Product) => (
        <Switch
          checked={status === ProductStatus.ONLINE}
          onChange={() => handleToggleStatus(record)}
          checkedChildren="上架"
          unCheckedChildren="下架"
        />
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      fixed: 'right',
      render: (_, record: Product) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个商品吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="product-list-container">
      <Title level={2}>商品管理</Title>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="name" label="商品名称">
                <Input placeholder="请输入商品名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="categoryId" label="商品分类">
                <Select placeholder="请选择商品分类" allowClear>
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="qualityLevel" label="质量等级">
                <Select placeholder="请选择质量等级" allowClear>
                  <Option value={QualityLevel.EXCELLENT}>优</Option>
                  <Option value={QualityLevel.GOOD}>良</Option>
                  <Option value={QualityLevel.MEDIUM}>中</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="商品状态">
                <Select placeholder="请选择商品状态" allowClear>
                  <Option value={ProductStatus.ONLINE}>上架</Option>
                  <Option value={ProductStatus.OFFLINE}>下架</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="origin" label="产地">
                <Input placeholder="请输入产地" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增商品
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchProducts}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 商品列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>

      {/* 商品编辑模态框 */}
      <Modal
        title={editingProduct ? '编辑商品' : '新增商品'}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
          setFileList([]);
          setEditingProduct(null);
        }}
        footer={null}
        width={800}
        destroyOnClose
        maskClosable={false}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="商品名称"
                rules={[
                  { required: true, message: '请输入商品名称' },
                  { min: 2, max: 50, message: '商品名称长度为2-50个字符' },
                ]}
              >
                <Input
                  placeholder="请输入商品名称"
                  showCount
                  maxLength={50}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="categoryId"
                label="商品分类"
                rules={[{ required: true, message: '请选择商品分类' }]}
              >
                <Select placeholder="请选择商品分类">
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="qualityLevel"
                label="质量等级"
                rules={[{ required: true, message: '请选择质量等级' }]}
              >
                <Select placeholder="请选择质量等级">
                  <Option value={QualityLevel.EXCELLENT}>优</Option>
                  <Option value={QualityLevel.GOOD}>良</Option>
                  <Option value={QualityLevel.MEDIUM}>中</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="origin"
                label="产地"
                rules={[
                  { required: true, message: '请输入产地' },
                  { max: 30, message: '产地名称不能超过30个字符' },
                ]}
              >
                <Input
                  placeholder="请输入产地"
                  showCount
                  maxLength={30}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="supplierId"
                label="供应商"
                rules={[{ required: true, message: '请选择供应商' }]}
              >
                <Select placeholder="请选择供应商">
                  {/* TODO: 从供应商服务获取数据 */}
                  <Option value={1}>供应商A</Option>
                  <Option value={2}>供应商B</Option>
                  <Option value={3}>供应商C</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="商品状态"
                valuePropName="checked"
              >
                <Switch checkedChildren="上架" unCheckedChildren="下架" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="商品描述"
            rules={[
              { max: 500, message: '商品描述不能超过500个字符' },
            ]}
          >
            <TextArea
              placeholder="请输入商品描述"
              rows={4}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="images"
            label="商品图片"
            extra="支持jpg、png格式，单张图片不超过5MB，最多上传5张"
          >
            <Upload
              listType="picture-card"
              fileList={fileList}
              onChange={handleUploadChange}
              beforeUpload={() => false} // 阻止自动上传
              maxCount={5}
              accept="image/*"
            >
              {fileList.length >= 5 ? null : (
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>上传图片</div>
                </div>
              )}
            </Upload>
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                  setFileList([]);
                  setEditingProduct(null);
                }}
                disabled={saving}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={saving}
                disabled={saving}
              >
                {saving ? '保存中...' : (editingProduct ? '更新' : '创建')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 商品详情模态框 */}
      <Modal
        title="商品详情"
        open={isDetailVisible}
        onCancel={() => setIsDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setIsDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {viewingProduct && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="商品ID">{viewingProduct.id}</Descriptions.Item>
            <Descriptions.Item label="商品名称">{viewingProduct.name}</Descriptions.Item>
            <Descriptions.Item label="商品分类">{viewingProduct.categoryName}</Descriptions.Item>
            <Descriptions.Item label="质量等级">
              <Tag color={qualityLevelMap[viewingProduct.qualityLevel]?.color}>
                {qualityLevelMap[viewingProduct.qualityLevel]?.label}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="产地">{viewingProduct.origin}</Descriptions.Item>
            <Descriptions.Item label="供应商">{viewingProduct.supplierName}</Descriptions.Item>
            <Descriptions.Item label="状态">
              <Tag color={viewingProduct.status === ProductStatus.ONLINE ? 'green' : 'red'}>
                {viewingProduct.status === ProductStatus.ONLINE ? '上架' : '下架'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {new Date(viewingProduct.createdAt).toLocaleString()}
            </Descriptions.Item>
            <Descriptions.Item label="商品描述" span={2}>
              {viewingProduct.description || '暂无描述'}
            </Descriptions.Item>
            {viewingProduct.images && viewingProduct.images.length > 0 && (
              <Descriptions.Item label="商品图片" span={2}>
                <Space wrap>
                  {viewingProduct.images.map((image, index) => (
                    <Image
                      key={index}
                      width={100}
                      height={100}
                      src={image}
                      style={{ objectFit: 'cover', borderRadius: 4 }}
                    />
                  ))}
                </Space>
              </Descriptions.Item>
            )}
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default ProductList;