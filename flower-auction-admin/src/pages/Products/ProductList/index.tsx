import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Upload,
  Image,
  InputNumber,
  Descriptions,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ExportOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { UploadFile } from 'antd/es/upload/interface';
import { productService } from '../../../services/productService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 商品质量等级枚举
export enum QualityLevel {
  EXCELLENT = 1, // 优
  GOOD = 2,      // 良
  MEDIUM = 3,    // 中
}

// 商品状态枚举
export enum ProductStatus {
  OFFLINE = 0, // 下架
  ONLINE = 1,  // 上架
}

// 商品数据接口
export interface Product {
  id: number;
  name: string;
  categoryId: number;
  categoryName: string;
  description?: string;
  qualityLevel: QualityLevel;
  origin: string;
  supplierId: number;
  supplierName: string;
  status: ProductStatus;
  images?: string[];
  createdAt: string;
  updatedAt: string;
}

// 商品类别接口
export interface Category {
  id: number;
  name: string;
  parentId?: number;
  level: number;
}

// 查询参数接口
interface ProductQueryParams {
  name?: string;
  categoryId?: number;
  qualityLevel?: QualityLevel;
  status?: ProductStatus;
  origin?: string;
  page: number;
  pageSize: number;
}

const ProductList: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<ProductQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState<Product | null>(null);
  const [viewingProduct, setViewingProduct] = useState<Product | null>(null);
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [form] = Form.useForm();

  // 质量等级映射
  const qualityLevelMap = {
    [QualityLevel.EXCELLENT]: { label: '优', color: 'green' },
    [QualityLevel.GOOD]: { label: '良', color: 'blue' },
    [QualityLevel.MEDIUM]: { label: '中', color: 'orange' },
  };

  // 获取商品列表
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await productService.getProductList(queryParams);
      if (response.success) {
        setProducts(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取商品列表失败');
      }
    } catch (error: any) {
      message.error(error.message || '获取商品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取商品类别
  const fetchCategories = async () => {
    try {
      const response = await productService.getCategoryList();
      if (response.success) {
        setCategories(response.data);
      }
    } catch (error: any) {
      console.error('获取商品类别失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增商品
  const handleAdd = () => {
    setEditingProduct(null);
    form.resetFields();
    setFileList([]);
    setIsModalVisible(true);
  };

  // 编辑商品
  const handleEdit = (product: Product) => {
    setEditingProduct(product);
    form.setFieldsValue({
      ...product,
      status: product.status === ProductStatus.ONLINE,
    });

    // 设置图片列表
    if (product.images && product.images.length > 0) {
      const imageFiles = product.images.map((url, index) => ({
        uid: `${index}`,
        name: `image-${index}`,
        status: 'done' as const,
        url: url,
      }));
      setFileList(imageFiles);
    } else {
      setFileList([]);
    }

    setIsModalVisible(true);
  };

  // 查看商品详情
  const handleView = (product: Product) => {
    setViewingProduct(product);
    setIsDetailVisible(true);
  };

  // 删除商品
  const handleDelete = async (id: number) => {
    try {
      const response = await productService.deleteProduct(id);
      if (response.success) {
        message.success('删除成功');
        fetchProducts();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };
