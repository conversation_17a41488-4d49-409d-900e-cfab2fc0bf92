import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  Image,
  Descriptions,
  Radio,
  Badge,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  CheckOutlined,
  CloseOutlined,
  ReloadOutlined,
  ExclamationCircleOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { productService } from '../../../services/productService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 审核状态枚举
export enum AuditStatus {
  PENDING = 'pending',    // 待审核
  APPROVED = 'approved',  // 已通过
  REJECTED = 'rejected',  // 已拒绝
}

// 商品数据接口
export interface AuditProduct {
  id: number;
  name: string;
  categoryName: string;
  description?: string;
  qualityLevel: number;
  origin: string;
  supplierName: string;
  images?: string[];
  auditStatus: AuditStatus;
  auditReason?: string;
  auditTime?: string;
  auditorName?: string;
  createdAt: string;
  updatedAt: string;
}

// 查询参数接口
interface AuditQueryParams {
  name?: string;
  auditStatus?: AuditStatus;
  supplierName?: string;
  page: number;
  pageSize: number;
}

const ProductAudit: React.FC = () => {
  const [products, setProducts] = useState<AuditProduct[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<AuditQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [isAuditVisible, setIsAuditVisible] = useState(false);
  const [viewingProduct, setViewingProduct] = useState<AuditProduct | null>(null);
  const [auditingProduct, setAuditingProduct] = useState<AuditProduct | null>(null);
  const [form] = Form.useForm();

  // 审核状态映射
  const auditStatusMap = {
    [AuditStatus.PENDING]: { label: '待审核', color: 'orange' },
    [AuditStatus.APPROVED]: { label: '已通过', color: 'green' },
    [AuditStatus.REJECTED]: { label: '已拒绝', color: 'red' },
  };

  // 质量等级映射
  const qualityLevelMap = {
    1: { label: '优', color: 'green' },
    2: { label: '良', color: 'blue' },
    3: { label: '中', color: 'orange' },
  };

  // 获取待审核商品列表
  const fetchProducts = async () => {
    setLoading(true);
    try {
      const response = await productService.getPendingAuditProducts(queryParams);
      if (response.success) {
        setProducts(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取商品列表失败');
      }
    } catch (error: any) {
      message.error(error.message || '获取商品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchProducts();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 查看商品详情
  const handleView = (product: AuditProduct) => {
    setViewingProduct(product);
    setIsDetailVisible(true);
  };

  // 开始审核
  const handleStartAudit = (product: AuditProduct) => {
    setAuditingProduct(product);
    form.resetFields();
    setIsAuditVisible(true);
  };

  // 提交审核
  const handleSubmitAudit = async (values: any) => {
    if (!auditingProduct) return;

    try {
      const response = await productService.auditProduct(
        auditingProduct.id,
        values.status,
        values.reason
      );
      if (response.success) {
        message.success('审核完成');
        setIsAuditVisible(false);
        fetchProducts();
      } else {
        message.error(response.message || '审核失败');
      }
    } catch (error: any) {
      message.error(error.message || '审核失败');
    }
  };

  // 快速审核通过
  const handleQuickApprove = async (product: AuditProduct) => {
    try {
      const response = await productService.auditProduct(product.id, 'approved');
      if (response.success) {
        message.success('审核通过');
        fetchProducts();
      } else {
        message.error(response.message || '审核失败');
      }
    } catch (error: any) {
      message.error(error.message || '审核失败');
    }
  };

  // 快速审核拒绝
  const handleQuickReject = (product: AuditProduct) => {
    Modal.confirm({
      title: '确认拒绝',
      icon: <ExclamationCircleOutlined />,
      content: '确定要拒绝这个商品吗？请输入拒绝原因：',
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        // 这里应该弹出一个输入框让用户输入拒绝原因
        // 为了简化，这里直接使用默认原因
        try {
          const response = await productService.auditProduct(
            product.id,
            'rejected',
            '商品信息不符合要求'
          );
          if (response.success) {
            message.success('审核拒绝');
            fetchProducts();
          } else {
            message.error(response.message || '审核失败');
          }
        } catch (error: any) {
          message.error(error.message || '审核失败');
        }
      },
    });
  };

  // 表格列定义
  const columns: ColumnsType<AuditProduct> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '商品图片',
      dataIndex: 'images',
      key: 'images',
      width: 100,
      render: (images: string[]) => (
        images && images.length > 0 ? (
          <Image
            width={60}
            height={60}
            src={images[0]}
            style={{ objectFit: 'cover', borderRadius: 4 }}
            fallback="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMIAAADDCAYAAADQvc6UAAABRWlDQ1BJQ0MgUHJvZmlsZQAAKJFjYGASSSwoyGFhYGDIzSspCnJ3UoiIjFJgf8LAwSDCIMogwMCcmFxc4BgQ4ANUwgCjUcG3awyMIPqyLsis7PPOq3QdDFcvjV3jOD1boQVTPQrgSkktTgbSf4A4LbmgqISBgTEFyFYuLykAsTuAbJEioKOA7DkgdjqEvQHEToKwj4DVhAQ5A9k3gGyB5IxEoBmML4BsnSQk8XQkNtReEOBxcfXxUQg1Mjc0dyHgXNJBSWpFCYh2zi+oLMpMzyhRcASGUqqCZ16yno6CkYGRAQMDKMwhqj/fAIcloxgHQqxAjIHBEugw5sUIsSQpBobtQPdLciLEVJYzMPBHMDBsayhILEqEO4DxG0txmrERhM29nYGBddr//5/DGRjYNRkY/l7////39v///y4Dmn+LgeHANwDrkl1AuO+pmgAAADhlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAAqACAAQAAAABAAAAwqADAAQAAAABAAAAwwAAAAD9b/HnAAAHlklEQVR4Ae3dP3Ik1RnG4W+FgYxN"
          />
        ) : (
          <div style={{ width: 60, height: 60, background: '#f5f5f5', borderRadius: 4, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            无图片
          </div>
        )
      ),
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      render: (text: string) => (
        <div style={{ fontWeight: 500 }}>{text}</div>
      ),
    },
    {
      title: '分类',
      dataIndex: 'categoryName',
      key: 'categoryName',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '质量等级',
      dataIndex: 'qualityLevel',
      key: 'qualityLevel',
      width: 100,
      render: (level: number) => {
        const levelInfo = qualityLevelMap[level];
        return (
          <Tag color={levelInfo?.color || 'default'}>
            {levelInfo?.label || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '产地',
      dataIndex: 'origin',
      key: 'origin',
      width: 120,
    },
    {
      title: '供应商',
      dataIndex: 'supplierName',
      key: 'supplierName',
      width: 120,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      width: 100,
      render: (status: AuditStatus) => {
        const statusInfo = auditStatusMap[status];
        return (
          <Badge
            status={status === AuditStatus.PENDING ? 'processing' : status === AuditStatus.APPROVED ? 'success' : 'error'}
            text={
              <Tag color={statusInfo?.color || 'default'}>
                {statusInfo?.label || '未知'}
              </Tag>
            }
          />
        );
      },
    },
    {
      title: '提交时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record: AuditProduct) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record)}
          >
            查看
          </Button>
          {record.auditStatus === AuditStatus.PENDING && (
            <>
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => handleQuickApprove(record)}
              >
                通过
              </Button>
              <Button
                type="link"
                size="small"
                icon={<CloseOutlined />}
                danger
                onClick={() => handleQuickReject(record)}
              >
                拒绝
              </Button>
              <Button
                type="link"
                size="small"
                onClick={() => handleStartAudit(record)}
              >
                详细审核
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];