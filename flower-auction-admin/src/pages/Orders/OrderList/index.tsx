import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  message,
  Typography,
  Row,
  Col,
  DatePicker,
  Statistic,
  Descriptions,
  Badge,
} from 'antd';
import {
  SearchOutlined,
  EyeOutlined,
  ExportOutlined,
  ReloadOutlined,
  DollarOutlined,
  ShoppingCartOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { orderService } from '../../../services/orderService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 订单状态枚举
export enum OrderStatus {
  PENDING_PAYMENT = 1,    // 待付款
  PAID = 2,              // 已付款
  SHIPPED = 3,           // 已发货
  DELIVERED = 4,         // 已送达
  COMPLETED = 5,         // 已完成
  CANCELLED = 6,         // 已取消
  REFUNDED = 7,          // 已退款
}

// 支付方式枚举
export enum PaymentMethod {
  ALIPAY = 'alipay',     // 支付宝
  WECHAT = 'wechat',     // 微信支付
  BANK = 'bank',         // 银行转账
  CASH = 'cash',         // 现金
}

// 订单数据接口
export interface Order {
  id: number;
  orderNo: string;
  buyerId: number;
  buyerName: string;
  sellerId: number;
  sellerName: string;
  productId: number;
  productName: string;
  quantity: number;
  unitPrice: number;
  totalAmount: number;
  paymentMethod: PaymentMethod;
  status: OrderStatus;
  shippingAddress: string;
  shippingPhone: string;
  remark?: string;
  createdAt: string;
  updatedAt: string;
  paidAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
}

// 查询参数接口
interface OrderQueryParams {
  orderNo?: string;
  buyerName?: string;
  sellerName?: string;
  status?: OrderStatus;
  paymentMethod?: PaymentMethod;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

const OrderList: React.FC = () => {
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<OrderQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isDetailVisible, setIsDetailVisible] = useState(false);
  const [viewingOrder, setViewingOrder] = useState<Order | null>(null);
  const [statistics, setStatistics] = useState({
    totalOrders: 0,
    totalAmount: 0,
    todayOrders: 0,
    pendingOrders: 0,
  });

  // 订单状态映射
  const orderStatusMap = {
    [OrderStatus.PENDING_PAYMENT]: { label: '待付款', color: 'orange' },
    [OrderStatus.PAID]: { label: '已付款', color: 'blue' },
    [OrderStatus.SHIPPED]: { label: '已发货', color: 'cyan' },
    [OrderStatus.DELIVERED]: { label: '已送达', color: 'purple' },
    [OrderStatus.COMPLETED]: { label: '已完成', color: 'green' },
    [OrderStatus.CANCELLED]: { label: '已取消', color: 'red' },
    [OrderStatus.REFUNDED]: { label: '已退款', color: 'magenta' },
  };

  // 支付方式映射
  const paymentMethodMap = {
    [PaymentMethod.ALIPAY]: { label: '支付宝', color: 'blue' },
    [PaymentMethod.WECHAT]: { label: '微信支付', color: 'green' },
    [PaymentMethod.BANK]: { label: '银行转账', color: 'orange' },
    [PaymentMethod.CASH]: { label: '现金', color: 'gray' },
  };

  // 获取订单列表
  const fetchOrders = async () => {
    setLoading(true);
    try {
      const response = await orderService.getOrderList(queryParams);
      if (response.success) {
        setOrders(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取订单列表失败');
      }
    } catch (error: any) {
      message.error(error.message || '获取订单列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取订单统计
  const fetchStatistics = async () => {
    try {
      const response = await orderService.getOrderStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error: any) {
      console.error('获取订单统计失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchOrders();
    fetchStatistics();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 查看订单详情
  const handleView = (order: Order) => {
    setViewingOrder(order);
    setIsDetailVisible(true);
  };

  // 导出订单数据
  const handleExport = async () => {
    try {
      const response = await orderService.exportOrders(queryParams);
      if (response.success) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response.data]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', `orders_${new Date().getTime()}.xlsx`);
        document.body.appendChild(link);
        link.click();
        link.remove();
        window.URL.revokeObjectURL(url);
        message.success('导出成功');
      } else {
        message.error(response.message || '导出失败');
      }
    } catch (error: any) {
      message.error(error.message || '导出失败');
    }
  };
