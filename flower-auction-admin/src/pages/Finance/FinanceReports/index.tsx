import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  DatePicker,
  Select,
  Button,
  Space,
  Table,
  Progress,
  Tag,
  Divider,
  message,
} from 'antd';
import {
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
  PieChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  DownloadOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

// 财务数据接口
interface FinanceData {
  totalRevenue: number;
  totalCommission: number;
  totalProfit: number;
  orderCount: number;
  avgOrderValue: number;
  growthRate: number;
}

// 收入分析数据
interface RevenueAnalysis {
  date: string;
  revenue: number;
  commission: number;
  orders: number;
}

// 佣金记录
interface CommissionRecord {
  id: number;
  orderNo: string;
  sellerName: string;
  orderAmount: number;
  commissionRate: number;
  commissionAmount: number;
  status: 'pending' | 'settled' | 'cancelled';
  createdAt: string;
  settledAt?: string;
}

const FinanceReports: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(30, 'day'),
    dayjs(),
  ]);
  const [reportType, setReportType] = useState<'daily' | 'monthly'>('daily');
  const [financeData, setFinanceData] = useState<FinanceData>({
    totalRevenue: 0,
    totalCommission: 0,
    totalProfit: 0,
    orderCount: 0,
    avgOrderValue: 0,
    growthRate: 0,
  });
  const [revenueAnalysis, setRevenueAnalysis] = useState<RevenueAnalysis[]>([]);
  const [commissionRecords, setCommissionRecords] = useState<CommissionRecord[]>([]);

  // 模拟数据
  const mockFinanceData: FinanceData = {
    totalRevenue: 156780.50,
    totalCommission: 7839.03,
    totalProfit: 148941.47,
    orderCount: 234,
    avgOrderValue: 670.26,
    growthRate: 15.6,
  };

  const mockRevenueAnalysis: RevenueAnalysis[] = [
    { date: '2024-01-01', revenue: 5200, commission: 260, orders: 8 },
    { date: '2024-01-02', revenue: 6800, commission: 340, orders: 12 },
    { date: '2024-01-03', revenue: 4500, commission: 225, orders: 6 },
    { date: '2024-01-04', revenue: 7200, commission: 360, orders: 15 },
    { date: '2024-01-05', revenue: 5900, commission: 295, orders: 9 },
  ];

  const mockCommissionRecords: CommissionRecord[] = [
    {
      id: 1,
      orderNo: 'ORD-20240115-001',
      sellerName: '花卉供应商A',
      orderAmount: 1200.00,
      commissionRate: 5.0,
      commissionAmount: 60.00,
      status: 'settled',
      createdAt: '2024-01-15 10:30:00',
      settledAt: '2024-01-16 09:00:00',
    },
    {
      id: 2,
      orderNo: 'ORD-20240115-002',
      sellerName: '花卉供应商B',
      orderAmount: 850.00,
      commissionRate: 4.5,
      commissionAmount: 38.25,
      status: 'pending',
      createdAt: '2024-01-15 14:20:00',
    },
    {
      id: 3,
      orderNo: 'ORD-20240114-003',
      sellerName: '花卉供应商C',
      orderAmount: 2100.00,
      commissionRate: 6.0,
      commissionAmount: 126.00,
      status: 'settled',
      createdAt: '2024-01-14 16:45:00',
      settledAt: '2024-01-15 10:00:00',
    },
  ];

  // 获取财务数据
  const fetchFinanceData = async () => {
    setLoading(true);
    try {
      // 这里应该调用后端API获取财务数据
      await new Promise(resolve => setTimeout(resolve, 1000));
      setFinanceData(mockFinanceData);
      setRevenueAnalysis(mockRevenueAnalysis);
      setCommissionRecords(mockCommissionRecords);
    } catch (error: any) {
      message.error(error.message || '获取财务数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchFinanceData();
  }, [dateRange, reportType]);

  // 导出报表
  const handleExport = () => {
    message.success('导出功能开发中...');
  };

  // 佣金状态映射
  const commissionStatusMap = {
    pending: { label: '待结算', color: 'orange' },
    settled: { label: '已结算', color: 'green' },
    cancelled: { label: '已取消', color: 'red' },
  };

  // 佣金记录表格列
  const commissionColumns: ColumnsType<CommissionRecord> = [
    {
      title: '订单号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      width: 150,
    },
    {
      title: '卖家',
      dataIndex: 'sellerName',
      key: 'sellerName',
      width: 120,
    },
    {
      title: '订单金额',
      dataIndex: 'orderAmount',
      key: 'orderAmount',
      width: 120,
      render: (amount: number) => (
        <Text strong>¥{amount.toFixed(2)}</Text>
      ),
    },
    {
      title: '佣金率',
      dataIndex: 'commissionRate',
      key: 'commissionRate',
      width: 100,
      render: (rate: number) => `${rate}%`,
    },
    {
      title: '佣金金额',
      dataIndex: 'commissionAmount',
      key: 'commissionAmount',
      width: 120,
      render: (amount: number) => (
        <Text strong style={{ color: '#f50' }}>¥{amount.toFixed(2)}</Text>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: keyof typeof commissionStatusMap) => {
        const statusInfo = commissionStatusMap[status];
        return (
          <Tag color={statusInfo?.color || 'default'}>
            {statusInfo?.label || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '结算时间',
      dataIndex: 'settledAt',
      key: 'settledAt',
      width: 160,
      render: (text?: string) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-',
    },
  ];

  return (
    <div style={{ padding: 24 }}>
      <Title level={2}>财务报表</Title>

      {/* 筛选条件 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col>
            <Space>
              <Text>时间范围:</Text>
              <RangePicker
                value={dateRange}
                onChange={(dates) => {
                  if (dates) {
                    setDateRange([dates[0]!, dates[1]!]);
                  }
                }}
              />
            </Space>
          </Col>
          <Col>
            <Space>
              <Text>报表类型:</Text>
              <Select
                value={reportType}
                onChange={setReportType}
                style={{ width: 120 }}
              >
                <Option value="daily">日报表</Option>
                <Option value="monthly">月报表</Option>
              </Select>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={fetchFinanceData}
                loading={loading}
              >
                刷新
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出报表
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 核心指标统计 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总收入"
              value={financeData.totalRevenue}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="元"
              valueStyle={{ color: '#3f8600' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                <RiseOutlined style={{ color: '#52c41a' }} />
                {' '}增长率: {financeData.growthRate}%
              </Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总佣金"
              value={financeData.totalCommission}
              precision={2}
              prefix={<PieChartOutlined />}
              suffix="元"
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Progress
                percent={Math.round((financeData.totalCommission / financeData.totalRevenue) * 100)}
                size="small"
                showInfo={false}
              />
              <Text type="secondary">
                占总收入 {((financeData.totalCommission / financeData.totalRevenue) * 100).toFixed(1)}%
              </Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="净利润"
              value={financeData.totalProfit}
              precision={2}
              prefix={<BarChartOutlined />}
              suffix="元"
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                利润率: {((financeData.totalProfit / financeData.totalRevenue) * 100).toFixed(1)}%
              </Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="订单数量"
              value={financeData.orderCount}
              suffix="笔"
              valueStyle={{ color: '#f50' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                平均订单价值: ¥{financeData.avgOrderValue.toFixed(2)}
              </Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 收入趋势分析 */}
      <Card title="收入趋势分析" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col xs={24} lg={16}>
            {/* 这里应该放置图表组件，如 ECharts 或 Chart.js */}
            <div
              style={{
                height: 300,
                background: '#f5f5f5',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 8,
              }}
            >
              <Text type="secondary">收入趋势图表（需要集成图表库）</Text>
            </div>
          </Col>
          <Col xs={24} lg={8}>
            <div style={{ padding: 16 }}>
              <Title level={4}>数据概览</Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>最高单日收入</Text>
                  <br />
                  <Text style={{ fontSize: 18, color: '#f50' }}>
                    ¥{Math.max(...revenueAnalysis.map(item => item.revenue)).toFixed(2)}
                  </Text>
                </div>
                <Divider />
                <div>
                  <Text strong>平均日收入</Text>
                  <br />
                  <Text style={{ fontSize: 18, color: '#1890ff' }}>
                    ¥{(revenueAnalysis.reduce((sum, item) => sum + item.revenue, 0) / revenueAnalysis.length).toFixed(2)}
                  </Text>
                </div>
                <Divider />
                <div>
                  <Text strong>总订单数</Text>
                  <br />
                  <Text style={{ fontSize: 18, color: '#52c41a' }}>
                    {revenueAnalysis.reduce((sum, item) => sum + item.orders, 0)} 笔
                  </Text>
                </div>
              </Space>
            </div>
          </Col>
        </Row>
      </Card>

      {/* 佣金管理 */}
      <Card title="佣金管理">
        <Table
          columns={commissionColumns}
          dataSource={commissionRecords}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
          }}
          summary={(pageData) => {
            const totalCommission = pageData.reduce((sum, record) => sum + record.commissionAmount, 0);
            const settledCommission = pageData
              .filter(record => record.status === 'settled')
              .reduce((sum, record) => sum + record.commissionAmount, 0);
            const pendingCommission = pageData
              .filter(record => record.status === 'pending')
              .reduce((sum, record) => sum + record.commissionAmount, 0);

            return (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4}>
                  <Text strong>合计</Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={4}>
                  <Text strong style={{ color: '#f50' }}>
                    ¥{totalCommission.toFixed(2)}
                  </Text>
                </Table.Summary.Cell>
                <Table.Summary.Cell index={5} colSpan={3}>
                  <Space>
                    <Text>已结算: ¥{settledCommission.toFixed(2)}</Text>
                    <Text>待结算: ¥{pendingCommission.toFixed(2)}</Text>
                  </Space>
                </Table.Summary.Cell>
              </Table.Summary.Row>
            );
          }}
        />
      </Card>
    </div>
  );
};

export default FinanceReports;
