.user-list-container {
  padding: 24px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.user-list-container .ant-typography {
  margin-bottom: 24px;
}

.search-card {
  margin-bottom: 16px;
}

.search-card .ant-card-body {
  padding: 16px;
}

.action-card {
  margin-bottom: 16px;
}

.action-card .ant-card-body {
  padding: 12px 16px;
}

/* 表格样式 */
.user-list-container .ant-table-wrapper {
  background: white;
  border-radius: 6px;
}

.user-list-container .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
}

.user-list-container .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 状态标签样式 */
.user-list-container .ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
}

/* 操作按钮样式 */
.user-list-container .ant-btn-link {
  padding: 0;
  height: auto;
}

/* 模态框样式 */
.user-list-container .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
}

.user-list-container .ant-modal-body {
  padding: 24px;
}

/* 表单样式 */
.user-list-container .ant-form-item-label > label {
  font-weight: 500;
}

.user-list-container .ant-input,
.user-list-container .ant-select-selector {
  border-radius: 4px;
}

.user-list-container .ant-input:focus,
.user-list-container .ant-input-focused,
.user-list-container .ant-select-focused .ant-select-selector {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 分页样式 */
.user-list-container .ant-pagination {
  margin-top: 16px;
  text-align: right;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-list-container {
    padding: 16px;
  }
  
  .search-card .ant-form-item {
    margin-bottom: 16px;
  }
  
  .action-card .ant-row {
    flex-direction: column;
    gap: 12px;
  }
  
  .user-list-container .ant-table-wrapper {
    overflow-x: auto;
  }
}

@media (max-width: 576px) {
  .user-list-container {
    padding: 12px;
  }
  
  .user-list-container .ant-typography {
    margin-bottom: 16px;
    font-size: 20px;
  }
  
  .search-card,
  .action-card {
    margin-bottom: 12px;
  }
}
