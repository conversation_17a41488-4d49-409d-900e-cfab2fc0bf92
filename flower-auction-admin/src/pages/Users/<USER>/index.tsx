import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Modal,
  Form,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Tree,
  Switch,
  Tag,
  Descriptions,
  Select,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  SettingOutlined,
  ReloadOutlined,
  UserOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import type { DataNode } from 'antd/es/tree';
import { roleService } from '../../../services/roleService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

// 角色数据接口
export interface Role {
  id: number;
  name: string;
  code: string;
  description?: string;
  status: number;
  userCount: number;
  permissions: number[];
  createdAt: string;
  updatedAt: string;
}

// 权限数据接口
export interface Permission {
  id: number;
  name: string;
  code: string;
  type: 'menu' | 'button' | 'api';
  parentId?: number;
  path?: string;
  children?: Permission[];
}

// 查询参数接口
interface RoleQueryParams {
  name?: string;
  code?: string;
  status?: number;
  page: number;
  pageSize: number;
}

const RoleManagement: React.FC = () => {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissions, setPermissions] = useState<Permission[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<RoleQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isPermissionModalVisible, setIsPermissionModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState<Role | null>(null);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [checkedPermissions, setCheckedPermissions] = useState<number[]>([]);
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [form] = Form.useForm();

  // 获取角色列表
  const fetchRoles = async () => {
    setLoading(true);
    try {
      const response = await roleService.getRoleList(queryParams);
      if (response.success) {
        setRoles(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取角色列表失败');
      }
    } catch (error: any) {
      message.error(error.message || '获取角色列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取权限列表
  const fetchPermissions = async () => {
    try {
      const response = await roleService.getPermissionList();
      if (response.success) {
        setPermissions(response.data);
      }
    } catch (error: any) {
      console.error('获取权限列表失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchRoles();
    fetchPermissions();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增角色
  const handleAdd = () => {
    setEditingRole(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 编辑角色
  const handleEdit = (role: Role) => {
    setEditingRole(role);
    form.setFieldsValue({
      ...role,
      status: role.status === 1,
    });
    setIsModalVisible(true);
  };

  // 删除角色
  const handleDelete = async (id: number) => {
    try {
      const response = await roleService.deleteRole(id);
      if (response.success) {
        message.success('删除成功');
        fetchRoles();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存角色
  const handleSave = async (values: any) => {
    try {
      const roleData = {
        ...values,
        status: values.status ? 1 : 0,
      };

      let response;
      if (editingRole) {
        response = await roleService.updateRole(editingRole.id, roleData);
      } else {
        response = await roleService.createRole(roleData);
      }

      if (response.success) {
        message.success(editingRole ? '更新成功' : '创建成功');
        setIsModalVisible(false);
        fetchRoles();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error: any) {
      message.error(error.message || '保存失败');
    }
  };

  // 配置权限
  const handleConfigPermissions = (role: Role) => {
    setSelectedRole(role);
    setCheckedPermissions(role.permissions || []);
    setIsPermissionModalVisible(true);
  };

  // 保存权限配置
  const handleSavePermissions = async () => {
    if (!selectedRole) return;

    try {
      const response = await roleService.updateRolePermissions(
        selectedRole.id,
        checkedPermissions
      );
      if (response.success) {
        message.success('权限配置成功');
        setIsPermissionModalVisible(false);
        fetchRoles();
      } else {
        message.error(response.message || '权限配置失败');
      }
    } catch (error: any) {
      message.error(error.message || '权限配置失败');
    }
  };

  // 转换权限数据为树形结构
  const convertPermissionsToTreeData = (permissions: Permission[]): DataNode[] => {
    return permissions.map(permission => ({
      title: permission.name,
      key: permission.id,
      children: permission.children ? convertPermissionsToTreeData(permission.children) : undefined,
    }));
  };

  // 权限树选择处理
  const handlePermissionCheck = (checkedKeys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    if (Array.isArray(checkedKeys)) {
      setCheckedPermissions(checkedKeys as number[]);
    } else {
      setCheckedPermissions(checkedKeys.checked as number[]);
    }
  };

  // 表格列定义
  const columns: ColumnsType<Role> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
    },
    {
      title: '角色编码',
      dataIndex: 'code',
      key: 'code',
      width: 120,
      render: (text: string) => <Tag color="blue">{text}</Tag>,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      render: (text: string) => text || '-',
    },
    {
      title: '用户数量',
      dataIndex: 'userCount',
      key: 'userCount',
      width: 100,
      render: (count: number) => (
        <Space>
          <UserOutlined />
          {count}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: number) => (
        <Tag color={status === 1 ? 'green' : 'red'}>
          {status === 1 ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text: string) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record: Role) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<SettingOutlined />}
            onClick={() => handleConfigPermissions(record)}
          >
            配置权限
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除这个角色吗？"
            description="删除后该角色下的用户将失去相应权限"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
              icon={<DeleteOutlined />}
              disabled={record.userCount > 0}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="role-management-container">
      <Title level={2}>角色管理</Title>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="name" label="角色名称">
                <Input placeholder="请输入角色名称" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="code" label="角色编码">
                <Input placeholder="请输入角色编码" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="状态">
                <Select placeholder="请选择状态" allowClear>
                  <Option value={1}>启用</Option>
                  <Option value={0}>禁用</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增角色
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchRoles}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 角色列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1000 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>

      {/* 角色编辑模态框 */}
      <Modal
        title={editingRole ? '编辑角色' : '新增角色'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="角色名称"
                rules={[
                  { required: true, message: '请输入角色名称' },
                  { max: 50, message: '角色名称不能超过50个字符' },
                ]}
              >
                <Input placeholder="请输入角色名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="角色编码"
                rules={[
                  { required: true, message: '请输入角色编码' },
                  { pattern: /^[A-Z_]+$/, message: '角色编码只能包含大写字母和下划线' },
                  { max: 50, message: '角色编码不能超过50个字符' },
                ]}
              >
                <Input placeholder="请输入角色编码" disabled={!!editingRole} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="角色描述"
            rules={[
              { max: 200, message: '角色描述不能超过200个字符' },
            ]}
          >
            <TextArea
              placeholder="请输入角色描述"
              rows={4}
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            valuePropName="checked"
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button onClick={() => setIsModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingRole ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 权限配置模态框 */}
      <Modal
        title={`配置权限 - ${selectedRole?.name}`}
        open={isPermissionModalVisible}
        onCancel={() => setIsPermissionModalVisible(false)}
        onOk={handleSavePermissions}
        width={800}
        destroyOnClose
      >
        <div style={{ marginBottom: 16 }}>
          <Descriptions size="small" column={2}>
            <Descriptions.Item label="角色名称">{selectedRole?.name}</Descriptions.Item>
            <Descriptions.Item label="角色编码">{selectedRole?.code}</Descriptions.Item>
            <Descriptions.Item label="用户数量">{selectedRole?.userCount}</Descriptions.Item>
            <Descriptions.Item label="当前权限数量">{checkedPermissions.length}</Descriptions.Item>
          </Descriptions>
        </div>

        <div style={{ border: '1px solid #d9d9d9', borderRadius: 6, padding: 16, maxHeight: 400, overflow: 'auto' }}>
          <Tree
            checkable
            checkedKeys={checkedPermissions}
            expandedKeys={expandedKeys}
            onCheck={handlePermissionCheck}
            onExpand={setExpandedKeys}
            treeData={convertPermissionsToTreeData(permissions)}
            height={350}
          />
        </div>
      </Modal>
    </div>
  );
};

export default RoleManagement;