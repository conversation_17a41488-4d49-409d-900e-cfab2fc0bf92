import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  DatePicker,
  Statistic,
  Progress,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { auctionService } from '../../../services/auctionService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 拍卖会状态枚举
export enum AuctionStatus {
  DRAFT = 1,        // 草稿
  SCHEDULED = 2,    // 已安排
  ONGOING = 3,      // 进行中
  PAUSED = 4,       // 已暂停
  COMPLETED = 5,    // 已完成
  CANCELLED = 6,    // 已取消
}

// 拍卖会数据接口
export interface Auction {
  id: number;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  status: AuctionStatus;
  totalItems: number;
  soldItems: number;
  totalAmount: number;
  participantCount: number;
  creatorName: string;
  location: string;
  createdAt: string;
  updatedAt: string;
}

// 查询参数接口
interface AuctionQueryParams {
  title?: string;
  status?: AuctionStatus;
  creatorName?: string;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

const AuctionList: React.FC = () => {
  const [auctions, setAuctions] = useState<Auction[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<AuctionQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAuction, setEditingAuction] = useState<Auction | null>(null);
  const [statistics, setStatistics] = useState({
    totalAuctions: 0,
    ongoingAuctions: 0,
    todayAuctions: 0,
    totalParticipants: 0,
  });
  const [form] = Form.useForm();

  // 拍卖会状态映射
  const auctionStatusMap = {
    [AuctionStatus.DRAFT]: { label: '草稿', color: 'default' },
    [AuctionStatus.SCHEDULED]: { label: '已安排', color: 'blue' },
    [AuctionStatus.ONGOING]: { label: '进行中', color: 'green' },
    [AuctionStatus.PAUSED]: { label: '已暂停', color: 'orange' },
    [AuctionStatus.COMPLETED]: { label: '已完成', color: 'purple' },
    [AuctionStatus.CANCELLED]: { label: '已取消', color: 'red' },
  };

  // 获取拍卖会列表
  const fetchAuctions = async () => {
    setLoading(true);
    try {
      const response = await auctionService.getAuctionList(queryParams);
      if (response.success) {
        setAuctions(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取拍卖会列表失败');
      }
    } catch (error: any) {
      message.error(error.message || '获取拍卖会列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取拍卖会统计
  const fetchStatistics = async () => {
    try {
      const response = await auctionService.getAuctionStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error: any) {
      console.error('获取拍卖会统计失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchAuctions();
    fetchStatistics();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增拍卖会
  const handleAdd = () => {
    setEditingAuction(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 编辑拍卖会
  const handleEdit = (auction: Auction) => {
    setEditingAuction(auction);
    form.setFieldsValue({
      ...auction,
      timeRange: [new Date(auction.startTime), new Date(auction.endTime)],
    });
    setIsModalVisible(true);
  };

  // 删除拍卖会
  const handleDelete = async (id: number) => {
    try {
      const response = await auctionService.deleteAuction(id);
      if (response.success) {
        message.success('删除成功');
        fetchAuctions();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存拍卖会
  const handleSave = async (values: any) => {
    try {
      const auctionData = {
        ...values,
        startTime: values.timeRange[0].toISOString(),
        endTime: values.timeRange[1].toISOString(),
      };
      delete auctionData.timeRange;

      let response;
      if (editingAuction) {
        response = await auctionService.updateAuction(editingAuction.id, auctionData);
      } else {
        response = await auctionService.createAuction(auctionData);
      }

      if (response.success) {
        message.success(editingAuction ? '更新成功' : '创建成功');
        setIsModalVisible(false);
        fetchAuctions();
      } else {
        message.error(response.message || '保存失败');
      }
    } catch (error: any) {
      message.error(error.message || '保存失败');
    }
  };

  // 开始拍卖
  const handleStart = async (id: number) => {
    try {
      const response = await auctionService.startAuction(id);
      if (response.success) {
        message.success('拍卖会已开始');
        fetchAuctions();
      } else {
        message.error(response.message || '开始拍卖失败');
      }
    } catch (error: any) {
      message.error(error.message || '开始拍卖失败');
    }
  };

  // 暂停拍卖
  const handlePause = async (id: number) => {
    try {
      const response = await auctionService.pauseAuction(id);
      if (response.success) {
        message.success('拍卖会已暂停');
        fetchAuctions();
      } else {
        message.error(response.message || '暂停拍卖失败');
      }
    } catch (error: any) {
      message.error(error.message || '暂停拍卖失败');
    }
  };

  // 结束拍卖
  const handleEnd = async (id: number) => {
    try {
      const response = await auctionService.endAuction(id);
      if (response.success) {
        message.success('拍卖会已结束');
        fetchAuctions();
      } else {
        message.error(response.message || '结束拍卖失败');
      }
    } catch (error: any) {
      message.error(error.message || '结束拍卖失败');
    }
  };
