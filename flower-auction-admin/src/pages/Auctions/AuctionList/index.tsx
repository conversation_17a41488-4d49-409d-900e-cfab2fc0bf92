import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  message,
  Typography,
  Row,
  Col,
  DatePicker,
  Statistic,
  Progress,
  Badge,
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  StopOutlined,
  ReloadOutlined,
} from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import { auctionService } from '../../../services/auctionService';
import './index.css';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

// 拍卖会状态枚举
export enum AuctionStatus {
  DRAFT = 1,        // 草稿
  SCHEDULED = 2,    // 已安排
  ONGOING = 3,      // 进行中
  PAUSED = 4,       // 已暂停
  COMPLETED = 5,    // 已完成
  CANCELLED = 6,    // 已取消
}

// 拍卖会数据接口
export interface Auction {
  id: number;
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  status: AuctionStatus;
  totalItems: number;
  soldItems: number;
  totalAmount: number;
  participantCount: number;
  creatorName: string;
  location: string;
  createdAt: string;
  updatedAt: string;
}

// 查询参数接口
interface AuctionQueryParams {
  title?: string;
  status?: AuctionStatus;
  creatorName?: string;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

const AuctionList: React.FC = () => {
  const [auctions, setAuctions] = useState<Auction[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [queryParams, setQueryParams] = useState<AuctionQueryParams>({
    page: 1,
    pageSize: 10,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingAuction, setEditingAuction] = useState<Auction | null>(null);
  const [saving, setSaving] = useState(false);
  const [statistics, setStatistics] = useState({
    totalAuctions: 0,
    ongoingAuctions: 0,
    todayAuctions: 0,
    totalParticipants: 0,
  });
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();

  // 拍卖会状态映射
  const auctionStatusMap = {
    [AuctionStatus.DRAFT]: { label: '草稿', color: 'default' },
    [AuctionStatus.SCHEDULED]: { label: '已安排', color: 'blue' },
    [AuctionStatus.ONGOING]: { label: '进行中', color: 'green' },
    [AuctionStatus.PAUSED]: { label: '已暂停', color: 'orange' },
    [AuctionStatus.COMPLETED]: { label: '已完成', color: 'purple' },
    [AuctionStatus.CANCELLED]: { label: '已取消', color: 'red' },
  };

  // 获取拍卖会列表
  const fetchAuctions = async () => {
    setLoading(true);
    try {
      const response = await auctionService.getAuctionList(queryParams);
      if (response.success) {
        setAuctions(response.data.list);
        setTotal(response.data.total);
      } else {
        message.error(response.message || '获取拍卖会列表失败');
        setAuctions([]);
        setTotal(0);
      }
    } catch (error: any) {
      console.error('获取拍卖会列表失败:', error);
      let errorMsg = '获取拍卖会列表失败';
      if (error.response) {
        const { status } = error.response;
        if (status === 401) {
          errorMsg = '登录已过期，请重新登录';
        } else if (status === 403) {
          errorMsg = '没有权限访问拍卖会列表';
        } else if (status === 500) {
          errorMsg = '服务器内部错误，请稍后重试';
        }
      }
      message.error(errorMsg);
      setAuctions([]);
      setTotal(0);
    } finally {
      setLoading(false);
    }
  };

  // 获取拍卖会统计
  const fetchStatistics = async () => {
    try {
      const response = await auctionService.getAuctionStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (error: any) {
      console.error('获取拍卖会统计失败:', error);
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchAuctions();
    fetchStatistics();
  }, [queryParams]);

  // 搜索处理
  const handleSearch = (values: any) => {
    setQueryParams({
      ...queryParams,
      ...values,
      page: 1,
    });
  };

  // 重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    setQueryParams({
      page: 1,
      pageSize: 10,
    });
  };

  // 新增拍卖会
  const handleAdd = () => {
    setEditingAuction(null);
    form.resetFields();
    setIsModalVisible(true);
  };

  // 编辑拍卖会
  const handleEdit = (auction: Auction) => {
    setEditingAuction(auction);
    form.setFieldsValue({
      ...auction,
      timeRange: [new Date(auction.startTime), new Date(auction.endTime)],
    });
    setIsModalVisible(true);
  };

  // 删除拍卖会
  const handleDelete = async (id: number) => {
    try {
      const response = await auctionService.deleteAuction(id);
      if (response.success) {
        message.success('删除成功');
        fetchAuctions();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error: any) {
      message.error(error.message || '删除失败');
    }
  };

  // 保存拍卖会
  const handleSave = async (values: any) => {
    setSaving(true);

    try {
      const auctionData = {
        ...values,
        startTime: values.timeRange[0].toISOString(),
        endTime: values.timeRange[1].toISOString(),
      };
      delete auctionData.timeRange;

      let response;
      if (editingAuction) {
        response = await auctionService.updateAuction(editingAuction.id, auctionData);
      } else {
        response = await auctionService.createAuction(auctionData);
      }

      if (response.success) {
        const successMsg = editingAuction ? '拍卖会信息更新成功！' : '拍卖会创建成功！';
        message.success({
          content: successMsg,
          duration: 3,
        });
        setIsModalVisible(false);
        form.resetFields();
        setEditingAuction(null);
        fetchAuctions();
        fetchStatistics(); // 刷新统计数据
      } else {
        // 处理具体的错误信息
        let errorMsg = '操作失败';
        if (response.message) {
          if (response.message.includes('title')) {
            errorMsg = '拍卖会标题已存在，请使用其他标题';
          } else if (response.message.includes('time')) {
            errorMsg = '拍卖时间设置不正确，请检查时间范围';
          } else if (response.message.includes('validation')) {
            errorMsg = '输入信息格式不正确，请检查后重试';
          } else {
            errorMsg = response.message;
          }
        }

        message.error({
          content: errorMsg,
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('保存拍卖会失败:', error);

      let errorMsg = '保存失败，请稍后重试';
      if (error.response) {
        const { status, data } = error.response;
        if (status === 400) {
          if (data.error && data.error.includes('title')) {
            errorMsg = '拍卖会标题格式不正确或已存在';
          } else if (data.error && data.error.includes('time')) {
            errorMsg = '拍卖时间设置无效，请重新选择';
          } else {
            errorMsg = data.error || '请求参数错误，请检查输入信息';
          }
        } else if (status === 409) {
          errorMsg = '拍卖会信息冲突，标题可能已存在';
        } else if (status === 500) {
          errorMsg = '服务器内部错误，请联系管理员';
        } else {
          errorMsg = `请求失败 (${status})，请稍后重试`;
        }
      } else if (error.message) {
        errorMsg = error.message;
      }

      message.error({
        content: errorMsg,
        duration: 5,
      });
    } finally {
      setSaving(false);
    }
  };

  // 开始拍卖
  const handleStart = async (id: number) => {
    try {
      const response = await auctionService.startAuction(id);
      if (response.success) {
        message.success('拍卖会已开始');
        fetchAuctions();
      } else {
        message.error(response.message || '开始拍卖失败');
      }
    } catch (error: any) {
      message.error(error.message || '开始拍卖失败');
    }
  };

  // 暂停拍卖
  const handlePause = async (id: number) => {
    try {
      const response = await auctionService.pauseAuction(id);
      if (response.success) {
        message.success('拍卖会已暂停');
        fetchAuctions();
      } else {
        message.error(response.message || '暂停拍卖失败');
      }
    } catch (error: any) {
      message.error(error.message || '暂停拍卖失败');
    }
  };

  // 结束拍卖
  const handleEnd = async (id: number) => {
    try {
      const response = await auctionService.endAuction(id);
      if (response.success) {
        message.success('拍卖会已结束');
        fetchAuctions();
      } else {
        message.error(response.message || '结束拍卖失败');
      }
    } catch (error: any) {
      message.error(error.message || '结束拍卖失败');
    }
  };

  // 表格列定义
  const columns: ColumnsType<Auction> = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: '拍卖会标题',
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (text: string) => (
        <div style={{ fontWeight: 500 }}>{text}</div>
      ),
    },
    {
      title: '拍卖状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: AuctionStatus) => {
        const statusInfo = auctionStatusMap[status];
        return (
          <Badge
            status={
              status === AuctionStatus.ONGOING ? 'processing' :
              status === AuctionStatus.COMPLETED ? 'success' :
              status === AuctionStatus.CANCELLED ? 'error' : 'default'
            }
            text={
              <Tag color={statusInfo?.color || 'default'}>
                {statusInfo?.label || '未知'}
              </Tag>
            }
          />
        );
      },
    },
    {
      title: '商品数量',
      dataIndex: 'totalItems',
      key: 'totalItems',
      width: 100,
      render: (total: number, record: Auction) => (
        <div>
          <div>{total}件</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            已售: {record.soldItems}
          </div>
        </div>
      ),
    },
    {
      title: '成交金额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      width: 120,
      render: (amount: number) => (
        <div style={{ fontWeight: 500, color: '#f50' }}>
          ¥{amount.toFixed(2)}
        </div>
      ),
    },
    {
      title: '参与人数',
      dataIndex: 'participantCount',
      key: 'participantCount',
      width: 100,
    },
    {
      title: '创建人',
      dataIndex: 'creatorName',
      key: 'creatorName',
      width: 100,
    },
    {
      title: '拍卖时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 160,
      render: (text: string, record: Auction) => (
        <div>
          <div>{new Date(text).toLocaleString()}</div>
          <div style={{ fontSize: 12, color: '#999' }}>
            至 {new Date(record.endTime).toLocaleString()}
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record: Auction) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {/* 查看详情 */}}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          {record.status === AuctionStatus.SCHEDULED && (
            <Button
              type="link"
              size="small"
              icon={<PlayCircleOutlined />}
              onClick={() => handleStart(record.id)}
            >
              开始
            </Button>
          )}
          {record.status === AuctionStatus.ONGOING && (
            <>
              <Button
                type="link"
                size="small"
                icon={<PauseCircleOutlined />}
                onClick={() => handlePause(record.id)}
              >
                暂停
              </Button>
              <Button
                type="link"
                size="small"
                icon={<StopOutlined />}
                onClick={() => handleEnd(record.id)}
              >
                结束
              </Button>
            </>
          )}
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record.id)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <div className="auction-list-container">
      <Title level={2}>拍卖管理</Title>

      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总拍卖会"
              value={statistics.totalAuctions}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="进行中"
              value={statistics.ongoingAuctions}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日拍卖"
              value={statistics.todayAuctions}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总参与人数"
              value={statistics.totalParticipants}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 搜索表单 */}
      <Card className="search-card" size="small">
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          autoComplete="off"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="title" label="拍卖会标题">
                <Input placeholder="请输入拍卖会标题" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="status" label="拍卖状态">
                <Select placeholder="请选择拍卖状态" allowClear>
                  <Option value={AuctionStatus.DRAFT}>草稿</Option>
                  <Option value={AuctionStatus.SCHEDULED}>已安排</Option>
                  <Option value={AuctionStatus.ONGOING}>进行中</Option>
                  <Option value={AuctionStatus.PAUSED}>已暂停</Option>
                  <Option value={AuctionStatus.COMPLETED}>已完成</Option>
                  <Option value={AuctionStatus.CANCELLED}>已取消</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="creatorName" label="创建人">
                <Input placeholder="请输入创建人姓名" allowClear />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Form.Item name="dateRange" label="拍卖时间">
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Form.Item>
                <Space>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                  <Button onClick={handleReset} icon={<ReloadOutlined />}>
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 操作按钮 */}
      <Card className="action-card" size="small">
        <Row justify="space-between" align="middle">
          <Col>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增拍卖会
            </Button>
          </Col>
          <Col>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchAuctions}
              loading={loading}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </Card>

      {/* 拍卖会列表表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={auctions}
          rowKey="id"
          loading={loading}
          scroll={{ x: 1400 }}
          pagination={{
            current: queryParams.page,
            pageSize: queryParams.pageSize,
            total: total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) =>
              `第 ${range[0]}-${range[1]} 条/共 ${total} 条`,
            onChange: (page, pageSize) => {
              setQueryParams({
                ...queryParams,
                page,
                pageSize: pageSize || 10,
              });
            },
          }}
        />
      </Card>

      {/* 拍卖会编辑模态框 */}
      <Modal
        title={editingAuction ? '编辑拍卖会' : '新增拍卖会'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
          autoComplete="off"
        >
          <Form.Item
            name="title"
            label="拍卖会标题"
            rules={[
              { required: true, message: '请输入拍卖会标题' },
              { min: 2, max: 100, message: '标题长度为2-100个字符' },
            ]}
          >
            <Input
              placeholder="请输入拍卖会标题"
              showCount
              maxLength={100}
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="拍卖会描述"
            rules={[
              { max: 500, message: '描述不能超过500个字符' },
            ]}
          >
            <Input.TextArea
              placeholder="请输入拍卖会描述"
              rows={4}
              showCount
              maxLength={500}
            />
          </Form.Item>

          <Form.Item
            name="timeRange"
            label="拍卖时间"
            rules={[{ required: true, message: '请选择拍卖时间' }]}
          >
            <RangePicker
              showTime
              style={{ width: '100%' }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Form.Item
            name="location"
            label="拍卖地点"
            rules={[
              { required: true, message: '请输入拍卖地点' },
              { min: 2, max: 200, message: '地点长度为2-200个字符' },
            ]}
          >
            <Input
              placeholder="请输入拍卖地点"
              showCount
              maxLength={200}
            />
          </Form.Item>

          <Form.Item>
            <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                  setEditingAuction(null);
                }}
                disabled={saving}
              >
                取消
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={saving}
                disabled={saving}
              >
                {saving ? '保存中...' : (editingAuction ? '更新' : '创建')}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default AuctionList;