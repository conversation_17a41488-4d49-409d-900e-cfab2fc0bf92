import React from 'react';
import { Card, Row, Col, Statistic, Typography } from 'antd';
import { UserOutlined, ShoppingOutlined, AuditOutlined, DollarOutlined } from '@ant-design/icons';
import './index.css';

const { Title } = Typography;

const Dashboard: React.FC = () => {
  return (
    <div className="dashboard">
      <Title level={2}>仪表盘</Title>
      
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="总用户数"
              value={1128}
              prefix={<UserOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="商品总数"
              value={2456}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="进行中拍卖"
              value={12}
              prefix={<AuditOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card>
            <Statistic
              title="今日交易额"
              value={125680}
              prefix={<DollarOutlined />}
              precision={2}
              valueStyle={{ color: '#722ed1' }}
              suffix="元"
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="最近活动" size="small">
            <div className="activity-item">
              <p>用户 张三 创建了新的拍卖会</p>
              <span className="activity-time">2分钟前</span>
            </div>
            <div className="activity-item">
              <p>商品 "玫瑰花束" 审核通过</p>
              <span className="activity-time">5分钟前</span>
            </div>
            <div className="activity-item">
              <p>订单 #12345 已完成支付</p>
              <span className="activity-time">10分钟前</span>
            </div>
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="系统状态" size="small">
            <div className="status-item">
              <span>系统运行状态</span>
              <span className="status-value status-normal">正常</span>
            </div>
            <div className="status-item">
              <span>数据库连接</span>
              <span className="status-value status-normal">正常</span>
            </div>
            <div className="status-item">
              <span>缓存服务</span>
              <span className="status-value status-normal">正常</span>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
