import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, message } from 'antd';
import { UserOutlined, LockOutlined, WifiOutlined, DisconnectOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import './index.css';

interface LoginFormValues {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [networkStatus, setNetworkStatus] = useState<'checking' | 'online' | 'offline'>('checking');
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  // 检测网络和服务器状态
  useEffect(() => {
    const checkServerStatus = async () => {
      try {
        // 尝试访问后端健康检查接口
        const response = await fetch('http://localhost:8081/health', {
          method: 'GET',
          timeout: 5000,
        } as any);

        if (response.ok) {
          setNetworkStatus('online');
        } else {
          setNetworkStatus('offline');
        }
      } catch (error) {
        setNetworkStatus('offline');
      }
    };

    checkServerStatus();

    // 监听网络状态变化
    const handleOnline = () => checkServerStatus();
    const handleOffline = () => setNetworkStatus('offline');

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleSubmit = async (values: LoginFormValues) => {
    setLoading(true);
    try {
      const result = await login(values);

      if (result.success) {
        message.success({
          content: '登录成功！正在跳转...',
          duration: 2,
        });

        // 延迟一下让用户看到成功提示
        setTimeout(() => {
          navigate(from, { replace: true });
        }, 500);
      } else {
        // 根据错误类型显示不同的提示
        let errorMessage = result.message || '登录失败';

        if (errorMessage.includes('Network Error') || errorMessage.includes('timeout')) {
          errorMessage = '服务器连接超时，请检查网络连接或稍后重试';
        } else if (errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503')) {
          errorMessage = '服务器暂时不可用，请稍后重试';
        } else if (errorMessage.includes('404')) {
          errorMessage = '登录服务不可用，请联系系统管理员';
        } else if (errorMessage.includes('用户名') || errorMessage.includes('密码')) {
          errorMessage = '用户名或密码错误，请重新输入';
        }

        message.error({
          content: errorMessage,
          duration: 6,
        });
      }
    } catch (error: any) {
      console.error('登录异常:', error);

      // 处理网络错误
      let errorMessage = '登录失败，请稍后重试';

      if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
        errorMessage = '无法连接到服务器，请检查网络连接';
      } else if (error.code === 'TIMEOUT_ERROR' || error.message?.includes('timeout')) {
        errorMessage = '请求超时，请检查网络连接或稍后重试';
      } else if (error.response?.status >= 500) {
        errorMessage = '服务器内部错误，请稍后重试或联系系统管理员';
      } else if (error.response?.status === 404) {
        errorMessage = '登录服务不可用，请联系系统管理员';
      }

      message.error({
        content: errorMessage,
        duration: 6,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-content">
          <Card className="login-card">
            <div className="login-header">
              <h1>昆明花卉拍卖系统</h1>
              <p>管理后台登录</p>

              {/* 网络状态指示器 */}
              <div style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                marginTop: '8px',
                fontSize: '12px',
                color: networkStatus === 'online' ? '#52c41a' : '#ff4d4f'
              }}>
                {networkStatus === 'checking' ? (
                  <>
                    <WifiOutlined style={{ marginRight: '4px' }} />
                    <span>检测服务器状态...</span>
                  </>
                ) : networkStatus === 'online' ? (
                  <>
                    <WifiOutlined style={{ marginRight: '4px' }} />
                    <span>服务器连接正常</span>
                  </>
                ) : (
                  <>
                    <DisconnectOutlined style={{ marginRight: '4px' }} />
                    <span>服务器连接异常，请检查网络或联系管理员</span>
                  </>
                )}
              </div>
            </div>

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  disabled={loading || networkStatus === 'offline'}
                  block
                  size="large"
                  style={{
                    height: '48px',
                    fontSize: '16px',
                    fontWeight: '500',
                  }}
                >
                  {loading ? '正在登录...' :
                   networkStatus === 'offline' ? '服务器连接异常' :
                   '立即登录'}
                </Button>
              </Form.Item>

              {/* 测试账号提示 */}
              <div style={{
                textAlign: 'center',
                marginTop: '16px',
                padding: '12px',
                backgroundColor: '#f6f8fa',
                borderRadius: '6px',
                fontSize: '14px',
                color: '#666'
              }}>
                <div style={{ marginBottom: '4px', fontWeight: '500' }}>测试账号</div>
                <div>用户名：admin</div>
                <div>密码：admin123</div>
              </div>
            </Form>

            <div className="login-footer">
              <p>© 2024 昆明花卉拍卖系统. All rights reserved.</p>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
