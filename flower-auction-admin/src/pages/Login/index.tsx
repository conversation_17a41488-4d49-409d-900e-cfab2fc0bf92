import React, { useState } from 'react';
import { Form, Input, But<PERSON>, Card, message, Spin } from 'antd';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import './index.css';

interface LoginFormValues {
  username: string;
  password: string;
}

const Login: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const { login } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/dashboard';

  const handleSubmit = async (values: LoginFormValues) => {
    setLoading(true);
    try {
      console.log('开始登录...', values);
      const result = await login(values);
      console.log('登录结果:', result);

      if (result.success) {
        message.success({
          content: '登录成功！正在跳转...',
          duration: 2,
        });

        // 延迟一下让用户看到成功提示
        setTimeout(() => {
          console.log('跳转到:', from);
          navigate(from, { replace: true });
        }, 500);
      } else {
        message.error({
          content: result.message || '登录失败，请检查用户名和密码',
          duration: 5,
        });
      }
    } catch (error: any) {
      console.error('登录异常:', error);
      message.error({
        content: error.message || '登录失败，请检查网络连接',
        duration: 5,
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="login-container">
      <div className="login-background">
        <div className="login-content">
          <Card className="login-card">
            <div className="login-header">
              <h1>昆明花卉拍卖系统</h1>
              <p>管理后台登录</p>
            </div>

            <Form
              form={form}
              name="login"
              onFinish={handleSubmit}
              autoComplete="off"
              size="large"
            >
              <Form.Item
                name="username"
                rules={[
                  { required: true, message: '请输入用户名' },
                  { min: 3, message: '用户名至少3个字符' },
                ]}
              >
                <Input
                  prefix={<UserOutlined />}
                  placeholder="用户名"
                  autoComplete="username"
                />
              </Form.Item>

              <Form.Item
                name="password"
                rules={[
                  { required: true, message: '请输入密码' },
                  { min: 6, message: '密码至少6个字符' },
                ]}
              >
                <Input.Password
                  prefix={<LockOutlined />}
                  placeholder="密码"
                  autoComplete="current-password"
                />
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  block
                  size="large"
                  style={{
                    height: '48px',
                    fontSize: '16px',
                    fontWeight: '500',
                  }}
                >
                  {loading ? '正在登录...' : '立即登录'}
                </Button>
              </Form.Item>

              {/* 测试账号提示 */}
              <div style={{
                textAlign: 'center',
                marginTop: '16px',
                padding: '12px',
                backgroundColor: '#f6f8fa',
                borderRadius: '6px',
                fontSize: '14px',
                color: '#666'
              }}>
                <div style={{ marginBottom: '4px', fontWeight: '500' }}>测试账号</div>
                <div>用户名：admin</div>
                <div>密码：admin123</div>
              </div>
            </Form>

            <div className="login-footer">
              <p>© 2024 昆明花卉拍卖系统. All rights reserved.</p>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Login;
