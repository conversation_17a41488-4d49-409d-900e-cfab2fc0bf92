import { apiClient } from './apiClient';
import { Category } from '../pages/Products/CategoryManagement';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface CreateCategoryRequest {
  name: string;
  code: string;
  description?: string;
  parentId?: number;
  level: number;
  sort: number;
  status: number;
}

export interface UpdateCategoryRequest {
  name: string;
  description?: string;
  sort: number;
  status: number;
}

export const categoryService = {
  // 获取分类树
  getCategoryTree: async (): Promise<ApiResponse<Category[]>> => {
    try {
      const response = await apiClient.get('/categories/tree');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取分类树失败');
    }
  },

  // 获取分类列表（平铺）
  getCategoryList: async (): Promise<ApiResponse<Category[]>> => {
    try {
      const response = await apiClient.get('/categories');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取分类列表失败');
    }
  },

  // 创建分类
  createCategory: async (categoryData: CreateCategoryRequest): Promise<ApiResponse<Category>> => {
    try {
      const response = await apiClient.post('/categories', categoryData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建分类失败');
    }
  },

  // 更新分类
  updateCategory: async (id: number, categoryData: UpdateCategoryRequest): Promise<ApiResponse<Category>> => {
    try {
      const response = await apiClient.put(`/categories/${id}`, categoryData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新分类失败');
    }
  },

  // 删除分类
  deleteCategory: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/categories/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '删除分类失败');
    }
  },

  // 获取分类详情
  getCategoryDetail: async (id: number): Promise<ApiResponse<Category>> => {
    try {
      const response = await apiClient.get(`/categories/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取分类详情失败');
    }
  },

  // 移动分类
  moveCategory: async (id: number, targetParentId?: number, position?: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.patch(`/categories/${id}/move`, {
        targetParentId,
        position,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '移动分类失败');
    }
  },

  // 批量删除分类
  batchDeleteCategories: async (ids: number[]): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete('/categories/batch', { data: { ids } });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '批量删除分类失败');
    }
  },

  // 检查分类编码是否存在
  checkCategoryCodeExists: async (code: string, excludeId?: number): Promise<ApiResponse<boolean>> => {
    try {
      const params = excludeId ? { code, excludeId } : { code };
      const response = await apiClient.get('/categories/check-code', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '检查分类编码失败');
    }
  },

  // 获取分类统计信息
  getCategoryStatistics: async (): Promise<ApiResponse<{
    total: number;
    activeCategories: number;
    maxLevel: number;
    categoryDistribution: Record<number, number>;
  }>> => {
    try {
      const response = await apiClient.get('/categories/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取分类统计信息失败');
    }
  },

  // 获取父级分类选项
  getParentCategoryOptions: async (excludeId?: number): Promise<ApiResponse<Category[]>> => {
    try {
      const params = excludeId ? { excludeId } : {};
      const response = await apiClient.get('/categories/parent-options', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取父级分类选项失败');
    }
  },

  // 更新分类排序
  updateCategorySort: async (sortData: { id: number; sort: number }[]): Promise<ApiResponse> => {
    try {
      const response = await apiClient.patch('/categories/sort', { sortData });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新分类排序失败');
    }
  },
};
