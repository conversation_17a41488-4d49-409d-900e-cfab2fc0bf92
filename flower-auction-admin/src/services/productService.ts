import { apiClient } from './apiClient';
import { Product, Category, QualityLevel, ProductStatus } from '../pages/Products/ProductList';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface ProductListResponse {
  list: Product[];
  total: number;
  page: number;
  pageSize: number;
}

export interface ProductQueryParams {
  name?: string;
  categoryId?: number;
  qualityLevel?: QualityLevel;
  status?: ProductStatus;
  origin?: string;
  page: number;
  pageSize: number;
}

export interface CreateProductRequest {
  name: string;
  categoryId: number;
  description?: string;
  qualityLevel: QualityLevel;
  origin: string;
  supplierId: number;
  status: ProductStatus;
  images?: string[];
}

export interface UpdateProductRequest {
  name: string;
  categoryId: number;
  description?: string;
  qualityLevel: QualityLevel;
  origin: string;
  supplierId: number;
  status: ProductStatus;
  images?: string[];
}

export const productService = {
  // 获取商品列表
  getProductList: async (params: ProductQueryParams): Promise<ApiResponse<ProductListResponse>> => {
    try {
      const response = await apiClient.get('/products', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取商品列表失败');
    }
  },

  // 创建商品
  createProduct: async (productData: CreateProductRequest): Promise<ApiResponse<Product>> => {
    try {
      const response = await apiClient.post('/products', productData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建商品失败');
    }
  },

  // 更新商品
  updateProduct: async (id: number, productData: UpdateProductRequest): Promise<ApiResponse<Product>> => {
    try {
      const response = await apiClient.put(`/products/${id}`, productData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新商品失败');
    }
  },

  // 删除商品
  deleteProduct: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/products/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '删除商品失败');
    }
  },

  // 更新商品状态
  updateProductStatus: async (id: number, status: ProductStatus): Promise<ApiResponse> => {
    try {
      const response = await apiClient.put(`/products/status/${id}`, { status });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新商品状态失败');
    }
  },

  // 获取商品详情
  getProductDetail: async (id: number): Promise<ApiResponse<Product>> => {
    try {
      const response = await apiClient.get(`/products/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取商品详情失败');
    }
  },

  // 获取商品分类列表
  getCategoryList: async (): Promise<ApiResponse<Category[]>> => {
    try {
      const response = await apiClient.get('/categories');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取商品分类失败');
    }
  },

  // 批量删除商品
  batchDeleteProducts: async (ids: number[]): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete('/products/batch', { data: { ids } });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '批量删除商品失败');
    }
  },

  // 导出商品数据
  exportProducts: async (params: ProductQueryParams): Promise<ApiResponse<Blob>> => {
    try {
      const response = await apiClient.get('/products/export', {
        params,
        responseType: 'blob',
      });
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '导出商品数据失败');
    }
  },

  // 批量导入商品
  importProducts: async (file: File): Promise<ApiResponse> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await apiClient.post('/products/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '导入商品数据失败');
    }
  },

  // 上传商品图片
  uploadProductImage: async (file: File): Promise<ApiResponse<{ url: string }>> => {
    try {
      const formData = new FormData();
      formData.append('image', file);
      const response = await apiClient.post('/products/upload-image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '上传图片失败');
    }
  },

  // 获取商品统计信息
  getProductStatistics: async (): Promise<ApiResponse<{
    total: number;
    onlineProducts: number;
    newProductsToday: number;
    categoryDistribution: Record<string, number>;
    qualityDistribution: Record<QualityLevel, number>;
  }>> => {
    try {
      const response = await apiClient.get('/products/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取商品统计信息失败');
    }
  },

  // 商品审核
  auditProduct: async (id: number, status: 'approved' | 'rejected', reason?: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/products/${id}/audit`, { status, reason });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '商品审核失败');
    }
  },

  // 获取待审核商品列表
  getPendingAuditProducts: async (params: { page: number; pageSize: number }): Promise<ApiResponse<ProductListResponse>> => {
    try {
      const response = await apiClient.get('/products/pending-audit', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取待审核商品列表失败');
    }
  },
};
