import { apiClient } from './apiClient';
import { Auction, AuctionStatus } from '../pages/Auctions/AuctionList';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface AuctionListResponse {
  list: Auction[];
  total: number;
  page: number;
  pageSize: number;
}

export interface AuctionQueryParams {
  title?: string;
  status?: AuctionStatus;
  creatorName?: string;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

export interface CreateAuctionRequest {
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  location: string;
}

export interface UpdateAuctionRequest {
  title: string;
  description?: string;
  startTime: string;
  endTime: string;
  location: string;
}

export interface AuctionStatistics {
  totalAuctions: number;
  ongoingAuctions: number;
  todayAuctions: number;
  totalParticipants: number;
  statusDistribution: Record<AuctionStatus, number>;
}

export const auctionService = {
  // 获取拍卖会列表
  getAuctionList: async (params: AuctionQueryParams): Promise<ApiResponse<AuctionListResponse>> => {
    try {
      const response = await apiClient.get('/auctions', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取拍卖会列表失败');
    }
  },

  // 创建拍卖会
  createAuction: async (auctionData: CreateAuctionRequest): Promise<ApiResponse<Auction>> => {
    try {
      const response = await apiClient.post('/auctions', auctionData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建拍卖会失败');
    }
  },

  // 更新拍卖会
  updateAuction: async (id: number, auctionData: UpdateAuctionRequest): Promise<ApiResponse<Auction>> => {
    try {
      const response = await apiClient.put(`/auctions/${id}`, auctionData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新拍卖会失败');
    }
  },

  // 删除拍卖会
  deleteAuction: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/auctions/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '删除拍卖会失败');
    }
  },

  // 获取拍卖会详情
  getAuctionDetail: async (id: number): Promise<ApiResponse<Auction>> => {
    try {
      const response = await apiClient.get(`/auctions/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取拍卖会详情失败');
    }
  },

  // 开始拍卖
  startAuction: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/auctions/${id}/start`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '开始拍卖失败');
    }
  },

  // 暂停拍卖
  pauseAuction: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/auctions/${id}/pause`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '暂停拍卖失败');
    }
  },

  // 恢复拍卖
  resumeAuction: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/auctions/${id}/resume`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '恢复拍卖失败');
    }
  },

  // 结束拍卖
  endAuction: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/auctions/${id}/end`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '结束拍卖失败');
    }
  },

  // 取消拍卖
  cancelAuction: async (id: number, reason: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/auctions/${id}/cancel`, { reason });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '取消拍卖失败');
    }
  },

  // 获取拍卖会统计信息
  getAuctionStatistics: async (): Promise<ApiResponse<AuctionStatistics>> => {
    try {
      const response = await apiClient.get('/auctions/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取拍卖会统计信息失败');
    }
  },

  // 获取拍卖会商品列表
  getAuctionItems: async (auctionId: number, params?: {
    page: number;
    pageSize: number;
  }): Promise<ApiResponse<{
    list: Array<{
      id: number;
      productId: number;
      productName: string;
      startingPrice: number;
      currentPrice: number;
      bidCount: number;
      status: string;
      images?: string[];
    }>;
    total: number;
  }>> => {
    try {
      const response = await apiClient.get(`/auction-items/auction/${auctionId}`, { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取拍卖商品列表失败');
    }
  },

  // 添加拍卖商品
  addAuctionItem: async (auctionId: number, itemData: {
    productId: number;
    startingPrice: number;
    reservePrice?: number;
    bidIncrement: number;
  }): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/auction-items`, { ...itemData, auctionId });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '添加拍卖商品失败');
    }
  },

  // 移除拍卖商品
  removeAuctionItem: async (auctionId: number, itemId: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/auctions/${auctionId}/items/${itemId}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '移除拍卖商品失败');
    }
  },

  // 获取竞价记录
  getBidRecords: async (auctionId: number, itemId?: number, params?: {
    page: number;
    pageSize: number;
  }): Promise<ApiResponse<{
    list: Array<{
      id: number;
      bidderName: string;
      bidAmount: number;
      bidTime: string;
      isWinning: boolean;
    }>;
    total: number;
  }>> => {
    try {
      const url = itemId ? `/bids/item/${itemId}` : `/bids/auction/${auctionId}`;
      const response = await apiClient.get(url, { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取竞价记录失败');
    }
  },

  // 获取拍卖参与者列表
  getAuctionParticipants: async (auctionId: number): Promise<ApiResponse<Array<{
    id: number;
    userName: string;
    joinTime: string;
    bidCount: number;
    totalBidAmount: number;
  }>>> => {
    try {
      const response = await apiClient.get(`/auctions/${auctionId}/participants`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取拍卖参与者列表失败');
    }
  },
};
