import { apiClient } from './apiClient';
import { LoginCredentials, User } from '../hooks/useAuth';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: User;
}

export const authService = {
  // 用户登录
  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {
    try {
      const response = await apiClient.post('/auth/login', credentials);

      // 后端返回的数据结构：{ user: {...}, token: "..." }
      // 需要转换为前端期望的结构：{ success: true, data: { token, refreshToken, user } }
      if (response.data && response.data.user && response.data.token) {
        return {
          success: true,
          data: {
            token: response.data.token,
            refreshToken: response.data.token, // 暂时使用同一个token
            user: {
              id: response.data.user.id,
              username: response.data.user.username,
              email: response.data.user.email,
              role: response.data.user.roles?.[0]?.name || 'user',
              realName: response.data.user.realName,
            }
          }
        };
      } else {
        return {
          success: false,
          message: '登录响应数据格式错误'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.error || error.message || '登录失败'
      };
    }
  },

  // 用户登出
  logout: async (): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/auth/logout');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '登出失败');
    }
  },

  // 获取用户信息
  getUserInfo: async (): Promise<ApiResponse<User>> => {
    try {
      const response = await apiClient.get('/auth/me');

      // 后端返回的数据结构需要转换
      if (response.data && response.data.success) {
        return {
          success: true,
          data: {
            id: response.data.data.id,
            username: response.data.data.username,
            email: response.data.data.email || '',
            role: response.data.data.role || 'user',
            realName: response.data.data.realName,
          }
        };
      } else {
        return {
          success: false,
          message: '获取用户信息失败'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.error || error.message || '获取用户信息失败'
      };
    }
  },

  // 刷新token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
    try {
      const response = await apiClient.post('/auth/refresh', { refreshToken });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '刷新token失败');
    }
  },

  // 修改密码
  changePassword: async (oldPassword: string, newPassword: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/auth/change-password', {
        oldPassword,
        newPassword,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '修改密码失败');
    }
  },
};
