import { apiClient } from './apiClient';
import { LoginCredentials, User } from '../hooks/useAuth';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: User;
}

export const authService = {
  // 用户登录
  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {
    try {
      const response = await apiClient.post('/auth/login', credentials);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '登录失败');
    }
  },

  // 用户登出
  logout: async (): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/auth/logout');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '登出失败');
    }
  },

  // 获取用户信息
  getUserInfo: async (): Promise<ApiResponse<User>> => {
    try {
      const response = await apiClient.get('/auth/me');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取用户信息失败');
    }
  },

  // 刷新token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
    try {
      const response = await apiClient.post('/auth/refresh', { refreshToken });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '刷新token失败');
    }
  },

  // 修改密码
  changePassword: async (oldPassword: string, newPassword: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/auth/change-password', {
        oldPassword,
        newPassword,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '修改密码失败');
    }
  },
};
