import { apiClient } from './apiClient';
import { LoginCredentials, User } from '../hooks/useAuth';

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
}

export interface LoginResponse {
  token: string;
  refreshToken: string;
  user: User;
}

export const authService = {
  // 用户登录
  login: async (credentials: LoginCredentials): Promise<ApiResponse<LoginResponse>> => {
    try {
      const response = await apiClient.post('/auth/login', credentials);

      // 后端返回的数据结构：{ success: true, data: { user: {...}, token: "...", refreshToken: "..." } }

      if (response.data && response.data.success && response.data.data) {
        const { data } = response.data;
        return {
          success: true,
          data: {
            token: data.token,
            refreshToken: data.refreshToken,
            user: {
              id: data.user.id,
              username: data.user.username,
              email: data.user.email || '',
              role: data.user.userType === 3 ? 'admin' : 'user',
              realName: data.user.realName,
            }
          }
        };
      } else {
        return {
          success: false,
          message: '登录响应数据格式错误'
        };
      }
    } catch (error: any) {
      console.error('Login error:', error);

      // 处理不同类型的网络错误
      let errorMessage = '登录失败';

      if (error.code === 'ECONNREFUSED' || error.code === 'ERR_NETWORK') {
        errorMessage = '无法连接到服务器，请检查服务器是否启动';
      } else if (error.code === 'TIMEOUT' || error.code === 'ECONNABORTED') {
        errorMessage = '请求超时，请检查网络连接';
      } else if (error.response) {
        // 服务器响应了错误状态码
        const status = error.response.status;
        if (status >= 500) {
          errorMessage = '服务器内部错误，请稍后重试';
        } else if (status === 404) {
          errorMessage = '登录接口不存在，请联系系统管理员';
        } else if (status === 401) {
          errorMessage = '用户名或密码错误';
        } else {
          errorMessage = error.response.data?.error || error.response.data?.message || `服务器错误 (${status})`;
        }
      } else if (error.request) {
        // 请求已发出但没有收到响应
        errorMessage = '服务器无响应，请检查网络连接或服务器状态';
      } else {
        // 其他错误
        errorMessage = error.message || '未知错误';
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  },

  // 用户登出
  logout: async (): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/auth/logout');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '登出失败');
    }
  },

  // 获取用户信息
  getUserInfo: async (): Promise<ApiResponse<User>> => {
    try {
      const response = await apiClient.get('/auth/me');

      // 后端返回的数据结构需要转换
      if (response.data && response.data.success) {
        return {
          success: true,
          data: {
            id: response.data.data.id,
            username: response.data.data.username,
            email: response.data.data.email || '',
            role: response.data.data.role || 'user',
            realName: response.data.data.realName,
          }
        };
      } else {
        return {
          success: false,
          message: '获取用户信息失败'
        };
      }
    } catch (error: any) {
      return {
        success: false,
        message: error.response?.data?.error || error.message || '获取用户信息失败'
      };
    }
  },

  // 刷新token
  refreshToken: async (refreshToken: string): Promise<ApiResponse<{ token: string; refreshToken: string }>> => {
    try {
      const response = await apiClient.post('/auth/refresh', { refreshToken });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '刷新token失败');
    }
  },

  // 修改密码
  changePassword: async (oldPassword: string, newPassword: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post('/auth/change-password', {
        oldPassword,
        newPassword,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '修改密码失败');
    }
  },
};
