import axios, { AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { message } from 'antd';

// 创建axios实例
export const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL || 'http://localhost:8081/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
apiClient.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 添加认证token
    const token = localStorage.getItem('token');
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const { response } = error;

    if (response) {
      switch (response.status) {
        case 401:
          // token过期或无效
          const refreshToken = localStorage.getItem('refreshToken');
          if (refreshToken) {
            try {
              // 尝试刷新token
              const refreshResponse = await axios.post(
                `${process.env.REACT_APP_API_BASE_URL || 'http://localhost:8080/api/v1'}/auth/refresh`,
                { refreshToken }
              );

              if (refreshResponse.data.success) {
                // 更新token
                localStorage.setItem('token', refreshResponse.data.data.token);
                localStorage.setItem('refreshToken', refreshResponse.data.data.refreshToken);

                // 重新发送原请求
                const originalRequest = error.config;
                originalRequest.headers.Authorization = `Bearer ${refreshResponse.data.data.token}`;
                return apiClient(originalRequest);
              }
            } catch (refreshError) {
              console.error('Token refresh failed:', refreshError);
            }
          }

          // 刷新失败，清除认证信息并跳转到登录页
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          window.location.href = '/login';
          message.error('登录已过期，请重新登录');
          break;

        case 403:
          message.error('没有权限访问该资源');
          break;

        case 404:
          message.error('请求的资源不存在');
          break;

        case 500:
          message.error('服务器内部错误');
          break;

        default:
          message.error(response.data?.message || '请求失败');
      }
    } else {
      // 网络错误
      message.error('网络连接失败，请检查网络设置');
    }

    return Promise.reject(error);
  }
);

export default apiClient;
