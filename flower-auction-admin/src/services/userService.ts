import { apiClient } from './apiClient';
import { User, UserType, UserStatus } from '../pages/Users/<USER>';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface UserListResponse {
  list: User[];
  total: number;
  page: number;
  pageSize: number;
}

export interface UserQueryParams {
  username?: string;
  phone?: string;
  userType?: UserType;
  status?: UserStatus;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

export interface CreateUserRequest {
  username: string;
  password: string;
  realName?: string;
  phone: string;
  email?: string;
  userType: UserType;
  status: UserStatus;
}

export interface UpdateUserRequest {
  realName?: string;
  phone: string;
  email?: string;
  userType: UserType;
  status: UserStatus;
}

export const userService = {
  // 获取用户列表
  getUserList: async (params: UserQueryParams): Promise<ApiResponse<UserListResponse>> => {
    try {
      const response = await apiClient.get('/users', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取用户列表失败');
    }
  },

  // 创建用户
  createUser: async (userData: CreateUserRequest): Promise<ApiResponse<User>> => {
    try {
      const response = await apiClient.post('/users', userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建用户失败');
    }
  },

  // 更新用户
  updateUser: async (id: number, userData: UpdateUserRequest): Promise<ApiResponse<User>> => {
    try {
      const response = await apiClient.put(`/users/${id}`, userData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新用户失败');
    }
  },

  // 删除用户
  deleteUser: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/users/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '删除用户失败');
    }
  },

  // 更新用户状态
  updateUserStatus: async (id: number, status: UserStatus): Promise<ApiResponse> => {
    try {
      const response = await apiClient.put(`/users/status/${id}`, { status });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新用户状态失败');
    }
  },

  // 获取用户详情
  getUserDetail: async (id: number): Promise<ApiResponse<User>> => {
    try {
      const response = await apiClient.get(`/users/info/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取用户详情失败');
    }
  },

  // 重置用户密码
  resetUserPassword: async (id: number, newPassword: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.put(`/users/${id}/password`, { password: newPassword });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '重置密码失败');
    }
  },

  // 批量删除用户
  batchDeleteUsers: async (ids: number[]): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete('/users/batch', { data: { ids } });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '批量删除用户失败');
    }
  },

  // 导出用户数据
  exportUsers: async (params: UserQueryParams): Promise<ApiResponse<Blob>> => {
    try {
      const response = await apiClient.get('/users/export', {
        params,
        responseType: 'blob',
      });
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '导出用户数据失败');
    }
  },

  // 批量导入用户
  importUsers: async (file: File): Promise<ApiResponse> => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      const response = await apiClient.post('/users/import', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '导入用户数据失败');
    }
  },

  // 获取用户统计信息
  getUserStatistics: async (): Promise<ApiResponse<{
    total: number;
    activeUsers: number;
    newUsersToday: number;
    userTypeDistribution: Record<UserType, number>;
  }>> => {
    try {
      const response = await apiClient.get('/users/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取用户统计信息失败');
    }
  },
};
