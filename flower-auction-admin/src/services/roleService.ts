import { apiClient } from './apiClient';
import { Role, Permission } from '../pages/Users/<USER>';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface RoleListResponse {
  list: Role[];
  total: number;
  page: number;
  pageSize: number;
}

export interface RoleQueryParams {
  name?: string;
  code?: string;
  status?: number;
  page: number;
  pageSize: number;
}

export interface CreateRoleRequest {
  name: string;
  code: string;
  description?: string;
  status: number;
}

export interface UpdateRoleRequest {
  name: string;
  description?: string;
  status: number;
}

export const roleService = {
  // 获取角色列表
  getRoleList: async (params: RoleQueryParams): Promise<ApiResponse<RoleListResponse>> => {
    try {
      const response = await apiClient.get('/roles', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取角色列表失败');
    }
  },

  // 创建角色
  createRole: async (roleData: CreateRoleRequest): Promise<ApiResponse<Role>> => {
    try {
      const response = await apiClient.post('/roles', roleData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '创建角色失败');
    }
  },

  // 更新角色
  updateRole: async (id: number, roleData: UpdateRoleRequest): Promise<ApiResponse<Role>> => {
    try {
      const response = await apiClient.put(`/roles/${id}`, roleData);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新角色失败');
    }
  },

  // 删除角色
  deleteRole: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete(`/roles/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '删除角色失败');
    }
  },

  // 获取角色详情
  getRoleDetail: async (id: number): Promise<ApiResponse<Role>> => {
    try {
      const response = await apiClient.get(`/roles/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取角色详情失败');
    }
  },

  // 获取权限列表
  getPermissionList: async (): Promise<ApiResponse<Permission[]>> => {
    try {
      const response = await apiClient.get('/permissions');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取权限列表失败');
    }
  },

  // 更新角色权限
  updateRolePermissions: async (roleId: number, permissionIds: number[]): Promise<ApiResponse> => {
    try {
      const response = await apiClient.put(`/roles/${roleId}/permissions`, {
        permissionIds,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新角色权限失败');
    }
  },

  // 获取角色权限
  getRolePermissions: async (roleId: number): Promise<ApiResponse<number[]>> => {
    try {
      const response = await apiClient.get(`/roles/${roleId}/permissions`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取角色权限失败');
    }
  },

  // 批量删除角色
  batchDeleteRoles: async (ids: number[]): Promise<ApiResponse> => {
    try {
      const response = await apiClient.delete('/roles/batch', { data: { ids } });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '批量删除角色失败');
    }
  },

  // 复制角色
  copyRole: async (id: number, newName: string, newCode: string): Promise<ApiResponse<Role>> => {
    try {
      const response = await apiClient.post(`/roles/${id}/copy`, {
        name: newName,
        code: newCode,
      });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '复制角色失败');
    }
  },

  // 获取角色统计信息
  getRoleStatistics: async (): Promise<ApiResponse<{
    total: number;
    activeRoles: number;
    totalPermissions: number;
    roleDistribution: Record<string, number>;
  }>> => {
    try {
      const response = await apiClient.get('/roles/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取角色统计信息失败');
    }
  },

  // 检查角色编码是否存在
  checkRoleCodeExists: async (code: string, excludeId?: number): Promise<ApiResponse<boolean>> => {
    try {
      const params = excludeId ? { code, excludeId } : { code };
      const response = await apiClient.get('/roles/check-code', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '检查角色编码失败');
    }
  },
};
