import { apiClient } from './apiClient';
import { Order, OrderStatus, PaymentMethod } from '../pages/Orders/OrderList';

export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
}

export interface OrderListResponse {
  list: Order[];
  total: number;
  page: number;
  pageSize: number;
}

export interface OrderQueryParams {
  orderNo?: string;
  buyerName?: string;
  sellerName?: string;
  status?: OrderStatus;
  paymentMethod?: PaymentMethod;
  dateRange?: [string, string];
  page: number;
  pageSize: number;
}

export interface OrderStatistics {
  totalOrders: number;
  totalAmount: number;
  todayOrders: number;
  pendingOrders: number;
  statusDistribution: Record<OrderStatus, number>;
  paymentMethodDistribution: Record<PaymentMethod, number>;
}

export const orderService = {
  // 获取订单列表
  getOrderList: async (params: OrderQueryParams): Promise<ApiResponse<OrderListResponse>> => {
    try {
      const response = await apiClient.get('/orders', { params });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取订单列表失败');
    }
  },

  // 获取订单详情
  getOrderDetail: async (id: number): Promise<ApiResponse<Order>> => {
    try {
      const response = await apiClient.get(`/orders/${id}`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取订单详情失败');
    }
  },

  // 更新订单状态
  updateOrderStatus: async (id: number, status: OrderStatus, remark?: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.patch(`/orders/${id}/status`, { status, remark });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '更新订单状态失败');
    }
  },

  // 取消订单
  cancelOrder: async (id: number, reason: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/orders/${id}/cancel`, { reason });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '取消订单失败');
    }
  },

  // 退款订单
  refundOrder: async (id: number, amount: number, reason: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/orders/${id}/refund`, { amount, reason });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '退款失败');
    }
  },

  // 发货
  shipOrder: async (id: number, shippingInfo: {
    trackingNumber: string;
    shippingCompany: string;
    shippingTime: string;
  }): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/orders/${id}/ship`, shippingInfo);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '发货失败');
    }
  },

  // 确认收货
  confirmDelivery: async (id: number): Promise<ApiResponse> => {
    try {
      const response = await apiClient.post(`/orders/${id}/confirm-delivery`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '确认收货失败');
    }
  },

  // 获取订单统计信息
  getOrderStatistics: async (): Promise<ApiResponse<OrderStatistics>> => {
    try {
      const response = await apiClient.get('/orders/statistics');
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取订单统计信息失败');
    }
  },

  // 导出订单数据
  exportOrders: async (params: OrderQueryParams): Promise<ApiResponse<Blob>> => {
    try {
      const response = await apiClient.get('/orders/export', {
        params,
        responseType: 'blob',
      });
      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '导出订单数据失败');
    }
  },

  // 批量更新订单状态
  batchUpdateOrderStatus: async (ids: number[], status: OrderStatus, remark?: string): Promise<ApiResponse> => {
    try {
      const response = await apiClient.patch('/orders/batch-status', { ids, status, remark });
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '批量更新订单状态失败');
    }
  },

  // 获取订单物流信息
  getOrderShipping: async (id: number): Promise<ApiResponse<{
    trackingNumber: string;
    shippingCompany: string;
    shippingTime: string;
    deliveryTime?: string;
    shippingStatus: string;
    trackingHistory: Array<{
      time: string;
      status: string;
      location: string;
      description: string;
    }>;
  }>> => {
    try {
      const response = await apiClient.get(`/orders/${id}/shipping`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取物流信息失败');
    }
  },

  // 获取订单支付信息
  getOrderPayment: async (id: number): Promise<ApiResponse<{
    paymentMethod: PaymentMethod;
    paymentTime?: string;
    paymentAmount: number;
    transactionId?: string;
    paymentStatus: string;
  }>> => {
    try {
      const response = await apiClient.get(`/orders/${id}/payment`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取支付信息失败');
    }
  },

  // 重新发起支付
  retryPayment: async (id: number): Promise<ApiResponse<{
    paymentUrl: string;
    qrCode?: string;
  }>> => {
    try {
      const response = await apiClient.post(`/orders/${id}/retry-payment`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '重新发起支付失败');
    }
  },

  // 获取订单操作日志
  getOrderLogs: async (id: number): Promise<ApiResponse<Array<{
    id: number;
    action: string;
    description: string;
    operatorName: string;
    createdAt: string;
  }>>> => {
    try {
      const response = await apiClient.get(`/orders/${id}/logs`);
      return response.data;
    } catch (error: any) {
      throw new Error(error.response?.data?.message || '获取订单日志失败');
    }
  },
};
