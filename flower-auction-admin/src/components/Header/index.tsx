import React from 'react';
import { Layout, Menu, Dropdown, Avatar, Badge, Space } from 'antd';
import {
  UserOutlined,
  BellOutlined,
  LogoutOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import './index.css';

const { Header } = Layout;

interface HeaderProps {
  collapsed: boolean;
  toggle: () => void;
}

const HeaderComponent: React.FC<HeaderProps> = ({ collapsed, toggle }) => {
  const { user, logoutUser } = useAuth();

  const handleLogout = () => {
    logoutUser();
  };

  const userMenu = (
    <Menu>
      <Menu.Item key="profile" icon={<UserOutlined />}>
        个人中心
      </Menu.Item>
      <Menu.Item key="settings" icon={<SettingOutlined />}>
        账号设置
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="logout" icon={<LogoutOutlined />} onClick={handleLogout}>
        退出登录
      </Menu.Item>
    </Menu>
  );

  const notificationMenu = (
    <Menu>
      <Menu.Item key="notification1">
        系统通知：新版本已发布
      </Menu.Item>
      <Menu.Item key="notification2">
        业务通知：有新的拍卖会已创建
      </Menu.Item>
      <Menu.Item key="notification3">
        提醒：今日有3个订单待处理
      </Menu.Item>
      <Menu.Divider />
      <Menu.Item key="all">
        查看全部通知
      </Menu.Item>
    </Menu>
  );

  return (
    <Header className="site-header">
      <div className="header-left">
        {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
          className: 'trigger',
          onClick: toggle,
        })}
      </div>
      <div className="header-right">
        <Space size="large">
          <Dropdown overlay={notificationMenu} placement="bottomRight">
            <Badge count={5} size="small">
              <BellOutlined className="header-icon" />
            </Badge>
          </Dropdown>
          <Dropdown overlay={userMenu} placement="bottomRight">
            <Space>
              <Avatar icon={<UserOutlined />} />
              <span className="username">{user?.username || '用户'}</span>
            </Space>
          </Dropdown>
        </Space>
      </div>
    </Header>
  );
};

export default HeaderComponent;