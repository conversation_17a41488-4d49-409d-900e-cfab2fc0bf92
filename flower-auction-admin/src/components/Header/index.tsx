import React from 'react';
import { Layout, Dropdown, Avatar, Badge, Space } from 'antd';
import {
  UserOutlined,
  BellOutlined,
  LogoutOutlined,
  SettingOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import './index.css';

const { Header } = Layout;

interface HeaderProps {
  collapsed: boolean;
  toggle: () => void;
}

const HeaderComponent: React.FC<HeaderProps> = ({ collapsed, toggle }) => {
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
  };

  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账号设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout,
    },
  ];

  const notificationMenuItems = [
    {
      key: 'notification1',
      label: '系统通知：新版本已发布',
    },
    {
      key: 'notification2',
      label: '业务通知：有新的拍卖会已创建',
    },
    {
      key: 'notification3',
      label: '提醒：今日有3个订单待处理',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'all',
      label: '查看全部通知',
    },
  ];

  return (
    <Header className="site-header">
      <div className="header-left">
        {React.createElement(collapsed ? MenuUnfoldOutlined : MenuFoldOutlined, {
          className: 'trigger',
          onClick: toggle,
        })}
      </div>
      <div className="header-right">
        <Space size="large">
          <Dropdown menu={{ items: notificationMenuItems }} placement="bottomRight">
            <Badge count={5} size="small">
              <BellOutlined className="header-icon" />
            </Badge>
          </Dropdown>
          <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
            <Space>
              <Avatar icon={<UserOutlined />} />
              <span className="username">{user?.username || '用户'}</span>
            </Space>
          </Dropdown>
        </Space>
      </div>
    </Header>
  );
};

export default HeaderComponent;