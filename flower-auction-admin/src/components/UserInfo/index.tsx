import React from 'react';
import { Card, Avatar, Typography, Tag, Descriptions } from 'antd';
import { UserOutlined, TeamOutlined, MailOutlined } from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import './index.css';

const { Title, Text } = Typography;

const UserInfo: React.FC = () => {
  const { user } = useAuth();

  // 角色映射
  const roleMap: Record<string, { label: string; color: string }> = {
    'admin': { label: '管理员', color: 'red' },
    'seller': { label: '卖家', color: 'blue' },
    'buyer': { label: '买家', color: 'green' },
    'quality_inspector': { label: '质检员', color: 'orange' },
  };

  // 如果没有用户信息，显示加载中
  if (!user) {
    return (
      <Card className="user-info-card">
        <div className="user-info-loading">加载用户信息中...</div>
      </Card>
    );
  }

  const userRole = roleMap[user.role] || { label: '未知', color: 'default' };

  return (
    <Card className="user-info-card">
      <div className="user-info-header">
        <Avatar size={64} icon={<UserOutlined />} />
        <div className="user-info-title">
          <Title level={4}>{user.username}</Title>
          <Tag color={userRole.color}>{userRole.label}</Tag>
        </div>
      </div>

      <Descriptions column={1} className="user-info-details">
        <Descriptions.Item label={<><UserOutlined /> 用户ID</>}>
          {user.id}
        </Descriptions.Item>
        <Descriptions.Item label={<><TeamOutlined /> 角色</>}>
          <Tag color={userRole.color}>{userRole.label}</Tag>
        </Descriptions.Item>
        <Descriptions.Item label={<><MailOutlined /> 邮箱</>}>
          {user.email || '未设置'}
        </Descriptions.Item>
        <Descriptions.Item label="真实姓名">
          {user.realName || '未设置'}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
};

export default UserInfo;