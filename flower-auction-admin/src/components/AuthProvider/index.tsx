import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setUser, clearUser, setLoading } from '../../store/slices/authSlice';
import { authService } from '../../services/authService';

interface AuthProviderProps {
  children: React.ReactNode;
}

// 全局初始化状态，确保只初始化一次
let hasInitialized = false;

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useDispatch();
  const [isInitializing, setIsInitializing] = useState(!hasInitialized);

  useEffect(() => {
    const initializeAuth = async () => {
      // 如果已经初始化过，直接返回
      if (hasInitialized) {
        setIsInitializing(false);
        return;
      }

      const token = localStorage.getItem('token');
      if (!token) {
        dispatch(clearUser());
        hasInitialized = true;
        setIsInitializing(false);
        return;
      }

      try {
        dispatch(setLoading(true));
        const response = await authService.getUserInfo();
        if (response.success && response.data) {
          dispatch(setUser(response.data));
        } else {
          // token无效，清除认证信息
          localStorage.removeItem('token');
          localStorage.removeItem('refreshToken');
          dispatch(clearUser());
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        dispatch(clearUser());
      } finally {
        dispatch(setLoading(false));
        hasInitialized = true;
        setIsInitializing(false);
      }
    };

    initializeAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // 空依赖数组，确保只在组件挂载时执行一次

  // 如果正在初始化，显示加载状态
  if (isInitializing) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        flexDirection: 'column',
        gap: '16px'
      }}>
        <div>正在初始化...</div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
