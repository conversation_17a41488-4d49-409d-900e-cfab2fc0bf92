import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../store';
import { setUser, clearUser, setLoading } from '../store/slices/authSlice';
import { authService } from '../services/authService';

export interface User {
  id: number;
  username: string;
  email: string;
  role: string;
  avatar?: string;
  realName?: string;
}

export interface LoginCredentials {
  username: string;
  password: string;
}

export const useAuth = () => {
  const dispatch = useDispatch();
  const { user, isAuthenticated, loading } = useSelector((state: RootState) => state.auth);

  // 登录
  const login = async (credentials: LoginCredentials) => {
    try {
      console.log('useAuth: 开始登录流程');
      dispatch(setLoading(true));
      const response = await authService.login(credentials);
      console.log('useAuth: 登录服务响应', response);

      if (response.success && response.data) {
        // 保存token到localStorage
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('refreshToken', response.data.refreshToken);
        console.log('useAuth: Token已保存到localStorage');

        // 直接使用登录响应中的用户信息
        dispatch(setUser(response.data.user));
        console.log('useAuth: 用户信息已设置到Redux', response.data.user);

        return { success: true };
      }

      console.log('useAuth: 登录失败', response.message);
      return { success: false, message: response.message || '登录失败' };
    } catch (error: any) {
      console.error('useAuth: 登录异常', error);
      return { success: false, message: error.message || '登录失败' };
    } finally {
      dispatch(setLoading(false));
    }
  };

  // 登出
  const logout = async () => {
    try {
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      // 清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      // 清除Redux状态
      dispatch(clearUser());
    }
  };

  // 检查认证状态
  const checkAuth = async () => {
    const token = localStorage.getItem('token');
    if (!token) {
      dispatch(clearUser());
      return false;
    }

    try {
      dispatch(setLoading(true));
      const response = await authService.getUserInfo();
      if (response.success && response.data) {
        dispatch(setUser(response.data));
        return true;
      } else {
        // token无效，清除认证信息
        localStorage.removeItem('token');
        localStorage.removeItem('refreshToken');
        dispatch(clearUser());
        return false;
      }
    } catch (error) {
      console.error('Check auth error:', error);
      localStorage.removeItem('token');
      localStorage.removeItem('refreshToken');
      dispatch(clearUser());
      return false;
    } finally {
      dispatch(setLoading(false));
    }
  };

  // 刷新token
  const refreshToken = async () => {
    const refreshTokenValue = localStorage.getItem('refreshToken');
    if (!refreshTokenValue) {
      return false;
    }

    try {
      const response = await authService.refreshToken(refreshTokenValue);
      if (response.success && response.data) {
        localStorage.setItem('token', response.data.token);
        localStorage.setItem('refreshToken', response.data.refreshToken);
        return true;
      }
      return false;
    } catch (error) {
      console.error('Refresh token error:', error);
      return false;
    }
  };

  // 初始化时检查认证状态
  useEffect(() => {
    checkAuth();
  }, []);

  return {
    user,
    isAuthenticated,
    loading,
    login,
    logout,
    checkAuth,
    refreshToken,
  };
};
